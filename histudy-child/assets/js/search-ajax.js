/**
 * AJAX Search functionality for Tutor LMS courses
 * Handles real-time search with course names and tags
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        let searchTimeout;

        // Handle search input events
        $(document).on('input keyup', '.ajax_search_tutor_courses input[name="s"]', function() {
            const $input = $(this);
            const $form = $input.closest('.ajax_search_tutor_courses');
            const $resultsContainer = $('#rbt-course-search-wrapper-layout-1');
            const searchTerm = $input.val().trim();
            const postType = $input.data('post_type') || 'courses';

            // Clear previous timeout
            clearTimeout(searchTimeout);

            // If search term is empty, show default courses
            if (searchTerm.length === 0) {
                loadDefaultCourses($resultsContainer, postType);
                return;
            }

            // If search term is too short, don't search
            if (searchTerm.length < 2) {
                return;
            }

            // Debounce search requests
            searchTimeout = setTimeout(function() {
                performSearch(searchTerm, postType, $resultsContainer);
            }, 300);
        });

        // Handle form submission (prevent default)
        $(document).on('submit', '.ajax_search_tutor_courses', function(e) {
            e.preventDefault();
            const $input = $(this).find('input[name="s"]');
            $input.trigger('input'); // Trigger search
        });
    });

    /**
     * Perform AJAX search
     */
    function performSearch(searchTerm, postType, $container) {
        // Show loading state
        showLoadingState($container);

        $.ajax({
            url: search_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'rbt_ajax_search_tutor_courses',
                courseName: searchTerm,
                postType: postType,
                nonce: search_ajax_object.nonce
            },
            success: function(response) {
                if (response) {
                    $container.html(response);
                } else {
                    showNoResults($container);
                }
            },
            error: function(xhr, status, error) {
                console.error('Search error:', error);
                showErrorState($container);
            }
        });
    }

    /**
     * Load default courses when search is empty
     */
    function loadDefaultCourses($container, postType) {
        $.ajax({
            url: search_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'rbt_ajax_search_tutor_courses',
                courseName: '',
                postType: postType,
                nonce: search_ajax_object.nonce
            },
            success: function(response) {
                if (response) {
                    $container.html(response);
                }
            },
            error: function(xhr, status, error) {
                console.error('Default courses load error:', error);
            }
        });
    }

    /**
     * Show loading state
     */
    function showLoadingState($container) {
        $container.html(`
            <div class="col-12">
                <div class="search-loading text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Searching...</span>
                    </div>
                    <p class="mt-2">Searching courses...</p>
                </div>
            </div>
        `);
    }

    /**
     * Show no results state
     */
    function showNoResults($container) {
        $container.html(`
            <div class="col-12">
                <div class="no-result-found text-center py-4">
                    <i class="feather-search mb-3" style="font-size: 48px; color: #ccc;"></i>
                    <h5>No courses found</h5>
                    <p>Try searching with different keywords or check course tags.</p>
                </div>
            </div>
        `);
    }

    /**
     * Show error state
     */
    function showErrorState($container) {
        $container.html(`
            <div class="col-12">
                <div class="search-error text-center py-4">
                    <i class="feather-alert-triangle mb-3" style="font-size: 48px; color: #e74c3c;"></i>
                    <h5>Search Error</h5>
                    <p>Something went wrong. Please try again.</p>
                </div>
            </div>
        `);
    }

})(jQuery);
