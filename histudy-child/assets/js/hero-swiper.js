/**
 * Hero Carousel functionality
 */
(function () {
    document.addEventListener('DOMContentLoaded', function () {
        initCarousel();

        function initCarousel() {
            const carouselTrack = document.querySelector('.carousel-track');
            const carouselCards = document.querySelectorAll('.carousel-card');

            if (!carouselTrack || !carouselCards.length) return;

            // Set exact positions for all cards
            const leftCards = document.querySelectorAll('.carousel-card[data-column="left"]');
            const rightCards = document.querySelectorAll('.carousel-card[data-column="right"]');

            // Define fixed positions for desktop
            const cardPositions = {
                desktop: {
                    height: 220,
                    gap: 24,
                    offset: 120
                },
                tablet: {
                    height: 170,
                    gap: 20,
                    offset: 100
                },
                mobile: {
                    height: 140,
                    gap: 16,
                    offset: 72
                },
                smallMobile: {
                    height: 120,
                    gap: 12,
                    offset: 60
                }
            };

            // Get current device settings
            let currentDevice = 'desktop';
            if (window.innerWidth <= 480) {
                currentDevice = 'smallMobile';
            } else if (window.innerWidth <= 767) {
                currentDevice = 'mobile';
            } else if (window.innerWidth <= 1024) {
                currentDevice = 'tablet';
            }

            const settings = cardPositions[currentDevice];

            // Position left column cards with fixed positions
            leftCards.forEach(card => {
                const index = parseInt(card.getAttribute('data-index'));
                const position = index * (settings.height + settings.gap);

                // Get existing background color from inline style
                const existingStyle = card.getAttribute('style') || '';
                const bgColorMatch = existingStyle.match(/background-color:\s*([^;]+)/);
                const backgroundColor = bgColorMatch ? bgColorMatch[1] : '';

                // Set exact position while preserving background color
                card.style.cssText = `
                    ${backgroundColor ? `background-color: ${backgroundColor};` : ''}
                    position: absolute !important;
                    top: ${position}px !important;
                    height: ${settings.height}px !important;
                    margin: 0 !important;
                    padding: 1.5rem !important;
                    box-sizing: border-box !important;
                    left: 0 !important;
                    width: 100% !important;
                    z-index: 5 !important;
                    border-radius: 0.75rem !important;
                    overflow: hidden !important;
                    cursor: pointer !important;
                    perspective: 1500px !important;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                    border: 1px solid rgba(255, 255, 255, 0.2) !important;
                    display: flex !important;
                    flex-direction: column !important;
                    justify-content: center !important;
                    transform: translate3d(0, 0, 0) !important;
                    flex-shrink: 0 !important;
                    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
                `;
            });

            // Position right column cards with fixed positions
            rightCards.forEach(card => {
                const index = parseInt(card.getAttribute('data-index'));
                const position = index * (settings.height + settings.gap) + settings.offset;

                // Get existing background color from inline style
                const existingStyle = card.getAttribute('style') || '';
                const bgColorMatch = existingStyle.match(/background-color:\s*([^;]+)/);
                const backgroundColor = bgColorMatch ? bgColorMatch[1] : '';

                // Set exact position while preserving background color
                card.style.cssText = `
                    ${backgroundColor ? `background-color: ${backgroundColor};` : ''}
                    position: absolute !important;
                    top: ${position}px !important;
                    height: ${settings.height}px !important;
                    margin: 0 !important;
                    padding: 1rem !important;
                    box-sizing: border-box !important;
                    left: 0 !important;
                    width: 100% !important;
                    z-index: 5 !important;
                    border-radius: 0.75rem !important;
                    overflow: hidden !important;
                    cursor: pointer !important;
                    perspective: 1500px !important;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                    border: 1px solid rgba(255, 255, 255, 0.2) !important;
                    display: flex !important;
                    flex-direction: column !important;
                    justify-content: center !important;
                    transform: translate3d(0, 0, 0) !important;
                    flex-shrink: 0 !important;
                    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
                `;
            });

            // Clone the wrapper for infinite scrolling
            const columnsWrapper = document.querySelector('.carousel-columns-wrapper');
            if (columnsWrapper) {
                // Create an exact clone
                const clone = columnsWrapper.cloneNode(true);

                // Position the clone cards as well
                const cloneLeftCards = clone.querySelectorAll('.carousel-card[data-column="left"]');
                const cloneRightCards = clone.querySelectorAll('.carousel-card[data-column="right"]');

                // Position clone left column cards with fixed positions
                cloneLeftCards.forEach(card => {
                    const index = parseInt(card.getAttribute('data-index'));
                    const position = index * (settings.height + settings.gap);

                    // Get existing background color from inline style
                    const existingStyle = card.getAttribute('style') || '';
                    const bgColorMatch = existingStyle.match(/background-color:\s*([^;]+)/);
                    const backgroundColor = bgColorMatch ? bgColorMatch[1] : '';

                    // Set exact position while preserving background color
                    card.style.cssText = `
                        ${backgroundColor ? `background-color: ${backgroundColor};` : ''}
                        position: absolute !important;
                        top: ${position}px !important;
                        height: ${settings.height}px !important;
                        margin: 0 !important;
                        padding: 1rem !important;
                        box-sizing: border-box !important;
                        left: 0 !important;
                        width: 100% !important;
                        z-index: 5 !important;
                        border-radius: 0.75rem !important;
                        overflow: hidden !important;
                        cursor: pointer !important;
                        perspective: 1500px !important;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                        border: 1px solid rgba(255, 255, 255, 0.2) !important;
                        display: flex !important;
                        flex-direction: column !important;
                        justify-content: center !important;
                        transform: translate3d(0, 0, 0) !important;
                        flex-shrink: 0 !important;
                        transition: transform 0.3s ease, box-shadow 0.3s ease !important;
                    `;
                });

                // Position clone right column cards with fixed positions
                cloneRightCards.forEach(card => {
                    const index = parseInt(card.getAttribute('data-index'));
                    const position = index * (settings.height + settings.gap) + settings.offset;

                    // Get existing background color from inline style
                    const existingStyle = card.getAttribute('style') || '';
                    const bgColorMatch = existingStyle.match(/background-color:\s*([^;]+)/);
                    const backgroundColor = bgColorMatch ? bgColorMatch[1] : '';

                    // Set exact position while preserving background color
                    card.style.cssText = `
                        ${backgroundColor ? `background-color: ${backgroundColor};` : ''}
                        position: absolute !important;
                        top: ${position}px !important;
                        height: ${settings.height}px !important;
                        margin: 0 !important;
                        padding: 1rem !important;
                        box-sizing: border-box !important;
                        left: 0 !important;
                        width: 100% !important;
                        z-index: 5 !important;
                        border-radius: 0.75rem !important;
                        overflow: hidden !important;
                        cursor: pointer !important;
                        perspective: 1500px !important;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                        border: 1px solid rgba(255, 255, 255, 0.2) !important;
                        display: flex !important;
                        flex-direction: column !important;
                        justify-content: center !important;
                        transform: translate3d(0, 0, 0) !important;
                        flex-shrink: 0 !important;
                        transition: transform 0.3s ease, box-shadow 0.3s ease !important;
                    `;
                });

                // Add the clone to the track
                carouselTrack.appendChild(clone);

                // Set the animation to use exact pixels for translateY
                setTimeout(function() {
                    // Calculate the exact height based on the number of cards
                    const maxCards = Math.max(leftCards.length, rightCards.length);
                    const lastCardPosition = (maxCards - 1) * (settings.height + settings.gap) + settings.height;
                    const wrapperHeight = lastCardPosition + settings.offset + 50; // Add offset and extra padding

                    // Create keyframes for smooth scrolling
                    const styleId = 'carousel-animation-style';
                    let styleElement = document.getElementById(styleId);

                    // Remove existing style if it exists
                    if (styleElement) {
                        styleElement.remove();
                    }

                    // Create new style element
                    styleElement = document.createElement('style');
                    styleElement.id = styleId;
                    styleElement.textContent = `
                    @keyframes scrollVertical {
                        0% {
                            transform: translateY(0);
                        }
                        100% {
                            transform: translateY(-${wrapperHeight}px);
                        }
                    }

                    .carousel-track.carousel-animate {
                        animation: scrollVertical 30s linear infinite;
                    }`;
                    document.head.appendChild(styleElement);

                    // Remove any existing animation class
                    carouselTrack.classList.remove('carousel-animate');

                    // Force a reflow to ensure the animation restarts
                    void carouselTrack.offsetWidth;

                    // Start animation
                    carouselTrack.classList.add('carousel-animate');
                }, 1000); // Longer delay to ensure all styles are applied
            }

            // Add event listeners to cards
            addCardEventListeners();

            // Pause animation on hover
            const carousel = document.querySelector('.hero-carousel');
            if (carousel) {
                carousel.addEventListener('mouseenter', function() {
                    if (carouselTrack.classList.contains('carousel-animate')) {
                        carouselTrack.style.animationPlayState = 'paused';
                    }
                });

                carousel.addEventListener('mouseleave', function() {
                    if (carouselTrack.classList.contains('carousel-animate')) {
                        carouselTrack.style.animationPlayState = 'running';
                    }
                });
            }

            // Handle window resize to maintain consistent spacing
            window.addEventListener('resize', function() {
                // Debounce the resize event
                clearTimeout(window.resizeTimer);
                window.resizeTimer = setTimeout(function() {
                    // Get current device settings
                    let currentDevice = 'desktop';
                    if (window.innerWidth <= 480) {
                        currentDevice = 'smallMobile';
                    } else if (window.innerWidth <= 767) {
                        currentDevice = 'mobile';
                    } else if (window.innerWidth <= 1024) {
                        currentDevice = 'tablet';
                    }

                    const settings = cardPositions[currentDevice];

                    // Get all cards
                    const allCards = document.querySelectorAll('.carousel-card');
                    const leftCards = document.querySelectorAll('.carousel-card[data-column="left"]');
                    const rightCards = document.querySelectorAll('.carousel-card[data-column="right"]');

                    // Update all cards with new positions
                    allCards.forEach(card => {
                        const index = parseInt(card.getAttribute('data-index'));
                        const column = card.getAttribute('data-column');
                        let position;

                        if (column === 'left') {
                            position = index * (settings.height + settings.gap);
                        } else {
                            position = index * (settings.height + settings.gap) + settings.offset;
                        }

                        // Get existing background color from inline style
                        const existingStyle = card.getAttribute('style') || '';
                        const bgColorMatch = existingStyle.match(/background-color:\s*([^;]+)/);
                        const backgroundColor = bgColorMatch ? bgColorMatch[1] : '';

                        // Set exact position while preserving background color
                        card.style.cssText = `
                            ${backgroundColor ? `background-color: ${backgroundColor};` : ''}
                            position: absolute !important;
                            top: ${position}px !important;
                            height: ${settings.height}px !important;
                            margin: 0 !important;
                            padding: 1rem !important;
                            box-sizing: border-box !important;
                            left: 0 !important;
                            width: 100% !important;
                            z-index: 5 !important;
                            border-radius: 0.75rem !important;
                            overflow: hidden !important;
                            cursor: pointer !important;
                            perspective: 1500px !important;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                            border: 1px solid rgba(255, 255, 255, 0.2) !important;
                            display: flex !important;
                            flex-direction: column !important;
                            justify-content: center !important;
                            transform: translate3d(0, 0, 0) !important;
                            flex-shrink: 0 !important;
                            transition: transform 0.3s ease, box-shadow 0.3s ease !important;
                        `;
                    });

                    // Recalculate animation height
                    const maxCards = Math.max(leftCards.length, rightCards.length);
                    const lastCardPosition = (maxCards - 1) * (settings.height + settings.gap) + settings.height;
                    const wrapperHeight = lastCardPosition + settings.offset + 50;

                    // Update animation
                    const styleId = 'carousel-animation-style';
                    let styleElement = document.getElementById(styleId);

                    // Remove existing style if it exists
                    if (styleElement) {
                        styleElement.remove();
                    }

                    // Create new style element
                    styleElement = document.createElement('style');
                    styleElement.id = styleId;
                    styleElement.textContent = `
                    @keyframes scrollVertical {
                        0% {
                            transform: translateY(0);
                        }
                        100% {
                            transform: translateY(-${wrapperHeight}px);
                        }
                    }

                    .carousel-track.carousel-animate {
                        animation: scrollVertical 30s linear infinite;
                    }`;
                    document.head.appendChild(styleElement);

                    // Remove any existing animation class
                    const carouselTrack = document.querySelector('.carousel-track');
                    if (carouselTrack) {
                        carouselTrack.classList.remove('carousel-animate');

                        // Force a reflow to ensure the animation restarts
                        void carouselTrack.offsetWidth;

                        // Start animation
                        carouselTrack.classList.add('carousel-animate');
                    }
                }, 250);
            });
        }

        // Function to add event listeners to cards
        function addCardEventListeners() {
            // Get all cards
            const allCards = document.querySelectorAll('.carousel-card');

            allCards.forEach(card => {
                // For click
                card.addEventListener('click', function () {
                    const cardId = this.getAttribute('data-id');

                    // If card is already flipped, just flip it back
                    if (this.classList.contains('flipped')) {
                        this.classList.remove('flipped');
                        return;
                    }

                    // Remove flipped class from all cards with the same ID
                    document.querySelectorAll(`.carousel-card[data-id="${cardId}"]`).forEach(c => {
                        c.classList.remove('flipped');
                    });

                    // Add flipped class to this card
                    this.classList.add('flipped');

                    // Show celebration message for learning a new sentence
                    showCardCelebration(this);

                    // Auto flip back after showing the English translation and celebration
                    setTimeout(() => {
                        if (this.classList.contains('flipped')) {
                            this.classList.remove('flipped');
                        }
                    }, 2500); // Flip back after 2.5 seconds
                });

                // For touch devices
                card.addEventListener('touchstart', function (e) {
                    e.preventDefault(); // Prevent scrolling on touch
                    const cardId = this.getAttribute('data-id');

                    // If card is already flipped, just flip it back
                    if (this.classList.contains('flipped')) {
                        this.classList.remove('flipped');
                        return;
                    }

                    // Remove flipped class from all cards with the same ID
                    document.querySelectorAll(`.carousel-card[data-id="${cardId}"]`).forEach(c => {
                        c.classList.remove('flipped');
                    });

                    // Add flipped class to this card
                    this.classList.add('flipped');

                    // Show celebration message for learning a new sentence
                    showCardCelebration(this);

                    // Auto flip back after showing the English translation and celebration
                    setTimeout(() => {
                        if (this.classList.contains('flipped')) {
                            this.classList.remove('flipped');
                        }
                    }, 2500); // Flip back after 2.5 seconds
                });
            });
        }

        // Function to show celebration when card is flipped
        function showCardCelebration(card) {
            // Get card position relative to viewport
            const cardRect = card.getBoundingClientRect();
            const cardCenterX = cardRect.left + cardRect.width / 2;
            const cardTopY = cardRect.top;

            // Adjust positioning for mobile
            const isMobile = window.innerWidth <= 480;
            const isTablet = window.innerWidth <= 768;
            const offsetY = isMobile ? 40 : isTablet ? 50 : 60;
            const fontSize = isMobile ? '9px' : isTablet ? '10px' : '12px';
            const padding = isMobile ? '5px 10px' : isTablet ? '6px 12px' : '8px 16px';
            const borderRadius = isMobile ? '15px' : '20px';

            // Create celebration message
            const celebration = document.createElement('div');
            celebration.innerHTML = '🎉 Tebrikler, yeni bir cümle daha öğrendin! ✨';
            celebration.className = 'card-celebration';
            celebration.style.cssText = `
                position: fixed;
                top: ${cardTopY - offsetY}px;
                left: ${cardCenterX}px;
                transform: translateX(-50%);
                background: linear-gradient(45deg, #27ae60, #2ecc71);
                color: white;
                padding: ${padding};
                border-radius: ${borderRadius};
                font-size: ${fontSize};
                font-weight: bold;
                white-space: nowrap;
                z-index: 10000;
                box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
                border: 1px solid rgba(255,255,255,0.8);
                opacity: 0;
                animation: cardCelebrationPop 3s ease forwards;
                pointer-events: none;
            `;

            // Add to body instead of card
            document.body.appendChild(celebration);

            // Create confetti effect
            createCardConfetti(card);

            // Remove celebration after animation
            setTimeout(() => {
                if (celebration && document.body.contains(celebration)) {
                    document.body.removeChild(celebration);
                }
            }, 3000);
        }

        // Function to create confetti effect for cards
        function createCardConfetti(card) {
            const colors = ['#FFD93D', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#27ae60', '#2ecc71'];
            const cardRect = card.getBoundingClientRect();
            const cardCenterX = cardRect.left + cardRect.width / 2;
            const cardCenterY = cardRect.top + cardRect.height / 2;

            for (let i = 0; i < 8; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'card-confetti';
                confetti.style.cssText = `
                    position: fixed;
                    width: 6px;
                    height: 6px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    border-radius: 50%;
                    top: ${cardCenterY}px;
                    left: ${cardCenterX}px;
                    z-index: 9999;
                    pointer-events: none;
                    animation: cardConfettiFall 2s ease-out forwards;
                    animation-delay: ${Math.random() * 0.3}s;
                `;

                // Random direction
                const angle = (Math.random() * 360) * (Math.PI / 180);
                const velocity = 50 + Math.random() * 30;
                const x = Math.cos(angle) * velocity;
                const y = Math.sin(angle) * velocity;

                confetti.style.setProperty('--x', x + 'px');
                confetti.style.setProperty('--y', y + 'px');

                document.body.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    if (confetti && document.body.contains(confetti)) {
                        document.body.removeChild(confetti);
                    }
                }, 2000);
            }
        }

        // Add CSS animations for card celebrations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes cardCelebrationPop {
                0% {
                    opacity: 0;
                    transform: translateX(-50%) scale(0) rotate(-10deg);
                }
                20% {
                    opacity: 1;
                    transform: translateX(-50%) scale(1.1) rotate(2deg);
                }
                40%, 60% {
                    opacity: 1;
                    transform: translateX(-50%) scale(1) rotate(0deg);
                }
                100% {
                    opacity: 0;
                    transform: translateX(-50%) scale(0.8) rotate(5deg);
                }
            }

            @keyframes cardConfettiFall {
                0% {
                    opacity: 1;
                    transform: translate(0, 0) rotate(0deg);
                }
                100% {
                    opacity: 0;
                    transform: translate(var(--x), var(--y)) rotate(360deg);
                }
            }

            /* Mobile responsive adjustments for card celebrations */
            @media (max-width: 768px) {
                .card-celebration {
                    font-size: 10px !important;
                    padding: 6px 12px !important;
                }
            }

            @media (max-width: 480px) {
                .card-celebration {
                    font-size: 9px !important;
                    padding: 5px 10px !important;
                    border-radius: 15px !important;
                }
            }
        `;
        document.head.appendChild(style);
    });
})();
