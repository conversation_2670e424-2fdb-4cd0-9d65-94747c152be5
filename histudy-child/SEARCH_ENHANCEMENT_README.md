# Search Enhancement for <PERSON><PERSON> Course Tags

## Overview
This enhancement extends the search functionality in the Histudy theme to include course tags in addition to course titles and content. The search now works with Tutor <PERSON>MS course tags taxonomy (`course-tag`).

## What's Changed

### 1. Enhanced Search Function
- **File**: `wp-content/themes/histudy-child/functions.php`
- **Function**: `rbt_ajax_search_tutor_courses_enhanced()`
- **Enhancement**: The search now queries both:
  - Course titles and content (original functionality)
  - Course tags using the `course-tag` taxonomy

### 2. Search Template
- **File**: `wp-content/themes/histudy-child/template-parts/header/elements/search-dropdown.php`
- **Change**: Copied from parent theme to child theme for future customizations
- **Note**: Template functionality remains the same, but now uses the enhanced search function

## How It Works

1. **User types in search box**: The AJAX search is triggered
2. **Dual search execution**:
   - First query: Searches course titles and content
   - Second query: Searches course tags for matching terms
3. **Results combination**: Results from both queries are combined and duplicates removed
4. **Display**: Combined results are displayed in the search dropdown

## Technical Details

### Search Logic
```php
// Title/Content search
$title_content_args = array(
    'post_type' => $postType,
    's' => $courseName
);

// Tag search
$tag_args = array(
    'post_type' => $postType,
    'tax_query' => array(
        array(
            'taxonomy' => 'course-tag',
            'field'    => 'name',
            'terms'    => $courseName,
            'operator' => 'LIKE'
        )
    )
);
```

### AJAX Hook Override
The child theme removes the parent theme's AJAX handlers and replaces them:
```php
remove_action('wp_ajax_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses');
remove_action('wp_ajax_nopriv_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses');

add_action('wp_ajax_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses_enhanced');
add_action('wp_ajax_nopriv_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses_enhanced');
```

## Benefits

1. **Improved Search Results**: Users can now find courses by searching for tags
2. **Better User Experience**: More comprehensive search results
3. **Child Theme Safe**: All modifications are in the child theme, safe from parent theme updates
4. **Backward Compatible**: Maintains all existing search functionality

## Usage Examples

- Search for "beginner" will find courses tagged with "beginner"
- Search for "javascript" will find courses with "javascript" in title, content, or tags
- Search for "web development" will find courses tagged with "web development"

## Maintenance

- The enhancement is contained within the child theme
- Parent theme updates will not affect this functionality
- The search dropdown template can be further customized in the child theme if needed

## Testing

To test the enhancement:
1. Create courses with various tags in Tutor LMS
2. Use the search dropdown in the header
3. Search for tag names to verify they return relevant courses
4. Verify that title/content search still works as before

### Debug Helper
For admin users, add `?debug_course_tags=1` to any page URL to see all available course tags. This helps with testing and troubleshooting.

### Detailed Testing
See `TESTING_GUIDE.md` for comprehensive testing instructions.
