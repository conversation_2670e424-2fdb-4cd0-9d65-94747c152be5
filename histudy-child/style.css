/*
    Theme Name: Histu<PERSON> Child
    Theme URI: https://rainbowthemes.net/themes/histudy
    Template: histudy
    Description: Histudy is created for Learning Management System. Online OR Offline The template is perfect for e-Learning, Course School, Online School, Kindergarten, Classic LMS, University Status, Instructor Portfolio, Language Academy, Gym Coaching, Online Course, Single Course, marketplace, University Classic, Home Elegant, Home technology, and other needed dashboard, inner and details pages availability. The template has included everything you need for a complete online education center and LMS.
    Author: Rainbow-Themes
    Author URI: https://themeforest.net/user/rainbow-themes/portfolio
    Version: 1.0.0
    License: Envato Marketplaces Split License
    License URI: https://themeforest.net/licenses/standard
    Text Domain: histudy-child
    Tags: one-column, two-columns, right-sidebar, custom-header, custom-menu, editor-style, featured-images, microformats, post-formats, sticky-post
    This theme, like WordPress, is licensed under the Envato Split License.
*/

/* Base styles */
:root {
  /* Colors - keeping the same as requested */
  --primary-color: #1149AB;
  --secondary-color: #9C93AB;
  --text-color: #1E1E1E;
  --light-bg: #F6F6F6;
  --white: #FFFFFF;
  --dark-blue: #071D44;
  --purple: #59427F;
  --light-purple: #E9E3F3;
  --dark-gray: #3E3848;
  --light-gray: #F2F2F2;
  --medium-gray: #DDDDDD;
  --green-primary: #27ae60;
  --green-secondary: #2ecc71;

  /* Typography - Refined Scale */
  --font-primary: 'Plus Jakarta Sans', sans-serif;
  --font-secondary: 'Inter', sans-serif;

  /* Font sizes - consistent typographic scale */
  --fs-xs: 0.75rem;     /* 12px */
  --fs-sm: 0.875rem;    /* 14px */
  --fs-base: 1rem;      /* 16px */
  --fs-md: 1.125rem;    /* 18px */
  --fs-lg: 1.25rem;     /* 20px */
  --fs-xl: 1.5rem;      /* 24px */
  --fs-2xl: 1.75rem;    /* 28px */
  --fs-3xl: 2rem;       /* 32px */
  --fs-4xl: 2.5rem;     /* 40px */
  --fs-5xl: 3rem;       /* 48px */

  /* Font weights - standardized */
  --fw-regular: 400;
  --fw-medium: 500;
  --fw-semibold: 600;
  --fw-bold: 700;
  --fw-extrabold: 800;

  /* Line heights - optimized for readability */
  --lh-none: 1;
  --lh-tight: 1.2;
  --lh-snug: 1.375;
  --lh-normal: 1.5;
  --lh-relaxed: 1.625;
  --lh-loose: 1.75;

  /* Spacing system - consistent scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.5rem;    /* 24px */
  --space-6: 2rem;      /* 32px */
  --space-7: 2.5rem;    /* 40px */
  --space-8: 3rem;      /* 48px */
  --space-9: 4rem;      /* 64px */
  --space-10: 5rem;     /* 80px */
  --space-11: 6rem;
  --space-12: 7rem;
  --space-13: 8rem;

  /* Border radius - consistent */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 0.75rem;  /* 12px */
  --radius-xl: 1rem;     /* 16px */
  --radius-full: 9999px; /* For circular elements */

  /* Shadows - refined for depth */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;

  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Text utilities */
.text-xs { font-size: var(--fs-xs); }
.text-sm { font-size: var(--fs-sm); }
.text-base { font-size: var(--fs-base); }
.text-md { font-size: var(--fs-md); }
.text-lg { font-size: var(--fs-lg); }
.text-xl { font-size: var(--fs-xl); }
.text-2xl { font-size: var(--fs-2xl); }
.text-3xl { font-size: var(--fs-3xl); }
.text-4xl { font-size: var(--fs-4xl); }

.font-regular { font-weight: var(--fw-regular); }
.font-medium { font-weight: var(--fw-medium); }
.font-semibold { font-weight: var(--fw-semibold); }
.font-bold { font-weight: var(--fw-bold); }
.font-extrabold { font-weight: var(--fw-extrabold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-dark { color: var(--text-color); }
.text-gray { color: var(--dark-gray); }
.text-light { color: var(--white); }
.text-light-gray { color: var(--light-gray); }

/* Hero styles */

.hero-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-13);
  margin: 15px auto 0;
}

@media (min-width: 768px) {
  .hero-wrapper {
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}

.hero-content {
  max-width: 500px;
  position: relative;
  z-index: 2;
  padding: var(--space-4) 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

/* Hero Title Responsive Styles - Educational & Cheerful */
.hero-content h1 {
  font-family: var(--font-primary);
  font-weight: var(--fw-extrabold);
  color: var(--text-color);
  line-height: var(--lh-tight);
  margin: 0 0 var(--space-5) 0;
  text-align: left;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  padding-top:10px;
  /* Mobile first approach - start with mobile size */
  font-size: var(--fs-2xl); /* 28px on mobile */
}

/* Educational word highlighting - like flashcards */
.hero-content h1 .edu-word {
  display: inline-block;
  position: relative;
  padding: 4px 8px;
  margin: 2px 0;
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  cursor: pointer;
}

.hero-content h1 .edu-word:hover {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  transform: translateY(-3px) rotate(2deg) scale(1.1);
  box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
}

.hero-content h1 .edu-word.learned {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  transform: translateY(-2px) scale(1.05);
  animation: learnedBounce 0.6s ease;
}

@keyframes learnedBounce {
  0% { transform: translateY(-2px) scale(1.05); }
  50% { transform: translateY(-8px) scale(1.15) rotate(5deg); }
  100% { transform: translateY(-2px) scale(1.05); }
}

/* Cheerful floating emojis - Properly positioned */
.hero-content h1::before {
  content: '📚';
  position: absolute;
  top: -10px;
  left: -30px;
  font-size: 20px;
  animation: floatEmoji 3s ease-in-out infinite;
  opacity: 0.8;
  z-index: 5;
}


@keyframes floatEmoji {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(5deg); }
  66% { transform: translateY(-5px) rotate(-3deg); }
}

/* Progress bar effect when clicking words */
.hero-content h1 .progress-bar {
  position: absolute;
  bottom: -8px;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
  border-radius: 2px;
  width: 0%;
  transition: width 0.8s ease;
}

.hero-content h1.learning .progress-bar {
  width: 100%;
}

/* Confetti effect */
.hero-content h1 .confetti {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #FFD93D;
  border-radius: 50%;
  animation: confettiFall 2s ease-out forwards;
  opacity: 0;
}

.hero-content h1 .confetti:nth-child(odd) {
  background: #FF6B6B;
  animation-delay: 0.1s;
}

.hero-content h1 .confetti:nth-child(even) {
  background: #4ECDC4;
  animation-delay: 0.2s;
}

@keyframes confettiFall {
  0% {
    opacity: 1;
    transform: translateY(-20px) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100px) rotate(360deg);
  }
}

/* Tablet styles */
@media (min-width: 768px) {
  .hero-content h1 {
    font-size: var(--fs-3xl); /* 32px on tablet */
    line-height: var(--lh-tight);
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .hero-content h1 {
    font-size: var(--fs-4xl); /* 40px on desktop */
    line-height: var(--lh-tight);
  }
}

/* Large desktop styles */
@media (min-width: 1200px) {
  .hero-content h1 {
    font-size: var(--fs-5xl); /* 48px on large desktop */
    line-height: var(--lh-tight);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .hero-content {
    text-align: center;
    align-items: center;
  }

  .hero-content h1 {
    text-align: center;
    font-size: var(--fs-xl); /* 24px on small mobile */
    line-height: var(--lh-snug);
    margin-bottom: var(--space-4);
  }
}

/* Very small mobile devices */
@media (max-width: 480px) {
  .hero-content h1 {
    font-size: var(--fs-2xl);
    line-height: var(--lh-snug);
    margin-bottom: 0;
  }

  /* Mobile optimizations for educational effects */
  .hero-content h1::before {
    font-size: 14px;
    top: 15px;
    left: 35px;
  }

  .hero-content h1::after {
    font-size: 12px;
    bottom: -12px;
    right: -30px;
  }

  .hero-content h1 .edu-word {
    padding: 2px 4px;
    margin: 1px;
    font-size: inherit;
  }

  .hero-content h1 .edu-word:hover {
    transform: translateY(-2px) scale(1.05);
  }

  /* CTA button emojis disabled for mobile */
  .hero-content .rbt-btn::before,
  .hero-cta-mobile .rbt-btn::before {
    display: none;
  }

  .hero-content .rbt-btn::after,
  .hero-cta-mobile .rbt-btn::after {
    display: none;
  }

  .language-score {
    font-size: 4px;
    padding: 1px 2px;
    top: -20px;
    right: 10px;
    margin: 2px 0;
    border-radius: 3px;
    max-width: 50px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.4);
    line-height: 1;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .language-score {
    font-size: 3px;
    padding: 1px 2px;
    top: -15px;
    right: 8px;
    margin: 1px 0;
    border-radius: 2px;
    max-width: 40px;
    line-height: 1;
  }
}

/* Hero Button Responsive Styles - Educational & Fun */
.hero-content .rbt-btn,
.hero-cta-mobile .rbt-btn {
  font-size: var(--fs-base);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--fw-bold);
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: inherit;
  cursor: pointer;
  background: linear-gradient(135deg, var(--green-primary), var(--green-secondary));
  background-size: 200% 200%;
  color: white;
  border: 3px solid transparent !important;
  outline: none !important;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* Remove any focus outlines */
.hero-content .rbt-btn:focus,
.hero-cta-mobile .rbt-btn:focus {
  border: 3px solid transparent !important;
  outline: none !important;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Hover effects - like a learning game button */
.hero-content .rbt-btn:hover,
.hero-cta-mobile .rbt-btn:hover {
  transform: translateY(-5px) scale(1.08);
  box-shadow: 0 12px 30px rgba(39, 174, 96, 0.4);
  animation: buttonBounce 0.6s ease;
  border: 3px solid transparent !important;
  outline: none !important;
}

@keyframes buttonBounce {
  0% { transform: translateY(-5px) scale(1.08); }
  50% { transform: translateY(-8px) scale(1.12) rotate(2deg); }
  100% { transform: translateY(-5px) scale(1.08); }
}

/* Educational icons floating around button - DISABLED */
.hero-content .rbt-btn::before,
.hero-cta-mobile .rbt-btn::before {
  content: '';
  display: none;
}

.hero-content .rbt-btn::after,
.hero-cta-mobile .rbt-btn::after {
  content: '';
  display: none;
}

@keyframes floatIcon {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-8px) rotate(10deg); }
}

/* Click effect - like completing a lesson */
.hero-content .rbt-btn:active,
.hero-cta-mobile .rbt-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  animation: successPulse 0.3s ease;
  border: 3px solid transparent !important;
  outline: none !important;
}

@keyframes successPulse {
  0% { transform: scale(0.95); }
  50% { transform: scale(1.05); }
  100% { transform: scale(0.95); }
}

/* Progress indicator inside button */
.hero-content .rbt-btn .btn-progress,
.hero-cta-mobile .rbt-btn .btn-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.5);
  width: 0%;
  transition: width 2s ease;
  border-radius: 0 0 25px 25px;
}

.hero-content .rbt-btn:hover .btn-progress,
.hero-cta-mobile .rbt-btn:hover .btn-progress {
  width: 100%;
}

/* Cheerful pulsing effect */
.hero-content .rbt-btn.pulse-learn,
.hero-cta-mobile .rbt-btn.pulse-learn {
  animation: learnPulse 2s infinite;
}

@keyframes learnPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  }
}

/* Achievement badge effect */
.hero-content .rbt-btn .achievement-badge,
.hero-cta-mobile .rbt-btn .achievement-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #FFD93D, #FF6B6B);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: bold;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.hero-content .rbt-btn:hover .achievement-badge,
.hero-cta-mobile .rbt-btn:hover .achievement-badge {
  opacity: 1;
  transform: scale(1);
  animation: badgeBounce 0.5s ease;
}

@keyframes badgeBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Language learning score display */
.language-score {
  position: absolute;
  top: -25px;
  right: 20px;
  background: linear-gradient(45deg, #e74c3c, #3498db);
  color: white;
  padding: 1px 3px;
  border-radius: 4px;
  font-size: 5px;
  font-weight: 600;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
  z-index: 15;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.5);
  margin: 2px 0;
  white-space: nowrap;
  max-width: 60px;
  text-align: center;
  line-height: 1.1;
}

.language-score.show {
  opacity: 1;
  transform: translateY(0) scale(1.1);
}

/* Turkish-English word states */
.hero-content h1 .edu-word.showing-english {
  background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
  color: white !important;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 15px rgba(39, 174, 96, 0.4);
}

.hero-content h1 .edu-word[data-english]:hover {
  cursor: pointer;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateY(-3px) scale(1.08);
}

/* Flag animation */
@keyframes flagFloat {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-20px) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translateY(-40px) scale(0.8);
  }
}

/* Instruction fade animation */
@keyframes instructionFade {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  20%, 80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
}

/* Enhanced celebration animation */
@keyframes celebrationPop {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0) rotate(-10deg);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) scale(1.2) rotate(5deg);
  }
  40%, 60% {
    opacity: 1;
    transform: translateX(-50%) scale(1) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scale(0.8) rotate(10deg);
  }
}

/* Desktop CTA - visible by default, hidden on mobile */
.hero-cta-desktop {
  display: inline-block;
}

/* Mobile CTA - hidden by default, visible on mobile */
.hero-cta-mobile {
  display: none;
  text-align: center;
  margin-top: var(--space-6);
  width: 100%;
}

@media (max-width: 767px) {
  /* Hide desktop CTA on mobile */
  .hero-cta-desktop {
    display: none !important;
  }

  /* Show mobile CTA on mobile */
  .hero-cta-mobile {
    display: block;
  }

  .hero-content .rbt-btn,
  .hero-cta-mobile .rbt-btn {
    font-size: var(--fs-sm);
    padding: var(--space-2) var(--space-5);
    width: auto;
    min-width: 140px;
  }
}

@media (max-width: 480px) {
  .hero-content .rbt-btn,
  .hero-cta-mobile .rbt-btn {
    font-size: var(--fs-sm);
    padding: var(--space-2) var(--space-4);
    min-width: 120px;
  }

  .hero-cta-mobile {
    margin-top: var(--space-5);
  }
}

.hero-carousel-wrapper {
  flex: 0 0 50%;
  height: 520px;
  max-width: 560px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: visible;
  position: relative;
  z-index: 1;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (max-width: 767px) {
  .hero-wrapper {
    flex-direction: column;
    gap: var(--space-4);
  }

  .hero-carousel-wrapper {
    height: 400px;
    max-width: 100%;
    width: 100%;
    margin-top: 0;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    order: 2; /* Place carousel in the middle */
  }

  .hero-content {
    max-width: 100%;
    flex: 0 0 100%;
    text-align: center;
    padding: 20px 0 10px 0;
    order: 1; /* Place content first */
  }

  .hero-cta-mobile {
    order: 3; /* Place mobile CTA last */
  }
}

@media (max-width: 480px) {
  .hero-carousel-wrapper {
    height: auto;
    max-height: 100vh;
    min-height: 300px;
    display: flex !important;
    align-items: flex-start;
    justify-content: center;
    margin-top: var(--space-4);
    width: 100%;
    overflow: visible;
    position: relative;
    z-index: 1;
    opacity: 1 !important;
    visibility: visible !important;
  }
}

/* Hero Carousel */
.hero-carousel {
  height: 500px;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 2;
  padding: 0 20px;
  box-sizing: border-box;
}

.carousel-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 3;
}

.carousel-track {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: visible;
  gap: 0;
  position: relative;
  box-sizing: border-box;
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
  padding: 0 !important;
  margin: 0 !important;
  top: 0 !important;
  margin-top: 0 !important;
}

/* Animation class applied by JavaScript */
.carousel-track.carousel-animate {
  animation: scrollVertical 30s linear infinite;
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* Pause animation on hover */
.hero-carousel:hover .carousel-track.carousel-animate {
  animation-play-state: paused;
}

/* Ensure columns wrapper has consistent spacing */
.carousel-columns-wrapper {
  display: flex;
  justify-content: center;
  gap: 20px;
  width: 100%;
  position: relative;
  padding: 0 !important;
  margin: 0 auto !important;
  max-width: 1000px;
  flex-shrink: 0;
  box-sizing: border-box;
  transform: translate3d(0, 0, 0);
  top: 0 !important;
  margin-top: 0 !important;
}

.carousel-column {
  position: relative;
  flex: 1;
  overflow: visible;
  min-width: 0;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  top: 0;
  height: 1500px; /* Tall enough to contain all cards */
}

/* Offset the right column to create staggered effect */
.right-column {
  margin-top: 0; /* No margin needed with absolute positioning */
}

/* Card styling with absolute positioning */
.carousel-card {
  position: absolute !important;
  left: 0;
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  perspective: 1500px;
  height: 220px !important; /* Force consistent height */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex !important;
  z-index: 5;
  padding: 1.5rem !important; /* Force consistent padding */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box !important;
  margin: 0 !important; /* Force no margins */
  flex-direction: column;
  justify-content: center;
  transform: translate3d(0, 0, 0);
  flex-shrink: 0 !important; /* Prevent shrinking */
  position: relative;
}

/* Subtle shimmer effect to indicate interactivity */
.carousel-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
  z-index: 1;
  pointer-events: none;
}

.carousel-card:hover::before {
  left: 100%;
}

/* Card hover effect */
.carousel-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}

/* Interactive flip indicator - using card-inner pseudo-elements to avoid conflict */
.carousel-card .card-inner::before {
  content: '';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  z-index: 15;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-normal) ease;
  opacity: 0.9;
  backdrop-filter: blur(4px);
}

.carousel-card .card-inner::after {
  content: '↻';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color);
  z-index: 16;
  animation: rotateHint 3s ease-in-out infinite;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes rotateHint {
  0%, 85%, 100% {
    transform: rotate(0deg);
    opacity: 0.8;
  }
  15%, 70% {
    transform: rotate(180deg);
    opacity: 1;
    color: var(--accent-color);
  }
}

/* Enhanced hover effects for better interactivity indication */
.carousel-card:hover .card-inner::before {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  opacity: 1;
}

.carousel-card:hover .card-inner::after {
  animation-duration: 1s;
  color: var(--accent-color);
  transform: scale(1.1);
}

/* Pulse effect to draw attention */
.carousel-card:nth-child(odd) .card-inner::before {
  animation: pulseIndicator 4s ease-in-out infinite;
  animation-delay: 0s;
}

.carousel-card:nth-child(even) .card-inner::before {
  animation: pulseIndicator 4s ease-in-out infinite;
  animation-delay: 2s;
}

@keyframes pulseIndicator {
  0%, 90%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  5%, 15% {
    transform: scale(1.2);
    opacity: 1;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }
}

/* Instruction tooltip for first card */
.carousel-card:first-child {
  position: relative;
}

.carousel-card:first-child .card-front::before {
  content: 'Hover to see English';
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 20;
  opacity: 0;
  animation: fadeInOut 6s ease-in-out infinite;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

.carousel-card:first-child .card-front::after {
  content: '';
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(0, 0, 0, 0.8);
  z-index: 19;
  opacity: 0;
  animation: fadeInOut 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes fadeInOut {
  0%, 20%, 80%, 100% { opacity: 0; }
  30%, 70% { opacity: 1; }
}

/* Hide instruction when card is hovered or flipped */
.carousel-card:first-child:hover .card-front::before,
.carousel-card:first-child:hover .card-front::after,
.carousel-card:first-child.flipped .card-front::before,
.carousel-card:first-child.flipped .card-front::after {
  opacity: 0 !important;
  animation: none;
}

/* Mobile instruction adjustments */
@media (max-width: 767px) {
  .carousel-card:first-child .card-front::before {
    content: 'Tap to see English';
    font-size: 11px;
    padding: 4px 8px;
    bottom: -30px;
  }

  .carousel-card:first-child .card-front::after {
    bottom: -20px;
    border-bottom-width: 5px;
    border-left-width: 5px;
    border-right-width: 5px;
  }

  /* Adjust flip indicator for mobile to prevent text overlap */
  .carousel-card .card-inner::before {
    width: 18px;
    height: 18px;
    top: 8px;
    right: 8px;
  }

  .carousel-card .card-inner::after {
    width: 18px;
    height: 18px;
    top: 8px;
    right: 8px;
    font-size: 12px;
  }
}

/* Extra small mobile adjustments */
@media (max-width: 480px) {
  /* Make flip indicator even smaller on very small screens */
  .carousel-card .card-inner::before {
    width: 16px;
    height: 16px;
    top: 6px;
    right: 6px;
  }

  .carousel-card .card-inner::after {
    width: 16px;
    height: 16px;
    top: 6px;
    right: 6px;
    font-size: 10px;
  }

  /* Reduce card padding to give more space for text */
  .carousel-card {
    padding: 1rem !important;
  }
}

/* Card inner styles */
.card-inner {
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  position: relative;
  display: flex;
  /* Safari-specific fixes for 3D transforms */
  -webkit-perspective: 1500px;
  perspective: 1500px;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.carousel-card:hover .card-inner {
  transform: rotateY(180deg);
}

.carousel-card.flipped .card-inner {
  transform: rotateY(180deg);
  -webkit-transform: rotateY(180deg);
}

/* Safari-specific fixes for card flip animation to prevent text overlap */
@supports (-webkit-appearance: none) {
  .card-front {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .card-back {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .carousel-card:hover .card-front,
  .carousel-card.flipped .card-front {
    opacity: 0;
    visibility: hidden;
  }

  .carousel-card:hover .card-back,
  .carousel-card.flipped .card-back {
    opacity: 1;
    visibility: visible;
  }

  /* Additional Safari fix for proper 3D rendering */
  .carousel-card {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }

  .card-inner {
    -webkit-transform-origin: center center;
    transform-origin: center center;
  }
}

/* Alternative Safari fix using WebKit-specific media query */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .card-front {
    z-index: 2;
  }

  .card-back {
    z-index: 1;
  }

  .carousel-card:hover .card-front,
  .carousel-card.flipped .card-front {
    z-index: 1;
    opacity: 0;
    pointer-events: none;
  }

  .carousel-card:hover .card-back,
  .carousel-card.flipped .card-back {
    z-index: 2;
    opacity: 1;
    pointer-events: auto;
  }
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  border-radius: 0.75rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Safari-specific fixes for flip animation */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-perspective: 1500px;
  perspective: 1500px;
}

.card-back {
  transform: rotateY(180deg);
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  /* Safari-specific fixes to prevent text overlap */
  -webkit-transform: rotateY(180deg);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.card-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  padding: 0.5rem;
  position: relative;
  z-index: 10;
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 0.75rem;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.card-front h3 {
  font-size: var(--fs-3xl);
  font-weight: var(--fw-bold);
  margin: 0;
  text-align: center;
  line-height: var(--lh-snug);
  /* Text color is set inline via PHP */
}

.card-back h3 {
  font-size: var(--fs-3xl);
  font-weight: var(--fw-bold);
  margin-bottom: 0.75rem;
  text-align: center;
  line-height: var(--lh-snug);
  /* Text color is set inline via PHP */
}

.card-back p {
  font-size: var(--fs-md);
  margin: 0;
  text-align: center;
  line-height: var(--lh-normal);
}

@media (max-width: 1024px) {
  .hero-carousel {
    height: 450px;
  }
  .hero-wrapper {
    gap: var(--space-8);
  }

  .card-front h3,
  .card-back h3 {
    font-size: var(--fs-xl);
    line-height: var(--lh-tight);
  }
}

@media (max-width: 767px) {
  .hero-carousel {
    height: 400px;
    padding: 0 10px;
  }

  .carousel-columns-wrapper {
    max-width: 90%;
  }

  .card-front h3,
  .card-back h3 {
    font-size: var(--fs-xl);
    line-height: var(--lh-tight);
  }

  .card-back p {
    font-size: var(--fs-sm);
    line-height: var(--lh-normal);
  }
}

@media (max-width: 480px) {
  .hero-carousel {
    height: 350px;
    padding: 0 5px;
  }

  .card-front h3,
  .card-back h3 {
    font-size: var(--fs-xl);
    line-height: var(--lh-tight);
  }

  .card-back p {
    font-size: var(--fs-xs);
    line-height: var(--lh-normal);
  }
}

@media (max-width: 480px) {


  .rbt-mobile-counterup {
    flex-direction: column;
    width: 100%;
    align-items: center;
    justify-content: center;
  }
  .rbt-mobile-counterup .col-12 {
    width: 100%;
    flex: 1;
  }
  .rbt-counterup .content .subtitle {
    font-size: var(--fs-2xl);
    line-height: var(--lh-snug);
  }
}
