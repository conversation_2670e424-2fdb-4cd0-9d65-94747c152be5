<?php
/**
 * Hero Carousel Component
 *
 * @package Asil
 */
?>

<div class="hero-carousel">
    <div class="carousel-container">
        <div class="carousel-track">
            <!-- Original Columns -->
            <div class="carousel-columns-wrapper">
                <!-- Left Column - Fixed position -->
                <div class="carousel-column left-column" style="position: relative; height: 1500px;">
                    <?php
                    // Define the cards data for left column
                    $left_cards = array(
                        array(
                            'id' => 1,
                            'bg_color' => 'rgba(17, 73, 171, 0.8)', /* Lighter primary-color */
                            'front_title' => 'Ters gidebilecek her şey ters gitti',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'Everything that could have gone wrong went wrong',
                            'english_title' => 'Everything that could have gone wrong went wrong',
                            'back_text_color' => '#FFFFFF',
                        ),
                        array(
                            'id' => 2,
                            'bg_color' => 'var(--light-purple)',
                            'front_title' => 'Suçluluktan ona gittim',
                            'front_text_color' => '#333333',
                            'back_title' => 'I went to her out of guilt',
                            'english_title' => 'I went to her out of guilt',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 3,
                            'bg_color' => 'rgba(89, 66, 127, 0.7)', /* Lighter purple */
                            'front_title' => 'Ağrı kesici henüz etkisini göstermedi',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'The painkiller hasn\'t kicked in yet',
                            'english_title' => 'The painkiller hasn\'t kicked in yet',
                            'back_text_color' => '#FFFFFF',
                        ),
                        array(
                            'id' => 4,
                            'bg_color' => '#B8B2CC', /* Lighter version of secondary color */
                            'front_title' => 'Haberi nasıl karşıladı',
                            'front_text_color' => '#333333',
                            'back_title' => 'How did she take the news?',
                            'english_title' => 'How did she take the news?',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 9,
                            'bg_color' => '#8FB8E0', /* Light blue */
                            'front_title' => 'Birinin yalan söyleyip söylemediğini nereden anlarsın?',
                            'front_text_color' => '#333333',
                            'back_title' => 'How do you know if somebody is lying?',
                            'english_title' => 'How do you know if somebody is lying?',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 10,
                            'bg_color' => '#6B8CC7', /* Medium blue */
                            'front_title' => 'En çok oyu olan sonraki aşamaya geçer',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'The one with the most votes moves to the next round',
                            'english_title' => 'The one with the most votes moves to the next round',
                            'back_text_color' => '#FFFFFF',
                        ),
                    );

                    // Output left column cards
                    foreach ($left_cards as $index => $card) :
                        $position = $index * 244; // 220px height + 24px gap
                    ?>
                        <div class="carousel-card"
                             data-id="<?php echo esc_attr($card['id']); ?>"
                             data-position="<?php echo esc_attr($position); ?>"
                             data-column="left"
                             data-index="<?php echo esc_attr($index); ?>"
                             style="background-color: <?php echo esc_attr($card['bg_color']); ?>; top: <?php echo esc_attr($position); ?>px;">
                            <div class="card-inner">
                                <!-- Front of card - Turkish -->
                                <div class="card-front">
                                    <div class="card-content">
                                        <h3 style="color: <?php echo esc_attr($card['front_text_color']); ?>"><?php echo esc_html($card['front_title']); ?></h3>
                                    </div>
                                </div>

                                <!-- Back of card - English -->
                                <div class="card-back">
                                    <h3 style="color: <?php echo esc_attr($card['back_text_color']); ?>"><?php echo esc_html($card['english_title']); ?></h3>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Right Column - Fixed position -->
                <div class="carousel-column right-column" style="position: relative; height: 1500px;">
                    <?php
                    // Define the cards for the right column
                    $right_cards = array(
                        array(
                            'id' => 5,
                            'bg_color' => '#A7C8FF', /* Light blue */
                            'front_title' => 'Bana söyleneni yaptım',
                            'front_text_color' => '#333333',
                            'back_title' => 'I did what I was told',
                            'english_title' => 'I did what I was told',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 6,
                            'bg_color' => '#7AA0EE', /* Medium-light blue */
                            'front_title' => 'Dönüşü olmayan noktadayız',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'We are at a point of no return',
                            'english_title' => 'We are at a point of no return',
                            'back_text_color' => '#FFFFFF',
                        ),
                        array(
                            'id' => 7,
                            'bg_color' => '#C8C1D8', /* Lighter secondary */
                            'front_title' => 'Kimse bunları yaşamak zorunda kalmamalı',
                            'front_text_color' => '#333333',
                            'back_title' => 'No one should have to go through this',
                            'english_title' => 'No one should have to go through this',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 8,
                            'bg_color' => '#B095D1', /* Light purple */
                            'front_title' => 'Bütün bunları aşmamda yardımı oldu',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'It helped me get through all of that',
                            'english_title' => 'It helped me get through all of that',
                            'back_text_color' => '#FFFFFF',
                        ),
                        array(
                            'id' => 11,
                            'bg_color' => '#9BAFD9', /* Medium blue-purple */
                            'front_title' => 'Japonya\'ya gitmeme neden olan bir kızdı',
                            'front_text_color' => '#333333',
                            'back_title' => 'What got me to go to Japan was a girl',
                            'english_title' => 'What got me to go to Japan was a girl',
                            'back_text_color' => '#333333',
                        ),
                        array(
                            'id' => 12,
                            'bg_color' => '#8A6CAF', /* Medium purple */
                            'front_title' => 'Gelişmeler oldu',
                            'front_text_color' => '#FFFFFF',
                            'back_title' => 'There have been some developments',
                            'english_title' => 'There have been some developments',
                            'back_text_color' => '#FFFFFF',
                        ),
                    );

                    // Output right column cards
                    foreach ($right_cards as $index => $card) :
                        $position = $index * 244; // 220px height + 24px gap
                        $offset = 120; // Right column offset
                    ?>
                        <div class="carousel-card"
                             data-id="<?php echo esc_attr($card['id']); ?>"
                             data-position="<?php echo esc_attr($position + $offset); ?>"
                             data-column="right"
                             data-index="<?php echo esc_attr($index); ?>"
                             style="background-color: <?php echo esc_attr($card['bg_color']); ?>; top: <?php echo esc_attr($position); ?>px;">
                            <div class="card-inner">
                                <!-- Front of card - Turkish -->
                                <div class="card-front">
                                    <div class="card-content">
                                        <h3 style="color: <?php echo esc_attr($card['front_text_color']); ?>"><?php echo esc_html($card['front_title']); ?></h3>
                                    </div>
                                </div>

                                <!-- Back of card - English -->
                                <div class="card-back">
                                    <h3 style="color: <?php echo esc_attr($card['back_text_color']); ?>"><?php echo esc_html($card['english_title']); ?></h3>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
