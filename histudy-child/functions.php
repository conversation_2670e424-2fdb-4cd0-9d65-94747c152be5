<?php
/**
 * Histudy Child Theme Functions
 */

add_action('wp_enqueue_scripts', 'histudy_child_enqueue_styles');
function histudy_child_enqueue_styles() {
    wp_enqueue_style('parent-style', get_template_directory_uri() . '/style.css', null, filemtime(get_template_directory() . '/style.css'));
    wp_enqueue_style('histudy-child-style', get_stylesheet_uri(), null, filemtime(get_stylesheet_directory() . '/style.css'));
}

// Disable auto updates
add_filter('auto_update_theme', '__return_false');
add_filter('auto_update_plugin', '__return_false');

remove_action('admin_init', '_maybe_update_core');
remove_action('admin_init', '_maybe_update_plugins');
remove_action('admin_init', '_maybe_update_themes');

/**
 * Hero shortcode - compatible with parent theme
 * Uses output buffering to ensure proper rendering with Elementor
 */
function hero_shortcode() {
    ob_start();
    get_template_part('template-parts/components/hero');
    return ob_get_clean();
}
add_shortcode('hero', 'hero_shortcode');

// Scripts enqueuing function
function asil_enqueue_scripts() {
    // Hero carousel script
    $hero_carousel_file = get_stylesheet_directory() . '/assets/js/hero-swiper.js';
    wp_enqueue_script(
        'asil-hero-carousel',
        get_stylesheet_directory_uri() . '/assets/js/hero-swiper.js',
        array('jquery'),
        (file_exists($hero_carousel_file) ? filemtime($hero_carousel_file) : '1.0'),
        true
    );

    // Hero educational effects script
    $hero_educational_file = get_stylesheet_directory() . '/assets/js/hero-educational.js';
    wp_enqueue_script(
        'asil-hero-educational',
        get_stylesheet_directory_uri() . '/assets/js/hero-educational.js',
        array(),
        (file_exists($hero_educational_file) ? filemtime($hero_educational_file) : '1.0'),
        true
    );
}
add_action('wp_enqueue_scripts', 'asil_enqueue_scripts');

/**
 * Enqueue search JavaScript
 */
function enqueue_search_js() {
    wp_enqueue_script(
        'histudy-search-ajax',
        get_stylesheet_directory_uri() . '/assets/js/search-ajax.js',
        array('jquery'),
        '1.0.0',
        true
    );

    // Localize script for AJAX
    wp_localize_script('histudy-search-ajax', 'search_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('search_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_search_js');

/**
 * Enhanced search function that includes course tags
 * Overrides the parent theme's search function
 */
function rbt_ajax_search_tutor_courses_enhanced() {
    $courseName = isset($_REQUEST['courseName']) ? sanitize_text_field( $_REQUEST['courseName'] ) : '';
    $postType = isset($_REQUEST['postType']) ? sanitize_text_field( $_REQUEST['postType'] ) : '';

    // Base query args
    $args = array(
        'post_type'      => $postType,
        'posts_per_page' => -1,
        'post_status'    => 'publish'
    );

    // If we have a search term, create a more complex query
    if (!empty($courseName)) {
        // Clean and prepare search term
        $search_term = trim($courseName);

        // First, let's search for posts that match the title/content
        $title_content_args = array_merge($args, array('s' => $search_term));

        // Second, let's search for posts that have matching tags
        // We'll also search for partial matches in tag names and descriptions
        $tag_args = array_merge($args, array(
            'tax_query' => array(
                'relation' => 'OR',
                array(
                    'taxonomy' => 'course-tag',
                    'field'    => 'name',
                    'terms'    => $search_term,
                    'operator' => 'LIKE'
                ),
                array(
                    'taxonomy' => 'course-tag',
                    'field'    => 'description',
                    'terms'    => $search_term,
                    'operator' => 'LIKE'
                )
            )
        ));

        // Get posts from both queries
        $title_content_query = new WP_Query($title_content_args);
        $tag_query = new WP_Query($tag_args);

        // Combine results and remove duplicates
        $all_posts = array();
        $post_ids = array();

        // Add posts from title/content search
        if ($title_content_query->have_posts()) {
            while ($title_content_query->have_posts()) {
                $title_content_query->the_post();
                $post_id = get_the_ID();
                if (!in_array($post_id, $post_ids)) {
                    $all_posts[] = get_post($post_id);
                    $post_ids[] = $post_id;
                }
            }
            wp_reset_postdata();
        }

        // Add posts from tag search
        if ($tag_query->have_posts()) {
            while ($tag_query->have_posts()) {
                $tag_query->the_post();
                $post_id = get_the_ID();
                if (!in_array($post_id, $post_ids)) {
                    $all_posts[] = get_post($post_id);
                    $post_ids[] = $post_id;
                }
            }
            wp_reset_postdata();
        }

        // Create a final query with the combined post IDs
        if (!empty($post_ids)) {
            $final_args = array_merge($args, array(
                'post__in' => $post_ids,
                'orderby' => 'post__in' // Maintain the order we found them
            ));
            $query = new WP_Query($final_args);
        } else {
            // No results found, create empty query
            $query = new WP_Query(array('post__in' => array(0)));
        }
    } else {
        // No search term, return recent courses (limit to 4 for better performance)
        $default_args = array_merge($args, array(
            'posts_per_page' => 4,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        $query = new WP_Query($default_args);
    }

    ob_start(); // Start output buffering to capture HTML

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            echo '<div class="col-lg-3 col-md-4 col-sm-6 col-6">';
            get_template_part('template-parts/components/card/layout', 2);
            echo '</div>';
        }
    } else {
        echo '<div class="no-result-found">'.esc_html__("No results found.","histudy").'</div>';
    }

    wp_reset_postdata();

    $response = ob_get_clean(); // Get the captured HTML

    wp_send_json($response); // Return the HTML as AJAX response

    wp_die(); // Always call wp_die() after handling AJAX requests
}

// Remove the parent theme's AJAX handlers and add our enhanced version
remove_action('wp_ajax_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses');
remove_action('wp_ajax_nopriv_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses');

// Hook our enhanced AJAX actions
add_action('wp_ajax_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses_enhanced');
add_action('wp_ajax_nopriv_rbt_ajax_search_tutor_courses', 'rbt_ajax_search_tutor_courses_enhanced');

/**
 * Helper function to get all course tags for debugging
 * Can be called via URL parameter ?debug_course_tags=1 (for admin users only)
 */
function debug_course_tags() {
    if (isset($_GET['debug_course_tags']) && current_user_can('manage_options')) {
        $tags = get_terms(array(
            'taxonomy' => 'course-tag',
            'hide_empty' => false,
        ));

        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Available Course Tags:</h3>';
        if (!empty($tags) && !is_wp_error($tags)) {
            echo '<ul>';
            foreach ($tags as $tag) {
                echo '<li><strong>' . esc_html($tag->name) . '</strong>';
                if (!empty($tag->description)) {
                    echo ' - ' . esc_html($tag->description);
                }
                echo ' (ID: ' . $tag->term_id . ', Count: ' . $tag->count . ')</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>No course tags found or error occurred.</p>';
        }
        echo '</div>';
    }
}
add_action('wp_head', 'debug_course_tags');