# Testing Guide for Enhanced Course Search

## Prerequisites
1. Ensure the child theme is active
2. Have some courses created in Tutor LMS
3. Add tags to your courses using the course-tag taxonomy

## Testing Steps

### 1. Basic Functionality Test
1. Go to your website's homepage or any page with the search dropdown
2. Click on the search icon to open the search dropdown
3. Type in a course title - verify it still works as before
4. Type in a tag name - verify it returns courses with that tag

### 2. Tag Search Test
1. In WordPress admin, go to Courses > Tags
2. Note down some tag names you've created
3. Go to the frontend search
4. Search for those tag names
5. Verify that courses with those tags appear in results

### 3. Mixed Results Test
1. Create a course with title "JavaScript Basics" and tag it with "programming"
2. Create another course with title "Programming Fundamentals" and tag it with "javascript"
3. Search for "javascript" - both courses should appear
4. Search for "programming" - both courses should appear

### 4. Partial Match Test
1. Create a tag like "web-development"
2. Search for "web" - courses with "web-development" tag should appear
3. Search for "development" - courses with "web-development" tag should appear

### 5. Debug Information
1. As an admin user, add `?debug_course_tags=1` to any page URL
2. This will display all available course tags at the top of the page
3. Use this to verify which tags exist and test searching for them

## Expected Results

### What Should Work:
- ✅ Original title/content search functionality preserved
- ✅ Tag name search returns relevant courses
- ✅ Partial tag matches work
- ✅ Combined results from both title and tag searches
- ✅ No duplicate results in search dropdown
- ✅ AJAX search works smoothly without page refresh

### What to Check:
- Search results appear in the dropdown without page reload
- No JavaScript errors in browser console
- Search is responsive (results appear as you type)
- Empty search shows default courses (if configured)
- "No results found" message appears when no matches

## Troubleshooting

### If Search Doesn't Work:
1. Check browser console for JavaScript errors
2. Verify child theme is active
3. Check if courses have tags assigned
4. Use the debug URL to see available tags

### If No Tag Results:
1. Verify courses have tags assigned in WordPress admin
2. Check that tags are using the 'course-tag' taxonomy
3. Try searching for exact tag names first

### If Duplicate Results:
1. This shouldn't happen with the current implementation
2. If it does, check the post_ids array logic in the function

## Performance Notes
- The enhanced search runs two queries instead of one
- For sites with many courses, consider adding caching if needed
- The search is limited to published courses only

## Advanced Testing

### Test with Different Post Types:
1. The search should work with 'courses' post type (Tutor LMS)
2. Test with other LMS plugins if you switch (LearnPress, etc.)

### Test AJAX Functionality:
1. Open browser developer tools
2. Go to Network tab
3. Perform a search
4. Verify AJAX call to `admin-ajax.php` with action `rbt_ajax_search_tutor_courses`
5. Check response contains HTML for course cards

### Test Edge Cases:
1. Search with special characters
2. Search with very long terms
3. Search with empty string
4. Search with numbers only
5. Search with mixed case (should be case-insensitive)

## Success Criteria
The enhancement is working correctly if:
1. All original search functionality is preserved
2. Searching for tag names returns courses with those tags
3. No errors appear in browser console
4. Search results combine both title/content and tag matches
5. Performance remains acceptable
6. The search dropdown UI remains unchanged
