<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

get_header();
$banner_layout = Rainbow_Helper::rainbow_banner_layout();
$banner_style  = $banner_layout['banner_style'];
$page_class = 'rbt-section-gap';
if( class_exists( 'WooCommerce' ) ) {
    if( is_cart() || is_checkout() || is_account_page() ) {
        $page_class = 'rbt-section-gap';
    }
}
?> 
<div class="rbt-page-area <?php echo esc_attr( $page_class );?>">
    <div class="container">  
        <?php while (have_posts()) :
            the_post(); 
            get_template_part('template-parts/content', 'page'); 
            // If comments are open or we have at least one comment, load up the comment template.
            if (comments_open() || get_comments_number()) :
                comments_template();
            endif; 
        endwhile; wp_reset_query(); // End of the loop. ?> 
    </div>
</div> 
<?php
get_footer();