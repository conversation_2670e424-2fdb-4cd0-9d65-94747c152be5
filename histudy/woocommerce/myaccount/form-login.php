<?php
/**
 * Login Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/form-login.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.7.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

do_action( 'woocommerce_before_customer_login_form' ); ?>

<?php if ( 'yes' === get_option( 'woocommerce_enable_myaccount_registration' ) ) : ?>

<div class="u-columns col2-set" id="customer_login">

	<div class="u-column1 <?php echo get_option( 'users_can_register' ) ? 'col-1': ' col-1 mx-auto float-none'; ?>">

<?php endif; ?>

        <div class="rbt-contact-form contact-form-style-1 max-width-auto">
            <?php do_action( 'tutor_before_login_form' ); ?>
            <form method="post">
                <h3 class="title"><?php esc_html_e( 'Login', 'histudy' ); ?></h3>
    
                <?php do_action( 'woocommerce_login_form_start' ); ?>
    
                <div class="form-group <?php echo ( isset( $_POST['username'] ) && empty( esc_attr( $_POST['username'] ) ) ) ? '': esc_attr( 'focused', 'histudy' ); ?>">
                    <input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="username" id="username" autocomplete="username" value="<?php echo ( isset( $_POST['username'] ) && ! empty( $_POST['username'] ) ) ? esc_attr( wp_unslash( $_POST['username'] ) ) : ''; ?>" /><?php // @codingStandardsIgnoreLine ?>
                    <label for="username"><?php esc_html_e( 'Username or email address', 'histudy' ); ?>&nbsp;<span class="required">*</span></label>
                    <span class="focus-border"></span>
                </div>
                <div class="form-group">
                    <input class="woocommerce-Input woocommerce-Input--text input-text" type="password" name="password" id="password" autocomplete="current-password" />
                    <label for="password"><?php esc_html_e( 'Password', 'histudy' ); ?>&nbsp;<span class="required">*</span></label>
                    <span class="focus-border"></span>
                </div>
    
                <?php do_action( 'woocommerce_login_form' ); ?>
                <div class="row mb--30">
                    <div class="col-lg-6">
                        <div class="rbt-checkbox">
                            <input class="d-none woocommerce-form__input woocommerce-form__input-checkbox" name="rememberme" type="checkbox" id="rememberme" value="forever" />
                            <label for="rememberme"><?php esc_html_e( 'Remember me', 'histudy' ); ?></label>
                            <?php wp_nonce_field( 'woocommerce-login', 'woocommerce-login-nonce' ); ?>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="rbt-lost-password text-end">
                            <a href="<?php echo esc_url( wp_lostpassword_url() ); ?>" class="rbt-btn-link"><?php esc_html_e( 'Lost your password?', 'histudy' ); ?></a>
                        </div>
                    </div>
                </div>
                <div class="form-submit-group">
                    <button type="submit" class="woocommerce-button woocommerce-form-login__submit<?php echo esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ); ?> rbt-btn btn-md btn-gradient hover-icon-reverse w-100" name="login" value="<?php esc_attr_e( 'Log in', 'histudy' ); ?>">
                        <span class="icon-reverse-wrapper">
                            <span class="btn-text"><?php esc_html_e( 'Log in', 'histudy' ); ?></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                        </span>
                    </button>
                </div>
    
                <?php do_action( 'woocommerce_login_form_end' ); ?>
    
            </form>
            <?php do_action( 'tutor_after_login_form_wrapper' ); ?>
            <?php do_action( 'tutor_after_login_form' ); ?>
        </div>

<?php if ( 'yes' === get_option( 'woocommerce_enable_myaccount_registration' ) ) : ?>

	</div>

	<div class="u-column2 col-2">
        <?php if ( get_option( 'users_can_register' ) ) : ?>
            <div class="rbt-contact-form contact-form-style-1 max-width-auto">
                <?php do_action( 'tutor_before_instructor_reg_form' ); ?>
                <form method="post" <?php do_action( 'woocommerce_register_form_tag' ); ?> >
                    <h3 class="title"><?php esc_html_e( 'Register', 'histudy' ); ?></h3>
                    <?php do_action( 'woocommerce_register_form_start' ); ?>

                    <?php if ( 'no' === get_option( 'woocommerce_registration_generate_username' ) ) : ?>

                        <div class="form-group <?php echo ( isset( $_POST['username'] ) && empty( esc_attr( $_POST['username']  )) ) ? '': __( 'focused', 'histudy' ); ?>">
                            <label for="reg_username"><?php esc_html_e( 'Username', 'histudy' ); ?>&nbsp;<span class="required">*</span></label>
                            <input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="username" id="reg_username" autocomplete="username" value="<?php echo ( isset( $_POST['username'] ) && ! empty( esc_attr( $_POST['username']  )) ) ? esc_attr( wp_unslash( esc_attr( $_POST['username']  )) ) : ''; ?>" /><?php // @codingStandardsIgnoreLine ?>
                        </div>

                    <?php endif; ?>

                    <div class="form-group <?php echo ( isset( $_POST['username'] ) && empty( esc_attr($_POST['email']) ) ) ? '': __( 'focused', 'histudy' ); ?>">
                        <label for="reg_email"><?php esc_html_e( 'Email address', 'histudy' ); ?>&nbsp;<span class="required">*</span></label>
                        <input type="email" class="woocommerce-Input woocommerce-Input--text input-text" name="email" id="reg_email" autocomplete="email" value="<?php echo ( isset( $_POST['email'] ) && ! empty( $_POST['email'] ) ) ? esc_attr( wp_unslash( $_POST['email'] ) ) : ''; ?>" /><?php // @codingStandardsIgnoreLine ?>
                    </div>

                    <?php if ( 'no' === get_option( 'woocommerce_registration_generate_password' ) ) : ?>
                        <div class="form-group">
                            <label for="reg_password"><?php esc_html_e( 'Password', 'histudy' ); ?>&nbsp;<span class="required">*</span></label>
                            <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password" id="reg_password" autocomplete="new-password" />
                        </div>

                    <?php else : ?>

                        <p><?php esc_html_e( 'A link to set a new password will be sent to your email address.', 'histudy' ); ?></p>

                    <?php endif; ?>

                    <div class="form-group">
                        <?php do_action( 'woocommerce_register_form' ); ?>
                    </div>
                    <div class="form-submit-group">
                        <button type="submit" class="woocommerce-Button <?php echo esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ); ?> woocommerce-form-register__submit rbt-btn btn-md btn-gradient hover-icon-reverse w-100"  name="register" value="<?php esc_attr_e( 'Register', 'histudy' ); ?>">
                            <?php wp_nonce_field( 'woocommerce-register', 'woocommerce-register-nonce' ); ?>
                            <span class="icon-reverse-wrapper">
                                <span class="btn-text"><?php esc_html_e( 'Register', 'histudy' ); ?></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                            </span>
                        </button>
                    </div>

                    <?php do_action( 'woocommerce_register_form_end' ); ?>
                    
                </form>
                <?php do_action( 'tutor_after_registration_form_wrap' ); ?>
                <?php do_action( 'tutor_after_instructor_reg_form' ); ?>
            </div>
        
        <?php endif; ?>

	</div>

</div>
<?php endif; ?>

<?php do_action( 'woocommerce_after_customer_login_form' );