<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;
/**
 * Redux attributes declation
 */
$single_product_attributes = Rainbow_Helper::single_product_attributes();
$wc_cats = $single_product_attributes['wc_cats'] ?? null;
$wc_tags = $single_product_attributes['wc_tags'] ?? null;
$wc_related_before_title = $single_product_attributes['wc_related_before_title'] ?? null;
$wc_related_title = $single_product_attributes['wc_related_title'] ?? null;
$wc_description = $single_product_attributes['wc_description'] ?? null;
$wc_reviews = $single_product_attributes['wc_reviews'] ?? null;
$wc_additional_info = $single_product_attributes['wc_additional_info'] ?? null;
$review_count = $product->get_review_count();
$rating_percentage = (isset($average_rating) && !empty($average_rating)) ? ( $average_rating / 5 ) * 100: 0;
$product_id = get_the_ID();
$author_id = get_post_field('post_author', $product_id);
$author_info = get_userdata($author_id);
$author_name = $author_info->display_name;
$product_sku = $product->get_sku();
/**
 * Hook: woocommerce_before_single_product.
 *
 * @hooked woocommerce_output_all_notices - 10
 */
do_action( 'woocommerce_before_single_product' );

if ( post_password_required() ) {
	echo get_the_password_form(); // WPCS: XSS ok.
	return;
}
?>
<div id="product-<?php the_ID(); ?>" <?php wc_product_class( 'row g-5 row--30 align-items-center mb-90', $product ); ?>>
    <div class="col-lg-6">
        <div class="thumbnail">
            <?php woocommerce_show_product_images(); ?>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="content">
            <div class="rbt-review justify-content-start">
                <div class="rating">
                    <?php woocommerce_template_single_rating(); ?>
                </div>
                <?php if(!empty($review_count)) : ?>
                    <span class="rating-count">(<?php echo esc_html($review_count); ?>) - <?php echo esc_html($rating_percentage); ?>% <?php echo esc_html__('Positive Reviews', 'histudy'); ?></span>
                <?php endif; ?>
            </div>

            <?php woocommerce_template_single_title(); ?>
            <?php if(!empty($author_name)) : ?>
                <span class="rbt-label-style description"><?php echo esc_html__('By:', 'histudy'); ?> <?php echo esc_html($author_name); ?></span>
            <?php endif; ?>
            <div class="rbt-price justify-content-start mt--10">
                <?php Rainbow_Helper::rbt_get_woo_price_markup(); ?>
            </div>

            <?php woocommerce_template_single_excerpt(); ?>

            <div class="product-action mb--20">
                <?php woocommerce_template_single_add_to_cart(); ?>
            </div>

            <ul class="product-feature">
                <?php if(!empty($product_sku)) : ?>
                <li><span><?php echo esc_html__('SKU:', 'histudy'); ?></span> <?php echo esc_html($product_sku); ?></li>
                <?php endif; ?>
                <?php if(!empty(Rainbow_Helper::get_single_product_category(get_the_ID())) && (1 == $wc_cats)) : ?>
                    <li><span><?php echo esc_html__('Categories: ', 'histudy'); ?></span> <?php echo Rainbow_Helper::get_single_product_category(get_the_ID()); ?></li>
                <?php endif; ?>
                <?php if( !empty( Rainbow_Helper::rbt_display_product_tags(get_the_ID()) )  && ( 1 == $wc_tags ) ) : ?>
                <li><span><?php echo esc_html__('Tag: ', 'histudy'); ?></span>
                    <?php echo Rainbow_Helper::rbt_display_product_tags(get_the_ID()); ?>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php
	/**
	 * Hook: woocommerce_after_single_product_summary.
	 *
	 * @hooked woocommerce_output_product_data_tabs - 10
	 * @hooked woocommerce_upsell_display - 15
	 * @hooked woocommerce_output_related_products - 20
	 */
	do_action( 'woocommerce_after_single_product_summary' );
?>
<?php do_action( 'woocommerce_after_single_product' );