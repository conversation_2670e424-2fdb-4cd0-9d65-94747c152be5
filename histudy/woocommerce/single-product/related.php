<?php
/**
 * Related Products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/related.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     9.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$single_product_attributes = Rainbow_Helper::single_product_attributes();
$wc_related_before_title = $single_product_attributes['wc_related_before_title'] ?? null;
$wc_related_title = $single_product_attributes['wc_related_title'] ?? null;


if ( $related_products ) : 
	$heading = apply_filters( 'woocommerce_product_related_products_heading', $wc_related_title );
?>

	<section class="rbt-related-product bg-color-white rbt-section-gapTop">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title text-center mb--50">
                    <span class="subtitle bg-secondary-opacity"><?php echo esc_html($wc_related_before_title); ?></span>
                    <?php if ( $heading ) : ?>
                        <h2 class="title"><?php echo esc_html( $heading ); ?></h2>
                    <?php endif; ?>
                </div>
            </div>
        </div>
		<?php woocommerce_product_loop_start(); ?>

			<?php foreach ( $related_products as $related_product ) : ?>

					<?php
					$post_object = get_post( $related_product->get_id() );

					setup_postdata( $GLOBALS['post'] =& $post_object ); // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited, Squiz.PHP.DisallowMultipleAssignments.Found

					wc_get_template_part( 'content', 'product' );
					?>

			<?php endforeach; ?>

		<?php woocommerce_product_loop_end(); ?>

	</section>
	<?php
endif;

wp_reset_postdata();