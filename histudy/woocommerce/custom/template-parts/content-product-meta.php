<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @package histudy
 */

global $product;
 
$rainbow_options = Rainbow_Helper::rainbow_get_options();
do_action( 'woocommerce_product_meta_start' );
$cats_html = wc_get_product_category_list( $product->get_id(), ', ', '<div class="product-meta"><span>' . _n( 'Category:', 'Categories:', count( $product->get_category_ids() ), 'histudy' ) . '</span> ', '</div>' );
$tags_html = wc_get_product_tag_list( $product->get_id(), ', ', '<div class="product-meta"><span>' . _n( 'Tag:', 'Tags:', count( $product->get_tag_ids() ), 'histudy' ) . '</span> ', '</div>' );
if ( $rainbow_options['wc_cats'] ) {
	echo wp_kses_post( $cats_html );
}
if ( $rainbow_options['wc_tags'] ) {
	echo wp_kses_post( $tags_html );
}
do_action( 'woocommerce_product_meta_end' );