<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @package histudy
 */
$rainbow_options = rainbow_helper::rainbow_get_options();
if('no' == $rainbow_options['rainbow_enable_shop_banner_overlap']) {
    $overlap_class = __( 'rbt-section-gapTop', 'histudy' );
} else {
    $overlap_class = __( 'rbt-section-overlayping-top', 'histudy' );
}
if( class_exists('WooCommerce') && !woocommerce_product_loop() ) {
    $overlap_class = __( 'rbt-section-gapTop', 'histudy' );
}
 if ( is_product() ) {
    $tutor_archive_class = '';
?>
 
<?php }elseif( is_shop() || is_product_category() || (is_archive() && 'product' == get_post_type() ) ){ ?>
   <div class="rbt-shop-area <?php echo esc_attr($overlap_class); ?> rbt-section-gapBottom">
        <div class="rbt-course-top-wrapper mt--40">
            <div class="container">
                <div class="row g-5 align-items-center">    
<?php }else{}