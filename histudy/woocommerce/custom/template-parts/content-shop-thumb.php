<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @package histudy
 */
global $product;
$product_id = get_the_ID();
$author_id = get_post_field('post_author', $product_id);
$author_info = get_userdata($author_id);
$author_name = $author_info->display_name;
$average_rating = $product->get_average_rating();
$rating_percentage = ( $average_rating / 5 ) * 100;
$review_count = $product->get_review_count();
$current_price = $product->get_price();
$regular_price = $product->get_regular_price();
/**
 * Get redux customizer data
 */
$course_card_redux_customizer_settings = Rainbow_Helper::rainbow_caourse_cart_settings();
?>


<div class="rbt-default-card style-three rbt-hover">
	<div class="inner">
		<div class="content pt--0 pb--10">
			<?php rainbow_shop_loop_product_title();?>
			<?php if(!empty($author_name)) : ?>
			<span class="team-form">
				<span class="location"><?php echo esc_html__('By', 'histudy'); ?> <?php echo esc_html($author_name); ?></span>
			</span>
			<?php endif; ?>
		</div>
		<div class="thumbnail">
			<a href="<?php echo get_the_permalink(); ?>" class="woocommerce-LoopProduct-link">
				<?php woocommerce_template_loop_product_thumbnail();?>
			</a> 
		</div>
		<div class="content">
			<?php if(!empty($review_count)) : ?>
			<div class="rbt-review justify-content-center">
				<div class="rating">
					<?php echo wc_get_rating_html($average_rating); // PHPCS:Ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?>
				</div>
				<span class="rating-count">(<?php echo esc_html($review_count); ?>) - <?php echo esc_html($rating_percentage). '%'; ?> <?php echo esc_html__('Positive Reviews', 'histudy'); ?></span>
			</div>
			<?php endif; ?>
			<div class="rbt-price justify-content-center mt--10">
				<?php Rainbow_Helper::rbt_get_woo_price_markup(); ?>
			</div>
			<div class="addto-cart-btn mt--20">
				<?php woocommerce_template_loop_add_to_cart(); ?>
			</div>
		</div>
	</div>
</div>