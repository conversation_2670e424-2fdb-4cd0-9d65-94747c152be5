<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @package histudy
 */

/**
 * Theme supports for WooCommerce
 */
if( !defined( 'ABSPATH' ) ) exit;

add_action('after_setup_theme', 'rainbow_shop_support');

/**
 * Breadcrumb
 */
remove_action('woocommerce_before_main_content', 'woocommerce_breadcrumb', 20, 0);

/**
 * Shop products per page
 */
add_filter('loop_shop_per_page', 'rainbow_shop_loop_shop_per_page');

/**
 * Shop/Archive Wrapper
 */
remove_action('woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10);
remove_action('woocommerce_sidebar', 'woocommerce_get_sidebar', 10);
remove_action('woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10);
add_action('woocommerce_before_main_content', 'rainbow_shop_wrapper_start', 10);
add_action('woocommerce_after_main_content', 'rainbow_shop_wrapper_end', 10);

/**
 * Shop top tab
 */
remove_action('woocommerce_before_shop_loop', 'woocommerce_result_count', 20);
remove_action('woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30);
add_action('woocommerce_before_shop_loop', 'rainbow_shop_shop_topbar', 20);

/**
 * Shop loop
 */
remove_action('woocommerce_before_shop_loop_item', 'woocommerce_template_loop_product_link_open', 10);
remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_product_link_close', 5);
remove_action('woocommerce_shop_loop_item_title', 'woocommerce_template_loop_product_title', 10);
remove_action('woocommerce_before_shop_loop_item_title', 'woocommerce_show_product_loop_sale_flash', 10);
remove_action('woocommerce_before_shop_loop_item_title', 'woocommerce_template_loop_product_thumbnail', 10);

add_filter('loop_shop_columns', 'rainbow_shop_loop_shop_columns');
add_action('woocommerce_before_shop_loop_item_title', 'rainbow_shop_shop_thumb_area', 11);
add_action('woocommerce_before_shop_loop_item_title', 'rainbow_shop_shop_info_wrap_start', 12);
add_action('woocommerce_after_shop_loop_item', 'rainbow_shop_shop_info_wrap_end', 12);


/**
 * Product
 */

remove_action( 'woocommerce_after_shop_loop_item',    'woocommerce_template_loop_add_to_cart', 10 );
remove_action( 'woocommerce_before_shop_loop',    'woocommerce_output_all_notices', 10 );


/**
 * Single Product
 */

remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40);
add_action('woocommerce_single_product_summary', 'rainbow_shop_render_sku', 15);
add_action('woocommerce_single_product_summary', 'rainbow_shop_render_meta', 40);
add_action('init', 'rainbow_shop_show_or_hide_related_products');

/**
 * Hide product data tabs
 */

add_filter('woocommerce_product_tabs', 'rainbow_shop_hide_product_data_tab');
add_filter('woocommerce_product_review_comment_form_args', 'rainbow_shop_product_review_form');

/**
 * Cart
 */
remove_action('woocommerce_cart_collaterals', 'woocommerce_cross_sell_display');
remove_action('woocommerce_cart_collaterals', 'woocommerce_cart_totals', 10);
add_action('woocommerce_cart_collaterals', 'woocommerce_cart_totals');
add_action('init', 'rainbow_shop_show_or_hide_cross_sells');

/**
 * Change the breadcrumb separator
 */
add_filter( 'woocommerce_breadcrumb_defaults', 'rainbow_change_breadcrumb_delimiter' );


add_action( 'woocommerce_after_single_product_summary',          'single_3_tabs_wrp_start', 9 );
add_action( 'woocommerce_after_single_product_summary',          'woocommerce_output_product_data_tabs', 10 );
add_action( 'woocommerce_after_single_product_summary',          'single_3_tabs_wrp_end' , 11  );


add_action( 'woocommerce_after_single_product_summary',          'woocommerce_upsell_display', 15 );



function single_up_wrp_end(){
 
echo '</div></div>';
}


function single_3_tabs_wrp_start(){
    echo '<div class="rbt-product-description bg-color-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 offset-lg-2">';
     
}

function single_3_tabs_wrp_end(){
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}





/**
 * ------------------------------------------------------------------------------------------------
 * Mini cart buttons
 * ------------------------------------------------------------------------------------------------
 */
if ( ! function_exists( 'histudy_mini_cart_view_cart_btn' ) ) {
    function histudy_mini_cart_view_cart_btn() {
        echo '<div class="view-cart-btn">';
        echo '<a class="rbt-btn btn-border icon-hover w-100 text-center btn-cart wc-forward" href="' . esc_url( wc_get_cart_url() ) . '">';
        echo '<span class="btn-text">' . esc_html__( 'View cart', 'histudy' ) . '</span>';
        echo '<span class="btn-icon"><i class="feather-arrow-right"></i></span>';
        echo '</a>';
        echo '</div>';
    }
    remove_action( 'woocommerce_widget_shopping_cart_buttons', 'woocommerce_widget_shopping_cart_button_view_cart', 10 );
    add_action( 'woocommerce_widget_shopping_cart_buttons', 'histudy_mini_cart_view_cart_btn', 10 );
}

/**
 * ------------------------------------------------------------------------------------------------
 * Mini cart buttons
 * ------------------------------------------------------------------------------------------------
 */
if ( ! function_exists( 'histudy_mini_cart_checkout_btn' ) ) {
    function histudy_mini_cart_checkout_btn() {
        echo '<div class="checkout-btn mt--20">';
        echo '<a class="rbt-btn btn-gradient icon-hover w-100 text-center checkout wc-forward" href="' . esc_url( wc_get_checkout_url() ) . '">';
        echo '<span class="btn-text">' . esc_html__( 'Checkout', 'histudy' ) . '</span>';
        echo '<span class="btn-icon"><i class="feather-arrow-right"></i></span>';
        echo '</a>';
        echo '</div>';

    }
    remove_action( 'woocommerce_widget_shopping_cart_buttons', 'woocommerce_widget_shopping_cart_proceed_to_checkout', 20 );
    add_action( 'woocommerce_widget_shopping_cart_buttons', 'histudy_mini_cart_checkout_btn', 20 );
}