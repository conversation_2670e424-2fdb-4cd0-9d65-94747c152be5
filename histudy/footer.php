<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package histudy
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}
?>


</main><!-- End Page Wrapper -->
    <?php
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $footer_layout = Rainbow_Helper::rainbow_footer_layout();
    $footer_area = $footer_layout['footer_area'];
    $footer_style = $footer_layout['footer_style'];

    if (shortcode_exists('histudy_custom_footer')) {
       echo do_shortcode('[histudy_custom_footer]');
    } else {
        if ("no" !== $footer_area || "0" !== $footer_area) {
            get_template_part('template-parts/footer/footer', $footer_style);
        }
    }
    
    $scroll_to_top_enable = isset( $rainbow_options['rainbow_scroll_to_top_enable'] ) ?  $rainbow_options['rainbow_scroll_to_top_enable'] : '';

    if ( $scroll_to_top_enable == 'yes') { ?>
        <div class="rbt-progress-parent">
            <svg class="rbt-back-circle svg-inner" width="100%" height="100%" viewBox="-1 -1 102 102">
                <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
            </svg>
        </div>
    <?php } ?>
    <?php
    if( ( (function_exists( 'tutor' )) )  ) {
        if('courses' != get_post_type() && !is_single()) {
            ob_start();
                tutor_load_template_from_custom_path( tutor()->path . '/views/modal/login.php' );
            echo ob_get_clean();
        }
    }
    ?>
    </div>
<?php 
do_action('histudy_modal_popup');
wp_footer(); 
?>
</body>
</html>