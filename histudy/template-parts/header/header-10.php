<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_enable = $rainbow_options['header_top_enable'];
$header_top_btn_badge = $rainbow_options['header_top_btn_badge'];
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? "header-transparent" : "bg-not-transparent";
$header_top_intro_text = $rainbow_options['header_top_intro_text'];
$header_sticky = $header_layout['header_sticky'];
$header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
$header_sticky = $header_sticky_condition ? " header-sticky" : " ";
$header_top_bg = !empty($rainbow_options['header_top_enable']) && !empty($rainbow_options['header_top_bg']['url']) ? $rainbow_options['header_top_bg']['url']: '';
?>
<!-- Start Header Area -->
<header class="rbt-header rbt-header-10 <?php echo esc_attr($header_transparent); ?>">
    <?php if(!empty($header_sticky_condition)) : ?>
        <div class="rbt-sticky-placeholder"></div>
    <?php endif; ?>
    <?php if ( !empty($header_top_enable) && 'false' !== $header_top_enable && "no" !== $header_top_enable && "0" !== $header_top_enable) { ?>
    <!-- Start Header Top  -->
    <div class="rbt-header-top rbt-header-top-1 header-space-betwween bg-color-darker top-expended-activation">
        <div class="container-fluid">
            <div class="top-expended-wrapper">
                <div class="top-expended-inner rbt-header-sec align-items-center ">
                    <div class="rbt-header-sec-col rbt-header-left d-none d-xl-block">
                        <div class="rbt-header-content">
                        <?php if ($rainbow_options['histudy_social_icons']){ $rainbow_socials = Rainbow_Helper::rainbow_socials(); ?>  
                            <!-- Start Header Information List  -->
                            <div class="header-info">
                                <ul class="rbt-information-list">
                                    <?php
                                    ob_start();
                                        get_template_part('template-parts/header/elements/header-top-info', '2');
                                    echo ob_get_clean();    
                                    ?>
                                    <?php
                                    ob_start();
                                        get_template_part('template-parts/header/elements/header-top', 'phone');
                                    echo ob_get_clean();
                                    ?>
                                </ul> 
                            </div>
                            <!-- End Header Information List  -->
                        </div>
                        <?php } ?>
                    </div>
                    <div class="rbt-header-sec-col rbt-header-center">
                        <div class="rbt-header-content justify-content-start justify-content-xl-center">
                            <div class="header-info">
                                <div class="rbt-header-top-news">
                                    <div class="inner">
                                        <?php if(!empty($header_top_intro_text)) : ?>
                                        <div class="content">
                                            <?php if(!empty($header_top_btn_badge)) : ?>
                                                <span class="rbt-badge variation-02 bg-color-primary color-white radius-round"><?php echo esc_html($header_top_btn_badge); ?></span>
                                            <?php endif; ?>
                                                <span class="news-text"><img src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/hand-emojji.svg'); ?>" alt="<?php echo esc_attr__( 'Hand Emojji Images', 'histudy' ); ?>"> <?php echo esc_html($header_top_intro_text); ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rbt-header-sec-col rbt-header-right mt_md--10 mt_sm--10">
                        <div class="rbt-header-content justify-content-start justify-content-lg-end">
                            <?php 
                            ob_start();
                                get_template_part('template-parts/header/elements/header-top', 'social');
                            echo ob_get_clean();
                            ?>
                            <!-- Start Header Information List  -->
                            <?php
                            ob_start();
                                get_template_part('template-parts/header/elements/header-top-switcher', 'language');
                            echo ob_get_clean();    
                            ?>
                            <!-- End Header Information List  -->
                            <!-- Start Header Information List  -->
                            <?php
                            ob_start();
                                get_template_part('template-parts/header/elements/header-top-switcher', 'currency');
                            echo ob_get_clean();
                            ?>
                            <!-- End Header Information List  -->
                        </div>
                    </div>
                </div>
                <div class="header-info">
                    <div class="top-bar-expended d-block d-lg-none">
                        <button class="topbar-expend-button rbt-round-btn"><i class="feather-plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Header Top  -->
    <?php } ?>
    <div class="rbt-header-wrapper header-space-betwween header-sticky">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left rbt-header-content">
                    <div class="header-info">
                        <?php 
                        ob_start();
                            get_template_part('template-parts/header/elements/logo', 'gradient');
                        echo ob_get_clean();
                        ?>
                    </div>
                    <div class="header-info">
                        <?php 
                        ob_start();
                            get_template_part('template-parts/header/elements/category-menu');
                        echo ob_get_clean();
                        ?>
                    </div>
                </div>

                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right">

                    <!-- Navbar Icons -->
                    <ul class="quick-access">
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/search-icon');
                        echo ob_get_clean();    
                        ?>
                        <?php 
                        ob_start();
                            get_template_part('template-parts/header/elements/cart-icon');
                        echo ob_get_clean();
                        ?>
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/user');
                        echo ob_get_clean();
                        ?>
                    </ul>

                    <?php
                    ob_start();
                        get_template_part('template-parts/header/elements/header-btn');
                    echo ob_get_clean();
                    ?>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button rbt-round-btn">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->

                </div>
            </div>
        </div>
        <?php
        ob_start();
            get_template_part('template-parts/header/elements/search-dropdown');
        echo ob_get_clean();
        ?>
    </div>
</header>
<!-- Mobile Menu Section -->