<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_enable = $rainbow_options['header_top_enable'];
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : "bg-color-white";
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky) ? " header-sticky" : " ";
?>
<header class="rbt-header rbt-header-4 <?php echo esc_attr( $header_transparent ); ?>">
    <div class="rbt-sticky-placeholder"></div>
    <?php if(!empty( $header_top_enable )) : ?>
    <!-- Start Header Top -->
    <div class="rbt-header-top rbt-header-top-1 variation-height-50 header-space-betwween bg-color-white border-top-bar-primary-color rbt-border-bottom d-none d-xl-block">
        <div class="container-fluid">
            <div class="rbt-header-sec align-items-center ">
                <div class="rbt-header-sec-col rbt-header-left">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="rbt-information-list">
                                <?php get_template_part('template-parts/header/elements/header-top', 'phone');?>
                            </ul>
                        </div>
                        <div class="rbt-separator"></div>
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/elements/header-top-social'); ?>   
                        </div>
                    </div>
                </div>

                <div class="rbt-header-sec-col rbt-header-right">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/header-top/header-top-menu'); ?>  
                        </div>
                        <div class="rbt-separator"></div>
                        <div class="header-info">
                            <div class="header-right-btn d-flex">
                                <?php get_template_part('template-parts/header/topbar/header-top-btn'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Header Top -->
    <?php endif; ?>

    <div class="rbt-header-wrapper header-space-betwween <?php echo esc_attr($header_transparent); ?> <?php echo esc_attr($header_sticky); ?>">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-start align-items-center">
                <div class="header-left">
                    <?php get_template_part('template-parts/header/elements/logo'); ?>
                </div>
                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>
                <div class="header-right">
                    <?php if( $rainbow_options['rainbow_search_icon'] || $rainbow_options['rainbow_minicart_icon']  || $rainbow_options['rainbow_admin_icon'] ): ?>
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/cart-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/user'); ?>
                        </ul>
                    <?php endif; ?>
                    <?php get_template_part('template-parts/header/elements/mobile-hamburger'); ?>
                </div>
            </div>
        </div>
        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
    </div>
</header>