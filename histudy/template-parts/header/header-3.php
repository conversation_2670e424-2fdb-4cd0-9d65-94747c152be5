<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : "bg-color-white";
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky) ? " header-sticky" : " ";
?>

<header class="rbt-header rbt-header-3 <?php echo esc_attr( $header_transparent ); ?>">
    <div class="rbt-sticky-placeholder"></div>
    
    <!-- Start Header Top -->
    <?php get_template_part('template-parts/header/topbar/topbar', '2'); ?>
    <!-- /. End header top -->
    <!-- start header middle -->
    <div class="rbt-header-middle position-relative rbt-header-mid-1 header-space-betwween bg-color-white rbt-border-bottom d-none d-xl-block">
        <div class="container-fluid">
            <div class="rbt-header-sec align-items-center ">

                <div class="rbt-header-sec-col rbt-header-left">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/elements/logo'); ?>
                        </div>
                    </div>
                </div>
                
                <?php get_template_part('template-parts/header/elements/header', 'search'); ?>

                <div class="rbt-header-sec-col rbt-header-right">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="quick-access rbt-quick-access-2">
                                <?php get_template_part('template-parts/header/elements/cart-icon'); ?>

                                <?php get_template_part('template-parts/header/elements/user'); ?>
                            </ul>
                        </div>

                        <div class="header-info rbt-btn-radius-6">
                            <?php get_template_part('template-parts/header/elements/header-btn'); ?>
                        </div>

                        <div class="header-info d-xl-none">
                            <ul class="quick-access">
                                <li class="access-icon">
                                    <a class="search-trigger-active rbt-round-btn" href="#">
                                        <i class="feather-search"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div class="header-info d-block d-xl-none">
                            <div class="mobile-menu-bar">
                                <div class="hamberger">
                                    <button class="hamberger-button rbt-round-btn">
                                        <i class="feather-menu"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Header Top -->



    <div class="rbt-header-wrapper height-50 header-space-betwween <?php echo esc_attr($header_transparent); ?> <?php echo esc_attr($header_sticky); ?>">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left d-block d-xl-none">
                    <div class="header-info">
                        <?php if( $rainbow_options['rainbow_search_icon'] || $rainbow_options['rainbow_minicart_icon']  || $rainbow_options['rainbow_admin_icon'] ): ?>
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                        </ul>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="header-center d-block d-xl-none">
                    <?php get_template_part('template-parts/header/elements/logo'); ?>
                </div>

                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right">

                    <div class="d-block d-xl-none">
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/cart-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/user'); ?>
                        </ul>
                    </div>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button rbt-round-btn ms-auto">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->
                </div>
            </div>
        </div>
        <!-- Start Search Dropdown  -->
        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
        <!-- End Search Dropdown  -->
    </div>
</header>