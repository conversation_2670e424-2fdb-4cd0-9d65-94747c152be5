<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " header-transparent" : "bg-color-white";
$header_sticky = $header_layout['header_sticky'];
$header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
$header_sticky = $header_sticky_condition ? " header-sticky" : " ";
?>
<header class="rbt-header rbt-header-6">
    <?php if(!empty($header_sticky_condition)) : ?>
    <div class="rbt-sticky-placeholder"></div>
    <?php endif; ?>
    <?php if(!empty($rainbow_options['header_top_enable'])) : ?>
    <!-- Start Header Top -->
    <div class="rbt-header-top rbt-header-top-1 header-space-betwween <?php echo esc_attr($header_transparent); ?> <?php echo esc_attr($header_sticky); ?> rbt-border-bottom d-none d-xl-block">
        <div class="container-fluid">
            <div class="rbt-header-sec align-items-center ">
                <div class="rbt-header-sec-col rbt-header-left">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="rbt-information-list">
                                <li>
                                    <?php get_template_part( 'template-parts/header/header-top/header-top', 'address' ); ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="rbt-header-sec-col rbt-header-center">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/elements/header-top-social'); ?> 
                        </div>
                    </div>
                </div>

                <div class="rbt-header-sec-col rbt-header-right">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="rbt-information-list">
                                <?php if(!empty($rainbow_options['phone']) && !empty($rainbow_options['rainbow_top_phone'])) : ?>
                                    <li>
                                        <a href="tel:<?php echo esc_attr($rainbow_options['phone']) ;?>"><i class="feather-phone"></i><?php echo esc_html($rainbow_options['phone']) ;?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <div class="header-info">
                            <div class="header-right-btn d-flex">
                                <?php get_template_part('template-parts/header/elements/header-btn', 2); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Header Top -->
    <?php endif; ?>

    <!-- Start Header Top -->
    <div class="rbt-header-middle position-relative rbt-header-mid-1 header-space-betwween bg-color-darker rbt-border-bottom d-none d-xl-block">
        <div class="container-fluid">
            <div class="rbt-header-sec align-items-center ">

                <div class="rbt-header-sec-col rbt-header-left">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php if( $rainbow_options['rainbow_search_icon'] || $rainbow_options['rainbow_minicart_icon']  || $rainbow_options['rainbow_admin_icon'] ): ?>
                                <ul class="quick-access">
                                    <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="rbt-header-sec-col rbt-header-center">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/elements/logo', 'white'); ?>
                        </div>
                    </div>
                </div>

                <div class="rbt-header-sec-col rbt-header-right">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="quick-access rbt-quick-access-2">
                                <?php if( $rainbow_options['rainbow_minicart_icon'] ): ?>
                                    <?php get_template_part('template-parts/header/elements/cart-icon'); ?>
                                <?php endif; ?>
                                <?php if( $rainbow_options['rainbow_admin_icon'] ): ?>
                                    <?php get_template_part('template-parts/header/elements/user'); ?>
                                <?php endif; ?>

                            </ul>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <div class="rbt-header-wrapper height-50 header-space-betwween <?php echo esc_attr($header_transparent); ?> <?php echo esc_attr($header_sticky); ?>">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left d-block d-xl-none">
                    <div class="header-info">
                        <?php if( $rainbow_options['rainbow_search_icon'] || $rainbow_options['rainbow_minicart_icon']  || $rainbow_options['rainbow_admin_icon'] ): ?>
                            <ul class="quick-access">
                                <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="header-center d-block d-xl-none">
                    <div class="logo">
                        <?php get_template_part('template-parts/header/elements/logo'); ?>
                    </div>
                </div>
                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>
                <div class="header-right">

                    <div class="d-block d-xl-none">
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/cart-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/user'); ?>
                        </ul>
                    </div>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button rbt-round-btn ms-auto">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->
                </div>

            </div>
        </div>
        <!-- Start Search Dropdown  -->
        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
        <!-- End Search Dropdown  -->
    </div>
</header>