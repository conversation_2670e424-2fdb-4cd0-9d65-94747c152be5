<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$logo = empty($rainbow_options['rainbow_gradient_logo']['url']) ? Rainbow_Helper::get_img('logo/logo.png') : $rainbow_options['rainbow_gradient_logo']['url'];

$dark_mode_logo = isset($rainbow_options['rainbow_dark_mode_logo']['url']) && !empty($rainbow_options['rainbow_dark_mode_logo']['url']) ? $rainbow_options['rainbow_dark_mode_logo']['url'] : Rainbow_Helper::get_img('logo/logo.png');
$logo_height = isset( $rainbow_options['logo_height'] ) && !empty($rainbow_options['logo_height']) ? $rainbow_options['logo_height'] : '';
?>  <div class="logo">
    <?php if (isset($rainbow_options['rainbow_logo_type'])): ?>
        <a href="<?php echo esc_url(home_url('/')); ?>"
           title="<?php echo esc_html(get_bloginfo('name')); ?>" rel="home">
            <?php if ('image' == $rainbow_options['rainbow_logo_type']): ?>
                <?php if ($rainbow_options['rainbow_gradient_logo']) { ?>
                    <img src="<?php echo esc_url($logo); ?>"
                         alt="<?php echo esc_attr(get_bloginfo('name')); ?>" class="logo-normal logo-light-mode"> 
                <?php } 
                if( $rainbow_options['rainbow_dark_mode_logo']) {
                    ?>
                        <img width="<?php echo esc_attr( $logo_height ). 'px'; ?>" height="<?php echo esc_attr( $logo_height ). 'px'; ?>" class="logo-dark-mode" src="<?php echo esc_url($dark_mode_logo);?>" alt="histudy">
                    <?php } ?>
            <?php else: ?>
                <?php if ('text' == $rainbow_options['rainbow_logo_type']): ?>
                    <?php echo esc_html($rainbow_options['rainbow_logo_text']); ?>
                <?php endif; ?>
            <?php endif; ?>

        </a>
    <?php else: ?>
        <h3>
            <a href="<?php echo esc_url(home_url('/')); ?>"
               title="<?php echo esc_html(get_bloginfo('name', 'display')); ?>" rel="home">
                <?php if (isset($rainbow_options['rainbow_logo_text']) ? $rainbow_options['rainbow_logo_text'] : '') {
                    echo esc_html($rainbow_options['rainbow_logo_text']);
                } else {
                    bloginfo('name');
                }
                ?>
            </a>
        </h3>

        <?php $description = get_bloginfo('description', 'display');
        if ($description || is_customize_preview()) { ?>
            <p class="site-description"><?php echo esc_html($description); ?> </p>
        <?php } ?>
    <?php endif; ?>
</div>