<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();

if (!empty($rainbow_options['histudy_social_icons']) && !empty($rainbow_options['rainbow_top_social_icons'])): ?>
    <ul class="social-icon social-default icon-naked">
        <?php foreach ($rainbow_options['histudy_social_icons'] as $key => $value) {
            if ($value != '') {
                echo '<li><a class="' . esc_attr($key) . '" href="' . esc_url($value) . '" target="_blank"><i class="fab fa-' . esc_attr($key) . '"></i></a></li>';
            }
        } ?>
    </ul>
<?php endif;