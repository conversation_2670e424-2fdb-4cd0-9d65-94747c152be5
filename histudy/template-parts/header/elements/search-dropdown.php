<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$post_type = get_post_type();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_search_placeholder = (!empty($rainbow_options['rainbow_search_placeholder'])) ? $rainbow_options['rainbow_search_placeholder'] : __( "What are you looking for?", "histudy" );
$rainbow_search_button_text = (!empty($rainbow_options['rainbow_search_button_text'])) ? $rainbow_options['rainbow_search_button_text'] : __( "Search", "histudy" );

$unique_id = esc_attr(rainbow_unique_id('header-search-'));
if ('post' !== $post_type) {
    if( function_exists( 'tutor' ) ) {
        $post_type = 'courses';
    } elseif( class_exists( 'LearnPress' ) ) {
        $post_type = 'lp_course';
    }
}
$post_args = wp_count_posts( $post_type );
$published_post = isset($post_args->publish) ? $post_args->publish: 0;

$rainbow_search_courses_title = "OUR TOP ". $post_type;
$rainbow_search_courses_title = (!empty($rainbow_options['rainbow_search_product_title'])) ? $rainbow_options['rainbow_search_product_title'] : $rainbow_search_courses_title;
if( $rainbow_options['rainbow_search_icon'] ): ?>
<!-- Start Search Dropdown  -->
<div class="rbt-search-dropdown">
    <div class="wrapper">
        <?php
            if( 0 == $published_post ) {
                echo '<div class="histudy-header-search-post-not-found">';
                    echo '<img src="'.get_template_directory_uri().'/assets/images/icons/post-not-found.svg" alt="'.esc_html__('Post Not Found', 'histudy').'" >';
                    echo '<h3>'.esc_html__('Post Not Found', 'histudy').'</h3>';
                echo '</div>';
            } else { ?>
                <div class="row">
                    <div class="col-lg-12">
                        <form class="ajax_search_tutor_courses" id="<?php echo esc_attr($unique_id); ?>" action="<?php echo esc_url(home_url('/')); ?>" method="GET">
                            <input type="text" data-post_type="<?php echo esc_attr($post_type); ?>" name="s"  placeholder="<?php echo esc_html($rainbow_search_placeholder); ?>" value="<?php echo esc_html(get_search_query(false)); ?>">
                        </form>
                    </div>
                </div>

                
                <?php if($rainbow_options['rainbow_search_courses']){?>
                    <div class="rbt-separator-mid">
                        <hr class="rbt-separator m-0">
                    </div>
                    <div class="section-title pt--30">
                        <div class="row align-items-center">
                            <div class="col-12">
                                <h5 class="rbt-title-style-2"><?php echo esc_html($rainbow_search_courses_title); ?></h5>
                            </div>
                        </div>
                    </div>
                    <div class="row g-4 pt-5 pb--60" id="rbt-course-search-wrapper-layout-1">
                        <?php
                            $posts_per_page = 4;
                            $args = array(
                                'post_type' => $post_type,
                                'posts_per_page' => $posts_per_page,
                                'paged' => get_query_var('paged')
                            );
                            $query = new WP_Query($args);
                            if( $query->have_posts() ) {
                                while($query->have_posts()) {
                                    $query->the_post();
                                    ob_start();
                                    echo '<div class="col-lg-3 col-md-4 col-sm-6 col-6">';
                                        get_template_part('template-parts/components/card/layout', 2, array('post_type' => $post_type));
                                    echo '</div>';
                                    echo ob_get_clean();
                                }
                            }
                            wp_reset_query();
                            $totalPosts = $query->found_posts;
                            $totalPages = ceil($totalPosts / $posts_per_page);
                        ?>
                    </div>
                <?php } ?> <!-- rainbow_search_courses -->
            <?php }
        ?>
    </div>
</div>
<!-- End Search Dropdown  -->
<?php endif;