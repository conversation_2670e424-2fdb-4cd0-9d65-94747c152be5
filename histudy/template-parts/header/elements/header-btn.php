<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_button = $header_layout['header_button'];
$header_button_target = $header_layout['header_button_target'];
$header_button_type = $header_layout['header_button_type'];
$header_button_txt = $header_layout['header_button_txt'];
if(empty($header_button_txt)) {
    $header_button_txt = $rainbow_options['header_button_txt'];
}
$header_button_url = $header_layout['header_button_url'];
if(empty($header_button_url)) {
    $header_button_url = $rainbow_options['header_button_url'];
}

if ( !empty($header_button) && 'false' !== $header_button && "no" !== $header_button && "0" !== $header_button): ?>
    <?php if(!empty( $header_button_txt )) : ?>
    <div class="rbt-btn-wrapper d-none d-xl-block">
        <a class="<?php echo esc_html($header_button_type); ?>" target="<?php echo esc_attr($header_button_target); ?>" href="<?php echo esc_url($header_button_url); ?>">
            <span data-text="<?php echo esc_html($header_button_txt); ?>"><?php echo esc_html($header_button_txt); ?></span>
        </a>
    </div>
    <?php endif; ?>
<?php endif; ?>
<?php if(!empty($rainbow_options['rainbow_enable_button_2'])) : ?>
    <?php if(!empty($rainbow_options['header_button_txt_2'])) : ?>
    <a target="<?php echo esc_attr( $rainbow_options['header_button_target_2'] ); ?>" class="<?php echo esc_html($rainbow_options['header_button_type_2']); ?> mt-0" target="<?php echo esc_attr($rainbow_options['header_button_target_2']); ?>" href="<?php echo esc_url($rainbow_options['header_button_url_2']); ?>">
        <span data-text="<?php echo esc_html($rainbow_options['header_button_txt_2']); ?>"><?php echo esc_html($rainbow_options['header_button_txt_2']); ?></span>
    </a>
    <?php endif; ?>
<?php endif; ?>