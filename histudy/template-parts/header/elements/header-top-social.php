<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_top_info_2 = $rainbow_options['rainbow_top_info_2'];
if (!empty($rainbow_options['histudy_social_icons']) && !empty($rainbow_options['rainbow_top_social_icons'])): ?>
    <?php if(!empty( $rainbow_top_info_2 )) : ?>
     <div class="header-info d-none d-xl-block">
        <ul class="social-share-transparent">
            <?php foreach ($rainbow_options['histudy_social_icons'] as $key => $value) {
                if ($value != '') {
                    echo '<li class="single-item"><a class="' . esc_attr($key) . '" href="' . esc_url($value) . '" target="_blank"><i class="fab fa-' . esc_attr($key) . '"></i></a></li>';
                }
            } ?>
        </ul>
     </div>
     <?php endif; ?>
<?php endif;