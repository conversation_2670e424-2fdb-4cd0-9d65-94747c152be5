<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
if (WOOC_WOO_ACTIVED):
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    global $woocommerce;
    $minicart_icon = isset($rainbow_options['rainbow_minicart_icon']) ? $rainbow_options['rainbow_minicart_icon'] : false;

    if ( ! \Elementor\Plugin::$instance->editor->is_edit_mode() ) {
        $cart_item_count = $woocommerce->cart->cart_contents_count;
    }else{
        $cart_item_count = '0';
    }

    $cart_url = '#';

    if( function_exists('tutor') ) {
        $cart_controller = new  \Tutor\Ecommerce\CartController();
        $get_cart        = $cart_controller->get_cart_items();
        $courses         = $get_cart['courses'];
        $total_count     = $courses['total_count'];

        $tutor_options = get_option('tutor_option');
        $monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';

        if( $monetize_by == 'tutor') {
            $cart_url = \Tutor\Ecommerce\CartController::get_page_url();
        } else {
            $cart_url = '#';
        }

        $cart_count = $cart_controller->get_user_cart_item_count();

    } else {
        $monetize_by = '';
    }

    
     
    ?>
    <?php if ($minicart_icon): ?>
        <li class="access-icon rbt-mini-cart shopping-cart shopping-items">
            <a class="rbt-cart-sidenav-activation rbt-round-btn" href="<?php echo esc_url($cart_url);?>">
                <i class="feather-shopping-cart"></i>
                    <span class="cart-count header-cart-num rbt-cart-count">
                    
                    <?php 
                    if( $monetize_by == 'tutor') {
                        echo wp_kses_post($cart_count );
                    } else {
                        if(class_exists('woocommerce') ) {
                            echo WC()->cart->get_cart_contents_count(); 
                        }
                    }
                     ?></span>
            </a>
        </li>
    <?php endif; ?>
<?php endif;