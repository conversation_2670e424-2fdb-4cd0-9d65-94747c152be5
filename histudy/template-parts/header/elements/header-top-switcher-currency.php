<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_header_currency_markup = isset( $rainbow_options['rainbow_header_currency_markup'] ) ? $rainbow_options['rainbow_header_currency_markup'] : ' ';

if($rainbow_options['rainbow_currency_switcher']): ?>
<?php
    if ( shortcode_exists( 'currency_switcher' ) ) {
        echo do_shortcode( '[currency_switcher]' );
    } else {
        ?>
        <div class="header-info">
            <?php if( !empty($rainbow_header_currency_markup)) { ?>
                <?php echo wp_kses_post($rainbow_header_currency_markup); ?>
            <?php } else { ?>
            <ul class="rbt-dropdown-menu currency-menu">
                <li class="has-child-menu">
                    <a href="#">
                        <span class="menu-item"><?php echo esc_html__('USD', 'histudy'); ?></span>
                        <i class="right-icon feather-chevron-down"></i>
                    </a>
                    <ul class="sub-menu hover-reverse">
                        <li>
                            <a href="#">
                                <span class="menu-item"><?php echo esc_html__('EUR', 'histudy'); ?></span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span class="menu-item"><?php echo esc_html__('GBP', 'histudy'); ?></span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            <?php } ?>
        </div>
        <?php
    }
?>
<?php endif;