<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
if (WOOC_WOO_ACTIVED):
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    global $woocommerce;
    $minicart_icon = isset($rainbow_options['rainbow_minicart_icon']) ? $rainbow_options['rainbow_minicart_icon'] : false;
    ?>
    <?php if ($minicart_icon): ?>
        <li class="access-icon rbt-mini-cart shopping-cart shopping-items">
            
            <a class="rbt-cart-sidenav-activation rbt-cart-sidenav-activation" href="#">
                <i class="feather-shopping-cart"></i> <span><?php echo esc_html__('Cart', 'histudy'); ?></span>
            </a>
        </li>
    <?php endif; ?>
<?php endif;