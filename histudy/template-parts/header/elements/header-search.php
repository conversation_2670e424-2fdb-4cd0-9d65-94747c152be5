<?php
$product_categories = get_terms('course-category', array(
    'orderby'    => 'name',
    'order'      => 'ASC',
    'hide_empty' => false,
));

// Check if the form is submitted
$selectedCategoryId = isset($_POST['categorySelect']) ? $_POST['categorySelect'] : 'all';
$searchQuery = isset($_POST['courseSearch']) ? sanitize_text_field($_POST['courseSearch']) : '';

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_search_placeholder = (!empty($rainbow_options['rainbow_search_placeholder'])) ? $rainbow_options['rainbow_search_placeholder'] : __( 'What are you looking for?', 'histudy' );

?>
<div class="rbt-header-sec-col rbt-header-center">
    <div class="rbt-header-content">
        <div class="header-info">
            <form role="search" method="get" action="<?php echo esc_url(home_url( '/' )); ?>">
                <div class="rbt-search-with-category">
                    <div class="search-field">
                        <input name="s" type="text" value="<?php the_search_query(); ?>" placeholder="<?php echo esc_attr($rainbow_search_placeholder); ?>">
                        <button class="rbt-round-btn serach-btn" type="submit"><i class="feather-search"></i></button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>