<?php
/**
 * Template part for displaying site Phone Number
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();

$phone = isset($rainbow_options['phone']) ? $rainbow_options['phone'] : '';
if (!empty($phone) && $rainbow_options['rainbow_top_phone']) {
    $number = trim(preg_replace('/[^\d|\+]/', '', $phone)); ?>
    <li><a href="tel:<?php echo esc_attr($number); ?>"><i class="feather-phone"></i> <?php echo esc_html($phone); ?></a></li>
<?php }