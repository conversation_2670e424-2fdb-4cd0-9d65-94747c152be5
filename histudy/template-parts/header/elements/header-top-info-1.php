<?php
/**
 * Template part for displaying Followers information 1
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
if ( (!empty($rainbow_options['followers_filed_1_icon']) || !empty($rainbow_options['followers_filed_1_label']) ) && $rainbow_options['rainbow_top_info_1']) { ?>
    <li>
        <?php if (!empty($rainbow_options['followers_filed_1_link'])){ ?>
        <a href="<?php echo esc_url($rainbow_options['followers_filed_1_link']); ?>">
            <?php } ?>

            <?php if (!empty($rainbow_options['followers_filed_1_icon'])){ ?>
                <i class="<?php echo esc_attr($rainbow_options['followers_filed_1_icon']); ?>"></i>
            <?php } ?>

            <?php echo wp_kses_post($rainbow_options['followers_filed_1_label']) ?>

            <?php if (!empty($rainbow_options['followers_filed_1_link'])){ ?>
        </a>
    <?php } ?>
    </li>
<?php }