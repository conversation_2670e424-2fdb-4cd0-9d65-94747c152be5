<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_button = $header_layout['header_button'];
$header_button_target = $header_layout['header_button_target'];
$header_button_txt = $header_layout['header_button_txt'];
if(empty($header_button_txt)) {
    $header_button_txt = $rainbow_options['header_button_txt'];
}
$header_button_url = $header_layout['header_button_url'];
if(empty($header_button_url)) {
    $header_button_url = $rainbow_options['header_button_url'];
}

if ( !empty($header_button) && 'false' !== $header_button && "no" !== $header_button && "0" !== $header_button): ?>
    <a class="rbt-btn rbt-switch-btn btn-gradient btn-xs" target="<?php echo esc_attr($header_button_target); ?>" href="<?php echo esc_url($header_button_url); ?>">
        <span data-text="<?php echo esc_html($header_button_txt); ?>"><?php echo esc_html($header_button_txt); ?></span>
    </a>
<?php endif;