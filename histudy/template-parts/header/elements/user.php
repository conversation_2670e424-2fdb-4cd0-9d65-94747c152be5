<?php

/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_admin_icon = $rainbow_options['rainbow_admin_icon'];
$rainbow_admin_icon_label = (!empty($rainbow_options['rainbow_admin_icon_label'])) ? $rainbow_options['rainbow_admin_icon_label'] : '';
$rainbow_admin_header_display_limit = (!empty($rainbow_options['rainbow_admin_header_display_limit'])) ? $rainbow_options['rainbow_admin_header_display_limit'] : '';
$rainbow_admin_view_profile_userlink = (!empty($rainbow_options['rainbow_admin_view_profile_userlink'])) ? $rainbow_options['rainbow_admin_view_profile_userlink'] : '';


if (TUTOR_ACTIVED && $rainbow_admin_icon):
    $user_id                    = get_current_user_id();
    $user                       = get_user_by('ID', $user_id);
    $dashboard_page_slug = '';
    if (isset($wp_query->query_vars['tutor_dashboard_page']) && $wp_query->query_vars['tutor_dashboard_page']) {
        $dashboard_page_slug    = $wp_query->query_vars['tutor_dashboard_page'];
    }

    $current_user = wp_get_current_user();
    if ($current_user) {
        $display_name = $current_user->display_name;
        if (!empty($rainbow_admin_view_profile_userlink)) {
            $profile_url = $rainbow_admin_view_profile_userlink;
        } else {
            $profile_url = home_url('/dashboard/my-profile');
        }

        if (!empty($rainbow_admin_header_display_limit) && is_numeric($rainbow_admin_header_display_limit)) {
            $limit = (int)$rainbow_admin_header_display_limit;
            if (mb_strlen($display_name) > $limit) {
                $display_name = mb_substr($display_name, 0, $limit) . "...";
            }
        }
    }
    if (!is_user_logged_in()) {
        $rainbow_account_enable_popup = isset($rainbow_options['rainbow_account_enable_popup']) && !empty($rainbow_options['rainbow_account_enable_popup']) ? $rainbow_options['rainbow_account_enable_popup'] : '';
        $default_account_url = '';
        if (class_exists('WooCommerce')) {
            $default_account_url = tutor_utils()->get_tutor_dashboard_page_permalink();
        }
        $rainbow_account_url = isset($rainbow_options['rainbow_account_url']) && !empty($rainbow_options['rainbow_account_url']) ? $rainbow_options['rainbow_account_url'] : $default_account_url;
        ob_start();

?>
        <?php if (!empty($rainbow_account_enable_popup)) : ?>
            <li class="account-access rbt-user-wrapper rbt-user-not-logged-in-btn">
                <a class="not-logged-in tutor-course-wishlist-btn tutor-btn  tutor-mr-16" href="#userLogin" role="button"><i class="feather-user"></i><span class="rbt-user-label  d-none d-xl-inline-block"><?php echo esc_html($rainbow_admin_icon_label); ?></span></a>
            </li>
        <?php else: ?>
            <li class="account-access rbt-user-wrapper rbt-user-not-logged-in-btn">
                <a class="not-logged-in" href="<?php echo esc_url($rainbow_account_url); ?>"><i class="feather-user"></i><span class="rbt-user-label  d-none d-xl-inline-block"><?php echo esc_html($rainbow_admin_icon_label); ?></span></a>
            </li>
        <?php endif; ?>
    <?php echo ob_get_clean();
        return;
    }
    ?>
    <li class="access-icon rbt-user-wrapper">
        <a class="rbt-round-btn" href="#"><i class="feather-user"></i></a>
        <div class="rbt-user-menu-list-wrapper">
            <div class="inner">
                <div class="rbt-admin-profile">
                    <div class="admin-thumbnail">
                        <img src="<?php echo get_avatar_url($user_id, array('size' => 150)); ?>" alt="<?php _e('User image', 'histudy'); ?>" />
                    </div>
                    <div class="admin-info">
                        <span class="name"><?php echo esc_html($display_name); ?></span>
                        <a class="rbt-btn-link color-primary" href="<?php echo esc_url($profile_url); ?>"><?php echo esc_html__('View Profile', 'histudy'); ?></a>
                    </div>
                </div>
                <ul class="user-list-wrapper">
                    <?php
                    $dashboard_pages = tutor_utils()->tutor_dashboard_nav_ui_items();
                    // get reviews settings value.
                    $disable = !get_tutor_option('enable_course_review');
                    foreach ($dashboard_pages as $dashboard_key => $dashboard_page) {
                        /**
                         * If not enable from settings then quit
                         *
                         *  @since v2.0.0
                         */
                        if ($disable && 'reviews' === $dashboard_key) {
                            continue;
                        }

                        $menu_title = $dashboard_page;
                        $menu_link  = tutor_utils()->get_tutor_dashboard_page_permalink($dashboard_key);
                        $separator  = false;
                        $menu_icon  = '';

                        if (is_array($dashboard_page)) {
                            $menu_title     = tutor_utils()->array_get('title', $dashboard_page);
                            $menu_icon_name = tutor_utils()->array_get('icon', $dashboard_page, (isset($dashboard_page['icon']) ? $dashboard_page['icon'] : ''));
                            if ($menu_icon_name) {
                                $menu_icon = "<span class='{$menu_icon_name} tutor-dashboard-menu-item-icon'></span>";
                            }
                            // Add new menu item property "url" for custom link
                            if (isset($dashboard_page['url'])) {
                                $menu_link = $dashboard_page['url'];
                            }
                            if (isset($dashboard_page['type']) && $dashboard_page['type'] == 'separator') {
                                $separator = true;
                            }
                        }
                        if ($separator) {
                            echo '<li class="tutor-dashboard-menu-divider"></li>';
                            if ($menu_title) {
                                echo "<li class='tutor-dashboard-menu-divider-header'>{$menu_title}</li>";
                            }
                        } else {
                            $li_class = "tutor-dashboard-menu-{$dashboard_key}";
                            if ($dashboard_key === 'index') {
                                $dashboard_key = '';
                            }
                            $active_class    = $dashboard_key == $dashboard_page_slug ? 'active' : '';
                            $data_no_instant = 'logout' == $dashboard_key ? 'data-no-instant' : '';

                            echo "<li class='tutor-dashboard-menu-item {$li_class}  {$active_class}'><a {$data_no_instant} href='" . $menu_link . "' class='tutor-dashboard-menu-item-link tutor-fs-6 tutor-color-black'>{$menu_icon} <span class='tutor-dashboard-menu-item-text tutor-ml-12'>{$menu_title}</span></a></li>";
                        }
                    }
                    ?>
                </ul>
            </div>
        </div>
    </li>
    <?php
else:
    if (!is_user_logged_in()) {
        $rainbow_account_enable_popup = isset($rainbow_options['rainbow_account_enable_popup']) && !empty($rainbow_options['rainbow_account_enable_popup']) ? $rainbow_options['rainbow_account_enable_popup'] : '';
        $rainbow_account_url = isset($rainbow_options['rainbow_account_url']) && !empty($rainbow_options['rainbow_account_url']) ? $rainbow_options['rainbow_account_url'] : '';
        ob_start(); ?>
        <?php if (!empty($rainbow_account_enable_popup)) : ?>
            <li class="account-access rbt-user-wrapper rbt-user-not-logged-in-btn">
                <a class="not-logged-in" data-bs-toggle="modal" href="#userLogin" role="button"><i class="feather-user"></i><span class="rbt-user-label  d-none d-xl-inline-block"><?php echo esc_html($rainbow_admin_icon_label); ?></span></a>
            </li>
        <?php else: ?>
            <li class="account-access rbt-user-wrapper rbt-user-not-logged-in-btn">
                <a class="not-logged-in" href="<?php echo esc_url($rainbow_account_url); ?>"><i class="feather-user"></i><span class="rbt-user-label  d-none d-xl-inline-block"><?php echo esc_html($rainbow_admin_icon_label); ?></span></a>
            </li>
        <?php endif; ?>
    <?php echo ob_get_clean();
        return;
    }

endif;



if (class_exists('LearnPress')) :

    $profile = LP_Profile::instance();
    if ($profile->get_user_current()->is_guest()) {
        return;
    }
    $user_id                    = get_current_user_id();
    $user                       = get_user_by('ID', $user_id);

    $current_user = wp_get_current_user();
    if ($current_user) {
        $display_name = $current_user->display_name;
        if (!empty($rainbow_admin_view_profile_userlink)) {
            $profile_url = $rainbow_admin_view_profile_userlink;
        } else {
            $profile_url = home_url('/lp-profile/');
        }

        if (!empty($rainbow_admin_header_display_limit) && is_numeric($rainbow_admin_header_display_limit)) {
            $limit = (int)$rainbow_admin_header_display_limit;
            if (mb_strlen($display_name) > $limit) {
                $display_name = mb_substr($display_name, 0, $limit) . "...";
            }
        }
    }
?>

    <li class="access-icon rbt-user-wrapper">
        <a class="rbt-round-btn" href="#"><i class="feather-user"></i></a>
        <div class="rbt-user-menu-list-wrapper">
            <div class="inner">
                <div class="rbt-admin-profile">
                    <div class="admin-thumbnail">
                        <img src="<?php echo get_avatar_url($user_id, array('size' => 150)); ?>" alt="<?php _e('User image', 'histudy'); ?>" />
                    </div>
                    <div class="admin-info">
                        <span class="name"><?php echo esc_html($display_name); ?></span>
                        <a class="rbt-btn-link color-primary" href="<?php echo esc_url(  $profile_url ); ?>"><?php echo esc_html__('View Profile', 'histudy'); ?></a>
                    </div>
                </div>
                <ul class="user-list-wrapper">
                    <?php
                    /**
                     * @var LP_Profile_Tab $profile_tab
                     */
                    foreach ($profile->get_tabs()->tabs() as $tab_key => $profile_tab) {
                        if (! is_object($profile_tab) || ! $profile_tab || $profile_tab->is_hidden() || ! $profile->current_user_can('view-tab-' . $tab_key)) {
                            continue;
                        }

                        // Admin view another user profile
                        if ($profile->get_user()->get_id() !== $profile->get_user_current()->get_id() && current_user_can(ADMIN_ROLE)) {
                            $tab_key_hidden_admin_view_user = ['settings', 'logout', 'orders', 'gradebook'];
                            if (in_array($tab_key, $tab_key_hidden_admin_view_user)) {
                                continue;
                            }
                        }

                        $slug        = $profile->get_slug($profile_tab, $tab_key);
                        $link        = $profile->get_tab_link($tab_key, true);
                        $tab_classes = array(esc_attr($tab_key));

                        $sections = $profile_tab->sections();

                        if ($sections && sizeof($sections) > 1) {
                            $tab_classes[] = 'has-child';
                        }

                        if ($profile->is_current_tab($tab_key)) {
                            $tab_classes[] = 'active';
                        }
                    ?>

                        <li class="<?php echo implode(' ', $tab_classes); ?>"> <a href="<?php echo esc_url_raw($link); ?>" data-slug="<?php echo esc_attr($link); ?>">
                        <?php
                            if (! empty($profile_tab->get('icon'))) {
                                echo wp_kses_post(str_replace(array('fas fa-', 'fa fa-'), 'lp-icon-', $profile_tab->get('icon')));
                            }
                        ?> 
                        <span class='tutor-dashboard-menu-item-text tutor-ml-12'><?php echo apply_filters('learn_press_profile_' . $tab_key . '_tab_title', $profile_tab->get('title'), $tab_key); ?></span></a></li>
                    <?php
                        }
                    ?>
                </ul>
            </div>
        </div>
    </li>
<?php
endif;
