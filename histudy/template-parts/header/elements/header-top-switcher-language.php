<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_language_switcher = isset( $rainbow_options['rainbow_language_switcher'] ) ? $rainbow_options['rainbow_language_switcher'] : ' ';
$rainbow_language_markup = isset( $rainbow_options['rainbow_language_markup'] ) ? $rainbow_options['rainbow_language_markup'] : ' ';


if( $rainbow_language_switcher  == '1') { 
    if(!shortcode_exists( 'wpml_language_selector_widget' )) : ?>
        <div class="header-info">
            <?php if(!empty($rainbow_language_markup)) { ?>
                <?php echo wp_kses_post($rainbow_language_markup); ?>
            <?php } else { ?>
            <ul class="rbt-dropdown-menu switcher-language">
                <li class="has-child-menu">
                    <a href="#">
                        <img class="left-image" src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/en-us.png'); ?>" alt="<?php echo esc_attr__( 'Language Images', 'histudy' ); ?>">
                        <span class="menu-item"><?php echo esc_html__( 'English', 'histudy' ); ?></span>
                        <i class="right-icon feather-chevron-down"></i>
                    </a>
                    <ul class="sub-menu">
                        <li>
                            <a href="#">
                                <img class="left-image" src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/fr.png'); ?>" alt="<?php echo esc_attr__( 'Language Images', 'histudy' ); ?>">
                                <span class="menu-item"><?php echo esc_html__( 'Français', 'histudy' ); ?></span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <img class="left-image" src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/de.png'); ?>" alt="<?php echo esc_attr__( 'Language Images', 'histudy' ); ?>">
                                <span class="menu-item"><?php echo esc_html__( 'Deutsch', 'histudy' ); ?></span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            <?php } ?>
        </div>
        <?php else: echo do_shortcode('[wpml_language_selector_widget]');  endif;
    }