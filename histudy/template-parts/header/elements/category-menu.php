<?php
/**
 * Template part for displaying header categories dropdown
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$cat_on__icons_off = isset($rainbow_options['rainbow_header_cat_on__icons_off'] ) ? $rainbow_options['rainbow_header_cat_on__icons_off'] : '0' ;
if( TUTOR_ACTIVED && !empty( $cat_on__icons_off ) ):
    ?>
    <div class="rbt-category-menu-wrapper rbt-category-update">
        <div class="rbt-category-btn">
            <div class="rbt-offcanvas-trigger md-size icon">
                <span class="d-none d-xl-block">
                    <i class="feather-grid"></i>
                </span>
                <i title="Category" class="feather-grid d-block d-xl-none"></i>
            </div>
            <span class="category-text d-none d-xl-block"><?php echo esc_html__('Category', 'histudy'); ?></span>
        </div>
        <?php
        $args = array(
            'taxonomy' => 'course-category', // Taxonomy name for course categories in Tutor LMS
            'hide_empty' => false, // Set to true to hide empty categories
            'parent' => 0, // Get only top-level parent categories
        );
        $parent_categories = get_categories($args);
        if (!empty($parent_categories)) {
            echo '<div class="update-category-dropdown d-none d-xl-block"><div class="inner"><ul class="dropdown-parent-wrapper">';
            foreach ($parent_categories as $index => $parent_category) {
                $active_class = ($index === 0) ? ' active' : ''; // Set the first tab content as active
                $thumbnail_id = get_term_meta( $parent_category->term_id, 'thumbnail_id', true );
                if ( $thumbnail_id ) {
                    $image = wp_get_attachment_thumb_url( $thumbnail_id );
                } else {
                    $image = "";
                }
                echo '<li class="dropdown-parent-list '. $active_class .'">';
                echo '<a href="' . get_term_link($parent_category) . '">';
                $image    = str_replace( ' ', '%20', $image );
                if( $cat_on__icons_off == 1 ) {
                    if (!empty($image)) {
                        echo '<img src="' . esc_url($image) . '" alt="' . esc_attr($parent_category->name) . '" class="category-image mr--10" height="20" width="20" />';
                    }
                }
                echo esc_html($parent_category->name) . '</a>';

                // Get sub-categories for the current parent category
                $args['parent'] = $parent_category->term_id;
                $sub_categories = get_categories($args);

                if (!empty($sub_categories)) {
                    echo '<div class="dropdown-child-wrapper d-block">
                        <h3 class="rbt-short-title">'.apply_filters( 'histudy_sub_cat_heading_label', __('Category Submenu Items', 'histudy') ).'</h3>
                            <div class="child-inner">';
                    foreach ($sub_categories as $sub_category) {
                        $thumbnail_id = get_term_meta( $sub_category->term_id, 'thumbnail_id', true );
                        if ( $thumbnail_id ) {
                            $image = wp_get_attachment_thumb_url( $thumbnail_id );
                        } else {
                            $image = "";
                        }
                        echo ' <div class="dropdown-child-list">';
                            echo '<a href="' . get_term_link($sub_category) . '">';
                            $image    = str_replace( ' ', '%20', $image );
                            if( $cat_on__icons_off == 1 ) {
                                if (!empty($image)) {
                                    echo '<img src="' . $image . '" alt="' . esc_attr($sub_category->name) . '" class="category-image mr--10" height="20" width="20" />';
                                }
                            }
                            echo esc_html($sub_category->name) . '</a>';
                        echo '</div>';
                    }
                    echo '</div></div>';
                }

                echo '</li>';
            }
            echo '</ul></div></div>';
        } else {
        }
        ?>
    </div>
<?php endif;