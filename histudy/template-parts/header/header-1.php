<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? "rbt-transparent-header" : "";
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky) ? " header-sticky" : " ";
if( 1 == $rainbow_options['rainbow_search_icon'] || 1 == $rainbow_options['rainbow_minicart_icon'] || 1 == $rainbow_options['rainbow_admin_icon'] || 1 == $rainbow_options['rainbow_enable_button']) {
    $menu_nav_position = '';
} else {
    $menu_nav_position = 'rbt-header-nav-pos-right';
}

?>
<!-- Start Header Area -->
<header class="rbt-header rbt-header-1 <?php echo esc_attr($header_transparent); echo esc_attr( $header_sticky ) ?> ">
    <div class="rbt-sticky-placeholder"></div>

    <?php get_template_part('template-parts/header/topbar/topbar', '1'); ?>

    <div class="rbt-header-wrapper header-space-betwween <?php echo esc_attr($header_sticky); ?>">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left rbt-header-content">
                    <div class="header-info">
                        <?php get_template_part('template-parts/header/elements/logo'); ?>
                    </div>
                    <div class="header-info">
                        <?php get_template_part('template-parts/header/elements/category-menu');?>
                    </div>
                </div>
                <div class="rbt-main-navigation d-none d-xl-block <?php echo esc_attr( $menu_nav_position ); ?>">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>
                <div class="header-right">
                    <!-- Navbar Icons -->
                    <?php if( $rainbow_options['rainbow_search_icon'] || $rainbow_options['rainbow_minicart_icon']  || $rainbow_options['rainbow_admin_icon'] ): ?>
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/cart-icon'); ?>
                            <?php get_template_part('template-parts/header/elements/user'); ?>
                        </ul>
                    <?php endif; ?>
                    <?php get_template_part('template-parts/header/elements/header-btn'); ?>
                    <?php get_template_part('template-parts/header/elements/mobile-hamburger'); ?>
                </div>
            </div>
        </div>

        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>

    </div>
</header>
