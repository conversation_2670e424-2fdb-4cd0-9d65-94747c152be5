<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
    $rainbow_nav_menu_args = Rainbow_Helper::rainbow_nav_menu_args_onepagenav();
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $header_top_enable = $rainbow_options['header_top_enable'];
    $header_top_btn_badge = $rainbow_options['header_top_btn_badge'];
    $header_layout = Rainbow_Helper::rainbow_header_layout();
    $header_transparent = $header_layout['header_transparent'];
    $header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : " ";
    $header_top_intro_text = $rainbow_options['header_top_intro_text'];
    $header_sticky = $header_layout['header_sticky'];
    $header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
    $header_sticky = $header_sticky_condition ? " header-sticky" : " ";
    $header_top_bg = !empty($rainbow_options['header_top_enable']) && !empty($rainbow_options['header_top_bg']['url']) ? $rainbow_options['header_top_bg']['url']: '';
?>
<header class="rbt-header <?php echo esc_attr( $header_transparent );?>">
    <div class="rbt-header-campaign rbt-header-campaign-1 rbt-header-top-news bg-image1">
        <div class="wrapper">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="inner justify-content-center">
                            <div class="content">
                                <?php if(!empty($header_top_btn_badge)) : ?>
                                    <span class="rbt-badge variation-02 bg-color-primary color-white radius-round"><?php echo esc_html($header_top_btn_badge); ?></span>
                                <?php endif; ?>
                                <?php if(!empty($header_top_intro_text)) : ?>
                                    <span class="news-text color-white-off"><img src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/hand-emojji.svg'); ?>" alt="Hand Emojji Images"> <?php echo esc_html($header_top_intro_text); ?>.</span>
                                <?php endif; ?>
                            </div>
                            <div class="right-button">
                                <?php 
                                ob_start();
                                    get_template_part( 'template-parts/header/topbar/header-top-btn', 2 );
                                echo ob_get_clean();    
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="icon-close position-right">
            <button class="rbt-round-btn btn-white-off bgsection-activation">
                <i class="feather-x"></i>
            </button>
        </div>
    </div>

    <div class="rbt-sticky-placeholder"></div>

    <div class="rbt-header-wrapper">
        <div class="container">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left">
                    <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/logo');
                        echo ob_get_clean();
                    ?>
                </div>

                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right">
                    <!-- Start OffCanvas Menu  -->
                    <div class="rbt-offcanvas-trigger" id="rbt-offcanvas-activation">
                        <span class="offcanvas-trigger">
                        <span class="offcanvas-bars">
                            <span></span>
                            <span></span>
                            <span></span>
                            </span>
                            </span>
                        </div>
                    <!-- End OffCanvas Menu  -->
                </div>
            </div>
        </div>
        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
    </div>
</header>
<?php get_template_part('template-parts/header/offcanvas-activation');