<?php
/**
 * Template part for displaying main header
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_nav_menu_args = Rainbow_Helper::rainbow_nav_menu_args_sidenav();
$rainbow_nav_menu_args_cat = Rainbow_Helper::rainbow_nav_menu_args_catnav();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_header_side_info = $rainbow_options['rainbow_header_side_info'] ? $rainbow_options['rainbow_header_side_info']: '';
?>
<!-- Start Side Vav -->
<div class="side-menu">
    <div class="inner-wrapper">
        <div class="inner-top">
            <div class="content">
                <?php get_template_part('template-parts/header/elements/logo'); ?> 
                <div class="rbt-btn-close" id="btn_sideNavClose">
                    <button class="rbt-round-btn"><i class="feather-x"></i></button>
                </div>
            </div>
            <?php if(!empty($rainbow_header_side_info)) : ?>
                <p class="description"><?php echo esc_html($rainbow_header_side_info); ?></p>
            <?php endif; ?>
            <ul class="navbar-top-left rbt-information-list justify-content-start">
                <?php if(!empty($rainbow_options['email'])) : ?>
                    <li>
                        <a href="mailto:<?php echo esc_attr($rainbow_options['email']) ;?>"><i class="feather-mail"></i><?php echo esc_html( $rainbow_options['email'] ); ?></a>
                    </li>
                <?php endif; ?>
                <?php if(!empty($rainbow_options['phone']) && !empty($rainbow_options['rainbow_top_phone'])) : ?>
                    <li>
                        <a href="tel:<?php echo esc_attr($rainbow_options['phone']) ;?>"><i class="feather-phone"></i><?php echo esc_html($rainbow_options['phone']) ;?></a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
        <?php if (has_nav_menu('sidenav')) {
            wp_nav_menu($rainbow_nav_menu_args);
        }; ?>
        <?php if ($rainbow_options['histudy_social_icons']){
            $histudy_social_label = $rainbow_options['histudy_social_label'];
            $rainbow_socials = Rainbow_Helper::rainbow_socials(); ?>  
                <div class="social-share-wrapper">
                    <span class="rbt-short-title d-block"><?php echo esc_html( $histudy_social_label ); ?></span>
                    <?php if ($rainbow_socials): ?> 
                        <ul class="social-icon social-default transparent-with-border justify-content-start mt--20">
                        <?php foreach ($rainbow_socials as $rbsocial): ?>
                            <li>
                                <a target="_blank" href="<?php echo esc_url($rbsocial['url']); ?>" title="<?php echo esc_attr($rbsocial['title']); ?>">
                                <i class="<?php echo esc_attr($rbsocial['icon']); ?>"></i></a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?> 
            </div> 
        <?php } ?>
        <?php if (has_nav_menu('categorymenu')) {
            wp_nav_menu($rainbow_nav_menu_args_cat);
        }; ?>
    </div>
</div>
<!-- End Side Vav -->