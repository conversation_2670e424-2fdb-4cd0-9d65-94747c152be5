<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_enable = $rainbow_options['header_top_enable'];
$header_top_btn_badge = $rainbow_options['header_top_btn_badge'];
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? "rbt-transparent-header" : "rbt-not-transparent-header";
$header_top_intro_text = $rainbow_options['header_top_intro_text'];
$header_sticky = $header_layout['header_sticky'];
$header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
$header_sticky = $header_sticky_condition ? " header-sticky" : " ";
$header_top_bg = !empty($rainbow_options['header_top_enable']) && !empty($rainbow_options['header_top_bg']['url']) ? $rainbow_options['header_top_bg']['url']: '';
?>
<!-- Start Header Area -->
<header class="rbt-header rbt-header-10 <?php echo esc_attr($header_transparent); ?>">
    <div class="rbt-sticky-placeholder"></div>
    <div class="rbt-header-wrapper header-not-transparent <?php echo esc_attr($header_sticky); ?>">
        <div class="container">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left rbt-header-content">
                    <div class="header-info">
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/logo');
                        echo ob_get_clean();
                        ?>
                    </div>
                </div>
                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right">
                    <!-- Navbar Icons -->
                    <ul class="quick-access">
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/search-icon');
                        echo ob_get_clean();
                        ?>
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/cart-icon');
                        echo ob_get_clean();    
                        ?>
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/user');
                        echo ob_get_clean();    
                        ?>
                    </ul>

                    <?php
                    ob_start();
                        get_template_part('template-parts/header/elements/header-btn');
                    echo ob_get_clean();
                    ?>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button rbt-round-btn">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->

                </div>
            </div>
        </div>
        <?php
            ob_start();
                get_template_part('template-parts/header/elements/search-dropdown');
            echo ob_get_clean();
        ?>
    </div>
</header>
<!-- Mobile Menu Section -->