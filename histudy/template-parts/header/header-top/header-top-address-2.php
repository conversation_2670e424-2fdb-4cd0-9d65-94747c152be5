<?php
    /**
     * Template part for displaying header layout one
     *
     * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
     *
     * @package histudy
     */
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $rainbow_header_address = isset($rainbow_options['rainbow_header_address']) && $rainbow_options['rainbow_header_address'] ? $rainbow_options['rainbow_header_address']: '';
    if(empty($rainbow_header_address)) {
        $rainbow_header_address = $rainbow_options['header_address'] ? $rainbow_options['header_address']: '';
    }
?>
<?php if(!empty($rainbow_header_address)) : ?>
    <p><i class="feather-map-pin"></i> <?php echo esc_html($rainbow_header_address); ?></p>
<?php endif;