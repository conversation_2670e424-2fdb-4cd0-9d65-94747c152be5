<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $header_top_enable = $rainbow_options['header_top_enable'];
    $header_top_btn_badge = $rainbow_options['header_top_btn_badge'];
    $header_top_intro_text = $rainbow_options['header_top_intro_text'];
    $header_top_button_text = $rainbow_options['header_top_button_text'];
    $header_top_button_text = $rainbow_options['header_top_button_text'];
    $header_top_button_url = $rainbow_options['header_top_button_url'];
 ?>
<?php if(!empty($header_top_enable)) : ?>
<div class="rbt-header-campaign rbt-header-campaign-1 rbt-header-top-news bg-image1">
    <div class="wrapper">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="inner justify-content-center">
                        <div class="content">
                            <?php if(!empty($header_top_btn_badge)) : ?>
                            <span class="rbt-badge variation-02 bg-color-primary color-white radius-round"><?php echo esc_html($header_top_btn_badge); ?></span>
                            <?php endif; ?>
                            <?php if(!empty($header_top_intro_text)) : ?>
                                <span class="news-text color-white-off"><img src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/icons/hand-emojji.svg'); ?>" alt="<?php echo esc_attr__( 'Hand Emojji Images', 'histudy' ); ?>"> <?php echo esc_html($header_top_intro_text); ?></span>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($header_top_button_text)) : ?>
                        <div class="right-button">
                            <a class="rbt-btn-link color-white" target="_blank" href="<?php echo wp_http_validate_url($header_top_button_url) ? wp_http_validate_url($header_top_button_url): ''; ?>">
                                <span><?php echo esc_html($header_top_button_text); ?> <i class="feather-arrow-right"></i></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="icon-close position-right">
        <button class="rbt-round-btn btn-white-off bgsection-activation">
            <i class="feather-x"></i>
        </button>
    </div>
</div>
<?php endif;