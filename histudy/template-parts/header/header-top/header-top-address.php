<?php
    /**
     * Template part for displaying header layout one
     *
     * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
     *
     * @package histudy
     */
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $rainbow_header_address = rainbow_get_acf_data( 'rainbow_header_address' );
    $rainbow_header_address_url = rainbow_get_acf_data( 'rainbow_header_address_url' );
    if( empty( $rainbow_header_address ) ) {
        $rainbow_header_address = $rainbow_options['header_address'];
    }
    if( empty( $rainbow_header_address_url ) ) {
        $rainbow_header_address_url = $rainbow_options['header_address_url'];
    }
    if(empty($rainbow_header_address)) {
        $rainbow_header_address = $rainbow_options['header_address'] ? $rainbow_options['header_address']: __( '20th New York, ND 8545, USA', 'histudy' );
    }
    if(empty($rainbow_header_address_url)) {
        $rainbow_header_address_url = $rainbow_options['header_address_url'] ? $rainbow_options['header_address_url']: '#';
    }
?>
<?php if(!empty($rainbow_header_address)) : ?>
    <a href="<?php echo esc_url($rainbow_header_address_url) ? esc_url($rainbow_header_address_url): ''; ?>"><i class="feather-map-pin"></i><?php echo esc_html($rainbow_header_address); ?></a>
<?php endif;