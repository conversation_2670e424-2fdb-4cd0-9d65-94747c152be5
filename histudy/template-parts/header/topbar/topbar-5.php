<?php
/**
 * Template part for displaying topbar layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_enable = $rainbow_options['header_top_enable'];
if ( !empty($header_top_enable) && 'false' !== $header_top_enable && "no" !== $header_top_enable && "0" !== $header_top_enable) { ?>
<div class="rbt-header-top rbt-header-top-1 header-space-betwween bg-color-white rbt-border-bottom d-none d-xl-block">
    <div class="container-fluid">
        <div class="top-expended-wrapper">
            <div class="top-expended-inner rbt-header-sec align-items-center ">
                <div class="rbt-header-sec-col rbt-header-left">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <?php get_template_part('template-parts/header/elements/header-top-social'); ?>   
                        </div>
                    </div>
                </div>
                <div class="rbt-header-sec-col rbt-header-right">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="rbt-information-list">
                                <?php if(!empty($rainbow_options['email'])) : ?>
                                <li>
                                    <a href="mailto:<?php echo esc_attr($rainbow_options['email']) ;?>"><i class="feather-mail"></i><?php echo esc_html( $rainbow_options['email'] ); ?></a>
                                </li>
                                <?php endif; ?>
                                <?php if(!empty($rainbow_options['phone']) && !empty($rainbow_options['rainbow_top_phone'])) : ?>
                                <li>
                                    <a href="tel:<?php echo esc_attr($rainbow_options['phone']) ;?>"><i class="feather-phone"></i><?php echo esc_html($rainbow_options['phone']) ;?></a>
                                </li>
                                <?php endif; ?>
                                <?php if(!empty($rainbow_options['rainbow_top_questions_switch'])) : ?>
                                <li>
                                    <?php get_template_part( 'template-parts/header/header-top/header-top', 'questions' ); ?>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php }