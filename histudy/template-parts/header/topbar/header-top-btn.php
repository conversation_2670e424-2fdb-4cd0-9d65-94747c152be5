<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
 $rainbow_options = Rainbow_Helper::rainbow_get_options();
 $header_top_button_switch = $rainbow_options['header_top_button_switch'] ? $rainbow_options['header_top_button_switch']: 0;
 $header_top_button_text = $rainbow_options['header_top_button_text'] ? $rainbow_options['header_top_button_text']: __( 'Purchase Now', 'histudy' );
 $header_top_button_url = $rainbow_options['header_top_button_url'] ? $rainbow_options['header_top_button_url']: '#';
?>
 <?php if( 1 == $header_top_button_switch ) : ?>
    <a class="rbt-btn rbt-switch-btn btn-gradient btn-xs" href="<?php echo esc_url($header_top_button_url); ?>">
        <span data-text="<?php echo esc_attr($header_top_button_text); ?>"><?php echo esc_html($header_top_button_text); ?></span>
    </a>
 <?php endif;