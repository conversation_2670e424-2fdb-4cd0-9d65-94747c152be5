<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
 $rainbow_options = Rainbow_Helper::rainbow_get_options();
 $header_top_button_text = $rainbow_options['header_top_button_text'] ? $rainbow_options['header_top_button_text']: '';
 $header_top_button_url = $rainbow_options['header_top_button_url'] ? $rainbow_options['header_top_button_url']: '#';
 $header_top_button_switch = $rainbow_options['header_top_button_switch'] ? $rainbow_options['header_top_button_switch']: '';
 ?>
 <?php if( !empty( $header_top_button_text ) && !empty( $header_top_button_switch ) ) : ?>
    <a class="rbt-btn-link color-white" href="<?php echo esc_url($header_top_button_url); ?>">
        <span><?php echo esc_html($header_top_button_text); ?> <i class="feather-arrow-right"></i></span>
    </a>
 <?php endif;