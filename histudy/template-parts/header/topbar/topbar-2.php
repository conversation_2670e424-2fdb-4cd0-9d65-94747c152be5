<?php
/**
 * Template part for displaying topbar layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_btn_badge = (!empty($rainbow_options['header_top_btn_badge'])) ? $rainbow_options['header_top_btn_badge'] : '';
$header_top_enable = $rainbow_options['header_top_enable'];
if ( !empty($header_top_enable) && 'false' !== $header_top_enable && "no" !== $header_top_enable && "0" !== $header_top_enable) { ?>
<!-- Start Header Top -->
<div class="rbt-header-top rbt-header-top-1 header-space-betwween bg-color-darker rbt-border-bottom top-expended-activation">
    <div class="container-fluid">
        <div class="top-expended-wrapper">
            <div class="top-expended-inner rbt-header-sec align-items-center ">

                <div class="rbt-header-sec-col rbt-header-left d-none d-xl-block">
                    <div class="rbt-header-content">
                        <div class="header-info">
                            <ul class="rbt-information-list">
                                <?php get_template_part('template-parts/header/elements/header-top-info', '2');?>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="rbt-header-sec-col rbt-header-center">
                    <div class="rbt-header-content justify-content-start justify-content-xl-center">
                        <div class="header-info">
                            <div class="rbt-header-top-news">
                                <div class="inner">
                                    <div class="content">
                                        <?php if(!empty($header_top_btn_badge)) : ?>
                                        <span class="rbt-badge variation-02 bg-color-primary color-white radius-round"><?php echo esc_html($header_top_btn_badge); ?></span>
                                        <?php endif; ?>
                                        <?php get_template_part('template-parts/header/elements/header-top', 'notification');?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rbt-header-sec-col rbt-header-right mt_md--10 mt_sm--10">
                    <div class="rbt-header-content justify-content-start justify-content-lg-end">
                        <!-- Start Header Information List  -->
                        <?php get_template_part('template-parts/header/elements/header-top-switcher', 'language');?>
                        <!-- End Header Information List  -->

                        <!-- Start Header Information List  -->
                        <?php get_template_part('template-parts/header/elements/header-top-switcher', 'currency');?>
                        <!-- End Header Information List  -->
                    </div>
                </div>

            </div>
            <div class="header-info">
                <div class="top-bar-expended d-block d-lg-none">
                    <button class="topbar-expend-button rbt-round-btn"><i class="feather-plus"></i></button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Header Top -->
<?php }