<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " header-transparent" : "bg-color-white";
$header_sticky = $header_layout['header_sticky'];
$header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
$header_sticky = $header_sticky_condition ? " header-sticky" : " ";
?>
<header class="rbt-header rbt-header-5">
    <?php if(!empty($header_sticky_condition)) : ?>
    <div class="rbt-sticky-placeholder"></div>
    <?php endif; ?>
    <!-- Start Header Top -->
    <?php get_template_part('template-parts/header/topbar/topbar', '5'); ?>
    <!-- End Header Top -->

    <div class="rbt-header-wrapper header-space-betwween <?php echo esc_attr($header_transparent); ?> <?php echo esc_attr($header_sticky); ?>">
        <div class="container-fluid">
            <div class="mainbar-row rbt-navigation-center align-items-center">
                <div class="header-left">
                    <?php get_template_part('template-parts/header/elements/logo'); ?>
                </div>

                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right">
                    <!-- Navbar Icons -->
                    <?php if( $rainbow_options['rainbow_search_icon'] ): ?>
                        <ul class="quick-access">
                            <?php get_template_part('template-parts/header/elements/search-icon'); ?>
                        </ul>
                    <?php endif; ?>

                    <div class="ml--20">
                        <?php get_template_part('template-parts/header/elements/header-btn'); ?>
                    </div>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar ml--5 d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->
                </div>
            </div>
        </div>
        <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
    </div>
</header>