<?php 
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
?>  
<!-- Mobile Menu Section -->
<div class="popup-mobile-menu">
    <div class="inner-wrapper">
        <div class="inner-top">
            <div class="content">
                <?php get_template_part('template-parts/header/elements/logo'); ?>
                <div class="rbt-btn-close">
                    <button class="close-button rbt-round-btn"><i class="feather-x"></i></button>
                </div>
            </div>
            <?php if(!empty($rainbow_options['rainbow_mobile_header_message'])) : ?>
                <p class="description"><?php echo esc_html($rainbow_options['rainbow_mobile_header_message']); ?></p>
            <?php endif; ?>
            <?php if(!empty($rainbow_options['email']) || !empty($rainbow_options['phone'])) : ?>
                <ul class="navbar-top-left rbt-information-list justify-content-start">
                    <?php if(!empty($rainbow_options['email'])) : ?>
                    <li>
                        <a href="mailto:<?php echo esc_attr($rainbow_options['email']) ;?>"><i class="feather-mail"></i><?php echo esc_html( $rainbow_options['email'] ); ?></a>
                    </li>
                    <?php endif; ?>
                    <?php if(!empty($rainbow_options['phone'])) : ?>
                    <li>
                        <a href="tel:<?php echo esc_attr($rainbow_options['phone']) ;?>"><i class="feather-phone"></i><?php echo esc_html($rainbow_options['phone']) ;?></a>
                    </li>
                    <?php endif; ?>
                </ul>
            <?php endif; ?>
        </div>
        <?php
        if( has_nav_menu( 'mobilemenu' ) ) {
            $args =  array(
                'theme_location' => 'mobilemenu',
                'container' => 'nav',
                'container_class' => 'mainmenu-nav',
                'container_id' => '',
                'menu_class' => 'mainmenu',
                'menu_id' => '',
                  'walker' => new RainbowNavWalker(),
            );
            wp_nav_menu( $args );
        } else {
            if (has_nav_menu('primary')) {
                wp_nav_menu($rainbow_nav_menu_args);
            }
        }
         ?> 
        <div class="mobile-menu-bottom">  
            <?php  
             if( $rainbow_options['rainbow_enable_button'] ): ?> 
                <div class="rbt-btn-wrapper mb--20">
                    <a class="rbt-btn btn-border-gradient radius-round btn-sm hover-transform-none w-100 justify-content-center text-center" href="<?php echo esc_url($rainbow_options['header_button_url']); ?>">
                        <span data-text="<?php echo esc_html($rainbow_options['header_button_txt']); ?>"><?php echo esc_html($rainbow_options['header_button_txt']); ?></span>
                    </a> 
                </div>
            <?php endif; ?>  

            <?php if ($rainbow_options['histudy_social_icons']){
                    $histudy_social_label = $rainbow_options['histudy_social_label'];
                    $rainbow_socials = Rainbow_Helper::rainbow_socials(); ?>  
                    <div class="social-share-wrapper">
                        <?php if(!empty($histudy_social_label)) : ?>
                        <span class="rbt-short-title d-block"><?php echo esc_html( $histudy_social_label ); ?></span>
                        <?php endif; ?>
                        <?php if ($rainbow_socials): ?> 
                           <ul class="social-icon social-default transparent-with-border justify-content-start mt--20">
                            <?php foreach ($rainbow_socials as $rbsocial): ?>
                                <li>
                                    <a target="_blank" href="<?php echo esc_url($rbsocial['url']); ?>" title="<?php echo esc_attr($rbsocial['title']); ?>">
                                    <i class="<?php echo esc_attr($rbsocial['icon']); ?>"></i></a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?> 
                </div> 
            <?php } ?>    
        </div>  
    </div>
</div>