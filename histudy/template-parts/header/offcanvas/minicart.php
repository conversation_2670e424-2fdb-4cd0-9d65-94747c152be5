<?php
/**
 * Mini-cart
 */

defined('ABSPATH')  || exit;

if(!class_exists('WooCommerce')) {
    return;
}
$cart_empty_check = '';
if( \Elementor\Plugin::$instance->editor->is_edit_mode() ) {
    return;
}
if ( WC()->cart->get_cart_contents_count() == 0 ) {
    $cart_empty_check = 'cart-empty';
}
$monetize_by = '';
if( function_exists('tutor') ) {
    $cart_controller = new  \Tutor\Ecommerce\CartController();
    $get_cart        = $cart_controller->get_cart_items();
    $courses         = $get_cart['courses'];
    $total_count     = $courses['total_count'];
    $course_list     = $courses['results'];
    $subtotal        = 0;
    $cart_page_url          = \Tutor\Ecommerce\CartController::get_page_url();
    $tutor_options = get_option('tutor_option');
    $monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
    $checkout_page_url = \Tutor\Ecommerce\CheckoutController::get_page_url();
} 

do_action('woocommerce_before_mini_cart'); 

if( 'wc' == $monetize_by ) {
?>

<!-- Start Side Vav -->
<div class="rbt-cart-side-menu">
    <div class="inner-wrapper">
        <div class="inner-top">
            <div class="content">
                <div class="title">
                    <h4 class="title mb--0"><?php echo esc_html__("Shopping cart", "histudy") ?></h4>
                </div>
                <div class="rbt-btn-close" id="btn_sideNavClose2">
                    <button class="minicart-close-button rbt-round-btn"><i class="feather-x"></i></button>
                </div>
            </div>
        </div>

        <nav class="side-nav w-100 <?php echo esc_attr( $cart_empty_check ); ?>">
            <?php if (!WC()->cart->is_empty()) : ?>
                <?php 
                    ob_start();
                        get_template_part('template-parts/components/woocommerce/minicart');
                    echo ob_get_clean();
                    ?>
                <?php else : ?>
                    <div class="rbt-minicart-wrapper">
                        <div class="section-title mb--30">
                            <h3 class="rbt-title-style-2"><?php echo esc_html__("No products in the cart.", "histudy") ?></h3>
                        </div>
                        <div class="rbt-no-cart-item-exits">
                            <img src="<?php echo esc_url( get_template_directory_uri(). '/assets/images/logo/cart-empty.png' ); ?>" alt="<?php echo esc_attr__( 'image', 'histudy' ) ?>">
                        </div>
                        
                            <a class="rbt-btn rbt-switch-btn btn-sm"
                            href="<?php echo esc_url(apply_filters('woocommerce_return_to_shop_redirect', wc_get_page_permalink('shop'))); ?>">
                                <span data-text="<?php echo esc_attr__('Return To Shop', 'histudy'); ?>"><?php echo esc_html__('Return To Shop', 'histudy'); ?></span>
                            </a>
                            
                    </div>
            <?php endif; ?>
        </nav>

        <div class="rbt-minicart-footer <?php echo(WC()->cart->is_empty() ? ' wd-cart-empty' : ''); ?>">
            <div class="rbt-side-cart-subtotal-box">
                <?php
                if (!empty(WC()->cart->get_cart_contents_count())) :
                    if (function_exists('WC') && version_compare(WC()->version, '3.7.0', '<')) : ?>
                        <hr class="mb--0">
                            <div class="rbt-cart-subttotal">
                                <p class="subtotal"><strong><?php echo esc_html__("Subtotal:", "histudy") ?></strong></p>
                                <p class="price"><?php echo WC()->cart->get_cart_subtotal(); ?></p>
                            </div>
                        <?php else : ?>
                            <div class="rbt-cart-subttotal">
                                <div class="subtotal">
                                    <?php
                                    /**
                                     * Woocommerce_widget_shopping_cart_total hook.
                                     *
                                     * @hooked woocommerce_widget_shopping_cart_subtotal - 10
                                     */
                                    do_action('woocommerce_widget_shopping_cart_total');
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <hr class="mb--0">
                    <div class="rbt-minicart-bottom mt--20">
                        <?php do_action('woocommerce_widget_shopping_cart_before_buttons'); ?>
                        <div class="woocommerce-mini-cart__buttons">
                            <?php do_action('woocommerce_widget_shopping_cart_buttons'); ?>
                        </div>
                        <?php do_action('woocommerce_widget_shopping_cart_after_buttons'); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php do_action('woocommerce_after_mini_cart'); ?>

    </div>
</div>
<!-- End Side Vav -->
<?php } elseif( 'tutordfdf' == $monetize_by ) { ?>

    <div class="rbt-cart-side-menu side-menu-active">
    <div class="inner-wrapper">
        <div class="inner-top">
            <div class="content">
                <div class="title">
                    <h4 class="title mb--0"><?php echo esc_html__("Shopping cart", "histudy") ?></h4>
                </div>
                <div class="rbt-btn-close" id="btn_sideNavClose2">
                    <button class="minicart-close-button rbt-round-btn"><i class="feather-x"></i></button>
                </div>
            </div>
        </div>
        <nav class="side-nav w-100 ">
            <?php if( !empty($course_list)) : ?>
            <ul class="rbt-minicart-wrapper">
                <?php
                    foreach ( $course_list as $key => $course ) :
                        $course_duration  = get_tutor_course_duration_context( $course->ID, true );
                        $course_price     = tutor_utils()->get_raw_course_price( $course->ID );
                        $regular_price    = $course_price->regular_price;
                        $sale_price       = $course_price->sale_price;
                        $tutor_course_img = get_tutor_course_thumbnail_src( '', $course->ID );

                        $subtotal += $sale_price ? $sale_price : $regular_price;
                        ?>
                <li class="minicart-item woocommerce-mini-cart-item">
                    <div class="thumbnail">
                        <a href="<?php echo esc_url( get_the_permalink( $course ) ); ?>" class="cart-item-image">
                            <img src="<?php echo esc_url( $tutor_course_img ); ?>" alt="Course thumb">
                        </a>
                    </div>
                    <div class="product-content">
                        <h6 class="title">
                            <a href="<?php echo esc_url( get_the_permalink( $course ) ); ?>">
							    <?php echo esc_html( $course->post_title ); ?>
							</a>
                        </h6>
                        <span class="quantity"><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol"></span><?php echo tutor_get_formatted_price( $regular_price ); //phpcs:ignore?></bdi></span></span>
                    </div>
                    <button class="tutor-btn tutor-btn-link tutor-cart-remove-button remove remove_from_cart_button" data-course-id="<?php echo esc_attr( $course->ID ); ?>">
                    <i class="feather-x"></i>
                    </button>
                </li>
                <?php endforeach; ?>
            </ul>
            <?php else : ?>
                    <div class="rbt-minicart-wrapper">
                        <div class="section-title mb--30">
                            <h3 class="rbt-title-style-2"><?php echo esc_html__("No products in the cart.", "histudy") ?></h3>
                        </div>
                        <div class="rbt-no-cart-item-exits">
                            <img src="<?php echo esc_url( get_template_directory_uri(). '/assets/images/logo/cart-empty.png' ); ?>" alt="<?php echo esc_attr__( 'image', 'histudy' ) ?>">
                        </div>
                        
                            <a class="rbt-btn rbt-switch-btn btn-sm"
                            href="<?php echo esc_url(apply_filters('woocommerce_return_to_shop_redirect', wc_get_page_permalink('shop'))); ?>">
                                <span data-text="<?php echo esc_attr__('Return To Shop', 'histudy'); ?>"><?php echo esc_html__('Return To Shop', 'histudy'); ?></span>
                            </a>
                            
                    </div>
            <?php endif; ?>
        </nav>

        <div class="rbt-minicart-footer ">
            <div class="rbt-side-cart-subtotal-box">
                <div class="rbt-cart-subttotal">
                    <div class="subtotal">
                        <strong><?php echo esc_html__("Subtotal:", "histudy") ?></strong> <span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol"></span><?php tutor_print_formatted_price( $subtotal ); ?></bdi></span>
                    </div>
                </div>
                <hr class="mb--0">
                <div class="rbt-minicart-bottom mt--20">
                    <div class="woocommerce-mini-cart__buttons">
                        <div class="view-cart-btn"><a class="rbt-btn btn-border icon-hover w-100 text-center btn-cart wc-forward" href="<?php echo esc_url($cart_page_url); ?>"><span class="btn-text"><?php echo esc_html__("View cart:", "histudy") ?></span><span class="btn-icon"><i class="feather-arrow-right"></i></span></a></div>
                        <div class="checkout-btn mt--20"><a class="rbt-btn btn-gradient icon-hover w-100 text-center checkout wc-forward" href="<?php echo esc_url($checkout_page_url); ?>"><span class="btn-text"><?php echo esc_html__("Checkout:", "histudy") ?></span><span class="btn-icon"><i class="feather-arrow-right"></i></span></a></div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>

<?php }

