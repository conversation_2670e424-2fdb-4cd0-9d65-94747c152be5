<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

    $rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $header_layout = Rainbow_Helper::rainbow_header_layout();
    $header_transparent = $header_layout['header_transparent'];
    $header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : "bg-not-transparent";
    $header_top_intro_text = $rainbow_options['header_top_intro_text'];
    $header_sticky = $header_layout['header_sticky'];
    $header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
    $header_sticky = $header_sticky_condition ? " header-sticky" : " ";
    $header_top_bg = !empty($rainbow_options['header_top_enable']) && !empty($rainbow_options['header_top_bg']['url']) ? $rainbow_options['header_top_bg']['url']: '';
?>
 <header class="rbt-header rbt-header-7 <?php echo esc_attr($header_transparent); ?>">
        <?php if(!empty($header_sticky_condition)) : ?>
            <div class="rbt-sticky-placeholder"></div>
        <?php endif; ?>
        <?php if(!empty($rainbow_options['header_top_enable'])) : ?>
            <div class="rbt-header-top-2  bg-gradient-7 color-white pt--15 pb--15 d-none d-xl-block">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-4 col-md-5 col-12 d-none d-md-block">
                            <div class="fancy-menu-text fancu-menu-start">
                            <?php if(!empty($header_top_intro_text)) : ?>
                                <p><a href="<?php echo esc_url(home_url('/')); ?>"><?php echo esc_html($header_top_intro_text); ?><i class="feather-chevron-right"></i></a></p>
                            <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-lg-8 col-md-7 col-12">
                            <div class="fancy-menu-address fancu-menu-end">
                                <div class="address-content">
                                    <?php
                                        ob_start();
                                            get_template_part( 'template-parts/header/header-top/header-top-address', 2 );
                                        echo ob_get_clean();    
                                    ?>
                                    <?php
                                    ob_start();
                                        get_template_part('template-parts/header/elements/header-top-phone', 2);
                                    echo ob_get_clean();    
                                    ?> 
                                </div>
                                <div class="social-icon-wrapper">
                                    <div class="icon-nacked">
                                        <?php 
                                        ob_start();
                                            get_template_part('template-parts/header/elements/header-top-social', 2);
                                        echo ob_get_clean();
                                        ?> 
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <div class="rbt-header-wrapper  bg-color-white color-white-variation <?php echo esc_attr($header_sticky); ?> rbt-border-bottom-light" data-background="<?php echo esc_attr($header_top_bg); ?>">
            <div class="container">
                <div class="mainbar-row rbt-navigation-end align-items-center">
                    <div class="header-left">
                        <?php
                        ob_start();
                            get_template_part('template-parts/header/elements/logo', 'gradient');
                        echo ob_get_clean();    
                        ?>
                    </div>
                    <div class="rbt-main-navigation d-none d-xl-block">
                        <?php if (has_nav_menu('primary')) {
                            wp_nav_menu($rainbow_nav_menu_args);
                        }; ?>
                    </div>
                    <div class="header-right">
                        <div class="rbt-btn-wrapper d-none d-xl-block">
                            <div class="rbt-btn-radius-6">
                                <?php
                                ob_start();
                                    get_template_part('template-parts/header/elements/header-btn');
                                echo ob_get_clean();
                                ?>
                            </div>
                        </div>

                        <!-- Start Mobile-Menu-Bar -->
                        <div class="mobile-menu-bar d-block d-xl-none">
                            <div class="hamberger">
                                <button class="hamberger-button rbt-round-btn">
                                    <i class="feather-menu"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Start Mobile-Menu-Bar -->
                    </div>

                </div>
            </div>
        </div>

        <?php
        ob_start();
            get_template_part('template-parts/header/elements/search-dropdown');
        echo ob_get_clean();
        ?>
    </header>