<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_top_enable = $rainbow_options['header_top_enable'];
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : "bg-not-transparent";
$header_top_intro_text = $rainbow_options['header_top_intro_text'];
$header_sticky = $header_layout['header_sticky'];
$header_sticky_condition = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky);
$header_sticky = $header_sticky_condition ? " header-sticky" : " ";
$header_top_bg = !empty($rainbow_options['header_top_enable']) && !empty($rainbow_options['header_top_bg']['url']) ? $rainbow_options['header_top_bg']['url']: '';
?>
<header class="rbt-header rbt-header-8 <?php echo esc_attr($header_transparent); ?>">
    <?php if(!empty($header_sticky_condition)) : ?>
        <div class="rbt-sticky-placeholder"></div>
    <?php endif; ?>
    <?php if ( !empty($header_top_enable) && 'false' !== $header_top_enable && "no" !== $header_top_enable && "0" !== $header_top_enable) { ?>
    <!-- Start Header Top  -->
    <div class="rbt-header-top rbt-header-top-1 variation-height-60 header-space-betwween bg-color-transparent top-expended-activation">
        <div class="container">
            <div class="top-expended-wrapper">
                <div class="top-expended-inner rbt-header-sec align-items-center ">
                    <div class="rbt-header-sec-col rbt-header-left">
                        <div class="rbt-header-content">
                            <div class="header-info d-none d-lg-block">
                                <ul class="rbt-information-list">
                                    <?php if(!empty($rainbow_options['rainbow_top_questions_switch'])) : ?>
                                    <li>
                                        <?php 
                                        ob_start();
                                            get_template_part( 'template-parts/header/header-top/header-top', 'questions' );
                                        echo ob_get_clean();
                                        ?>
                                    </li>
                                    <?php endif; ?>
                                    <?php if(!empty($rainbow_options['email'])) : ?>
                                    <li>
                                        <a href="mailto:<?php echo esc_attr($rainbow_options['email']) ;?>"><i class="feather-mail"></i><?php echo esc_html( $rainbow_options['email'] ); ?></a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if(!empty($rainbow_options['phone']) && !empty($rainbow_options['rainbow_top_phone'])) : ?>
                                    <li>
                                        <a href="tel:<?php echo esc_attr($rainbow_options['phone']) ;?>"><i class="feather-phone"></i><?php echo esc_html($rainbow_options['phone']) ;?></a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="rbt-header-sec-col rbt-header-right mt_md--10 mt_sm--10">
                        <div class="rbt-header-content justify-content-start justify-content-lg-end">
                            <?php 
                            ob_start();
                                get_template_part('template-parts/header/header-top/header-top-switcher-language');
                            echo ob_get_clean();
                            ?>
                            <?php
                            ob_start();
                                get_template_part('template-parts/header/elements/header-top-switcher-currency');
                            echo ob_get_clean();
                            ?>   
                        </div>
                    </div>
                </div>
                <div class="header-info">
                    <div class="top-bar-expended d-block d-lg-none">
                        <button class="topbar-expend-button rbt-round-btn"><i class="color-body feather-plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Header Top  -->
    <?php } ?>
    <div class="rbt-header-wrapper  header-sticky">
        <div class="container">
            <div class="mainbar-row rbt-navigation-end align-items-center">
                <div class="header-left">
                    <?php
                    ob_start();
                        get_template_part('template-parts/header/elements/logo');
                    echo ob_get_clean();
                    ?>
                </div>

                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>

                <div class="header-right rbt-btn-radius-6">
                    <?php
                    ob_start();
                        get_template_part('template-parts/header/elements/header-btn');
                    echo ob_get_clean();    
                    ?>

                    <!-- Start Mobile-Menu-Bar -->
                    <div class="mobile-menu-bar d-block d-xl-none">
                        <div class="hamberger">
                            <button class="hamberger-button rbt-round-btn">
                                <i class="feather-menu"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Start Mobile-Menu-Bar -->
                </div>

            </div>
        </div>
    </div>
</header>