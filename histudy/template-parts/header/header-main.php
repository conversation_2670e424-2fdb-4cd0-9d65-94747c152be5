<?php
/**
 * Template part for displaying main header
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_area = $header_layout['header_area'];
$header_style = $header_layout['header_style'];
$header_layout = Rainbow_Helper::rainbow_header_layout();

if (shortcode_exists('histudy_custom_header')) {
    echo do_shortcode('[histudy_custom_header]');
} else {
    if ("no" !== $header_area && "0" !== $header_area) {
        get_template_part('template-parts/header/header', $header_style); 
        get_template_part('template-parts/header/popup-mobile-menu');
        get_template_part('template-parts/header/popup-mobile-sidebar-menu');
        get_template_part('template-parts/header/offcanvas/popup-mobile-menu');
        get_template_part('template-parts/header/offcanvas/minicart');
        echo '<button class="close_side_menu"></button>';
    }
}
?>
<main class="main-page-wrapper"> <!-- Start Page Wrapper -->
<?php
if (!is_404() && !is_singular('post') && !is_singular("elementor_library") && !is_singular("courses")) {
    get_template_part('template-parts/content-banner');
    
}