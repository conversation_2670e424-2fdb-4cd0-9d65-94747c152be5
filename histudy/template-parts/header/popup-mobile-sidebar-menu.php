<?php 
    if( !TUTOR_ACTIVED ) {
        return;
    }
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $args = array(
        'taxonomy' => 'course-category', // Taxonomy name for course categories in Tutor LMS
        'hide_empty' => false, // Set to true to hide empty categories
        'parent' => 0, // Get only top-level parent categories
    );
    $parent_categories = get_categories($args);
?>  
<!-- Mobile Menu Section -->
<div class="popup-mobile-menu rainbow-mobile-sidebar-cat-menu" id="mobile-sidebar-popup-menu">
    <div class="inner-wrapper">
        <div class="inner-top">
            <div class="content">
                <div class="rbt-btn-close">
                    <button class="close-button rbt-round-btn"><i class="feather-x"></i></button>
                </div>
            </div>
        </div>
        <?php if(!empty($parent_categories)) : ?>
            <ul class="mainmenu">
            <?php foreach ($parent_categories as $index => $parent_category) :
                $args['parent'] = $parent_category->term_id;
                $sub_categories = get_categories($args);   
                $sub_category_count = count($sub_categories);
                $has_category = '';
                if( 0 !== absint( $sub_category_count ) ) {
                    $has_category = 'has-dropdown';
                }
            ?>
                <!-- parent list -->
                <li class="menu-item <?php echo esc_attr( $has_category ); ?>"><a href="<?php echo esc_url(get_term_link($parent_category)); ?>"><?php echo esc_html($parent_category->name); ?></a>
                    <!-- child list -->
                    <?php
                        $args['parent'] = $parent_category->term_id;
                        $sub_categories = get_categories($args);
                        if (!empty($sub_categories)) {
                            echo '<ul class="submenu">';
                                foreach ($sub_categories as $sub_category) {
                                    echo sprintf( '<li class="menu-item"><a href="%s">%s</a></li>', get_term_link($sub_category), esc_attr($sub_category->name));
                                }
                            echo '</ul>';
                        }
                    ?>
                </li>
            <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
</div>