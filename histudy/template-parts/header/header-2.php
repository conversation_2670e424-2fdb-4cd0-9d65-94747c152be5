<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_nav_menu_args = Rainbow_Helper::nav_menu_args();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$header_layout = Rainbow_Helper::rainbow_header_layout();
$header_transparent = $header_layout['header_transparent'];
$header_transparent = (!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent) ? " rbt-transparent-header" : " ";
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ( !empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky) ? " header-sticky" : " ";
?>

<header class="rbt-header rbt-header-2 <?php echo esc_attr($header_transparent); ?> rbt-btn-radius-6">
    <div class="rbt-sticky-placeholder"></div>

    <div class="rbt-header-wrapper  header-not-transparent <?php echo esc_attr($header_sticky); ?>">
        <div class="container">
            <div class="mainbar-row rbt-navigation-end align-items-center">
                <div class="header-left">
                    <?php get_template_part('template-parts/header/elements/logo'); ?>
                </div>
                <div class="rbt-main-navigation d-none d-xl-block">
                    <?php if (has_nav_menu('primary')) {
                        wp_nav_menu($rainbow_nav_menu_args);
                    }; ?>
                </div>
                <div class="header-right">

                    <?php get_template_part('template-parts/header/elements/header-btn'); ?>
                    <?php get_template_part('template-parts/header/elements/mobile-hamburger'); ?>

                </div>

            </div>
        </div>
    </div>
    <?php get_template_part('template-parts/header/elements/search-dropdown'); ?>
</header>