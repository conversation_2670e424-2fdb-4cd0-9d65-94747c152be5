<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_footer_bottom_menu_args = Rainbow_Helper::rainbow_footer_bottom_menu_args();
$allowed_tags = wp_kses_allowed_html('post');
$copyright_class = empty($rainbow_options['rainbow_footer_footerbottom']) ? 'col-12': 'col-xxl-6 col-xl-6 col-lg-6 col-md-12 col-12';
?>
<!-- Start Copyright Area  -->
<div class="copyright-area copyright-style-1 ptb--20">
    <div class="container">
        <div class="rbt-copyright-content-top">
            <div class="row align-items-center">
                <div class="<?php echo esc_attr( $copyright_class ); ?>">
                    <?php if (!empty($rainbow_options['rainbow_copyright_contact']) || !empty($rainbow_options['rainbow_footer_footerbottom'])) { ?>
                        <p class="rbt-link-hover text-center text-lg-start">
                                <?php echo do_shortcode($rainbow_options['rainbow_copyright_contact'], $allowed_tags); ?>
                        </p>
                    <?php } ?>  
                </div>
                <?php if(!empty($rainbow_options['rainbow_footer_footerbottom'] )){ ?> 
                    <div class="col-xxl-6 col-xl-6 col-lg-6 col-md-12 col-12">
                        <?php if (has_nav_menu('footerbottom')) { ?>
                            <?php wp_nav_menu($rainbow_footer_bottom_menu_args); ?>
                        <?php } ?> 
                    </div>
                <?php } ?>
            </div>
        </div>
        <?php if (is_active_sidebar('footer-bottom-additional-data')) : ?>
            <div class="pt-5">
                <?php dynamic_sidebar('footer-bottom-additional-data'); ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- End Copyright Area  -->