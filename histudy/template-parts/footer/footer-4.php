<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_footer_bottom_menu_args = Rainbow_Helper::rainbow_footer_bottom_menu_args();
$lineclass = (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3') || is_active_sidebar('footer-4')) ? "footer-menu-active" : "";
$allowed_tags = wp_kses_allowed_html('post');
$logo_light = empty($rainbow_options['rainbow_light_logo']['url']) ? Rainbow_Helper::get_img('logo/logo.png') : $rainbow_options['rainbow_light_logo']['url'];
$logo_dark = empty($rainbow_options['rainbow_head_logo']['url']) ? Rainbow_Helper::get_img('logo/logo-dark.png') : $rainbow_options['rainbow_head_logo']['url'];
?>
<?php if (!is_404()) { ?>
<div class="rbt-separator-mid">
    <div class="container">
        <hr class="rbt-separator m-0">
    </div>
</div>
<?php } ?>
<!-- Start Footer aera -->
  <footer class="rbt-footer footer-layout-4 bg-color-white overflow-hidden">
  <?php if (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3') || is_active_sidebar('footer-4')) { ?>
    <div class="footer-top"> 
        <div class="container">
            <div class="row g-5">
                <?php if (is_active_sidebar('footer-1')) { ?>
                    <div class="col-xxl-3 col-xl-3 col-lg-6 col-md-6 col-12">
                        <div class="footer-widget">
                            <?php dynamic_sidebar('footer-1'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-2')) { ?>
                    <div class="col-xxl-3 col-xl-3 col-lg-6 col-md-6 col-12">
                        <div class="footer-widget">
                            <?php dynamic_sidebar('footer-2'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-1')) { ?>
                    <div class="col-xxl-3 col-xl-3 col-lg-6 col-md-6 col-12">
                        <div class="footer-widget">
                            <?php dynamic_sidebar('footer-3'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-4')) { ?>
                    <div class="col-xxl-3 col-xl-3 col-lg-6 col-md-6 col-12">
                        <div class="footer-widget">
                            <?php dynamic_sidebar('footer-4'); ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
    <div class="rbt-separator-mid">
        <div class="container">
            <hr class="rbt-separator m-0">
        </div>
    </div>
    <?php } ?> 

    

    <!-- Start Copyright Area  -->
    <div class="copyright-area copyright-style-1 ptb--20">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-xxl-12 col-xl-12 col-lg-12 col-md-12 col-12">
                        <?php if (!empty($rainbow_options['rainbow_copyright_contact']) || !empty($rainbow_options['rainbow_footer_footerbottom'])) { ?>
                            <p class="rbt-link-hover text-center text-lg-center">
                                <?php echo do_shortcode($rainbow_options['rainbow_copyright_contact'], $allowed_tags); ?>
                            </p>
                        <?php } ?>  
                </div>
            </div>
        </div>
    </div>
    <!-- End Copyright Area  -->


</footer>
<!-- End Footer aera -->