<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_footer_bottom_menu_args = Rainbow_Helper::rainbow_footer_bottom_menu_args();
$lineclass = (is_active_sidebar('footer-2-1') || is_active_sidebar('footer-2-2') || is_active_sidebar('footer-2-3') || is_active_sidebar('footer-2-4')) ? "rn-section-gapTop section-separator" : "";
$allowed_tags = wp_kses_allowed_html('post');
$footerlogo = empty($rainbow_options['rainbow_footer_logo']['url']) ? Rainbow_Helper::get_img('logo.png') : $rainbow_options['rainbow_footer_logo']['url'];
$rainbow_socials = Rainbow_Helper::rainbow_socials();
$current_template = get_current_template_name();

?>


<footer class="rbt-footer footer-style-1 has-rainbow-footer-style-1 bg-color-white overflow-hidden custom-footer-two <?php echo esc_attr($current_template) === 'elementor_header_footer' ? 'has-elementor-full-width': 'not-elementor-page' ?>">

<?php if (is_active_sidebar('footer-2-1') || is_active_sidebar('footer-2-2') || is_active_sidebar('footer-2-3') || is_active_sidebar('footer-2-4')) { ?>  
        <div class="footer-top">
            <div class="container">
                <div class="row g-5">
                <?php if (is_active_sidebar('footer-2-1')) { ?>
                    <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                          <div class="footer-widget">
                            <?php dynamic_sidebar('footer-2-1'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-2-2')) { ?>
                   <div class="col-lg-2 col-md-6 col-sm-6 col-12">
                          <div class="footer-widget">
                            <?php dynamic_sidebar('footer-2-2'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-2-1')) { ?>
                   <div class="col-lg-2 col-md-6 col-sm-6 col-12">
                          <div class="footer-widget">
                            <?php dynamic_sidebar('footer-2-3'); ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if (is_active_sidebar('footer-2-4')) { ?>
                    <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                          <div class="footer-widget">
                            <?php dynamic_sidebar('footer-2-4'); ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
    <?php } ?>
</footer>
<?php get_template_part('template-parts/footer/footer-bottom');