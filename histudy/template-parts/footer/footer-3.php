<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_footer_bottom_menu_args = Rainbow_Helper::rainbow_footer_bottom_menu_args();
$lineclass = (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3') || is_active_sidebar('footer-4')) ? "rn-section-gapTop section-separator" : "";
$allowed_tags = wp_kses_allowed_html('post');
$footerlogo = empty($rainbow_options['rainbow_footer_logo']['url']) ? Rainbow_Helper::get_img('logo.png') : $rainbow_options['rainbow_footer_logo']['url'];
$rainbow_socials = Rainbow_Helper::rainbow_socials();
?>

<div class="footer-style-2 ptb--60 bg-color-white">
    <div class="container">
        <?php if (is_active_sidebar('footer-4-1')) { ?>
        <div class="row align-items-center justify-content-between">
            <div class="col-lg-12">
                <div class="inner text-center">
                    <?php dynamic_sidebar('footer-4-1'); ?>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>
</div>