<?php
    $rainbow_caourse_cart_settings           = Rainbow_Helper::rainbow_caourse_cart_settings();
    $rainbow_course_card_pricing_switch = $rainbow_caourse_cart_settings['rainbow_course_card_pricing_switch'];
    $post_type = isset($args['post_type']) ? $args['post_type']: 'courses';
    $rainbow_course_grid_archive_img_size = $rainbow_caourse_cart_settings['rainbow_course_grid_archive_img_size'];
    $rainbow_course_content_limit = $rainbow_caourse_cart_settings['rainbow_course_content_limit'];
    $bundle_total = 0;
    if(isset($args['course_id'])) {
        $course_id          = $args['course_id'];
    } else {
        $course_id          = get_the_ID();
    }
    if ( class_exists( '\TutorPro\CourseBundle\Models\BundleModel' ) ) {
        $bundle_course_ids = \TutorPro\CourseBundle\Models\BundleModel::get_bundle_course_ids( $course_id );
        if( count( $bundle_course_ids ) > 0 ) {
            $bundle_total = count( $bundle_course_ids ); 
        }
    }

    $userdata = get_query_var('set_filter_userdata');

    ?>
    <?php 
    if( 'post' == $post_type ) {
        $post_id = get_the_ID();
        ?>
        <!-- Start Single Card  -->
        <div class="rbt-card variation-01 rbt-hover">
            <div class="rbt-card-img">
                <a href="<?php echo get_the_permalink($post_id); ?>">
                    <?php echo get_the_post_thumbnail($post_id,$rainbow_course_grid_archive_img_size); ?>
                </a>
            </div>
            <div class="rbt-card-body">
                <h5 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h5>
                <p><?php echo wp_trim_words( get_the_excerpt(), $rainbow_course_content_limit ); ?></p>
            </div>
        </div>
        <!-- End Single Card  -->
    <?php }
?>
<?php if( function_exists( 'tutor' ) ) : ?>
    <?php if( 'courses' === $post_type ) : ?>
        <?php
            $course_id = get_the_ID();
            $course_rating = tutor_utils()->get_course_rating( $course_id );
        ?>
        <!-- Start Single Card  -->
        <div class="rbt-card variation-01 rbt-hover">
            <div class="rbt-card-img">
                <a href="<?php echo get_the_permalink($course_id); ?>">
                    <?php echo get_the_post_thumbnail($course_id,$rainbow_course_grid_archive_img_size); ?>
                    <?php if( $bundle_total > 0 ) { ?>
                        <div class="tutor-bundle-course-count-badge">
                            <span class="tutor-icon-layer"></span>
                            <span class="tutor-bundle-course-count-number"><?php echo esc_html($bundle_total); ?></span>
                            <span class="tutor-bundle-course-count-text"> - <?php echo esc_html__("course bundle","histudy");?></span>
                        </div>
                    <?php } ?>
                </a>
            </div>
            <div class="rbt-card-body">
                <h5 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h5>
                <div class="rbt-review">
                    <div class="rating">
                        <?php tutor_utils()->star_rating_generator_v2( $course_rating->rating_avg ); ?>
                    </div>
                    <span class="rating-count"> (<?php echo esc_html($course_rating->rating_count); ?> <?php echo esc_html__('Reviews', 'histudy'); ?>)</span>
                </div>
                <div class="rbt-card-bottom">
                    <?php if(1 == $rainbow_course_card_pricing_switch) : ?>
                        <?php get_template_part('template-parts/components/price/layout', 1); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <!-- End Single Card  -->
    <?php endif; ?>
<?php endif; ?>
<?php if( class_exists( 'LearnPress' ) ) :

    $course_id = get_the_ID();
    $is_editor 		= class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
    $user_id = get_current_user_id();
    $wishlist = (array) get_user_meta( $user_id, '_lpr_wish_list', true );
    $is_already_wishlist = in_array( $course_id, $wishlist ) ? true : false;
    $rated = $course_rate_res['rated'] ?? 0;
    $total = $course_rate_res['total'] ?? 0;


    if( $rated == 1 ) {
        $review = __('Review','histudy');
    } else {
        $review = __('Reviews','histudy');
    }
?>

    <!-- Start Single Card  -->
    <div class="rbt-card variation-01 rbt-hover">
            <div class="rbt-card-img">
                <a href="<?php echo get_the_permalink($course_id); ?>">
                    <?php echo get_the_post_thumbnail($course_id,$rainbow_course_grid_archive_img_size); ?>
                </a>
            </div>
            <div class="rbt-card-body">
                <h5 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h5>
                <div class="rbt-review">
                    <div class="rating">
                    <?php
                        if(class_exists('LP_Addon_Course_Review_Preload') ) {
                            LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                        }
                    ?>
                    </div>
                    <span class="rating-count"> (<?php echo esc_attr($rated); ?> <?php echo esc_html($review ); ?>)</span>
                </div>
                <div class="rbt-card-bottom">
                    <?php get_template_part('template-parts/components/price/layout', 1); ?>
                </div>
            </div>
        </div>
    <!-- End Single Card  -->
<?php endif; ?>