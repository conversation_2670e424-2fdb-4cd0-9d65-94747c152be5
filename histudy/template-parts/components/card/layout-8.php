<?php
$rainbow_caourse_cart_settings           = Rainbow_Helper::rainbow_caourse_cart_settings();
$rainbow_tutor_card_layout               = $rainbow_caourse_cart_settings['rainbow_tutor_card_layout'];
$rainbow_course_card_title_switch       = $rainbow_caourse_cart_settings['rainbow_course_card_title_switch'];
$rainbow_course_card_author_switch      = $rainbow_caourse_cart_settings['rainbow_course_card_author_switch'];
$rainbow_course_card_image_switch       = $rainbow_caourse_cart_settings['rainbow_course_card_image_switch'];
$rainbow_course_card_rating_switch      = $rainbow_caourse_cart_settings['rainbow_course_card_rating_switch'];
$rainbow_course_card_meta_switch = $rainbow_caourse_cart_settings['rainbow_course_card_meta_switch'];
$rainbow_course_card_add_to_cart_switch = $rainbow_caourse_cart_settings['rainbow_course_card_add_to_cart_switch'];
$rainbow_course_card_pricing_switch = $rainbow_caourse_cart_settings['rainbow_course_card_pricing_switch'];
$rainbow_course_grid_archive_img_size = $rainbow_caourse_cart_settings['rainbow_course_grid_archive_img_size'];
$rainbow_course_content_limit = $rainbow_caourse_cart_settings['rainbow_course_content_limit'];
if (isset($args['course_id'])) {
    $course_id          = $args['course_id'];
} else {
    $course_id          = get_the_ID();
}
$course_attributes      = get_post_meta($course_id);
if (tutor_utils()->is_course_purchasable($course_id)) {
    $course_product_id      = isset( $course_attributes['_tutor_course_product_id'][0] ) ? $course_attributes['_tutor_course_product_id'][0] : null;
} else {
    $course_product_id = null;
}
$product_prices = Rainbow_Helper::rb_get_product_prices($course_product_id);
$current_product_price = isset( $product_prices['current_price'] ) ? $product_prices['current_price'] : 0;
$regular_price  = isset( $product_prices['regular_price'] ) ? $product_prices['regular_price'] : 0;
$product_percentage     = 0;

$tutor_options = get_option('tutor_option');

$monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
$product_percentage     = 0;
if (function_exists('tutor') &&  ! function_exists( 'tutor_pro' )  ) {
    if ($monetize_by == 'wc' || $monetize_by == 'tutor') {
        $course_product_id = isset($course_attributes['_tutor_course_product_id'][0]) ? $course_attributes['_tutor_course_product_id'][0] : ' ';
        $current_product_price = isset($product_prices['current_price']) ? $product_prices['current_price'] : '';
        $regular_price = isset($product_prices['regular_price']) ? $product_prices['regular_price'] : '';
    }
} else {
    $course_product_id = $course_id;
    $regular_price = get_post_meta( $course_id, 'tutor_course_price', true );
    $current_product_price = get_post_meta( $course_id, 'tutor_course_sale_price', true );
}

if($course_product_id) {
    $product_percentage = Rainbow_Helper::rb_get_product_offer_percentage($course_product_id);
}

$course_lessons         = tutor_utils()->get_lesson_count_by_course($course_id);
$student                = tutor_utils()->count_enrolled_users_by_course($course_id);
$student_count          = sprintf(_n('%s Student', '%s Students', $student, 'histudy'), $student);
$author_id              = get_post_field('post_author', $course_id);
$author_name            = get_the_author_meta('display_name', $author_id);
$author_image           = get_avatar_url($author_id);
$author_id              = get_post_field('post_author', $course_id);
$author_image_alt       = get_the_author_meta('description', $author_id);
$categories             = get_the_terms($course_id, 'course-category');
$first_category         = '';
$category_name          = '';
$category_id            = '';
$category_link          = '';
$is_editor         = class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
if ($categories && !is_wp_error($categories)) {
    $first_category     = array_shift($categories);
    $category_name      = $first_category->name;
    $category_id        = $first_category->term_id;
    $category_link      = get_term_link($category_id, 'course-category');
}
$is_wishlisted     = tutor_utils()->is_wishlisted($course_id, get_current_user_id());
global $authordata;
$profile_url       = tutor_utils()->profile_url($authordata->ID, true);

$course_cat = get_the_terms( $course_id, "course-category" );

$bundle_total = 0;
if ( class_exists( '\TutorPro\CourseBundle\Models\BundleModel' ) ) {
    $bundle_course_ids = \TutorPro\CourseBundle\Models\BundleModel::get_bundle_course_ids( $course_id );
    if( count( $bundle_course_ids ) > 0 ) {
        $bundle_total = count( $bundle_course_ids ); 
    }
}

if( function_exists( 'tutor' ) ) {
    $total_lessons = tutor_utils()->get_lesson_count_by_course( $course_id );  
    $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );
}

?>
<!-- Start Single Card  -->
<div class="col-lg-6 col-md-6 col-sm-6 col-12 mt--30 sal-animate" data-sal-delay="150" data-sal="slide-up" data-sal-duration="800">
    <div class="rbt-card variation-01 rbt-hover card-list-2">
        <?php if (1 == $rainbow_course_card_image_switch) : ?>
            <div class="rbt-card-img">
                <a href="<?php echo get_the_permalink($course_id); ?>">
                    <?php the_post_thumbnail($rainbow_course_grid_archive_img_size); ?>
                    <?php if( $bundle_total > 0 ) { ?>
                        <div class="tutor-bundle-course-count-badge">
                            <span class="tutor-icon-layer"></span>
                            <span class="tutor-bundle-course-count-number"><?php echo esc_html($bundle_total); ?></span>
                            <span class="tutor-bundle-course-count-text"> - <?php echo esc_html__("course bundle","histudy");?></span>
                        </div>
                    <?php } ?>
                </a>
            </div>
        <?php endif; ?>
        <div class="rbt-card-body">
            <div class="rbt-category">
                <?php 
                foreach ( $course_cat as $term ) { // for each term 
                    $terms_url = get_term_link( (int) $term->term_id, 'course-category' )
                    ?>
                    <a href="<?php echo esc_url($terms_url);?>"><?php echo esc_html($term->name); ?></a>
                    <?php 
                }
                ?>
            </div>
            <h4 class="rbt-card-title"><a href="<?php echo get_the_permalink($course_id); ?>"><?php echo get_the_title($course_id); ?></a>
            </h4>
            <?php if (1 == $rainbow_course_card_meta_switch) : ?>
                <span class="lesson-number"><?php echo esc_html( $total_lessons ); ?> <span class="lesson-time"><?php echo esc_html($student_count); ?></span></span>
            <?php endif; ?>
            <p class="rbt-card-text"><?php echo wp_trim_words(get_the_excerpt($course_id), $rainbow_course_content_limit); ?></p>
          

            <?php if (1 == $rainbow_course_card_add_to_cart_switch) : ?>
                <div class="rbt-card-bottom">
                    <?php if ((!empty($current_product_price) || !empty($regular_price)) && tutor_utils()->is_course_purchasable($course_id)) : 
                        $isLoggedIn               = is_user_logged_in();
                        $enable_guest_course_cart = tutor_utils()->get_option( 'enable_guest_course_cart' );

                        if ( ! $isLoggedIn && ! $enable_guest_course_cart ) { ?>
                            <a href="#"  class="tutor-open-login-modal tutor-btn tutor-btn-outline-primary tutor-btn-md tutor-btn-block tutor-nowrap-ellipsis add_to_cart_button ajax_add_to_cart"  aria-label="Add to cart: “The Ultimate Course Bundle”" rel="nofollow"><span class="tutor-icon-cart-line tutor-mr-8"></span><span class="cart-text"><?php echo esc_html__("Add to cart","histudy"); ?></span></a>
                        <?php } else {
                            tutor_course_loop_add_to_cart($course_id);
                            }
                        else : ?>
                        <a class="transparent-button" href="<?php echo get_the_permalink($course_id); ?>"><?php echo esc_html__('Learn More', 'histudy'); ?><i><svg width="17" height="12" xmlns="http://www.w3.org/2000/svg">
                                <g stroke="#27374D" fill="none" fill-rule="evenodd">
                                    <path d="M10.614 0l5.629 5.629-5.63 5.629"></path>
                                    <path stroke-linecap="square" d="M.663 5.572h14.594"></path>
                                </g>
                            </svg></i></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>



        </div>
    </div>
</div>
<!-- End Single Card  -->