<?php
    $rainbow_caourse_cart_settings           = Rainbow_Helper::rainbow_caourse_cart_settings();
    $rainbow_tutor_card_layout               = $rainbow_caourse_cart_settings['rainbow_tutor_card_layout'];
    $rainbow_course_card_title_switch       = $rainbow_caourse_cart_settings['rainbow_course_card_title_switch'];
    $rainbow_course_card_author_switch      = $rainbow_caourse_cart_settings['rainbow_course_card_author_switch'];
    $rainbow_course_card_image_switch       = $rainbow_caourse_cart_settings['rainbow_course_card_image_switch'];
    $rainbow_course_card_rating_switch      = $rainbow_caourse_cart_settings['rainbow_course_card_rating_switch'];
    $rainbow_course_card_meta_switch = $rainbow_caourse_cart_settings['rainbow_course_card_meta_switch'] ;
    $rainbow_course_card_add_to_cart_switch = $rainbow_caourse_cart_settings['rainbow_course_card_add_to_cart_switch'];
    $rainbow_course_card_pricing_switch = $rainbow_caourse_cart_settings['rainbow_course_card_pricing_switch'];
    $rainbow_course_grid_archive_img_size = $rainbow_caourse_cart_settings['rainbow_course_grid_archive_img_size'];
    if(isset($args['course_id'])) {
        $course_id          = $args['course_id'];
    } else {
        $course_id          = get_the_ID();
    }
    $course_attributes      = get_post_meta($course_id);
    if(tutor_utils()->is_course_purchasable($course_id)) {
        $course_product_id      = isset( $course_attributes['_tutor_course_product_id'][0] ) ? $course_attributes['_tutor_course_product_id'][0] : null;
    } else {
        $course_product_id = null;
    }
    $product_prices = Rainbow_Helper::rb_get_product_prices($course_product_id);
    $product_percentage     = 0;

    $tutor_options = get_option('tutor_option');

    $monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
    $product_percentage     = 0;
    if (function_exists('tutor') &&  ! function_exists( 'tutor_pro' )  ) {
        if ($monetize_by == 'wc' || $monetize_by == 'tutor') {
            $course_product_id = isset($course_attributes['_tutor_course_product_id'][0]) ? $course_attributes['_tutor_course_product_id'][0] : ' ';
            $current_product_price = isset($product_prices['current_price']) ? $product_prices['current_price'] : '';
            $regular_price = isset($product_prices['regular_price']) ? $product_prices['regular_price'] : '';
        }
    } else {
        $course_product_id = $course_id;
        $regular_price = get_post_meta( $course_id, 'tutor_course_price', true );
        $current_product_price = get_post_meta( $course_id, 'tutor_course_sale_price', true );
    }
    
    if($course_product_id) {
        $product_percentage = Rainbow_Helper::rb_get_product_offer_percentage($course_product_id);
    }
    $course_lessons         = tutor_utils()->get_lesson_count_by_course( $course_id );
    $student                = tutor_utils()->count_enrolled_users_by_course( $course_id );
    $student_count          = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );
    $author_id              = get_post_field('post_author', $course_id);
    $author_name            = get_the_author_meta('display_name', $author_id);
    $author_image           = get_avatar_url($author_id);
    $author_id              = get_post_field('post_author', $course_id);
    $author_image_alt       = get_the_author_meta('description', $author_id);
    $categories             = get_the_terms($course_id, 'course-category');
    $first_category         = '';
    $category_name          = '';
    $category_id            = '';
    $category_link          = '';
    $is_editor 		= class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
    if ($categories && !is_wp_error($categories)) {
        $first_category     = array_shift($categories);
        $category_name      = $first_category->name;
        $category_id        = $first_category->term_id;
        $category_link      = get_term_link($category_id, 'course-category');
    }
    $is_wishlisted 	= tutor_utils()->is_wishlisted( $course_id, get_current_user_id() );
    global $authordata;
    $profile_url       = isset($authordata->ID) ? tutor_utils()->profile_url( $authordata->ID, true ) : '';
    $bundle_total = 0;
    if ( class_exists( '\TutorPro\CourseBundle\Models\BundleModel' ) ) {
        $bundle_course_ids = \TutorPro\CourseBundle\Models\BundleModel::get_bundle_course_ids( $course_id );
        if( count( $bundle_course_ids ) > 0 ) {
            $bundle_total = count( $bundle_course_ids ); 
        }
    }

    if( function_exists( 'tutor' ) ) {
        $total_lessons = tutor_utils()->get_lesson_count_by_course( $course_id );  
        $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );
    }
?>
<div class="rbt-card variation-01 rbt-hover">
    <?php if(1 == $rainbow_course_card_image_switch) : ?>
        <div class="rbt-card-img">
            <a href="<?php echo get_the_permalink($course_id); ?>">
                <?php the_post_thumbnail($rainbow_course_grid_archive_img_size); ?>
                <?php if(!empty($product_percentage)) : ?>
                <div class="rbt-badge-3 bg-white">
                    <span>-<?php echo esc_html($product_percentage); ?>%</span>
                    <span><?php echo esc_html__('Off', 'histudy'); ?></span>
                </div>
                <?php endif; ?>
                <?php if( $bundle_total > 0 ) { ?>
                        <div class="tutor-bundle-course-count-badge">
                            <span class="tutor-icon-layer"></span>
                            <span class="tutor-bundle-course-count-number"><?php echo esc_html($bundle_total); ?></span>
                            <span class="tutor-bundle-course-count-text"> - <?php echo esc_html__("course bundle","histudy");?></span>
                        </div>
                    <?php } ?>
            </a>
        </div>
    <?php endif; ?>
    <div class="rbt-card-body">
        <?php if(1 == $rainbow_course_card_meta_switch) : ?>
            <ul class="rbt-meta">
                <li><i class="feather-book"></i><?php echo esc_html( $total_lessons ); ?></li>
                <li><i class="feather-users"></i><?php echo esc_html($student_count); ?></li>
            </ul>
        <?php endif; ?>
        <?php if(!empty(get_the_title($course_id))) : ?>
            <h4 class="rbt-card-title"><a href="<?php echo get_the_permalink($course_id); ?>"><?php echo get_the_title($course_id); ?></a></h4>
        <?php endif; ?>
        <?php if('1' == $rainbow_course_card_author_switch) : ?>
            <div class="rbt-review">
                <div class="rating">
                    <?php $course_rating = tutor_utils()->get_course_rating( $course_id ); tutor_utils()->star_rating_generator_v2( $course_rating->rating_avg ); ?>
                </div>
                <span class="rating-count"> (<?php echo esc_html($course_rating->rating_count); ?> <?php echo esc_html__('Reviews', 'histudy'); ?>)</span>
            </div>
        <?php endif; ?>
        <?php if(1 == $rainbow_course_card_add_to_cart_switch) : ?>
            <div class="rbt-card-bottom">
                <?php if(1 == $rainbow_course_card_pricing_switch) : ?>
                    <?php get_template_part('template-parts/components/price/layout', 1); ?>
                <?php endif; ?>
                <?php if((!empty($current_product_price) || !empty($regular_price)) && tutor_utils()->is_course_purchasable($course_id)) : 
                    $isLoggedIn               = is_user_logged_in();
                    $enable_guest_course_cart = tutor_utils()->get_option( 'enable_guest_course_cart' );

                    if ( ! $isLoggedIn && ! $enable_guest_course_cart ) { ?>
	                    <a href="#"  class="tutor-open-login-modal tutor-btn tutor-btn-outline-primary tutor-btn-md tutor-btn-block tutor-nowrap-ellipsis add_to_cart_button ajax_add_to_cart"  aria-label="Add to cart: “The Ultimate Course Bundle”" rel="nofollow"><span class="tutor-icon-cart-line tutor-mr-8"></span><span class="cart-text"><?php echo esc_html__("Add to cart","histudy"); ?></span></a>
                    <?php } else {
                        tutor_course_loop_add_to_cart($course_id);
                        }
                    else: ?>
                        <a class="rbt-btn-link" href="<?php echo get_the_permalink($course_id); ?>"><?php echo esc_html__('Learn More', 'histudy'); ?><i class="feather-arrow-right"></i></a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>