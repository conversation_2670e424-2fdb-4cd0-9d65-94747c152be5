<?php

    $course_id = get_the_ID();
    $is_editor 		= class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
    $user_id = get_current_user_id();
    $wishlist = (array) get_user_meta( $user_id, '_lpr_wish_list', true );
    $is_already_wishlist = in_array( $course_id, $wishlist ) ? true : false;
    $rated = $course_rate_res['rated'] ?? 0;
    $total = $course_rate_res['total'] ?? 0;


    if( $rated == 1 ) {
        $review = __('Review','histudy');
    } else {
        $review = __('Reviews','histudy');
    }
?>
<div class="rbt-card-top">
    <div class="rbt-review">
        <div class="rating">
        <?php
        if(class_exists('LP_Addon_Course_Review_Preload') ) {
			LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
        }
        ?>
        </div>
        <span class="rating-count"> (<?php echo esc_attr($rated); ?> <?php echo esc_html($review ); ?>)</span>
    </div>
</div>