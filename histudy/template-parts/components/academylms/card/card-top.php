<?php

    $course_id = get_the_ID();
    $rating = \Academy\Helper::get_course_rating( $course_id );
    $reviews_status = Academy\Helper::get_settings( 'is_enabled_course_review', true );

    global $wpdb;
    $user_id                = get_current_user_id();
    $is_already_in_wishlist = $wpdb->get_row( $wpdb->prepare( "SELECT * from {$wpdb->usermeta} WHERE user_id = %d AND meta_key = 'academy_course_wishlist' AND meta_value = %d;", $user_id, $course_id ) );
    $is_show_wishlist = (bool) \Academy\Helper::get_settings( 'is_enabled_course_wishlist', true );
    $card_style = Academy\Helper::get_settings( 'course_card_style' );

    $wishlist_class = 'layout_two' === $card_style || 'layout_four' === $card_style ? 'academy-wishlist-dynamic' : '';

?>
<div class="rbt-card-top">
    <div class="rbt-review">
        <div class="rating">
        <?php 
            $rating_avg = $rating->rating_avg;
            $fullStars = floor($rating_avg);  
            $halfStar = ($rating_avg - $fullStars) >= 0.5 ? 1 : 0; 
            $emptyStars = 5 - ($fullStars + $halfStar); 
        ?>
            <?php for ($i = 0; $i < $fullStars; $i++): ?>
                <i class="fas fa-star"></i> 
            <?php endfor; ?>

            <?php if ($halfStar): ?>
                <i class="fas fa-star-half-alt"></i> 
            <?php endif; ?>

            <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                <i class="far fa-star"></i> 
            <?php endfor; ?>
        
            <?php
            if ( $reviews_status ) :
            // Display course rating
                echo wp_kses_post( \Academy\Helper::star_rating_generator( $rating->rating_avg ) );
            endif;
            ?>
        </div>
    </div>
    <?php 
	if ( $is_show_wishlist ) :
		?>
    
    <div class="rbt-bookmark-btn academy-course-header-meta">
        <?php 
        if ( $is_already_in_wishlist ) : 
            ?>
            <a  class="academy-course__wishlist academy-add-wishlist-btn rbt-round-btn" data-course-id="<?php echo esc_attr( get_the_ID() ); ?>"><i class="feather-bookmark farid"></i></a>
        <?php else : ?>
            <a  class="academy-course__wishlist academy-add-wishlist-btn rbt-round-btn" data-course-id="<?php echo esc_attr( get_the_ID() ); ?>"><i class="feather-bookmark"></i></a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>