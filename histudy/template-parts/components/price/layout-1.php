<?php
$course_id = get_query_var('course_id');
if($course_id) {
    $course_id  = $course_id;
}
elseif(isset($args['course_id'])) {
    $course_id          = $args['course_id'];
} else {
    $course_id          = get_the_ID();
}

if(empty($course_id)) {
    $course_id = isset($args[0]) ? $args[0] : null;
}

if( function_exists( 'tutor' ) ) {
    $course_attributes      = get_post_meta($course_id);
    if(tutor_utils()->is_course_purchasable($course_id)) {
        $course_product_id      = isset( $course_attributes['_tutor_course_product_id'][0] ) ? $course_attributes['_tutor_course_product_id'][0] : null;
    } else {
        $course_product_id = null;
    }
    $product = ( class_exists( 'WooCommerce' ) ) ? wc_get_product( $course_product_id ) : '';
    $product_prices = '';
    $regular_price  = '';
    $sale_price     = '';
    if(class_exists( 'WooCommerce' ) && isset( $product ) && false !== $product) {
        $product_price = $product->get_price();
        $sale_price    = $product->get_sale_price();
        $regular_price = !empty( $sale_price ) ? $product->get_regular_price(): '';
    }
    $currency_symbol = class_exists('WooCommerce') ? get_woocommerce_currency_symbol(): '$';

    $price 	= tutor_utils()->get_course_price( get_the_ID() );


} elseif( class_exists( 'LearnPress' ) ) {
    // course price
    $course = LP_Course::get_course($course_id);
    if ($course) {
        $currency_symbol     = learn_press_get_currency_symbol();
        if ($course->is_free()) {
            $course_price = __( 'Free', 'histudy' );
        } else {
            $course_price = !empty($course->get_sale_price()) ? $currency_symbol. $course->get_sale_price(): $currency_symbol. $course->get_price();
        }

        $regular_price = $course->get_regular_price();
    }
}
elseif (  function_exists( 'academy_start' ) ) {

    $is_paid     = \Academy\Helper::is_course_purchasable( $course_id );
    // Check if WooCommerce is active and the course is purchasable
    if ( \Academy\Helper::is_active_woocommerce() && $is_paid ) {
        $product_id = \Academy\Helper::get_course_product_id( $course_id );
        if ( $product_id ) {
            $product = wc_get_product( $product_id );
            if ( $product ) {
                $price = $product->get_price_html();
            }
        }
    }

    $course_type   = \Academy\Helper::get_course_type( $course_id );

	$course_price = '';

	if ( $is_paid && $price ) {
		$course_price = wp_kses_post( $price );
	} elseif ( $is_paid ) {
		$course_price = esc_html__( 'Paid', 'histudy' );
	} elseif ( 'public' === $course_type ) {
		$course_price = esc_html__( 'Public', 'histudy' );
	} else {
		$course_price = esc_html__( 'Free', 'histudy' );
	}

	$course_price = apply_filters( 'academy/templates/loop/price', $course_price, get_the_ID() );

}
	
?>



<?php if( function_exists( 'tutor' ) ) : ?>
    <?php if( tutor_utils()->is_course_purchasable($course_id) ) : ?>
        <div class="rbt-price">
            <?php if(!empty($price)) : ?>
                <span class="current-price"><?php echo wp_kses_post((string) $price); ?></span>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div class="rbt-price">
            <span class="current-price"><?php echo esc_html__('Free', 'histudy'); ?></span>
        </div>
    <?php endif; ?>

<?php elseif( class_exists( 'LearnPress' ) ) : ?>
    <?php if(!empty($course_price)) : ?>
        <div class="rbt-price">
            <span class="current-price"><?php echo esc_html($course_price); ?></span>
            <?php if(!empty($regular_price) && $regular_price > $course_price ) : ?>
                <span class="off-price"><?php echo wp_kses_post($currency_symbol); ?><?php echo wp_kses_post(number_format($regular_price)); ?></span>
            <?php endif; ?>
        </div>
    <?php endif; ?>

<?php elseif( function_exists( 'academy_start' ) ) : ?>
    <div class="rbt-price rbt-course-card-academy-price">
        <?php echo wp_kses_post($course_price); ?>
    </div>
<?php endif; ?>
