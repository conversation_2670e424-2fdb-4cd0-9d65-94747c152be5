<?php
global $product;
$current_price = $product->get_price();
$regular_price = $product->get_regular_price();?>


<?php if($product->is_on_sale()) : ?>
    <span class="current-price theme-gradient"><?php echo wp_kses_post(wc_price($current_price)); ?></span>
    <span class="off-price"><?php echo wp_kses_post(wc_price($regular_price)); ?></span>
    <?php else: ?>
        <span class="current-price theme-gradient"><?php echo wp_kses_post(wc_price($current_price)); ?></span>
<?php endif;