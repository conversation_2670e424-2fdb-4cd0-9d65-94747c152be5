<?php
$course_terms                           = class_exists('Elementor_Helper') ? Elementor_Helper::get_course_terms() : array();
$course_terms_tags                      = class_exists('Elementor_Helper') ? Elementor_Helper::get_course_terms(false, 'course-tag') : '';
$users_data                             = class_exists('Elementor_Helper') ? Elementor_Helper::get_instructor() : '';
$rainbow_layout_tutor_settings          = Rainbow_Helper::rainbow_layout_tutor_settings();
$rainbow_tutor_archive_title            = $rainbow_layout_tutor_settings['rainbow_tutor_archive_title'];
$rainbow_tutor_archive_subtitle         = $rainbow_layout_tutor_settings['rainbow_tutor_archive_subtitle'];
$rainbow_tutor_archive_enable_grid_list = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_grid_list'];
$rainbow_tutor_archive_enable_search    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_search'];
$rainbow_tutor_course_badge_show_hide   = $rainbow_layout_tutor_settings['rainbow_tutor_course_badge_show_hide'];
$rainbow_tutor_course_toggle_tab        = $rainbow_layout_tutor_settings['rainbow_tutor_course_toggle_tab'];
$rainbow_tutor_course_badge_label       = $rainbow_layout_tutor_settings['rainbow_tutor_course_badge_label'];
$rainbow_tutor_archive_enable_filter    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter'];
$rainbow_tutor_category_archive_enable_filter    = $rainbow_layout_tutor_settings['rainbow_tutor_category_archive_enable_filter'];
$rainbow_tutor_category_archive_category_enable_filter    = $rainbow_layout_tutor_settings['rainbow_tutor_category_archive_category_enable_filter'];

$rainbow_tutor_archive_enable_filter_enable_sort_by       = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_sort_by'];
$rainbow_tutor_archive_orderby_front                      = $rainbow_layout_tutor_settings['rainbow_tutor_archive_orderby_front'];

$rainbow_course_details_breadcrumb_overlap_switch         = $rainbow_layout_tutor_settings['rainbow_course_details_breadcrumb_overlap_switch'];
$rainbow_tutor_course_badge_label                         = $rainbow_layout_tutor_settings['rainbow_tutor_course_badge_label'];
$rainbow_tutor_archive_filter_show_default                = $rainbow_layout_tutor_settings['rainbow_tutor_archive_filter_show_default'];
if (true == get_query_var('course_banner_overlap')) {
    $rainbow_course_details_breadcrumb_overlap_switch = 1;
}
if (1 == $rainbow_course_details_breadcrumb_overlap_switch) {
    $banner_wrapper_class = '';
} else {
    $banner_wrapper_class = 'rbt-page-gradient-breadcrumb';
}
$col_class = 'col-lg-12';
if ('blog-list' == get_query_var('blog_layout')) {
    $col_class = 'col-lg-10 offset-lg-1 histudy-post-wrapper';
}
// enable filter control
if (!empty(get_query_var('enable_filter'))) {
    $rainbow_tutor_archive_enable_filter = get_query_var('enable_filter');
}
if (!empty(get_query_var('show_archive_filter'))) {
    $rainbow_tutor_archive_filter_show_default = get_query_var('show_archive_filter');
}
$show_archive_tab = '';
if (!empty(get_query_var('show_archive_tab'))) {
    $rainbow_tutor_course_toggle_tab = get_query_var('show_archive_tab');
    $show_archive_tab = 'show-course-archive-tab';
}
if (!is_post_type_archive('courses')) {
    $rainbow_tutor_archive_title = wp_get_document_title();
}

/**
 * Filter layout
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$filter_layout =  $rainbow_options['rainbow_course_details_breadcrumb_filter_layout'];
$course_filter_layout = get_query_var('course_filter_layout');
if (!empty($course_filter_layout)) {
    $filter_layout = $course_filter_layout;
}

?>
<div class="rbt-page-banner-wrapper rbt-breadcrumb-default bg-gradient-1 rainbow-tutor-lms-breadcrumb-center-content <?php echo esc_attr($banner_wrapper_class); ?> <?php echo esc_attr($show_archive_tab); ?>">
    <!-- Start Banner BG Image  -->
    <div class="rbt-banner-image"></div>
    <!-- End Banner BG Image  -->
    <div class="rbt-banner-content">
        <!-- Start Banner Content Top  -->
        <div class="rbt-banner-content-top">
            <div class="container">
                <div class="row">
                    <div class="<?php echo esc_attr($col_class); ?>">
                        <!-- Start Breadcrumb Area  -->
                        <ul class="page-list">
                            <li class="rbt-breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>"><?php echo esc_html__('Home', 'histudy'); ?></a></li>
                            <li>
                                <div class="icon-right"><i class="feather-chevron-right"></i></div>
                            </li>
                            <li class="rbt-breadcrumb-item active"><?php echo esc_html($rainbow_tutor_archive_title); ?></li>
                        </ul>
                        <!-- End Breadcrumb Area  -->
                        <div class=" title-wrapper">
                            <h1 class="title mb--0"><?php echo esc_html($rainbow_tutor_archive_title); ?></h1>
                            <?php if (('yes' == $rainbow_tutor_course_badge_show_hide) && !empty($rainbow_tutor_course_badge_label)) : ?>
                                <a href="<?php echo esc_url(get_post_type_archive_link('courses')); ?>" class="rbt-badge-2 rtb-course-archive-count">
                                    <div class="image">🎉</div>
                                    <div class="ajax-spinner"></div><span class="rbt-course-archive-count"></span> <?php echo esc_html($rainbow_tutor_course_badge_label); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($rainbow_tutor_archive_subtitle)) : ?>
                            <p class="description"><?php echo wp_kses_post($rainbow_tutor_archive_subtitle); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Banner Content Top  -->
        <!-- Start Course Top  -->
        <div class="rbt-course-top-wrapper mt--40 mt_sm--20">
            <div class="container">
                <div class="row g-5 align-items-center">
                    <div class="col-lg-5 col-md-6">
                        <div class="rbt-sorting-list d-flex flex-wrap align-items-center rbt-course-grid-list-custom">
                            <?php if ('yes' == $rainbow_tutor_archive_enable_grid_list) : ?>
                                <div class="rbt-short-item switch-layout-container">
                                    <ul class="course-switch-layout">
                                        <li class="course-switch-item"><button class="rbt-grid-view active" title="<?php echo esc_attr__('Grid Layout', 'histudy'); ?>"><i class="feather-grid"></i> <span class="text"><?php echo esc_html__('Grid', 'histudy'); ?></span></button></li>
                                        <li class="course-switch-item"><button class="rbt-list-view" title="<?php echo esc_attr__('List Layout', 'histudy'); ?>"><i class="feather-list"></i> <span class="text"><?php echo esc_html__('List', 'histudy'); ?></span></button></li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            <?php if( function_exists('academy_start')  ) { 
                               global $wp_query;
                                ?>
                            <div class="rbt-short-item">
                                <span class="course-index"><?php echo esc_html__('Showing all', 'histudy'); ?> <span class="rbt-course-archive-count-to">  <span class="rbt-course-archive-count"><?php echo esc_html( $wp_query->found_posts ); ?></span> <?php echo esc_html__('results', 'histudy'); ?></span>
                            </div>
                            <?php } else { ?>
                                <div class="rbt-short-item">
                                <span class="course-index"><?php echo esc_html__('Showing', 'histudy'); ?> <span class="rbt-course-archive-count-from"></span>-<span class="rbt-course-archive-count-to"></span> <?php echo esc_html__('of', 'histudy'); ?> <span class="rbt-course-archive-count"></span> <?php echo esc_html__('results', 'histudy'); ?></span>
                            </div>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="col-lg-7 col-md-6">
                        <div class="rbt-sorting-list d-flex flex-wrap align-items-center justify-content-start justify-content-lg-end rbt-mobile-course-archive-filter">
                            <?php if ('yes' == $rainbow_tutor_archive_enable_search && !function_exists('academy_start') ) : ?>
                                <div class="rbt-short-item rbt-short-item-searchbar">
                                    <form action="#" class="rbt-search-style me-0">
                                        <input type="text" class="form-control course_search_input" placeholder="<?php echo esc_attr__('Search Your Course..', 'histudy'); ?>" aria-label="<?php echo esc_attr__('Search Your Course..', 'histudy'); ?>" aria-describedby="<?php echo esc_attr__('basic-addon2', 'histudy'); ?>">
                                        <button type="submit" class="course_search_button rbt-search-btn rbt-round-btn">
                                            <i class="feather-search"></i>
                                        </button>
                                    </form>
                                </div>
                            <?php endif;
                            if( function_exists('academy_start') && ( $filter_layout == 'layout-2' || $filter_layout == 'layout-3' ) ) { 
                                histudy_archive_course_header_filter();
                            }
                             ?>
                             
                            <?php if ((('yes' == $rainbow_tutor_archive_enable_filter_enable_sort_by) && ($rainbow_tutor_archive_orderby_front === 'yes')) || ('yes' == get_query_var('show_orderby_front'))) : ?>
                                <div class="filter-select-option rbt-show-orderby-front rbt-mobile-view-filter-custom">
                                    <div class="filter-select rbt-modern-select">
                                        <select class="form-select" aria-label="<?php echo esc_attr__('Default select example', 'histudy'); ?>" data-var="sortby">
                                            <option selected><?php _e('Default', 'histudy'); ?></option>
                                            <option value="DESC" name="Latest"><?php _e('Latest', 'histudy'); ?></option>
                                            <option value="ASC" name="Oldest"><?php _e('Oldest', 'histudy'); ?></option>
                                        </select>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if (( 'layout-1' == $filter_layout || 'layout-2' == $filter_layout) && !is_tax( 'course-category' ) || ( 'layout-1' == $filter_layout || 'layout-2' == $filter_layout) && is_tax( 'course-category' ) && 'yes' == $rainbow_tutor_category_archive_enable_filter ) : ?>
                                <?php if ( ('yes' == $rainbow_tutor_archive_enable_filter) && ('yes' !== $rainbow_tutor_archive_filter_show_default) || ('yes' == $rainbow_tutor_category_archive_enable_filter) && ( 'yes' !== $rainbow_tutor_archive_filter_show_default )) : ?>
                                    <div class="rbt-short-item rbt-short-item-filter">
                                        <div class="view-more-btn text-start text-sm-end">
                                            <button class="discover-filter-button discover-filter-activation rbt-btn btn-white btn-md radius-round"><span><?php echo esc_html__('FILTERS', 'histudy'); ?></span><i class="feather-filter"></i></button>
                                        </div>
                                    </div>
                                    <?php else : ?>
                                    <div class="rbt-short-item rbt-short-item-filter filter-show-only-mobile-view" style="display: none;">
                                        <div class="view-more-btn text-start text-sm-end">
                                            <button class="discover-filter-button discover-filter-activation rbt-btn btn-white btn-md radius-round"><span><?php echo esc_html__('FILTERS', 'histudy'); ?></span><i class="feather-filter"></i></button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!is_category() && !is_tag() && !is_tax()) :  ?>
                        <?php
                        if ('yes' == $rainbow_tutor_course_toggle_tab) :
                            /**
                             * Fetch all courses
                             * 
                             * @since 1.0.0
                             **/

                            if( function_exists('tutor') ) {
                                $texonomy = 'course-category';
                                
                            } elseif( function_exists( 'academy_start' ) ) {
                                $texonomy = 'academy_courses_category';
                            }

                            $args = array(
                                'taxonomy' => $texonomy,
                                'orderby' => 'count',
                                'order'   => 'DESC',
                            );

                            $categories = get_categories($args); // taxonomy

                            $total_post = wp_count_posts( 'courses' );
                            if ( function_exists( 'academy_start' ) ) {
                                $total_post = wp_count_posts( 'academy_courses' );
                            }

                            $total_published_post = absint( $total_post->publish );
                        ?>
                            <div class="col-12 mt--60 rbt-mobile-view-filter-custom">
                                <ul class="rbt-portfolio-filter filter-tab-button justify-content-start nav nav-tabs" id="rbt-myTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="active" data-cat_slug="all" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true"><span class="filter-text"><?php echo esc_html__('All Course', 'histudy'); ?></span> <span class="course-number"><?php echo esc_html($total_published_post); ?></span></button>
                                    </li>
                                    <?php if (!empty($categories)) : ?>
                                        <?php
                                        ob_start();
                                        foreach ($categories as $index => $cat) :
                                            $cat_id = $cat->term_id;
                                            $cat_name = $cat->name;
                                            $cat_slug = $cat->slug;
                                        ?>
                                            <?php if ($index < 3) : ?>
                                                <li class="nav-item" role="presentation">
                                                    <button data-cat_slug="<?php echo esc_attr($cat_slug); ?>" data-bs-toggle="tab" type="button" role="tab" aria-controls="featured" aria-selected="false"><span
                                                            class="filter-text"><?php echo esc_html($cat_name); ?></span> <span class="course-number"></button>
                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach;
                                        echo ob_get_clean();
                                        ?>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <!-- Start Filter Toggle  -->

                <?php 
                if ( 
                    'yes' === $rainbow_tutor_archive_filter_show_default && 
                    (
                        ( 'yes' == $rainbow_tutor_archive_enable_filter && !is_tax( 'course-category' ) && !is_tax( 'academy_courses_category' ) ) || 
                        ( 'yes' == $rainbow_tutor_category_archive_enable_filter && ( is_tax( 'course-category' ) || is_tax( 'academy_courses_category' ) ) )
                    ) 
                )  : ?>

                    <div class="default-exp-wrapper histudy-filter-style-1 rbt-mobile-view-filter-custom">
                        <?php get_template_part('template-parts/components/tutor/filter/filter-layout'); ?>
                    </div>
                <?php endif; ?>
                <div class="default-exp-wrapper default-exp-expand histudy-filter-style-1 rbt-mobile-view-filter-custom">
                    <?php if (('yes' !== $rainbow_tutor_archive_filter_show_default) && ('yes' == $rainbow_tutor_archive_enable_filter) && !is_tax( 'course-category' ) && !is_tax( 'academy_courses_category' ) ) { ?>
                        <?php get_template_part('template-parts/components/tutor/filter/filter-layout'); 
                        
                    } elseif ( ('yes' !== $rainbow_tutor_archive_filter_show_default) && ('yes' == $rainbow_tutor_category_archive_enable_filter) && ( is_tax( 'course-category' ) || is_tax( 'academy_courses_category' ) ) ) {
                        get_template_part('template-parts/components/tutor/filter/filter-layout');
                        ?>
                    <?php } ?>
                    <div class="row histudy-reset-filter-hide-mobile">
                        <div class="selected_course_filters histudy-selected-course-filters-114">
                            <ul></ul>
                        </div>
                    </div>
                </div>
                <!-- End Filter Toggle  -->
            </div>
        </div>

    </div>
</div>