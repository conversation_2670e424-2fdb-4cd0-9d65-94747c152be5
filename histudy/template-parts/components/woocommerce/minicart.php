<?php
global $woocommerce;

$product_id = absint( 575 );

$carts = WC()->cart->get_cart();

$items_to_show = apply_filters('histudy_mini_cart_items_to_show', 10);
?>
<ul class="rbt-minicart-wrapper">
    <?php
    do_action('woocommerce_before_mini_cart_contents');
    $_i = 0;
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        $_i++;
        if ($_i > $items_to_show) break;
        $_product = apply_filters('woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key);
        $product_id = apply_filters('woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key);
        $course_args = get_posts(
            array(
                'meta_key' => '_tutor_course_product_id',
                'meta_value' => $product_id,
                'post_type' => 'courses'
            )
        );
        $course_id = isset( $course_args[0]->ID ) ? $course_args[0]->ID: 0;
        $thumbnail_url = !empty( $course_id ) && has_post_thumbnail( $course_id ) ? get_the_post_thumbnail_url( $course_id, 'thumbnail' ): get_the_post_thumbnail_url( $product_id );
        if ($_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters('woocommerce_widget_cart_item_visible', true, $cart_item, $cart_item_key)) {
            /**
             * This filter is documented in woocommerce/templates/cart/cart.php.
             *
             * @param string $product_name Name of the product in the cart.
             */
            $product_name = !empty( $course_id ) ? get_the_title( $course_id ): apply_filters('woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key);
            $product_price = apply_filters('woocommerce_cart_item_price', WC()->cart->get_product_price($_product), $cart_item, $cart_item_key);
            $product_permalink = !empty( $course_id ) ? get_the_permalink($course_id): get_the_permalink($product_id);
            ?>

            <li class="minicart-item woocommerce-mini-cart-item">
                <div class="thumbnail">
                    <a href="<?php echo esc_url($product_permalink); ?>" class="cart-item-image">
                        <img src="<?php echo esc_url( $thumbnail_url ); ?>" alt="<?php echo esc_attr__( 'Image', 'histudy' ); ?>">
                    </a>
                </div>
                <div class="product-content">
                    <h6 class="title"><a href="<?php echo esc_url($product_permalink); ?>"><?php echo esc_html($product_name); ?></a>
                    </h6>
                    <?php echo wc_get_formatted_cart_item_data($cart_item); ?>
                    <?php echo apply_filters('woocommerce_widget_cart_item_quantity', '<span class="quantity">' . sprintf('%s &times; %s', $cart_item['quantity'], $product_price) . '</span>', $cart_item, $cart_item_key); ?>
                </div>
                <?php
                    echo apply_filters( 'woocommerce_cart_item_remove_link', sprintf(
                        '<a href="%s" class="remove remove_from_cart_button" aria-label="%s" data-product_id="%s" data-cart_item_key="%s" data-product_sku="%s"><i class="feather-x"></i></a>',
                        esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
                        /* translators: %s is the product name */
                        esc_attr( sprintf( __( 'Remove %s from cart', 'histudy' ), wp_strip_all_tags( $product_name ) ) ),
                        esc_attr( $product_id ),
                        esc_attr( $cart_item_key ),
                        esc_attr( $_product->get_sku() )
                    ), $cart_item_key );
                ?>
            </li>
            <?php
        }
    }
    do_action('woocommerce_mini_cart_contents');
    ?>
</ul>