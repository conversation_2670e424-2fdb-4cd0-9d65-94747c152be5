<?php
    // required variables
    global $thumb_size, $content;
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $blog_minimal_img_on_off =  isset( $rainbow_options['blog_minimal_img_on_off'] ) ? $rainbow_options['blog_minimal_img_on_off'] : '';
    $image_size = isset( $args['image_size'] ) ? $args['image_size']: 'full';
?>
<div class="rbt-card variation-02 rbt-card-post-box rbt-hover card-minimal">
    <div class="rbt-card-body">
        <?php 
        $meta_gap = 'meta-gap';
        if( $blog_minimal_img_on_off == 1 ) {
            $meta_gap = 'no-meta-img';
        if (has_post_thumbnail() && Rainbow_Helper::rbt_hide_post()) {
            ?>
            <div class="rbt-card-img">
                <a href="<?php the_permalink(); ?>">
                    <?php Rainbow_Helper::rainbow_get_image( $image_size );?>
                </a>
            </div>
        <?php } } ?>
        <?php
            if( 'audio' == get_post_format() ) {
                $audio_url = rainbow_get_acf_data( "rainbow_upload_audio" );
                $audio_url = isset($audio_url['url']) ? $audio_url['url']: '';
                if( $audio_url ) {
                    $thumbnail = <<<EOD
                    <audio controls="">
                        <source src="$audio_url" control type="audio/ogg">
                        <source src="$audio_url" control type="audio/mpeg">
                    </audio>
                    EOD;
                }
                echo wp_kses_post($thumbnail);
            }
        ?>
         <?php Rainbow_Helper::rainbow_postmeta(); ?>
        <?php if( 'quote' == get_post_format() ) : ?>
            <blockquote class="m-0">
                <h3 class="rbt-card-title <?php echo esc_attr($meta_gap ); ?>"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
            </blockquote>
        <?php else: ?>
                <h3 class="rbt-card-title <?php echo esc_attr($meta_gap ); ?>"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
        <?php endif; ?>
        <?php if( Rainbow_Helper::rbt_hide_post() ) : ?>
            <p class="rbt-card-text"><?php echo esc_html( $content ); ?></p>
            <div class="rbt-card-bottom">
                <?php Rainbow_Helper::rainbow_read_more(); ?> 
            </div>
        <?php endif; ?>
    </div>
</div>