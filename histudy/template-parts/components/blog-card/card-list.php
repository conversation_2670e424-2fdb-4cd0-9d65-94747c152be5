<?php
    // required variables
    global $rainbow_options;
    $content = wp_trim_words( get_the_excerpt(),  $rainbow_options['rainbow_post_content_limit'], '.' );
    $rbt_post_class = 'quote' == get_post_format() ? 'has-quote': '';
    $rbt_post_class = 'gallery' == get_post_format() ? 'has-gallery': '';
    $blog_list_card_meta_toggle = rest_sanitize_boolean( $rainbow_options['blog_list_card_meta_toggle'] );
    $blog_list_card_image_size = sanitize_text_field( $rainbow_options['blog_list_card_image_size'] );
    $image_size = isset( $args['image_size'] ) ? $args['image_size']: 'full';
    
?>
<div class="<?php echo esc_attr($rbt_post_class); ?> rbt-card card-list rbt-card-post-box variation-02 rbt-has-blog-card-list rbt-hover mt--30 card-list-custom">
    <?php if (has_post_thumbnail() && Rainbow_Helper::rbt_hide_post()) {
        ?>
        <div class="rbt-card-img">
            <a href="<?php the_permalink(); ?>">
                <?php Rainbow_Helper::rainbow_get_image_for_list( $image_size );?>
            </a>
        </div>
    <?php } ?>
    <div class="rbt-card-body">
        <?php
            if( 'audio' == get_post_format() ) {
                $audio_url = rainbow_get_acf_data( "rainbow_upload_audio" );
                $audio_url = isset($audio_url['url']) ? $audio_url['url']: '';
                if( $audio_url ) {
                    $thumbnail = <<<EOD
                    <audio controls="">
                        <source src="$audio_url" control type="audio/ogg">
                        <source src="$audio_url" control type="audio/mpeg">
                    </audio>
                    EOD;
                }
                echo wp_kses_post($thumbnail);
            }
        ?>
        <?php if( 'quote' == get_post_format() ) : ?>
            <blockquote class="m-0">
                <h3 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
            </blockquote>
        <?php else: ?>
                <h3 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
        <?php endif; ?>
        <?php if( Rainbow_Helper::rbt_hide_post() ) : ?>
            <?php if( !empty( $blog_list_card_meta_toggle ) ) : ?>
                <?php Rainbow_Helper::rainbow_postmeta(); ?>
                <p class="rbt-card-text"><?php echo esc_html( $content ); ?></p>
            <?php endif; ?>
            <div class="rbt-card-bottom">
                <?php Rainbow_Helper::rainbow_read_more(); ?> 
            </div>
        <?php endif; ?>
    </div>
</div>