<?php
// required variables
global $rainbow_options;
$content = wp_trim_words( get_the_excerpt(),  $rainbow_options['rainbow_post_content_limit'], '.' );
?>
<div <?php post_class('rbt-card variation-02 height-auto rbt-hover card-boxed'); ?>>
    <?php if (has_post_thumbnail() && Rainbow_Helper::rbt_hide_post()) {
        ?>
        <div class="rbt-card-img">
            <a href="<?php the_permalink(); ?>">
                <?php Rainbow_Helper::rainbow_get_image_full();?>
            </a>
        </div>
    <?php } ?>
    <div class="rbt-card-body">
        <?php
            if( 'audio' == get_post_format() ) {
                $audio_url = rainbow_get_acf_data( "rainbow_upload_audio" );
                $audio_url = isset($audio_url['url']) ? $audio_url['url']: '';
                if( $audio_url ) {
                    $thumbnail = <<<EOD
                    <audio controls="">
                        <source src="$audio_url" control type="audio/ogg">
                        <source src="$audio_url" control type="audio/mpeg">
                    </audio>
                    EOD;
                }
                echo wp_kses_post( $thumbnail );
            }
        ?>
        <?php if( 'quote' == get_post_format() ) : ?>
            <blockquote class="m-0">
                <h3 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
            </blockquote>
        <?php else: ?>
                <h3 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
        <?php endif; ?>
        <?php if( Rainbow_Helper::rbt_hide_post() ) : ?>
            <?php Rainbow_Helper::rainbow_postmeta(); ?>
            <p class="rbt-card-text"><?php echo esc_html( $content ); ?></p>
            <div class="rbt-card-bottom">
                <?php Rainbow_Helper::rainbow_read_more(); ?> 
            </div>
        <?php endif; ?>
    </div>
</div>