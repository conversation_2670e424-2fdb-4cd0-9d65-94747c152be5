<?php
/**
 * This file will decide which card will be view based on selected layout and card
 * 
 * @since 1.0.0
 */
// required variables
global $rainbow_blog_layout, $rainbow_options;
$image_size = isset( $args['image_size'] ) ? $args['image_size']: 'full';
$blog_layout = get_query_var( 'blog_layout' );
if( $blog_layout ) {
    $rainbow_blog_layout = $blog_layout;
}

$card_style = 'card-boxed';
// decide, which card box will be view
if( 'blog-list' == $rainbow_blog_layout ) {
    $blog_list_card_layout = isset($rainbow_options['blog_list_card_layout']) ? $rainbow_options['blog_list_card_layout']: 'card-list';
    $card_layout = get_query_var( 'card_layout' );
    if( $blog_layout ) {
        $blog_list_card_layout = $card_layout;
    }
    switch( $blog_list_card_layout ) {
        case 'card-boxed':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed',array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-list':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'list', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-minimal':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'minimal', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        default:
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
    }
} elseif( 'blog-grid' == $rainbow_blog_layout ) {
    $blog_grid_card_layout = isset($rainbow_options['blog_grid_card_layout']) ? $rainbow_options['blog_grid_card_layout']: 'card-boxed';
    $card_layout = get_query_var( 'card_layout' );
    if( $blog_layout ) {
        $blog_grid_card_layout = $card_layout;
    }
    switch( $blog_grid_card_layout ) {
        case 'card-boxed':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-list':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'list', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-minimal':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'minimal', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        default:
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
    }
} elseif( 'blog-grid-minimal' == $rainbow_blog_layout ) {
    $blog_grid_minimal_card_layout = isset($rainbow_options['blog_grid_minimal_card_layout']) ? $rainbow_options['blog_grid_minimal_card_layout']: 'card-boxed';
    $card_layout = get_query_var( 'card_layout' );
    if( $blog_layout ) {
        $blog_grid_minimal_card_layout = $card_layout;
    }
    switch( $blog_grid_minimal_card_layout ) {
        case 'card-boxed':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-list':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'list', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        case 'card-minimal':
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'minimal', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
        default:
            ob_start();
                get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
            echo ob_get_clean();
            break;
    }
} else {
    ob_start();
        get_template_part( 'template-parts/components/blog-card/card', 'boxed', array( 'image_size' => $image_size ) );
    echo ob_get_clean();
}