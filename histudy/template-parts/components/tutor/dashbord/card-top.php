<?php
    $course_id = get_the_ID();
    $is_editor 		= class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
    $is_wish_listed = tutor_utils()->is_wishlisted( $course_id );
    $is_wishlisted 	= tutor_utils()->is_wishlisted( $course_id, get_current_user_id() );
    $login_url_attr = '';
    $action_class   = '';
    $course_rating = tutor_utils()->get_course_rating( $course_id );
    if ( is_user_logged_in() ) {
        $action_class = apply_filters( 'tutor_wishlist_btn_class', 'tutor-course-wishlist-btn' );
    } else {
        $action_class = apply_filters( 'tutor_popup_login_class', 'tutor-open-login-modal' );

        if ( ! tutor_utils()->get_option( 'enable_tutor_native_login', null, true, true ) ) {
            $login_url_attr = 'data-login_url="' . esc_url( wp_login_url() ) . '"';
        }
    }

    if( $course_rating->rating_count == 1) {
        $review_text = __('Review','histudy');
    } else {
        $review_text = __('Reviews','histudy');
    }
?>
<div class="rbt-card-top">
    <div class="rbt-review">
        <div class="rating">
            <?php
                $course_rating = tutor_utils()->get_course_rating( $course_id );
                tutor_utils()->star_rating_generator_v2( $course_rating->rating_avg ); ?>
        </div>
        <span class="rating-count"> (<?php echo esc_attr($course_rating->rating_count); ?> <?php echo esc_html($review_text); ?>)</span>
    </div>

    <div class="rbt-bookmark-btn">
        <a href="javascript:;" class="<?php echo esc_attr( ! $is_editor ? 'tutor-course-wishlist-btn ' : '' ); ?>tutor-btn tutor-btn-ghost tutor-course-wishlist-btn tutor-mr-16" data-course-id="<?php echo esc_attr($course_id); ?>">
            <i class="tutor-icon-bookmark-<?php echo esc_attr( $is_wishlisted ? esc_attr__( 'bold', 'histudy' ) : esc_attr__( 'line', 'histudy' ) ); ?> tutor-mr-8" ></i></a>
    </div>
</div>