<?php
    $course_id = get_the_ID();
    $is_editor 		= class_exists('Elementor') ? \Elementor\Plugin::instance()->editor->is_edit_mode(): false;
    $is_wishlisted 	= tutor_utils()->is_wishlisted( $course_id, get_current_user_id() );
   
    $student                = tutor_utils()->count_enrolled_users_by_course( $course_id );
    $student_count          = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );
    if( function_exists( 'tutor' ) ) {
        $total_lessons = tutor_utils()->get_lesson_count_by_course( $course_id );  
        $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );
    }
?>

<div class="rbt-card variation-01 rbt-hover">
    <?php tutor_load_template( 'loop.thumbnail' ); ?>
    <div class="rbt-card-body">
        <?php get_template_part('template-parts/components/tutor/dashbord/card-top'); ?>
        
        <h4 class="rbt-card-title">
            <a href="<?php echo esc_url( get_the_permalink() ); ?>">
                <?php the_title(); ?>
            </a>
        </h4>
        <ul class="rbt-meta mt-3">
            <li><i class="feather-book"></i><?php echo esc_html( $total_lessons ); ?></li>
            <li><i class="feather-users"></i><?php echo esc_html($student_count); ?></li>
        </ul>
        <div class="tutor-mt-auto">
            <?php tutor_load_template( 'loop.enrolled-course-progress' ); ?>
        </div>

        <div class="tutor-mt-24">
            <?php tutor_course_loop_price(); ?>
        </div>
    </div>
</div>