<?php
   global $rainbow_options;
    $course_id = get_the_ID();
    $course_single_url = get_the_permalink($course_id);
	$page_title = get_the_title();
	$facebookShareUrl = "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($course_single_url);
	$twitterShareUrl = "https://twitter.com/intent/tweet?url=" . urlencode($course_single_url) . "&text=" . urlencode($page_title);
	$linkedInShareUrl = "https://www.linkedin.com/sharing/share-offsite/?url=" . urlencode($course_single_url);
    $lesson_count     = tutor_utils()->get_lesson_count_by_course();
    $passing_grade     = tutor_utils()->get_quiz_option( $course_id, 'passing_grade', 0 );
    $course_video = '';
    $current_user_id = get_current_user_id();
    $is_enrolled = function_exists( 'tutor_utils' ) ? tutor_utils()->is_enrolled($course_id, $current_user_id): false;
    $course_attributes      = get_post_meta($course_id);
    $product_type = '';
    if(tutor_utils()->is_course_purchasable($course_id)) {
		$course_product_id      = isset( $course_attributes['_tutor_course_product_id'][0] ) ? $course_attributes['_tutor_course_product_id'][0] : null;

        if ( class_exists( 'WooCommerce' ) ) {
            $product = wc_get_product($course_product_id);
            if ($product) {
                // Get the product type
                $product_type = 'product-'.$product->get_type();
            }
        }
	} else {
		$course_product_id = null;
	}
    $students          = tutor_utils()->get_total_students_by_instructor( get_current_user_id() );
    $last_updated = get_tutor_option( 'enable_course_update_date' ) ? get_the_modified_date( get_option( 'date_format' ) ): null;
    $total_student      = sprintf( _n( '%s', '%s', $students, 'histudy' ), $students );
	$total_student      = tutor_utils()->get_option( 'enable_course_total_enrolled' ) ? tutor_utils()->count_enrolled_users_by_course() : 0;
	if($total_student > -1) {
		$is_best_sellar    = true;
	}
    $rainbow_course_details_settings                        = Rainbow_Helper::rainbow_course_details_settings();
	$course_duration   = get_tutor_course_duration_context( $course_id, true );
	$total_student_html = sprintf( _n( '%s Student', '%s Students', $students, 'histudy' ), $students );
	$author_id              = get_post_field('post_author', $course_id);
	$author_image           = get_avatar_url($author_id);
	$author_url             = get_author_posts_url($author_id);
	$author_image_alt       = get_the_author_meta('description', $author_id);
	$author_name            = get_the_author_meta('display_name', $author_id);
	$last_updated = get_tutor_option( 'enable_course_update_date' ) ? get_the_modified_date( get_option( 'date_format' ) ) : null;
	global $post;
	$string             = apply_filters( 'tutor_course_about_content', get_the_content() );
	$course_level = get_post_meta( $course_id, '_tutor_course_level', true );
	$course_level_str = null;

    $rainbow_tutor_single_brd_language_text    = isset( $rainbow_options['rainbow_tutor_single_brd_language_text'] ) ? $rainbow_options['rainbow_tutor_single_brd_language_text'] : '';

	$language = $rainbow_tutor_single_brd_language_text;
	if ( $course_level ) {
		$course_level_str = tutor_utils()->course_levels( $course_level );
	}
	$get_quiz_count_by_course = tutor_utils()->get_quiz_count_by_course($course_id);
    /**
     * Course sidebar redux
     */
	$rainbow_course_details_card_video_thumbnail_show       = $rainbow_course_details_settings['rainbow_course_details_card_video_thumbnail_show'];  
	$rainbow_course_details_card_price_show                 = $rainbow_course_details_settings['rainbow_course_details_card_price_show'];  
	$rainbow_course_details_card_progress_show              = $rainbow_course_details_settings['rainbow_course_details_card_progress_show'];  
	$rainbow_course_details_card_mony_back_badge_show       = $rainbow_course_details_settings['rainbow_course_details_card_mony_back_badge_show'];  
	$rainbow_course_details_card_last_udpate_show           = $rainbow_course_details_settings['rainbow_course_details_card_last_udpate_show'];  
	$rainbow_course_details_card_enrolled_show              = $rainbow_course_details_settings['rainbow_course_details_card_enrolled_show'];  
	$rainbow_course_details_lecture_count_show              = $rainbow_course_details_settings['rainbow_course_details_lecture_count_show'];  
	$rainbow_course_details_skill_level_show                = $rainbow_course_details_settings['rainbow_course_details_skill_level_show'];  
	$rainbow_course_details_language_show                   = $rainbow_course_details_settings['rainbow_course_details_language_show'];  
	$rainbow_course_details_quizzes_show                    = $rainbow_course_details_settings['rainbow_course_details_quizzes_show'];  
	$rainbow_course_details_card_duration_show              = $rainbow_course_details_settings['rainbow_course_details_card_duration_show'];  
	$rainbow_course_details_card_instructore_show           = $rainbow_course_details_settings['rainbow_course_details_card_instructore_show'];  
	$rainbow_course_details_card_requirement_show           = $rainbow_course_details_settings['rainbow_course_details_card_requirement_show'];  
	$rainbow_course_details_card_tags_show                  = $rainbow_course_details_settings['rainbow_course_details_card_tags_show'];  
	$rainbow_course_details_card_target_audience_show       = $rainbow_course_details_settings['rainbow_course_details_card_target_audience_show'];  
	$rainbow_course_details_card_bottom_show                = $rainbow_course_details_settings['rainbow_course_details_card_bottom_show'];  
	$rainbow_course_details_card_social_show                = $rainbow_course_details_settings['rainbow_course_details_card_social_show'];  
	$rainbow_course_details_card_fb_switch                  = $rainbow_course_details_settings['rainbow_course_details_card_fb_switch'];  
	$rainbow_course_details_card_twitter_switch             = $rainbow_course_details_settings['rainbow_course_details_card_twitter_switch'];  
	$rainbow_course_details_card_linkedin_switch            = $rainbow_course_details_settings['rainbow_course_details_card_linkedin_switch'];  
	$rainbow_course_details_card_contact_label              = $rainbow_course_details_settings['rainbow_course_details_card_contact_label'];  
	$rainbow_course_details_card_contact_number_before_text = $rainbow_course_details_settings['rainbow_course_details_card_contact_number_before_text'];  
	$rainbow_course_details_card_contact_number_link        = $rainbow_course_details_settings['rainbow_course_details_card_contact_number_link'];
    $image_size = isset($rainbow_options['rainbow_course_details_img_size']) ? sanitize_text_field( $rainbow_options['rainbow_course_details_img_size'] ): 'full';

	$enable_course_details_sidebar_show_more                = isset( $rainbow_course_details_settings['enable_course_details_sidebar_show_more'] ) ? $rainbow_course_details_settings['enable_course_details_sidebar_show_more'] : 0;

    if( function_exists( 'tutor' ) ) {
        $total_lessons = tutor_utils()->get_lesson_count_by_course( $course_id );  
        $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );
    }

    $course_layout_class = '';
    if( !empty(get_query_var( 'course_single_layout' )) ) {
		$rainbow_course_details_layout = get_query_var( 'course_single_layout' );
        $course_layout_class = ( $rainbow_course_details_layout == 'layout-3') ? 'course--single-layout-three' : '';
	}
?>

<div class="course-sidebar sticky-top rbt-shadow-box course-sidebar-top rbt-gradient-border <?php echo esc_attr($course_layout_class);?>">
    <div class="inner">
        <?php if(1 == $rainbow_course_details_card_video_thumbnail_show) : ?>
            <div class="video-popup-wrapper popup-video sidebar-video-hidden">
                <?php if(tutor_utils()->has_video_in_single()) : ?>
                    <!-- Start Viedo Wrapper  -->
                    <?php
                        if ( tutor_utils()->has_video_in_single() ) {
                            tutor_course_video();
                        } else {
                            get_tutor_course_thumbnail();
                            
                        }
                    ?>
                    <!-- End Viedo Wrapper  -->
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <div class="content-item-content">
            <div class="rbt-price-wrapper d-flex flex-wrap align-items-center justify-content-between">
                <?php if(1 == $rainbow_course_details_card_price_show) : ?>
                    <?php get_template_part('template-parts/components/price/layout', '1'); ?>
                <?php endif; ?>
                <?php if(1 == $rainbow_course_details_card_progress_show) : ?>
                    <?php if( class_exists( 'Elementor_Helper' ) && ( !empty(Elementor_Helper::rbt_get_tutor_course_offer_left($course_id)))) : ?>
                        <div class="discount-time">
                            <span class="rbt-badge color-danger bg-color-danger-opacity"><i class="feather-clock"></i>
                            <?php echo class_exists('Elementor_Helper') ? Elementor_Helper::rbt_get_tutor_course_offer_left($course_id): ''; ?></span>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <?php get_template_part('template-parts/components/tutor/add-to-cart/layout', '1'); ?>
            
                
            <?php if(tutor_utils()->is_course_purchasable($course_id)) : ?>
                <?php
                    if( $is_enrolled ) {
                        tutor_load_template( 'single.course.course-entry-box' );
                    } else {
                        ob_start();

                        $redirect_url = '';
                        $popup_modal_class = '';
                        $get_product_id = '0';

                        $tutor_options = get_option('tutor_option');
                        $monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
                        $checkout_page_url = \Tutor\Ecommerce\CheckoutController::get_page_url();
                        if (  is_user_logged_in() ) {

                            if( class_exists('WooCommerce') &&  $monetize_by =='wc' ) {
                                $redirect_url = wc_get_checkout_url();
                                $get_product_id = $course_product_id;
                            }
                            
                        } else {
                            $popup_modal_class = 'tutor-open-login-modal';
                        }
                        if( $monetize_by =='wc') { ?>
                        <div class="buy-now-btn mt--15">
                            
                                <button data-redirect="<?php echo esc_url( $redirect_url ); ?>" data-product_id="<?php echo esc_attr($get_product_id); ?>" class="rbt-btn ajax-buy-now-product btn-border icon-hover w-100 d-block text-center <?php echo esc_attr( $product_type ); ?> <?php echo esc_attr( $popup_modal_class ); ?>">
                                <span class="btn-text"><?php echo esc_html__('Buy Now', 'histudy'); ?></span>
                                <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                            </button>
                        </div>
                    <?php } }
                ?>
            <?php else: ?>
                <div class="mt-2">
                    <?php tutor_load_template( 'single.course.course-entry-box' ); ?>
                </div>
            <?php endif; ?>
            <?php if( 1 == $rainbow_course_details_card_mony_back_badge_show ) : ?>
                <span class="subtitle"><i class="feather-rotate-ccw"></i><?php echo esc_html__(' 30-Day Money-Back Guarantee', 'histudy'); ?></span>
            <?php endif; ?>
            <?php
            $show_more_parent_class = 1 == $enable_course_details_sidebar_show_more ? 'has-show-more': 'rbt-not-show-more';
            $show_more_inner_class = 1 == $enable_course_details_sidebar_show_more ? 'has-show-more-inner-content': 'rbt-not-has-show-more-inner-content';
            ?>
                <div class="<?php echo esc_attr( $show_more_parent_class ); ?>">
                    <div class="<?php echo esc_attr( $show_more_inner_class ); ?> rbt-widget-details <?php echo esc_attr( $show_more_parent_class ); ?>">
                        <div class="rbt-widget-details-content-wrap-inner">
                            <ul class="rbt-course-details-list-wrapper h-max-auto">
                                <?php if(1 == $rainbow_course_details_card_last_udpate_show) : ?>
                                    <?php if(!empty($last_updated)) : ?>
                                    <li><span><?php echo esc_html__("Update:", "histudy"); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($last_updated); ?></span>
                                    </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_card_enrolled_show) : ?>
                                    <?php if(!empty($total_student)) : ?>
                                    <li><span><?php echo esc_html__("Enrolled", "histudy"); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($total_student); ?></span></li>
                                    <?php endif; ?>
						        <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_lecture_count_show) : ?>
                                    <?php if(!empty($lesson_count)) : ?>
                                        <li><span><?php echo esc_html__('Lectures', 'histudy'); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($lesson_count); ?></span></li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_skill_level_show) : ?>
                                    <?php if(!empty($course_level_str)) : ?>
                                        <li><span><?php echo esc_html__('Skill Level', 'histudy'); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($course_level_str); ?></span></li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_language_show) : ?>
                                    <?php if(!empty($language)) : ?>
                                    <li><span><?php echo esc_html__('Language', 'histudy'); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($language); ?></span></li>
                                    <?php endif; ?>
						        <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_quizzes_show) : ?>
                                    <?php if(!empty($get_quiz_count_by_course)) : ?>
                                    <li><span><?php echo esc_html__('Quizzes', 'histudy'); ?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($get_quiz_count_by_course); ?></span></li>
                                    <?php endif; ?>
						        <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_card_duration_show) : ?>
                                    <?php if ( ! empty( $course_duration ) ) : ?>
                                    <li><span><?php echo esc_html__('Course Duration', 'histudy'); ?>:</span><span class="rbt-feature-value rbt-badge-5">
                                    <?php
                                        //phpcs:ignore --data sanitize through helper method
                                        echo tutor_utils()->clean_html_content( $course_duration );
                                    ?>
                                    </span></li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                            <div class="rbt-tutor-course-details-widebar-widget-load-more">
                                <?php if(1 == $rainbow_course_details_card_instructore_show) : ?>
                                    <?php tutor_course_instructors_html(); ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_card_requirement_show) : ?>
                                    <?php tutor_course_requirements_html(); ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_card_tags_show) : ?>
                                    <?php tutor_course_tags_html(); ?>
                                <?php endif; ?>
                                <?php if(1 == $rainbow_course_details_card_target_audience_show) : ?>
                                    <?php tutor_course_target_audience_html(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php if(1 == $enable_course_details_sidebar_show_more) : ?>
                    <div class="rbt-show-more-btn"><?php echo esc_html( $rainbow_options['rainbow_show_more_text'] ); ?></div>
                    <?php endif; ?>
                <?php if(1 == $rainbow_course_details_card_bottom_show) : ?>
                    <div class="social-share-wrapper text-center mt-4">
                        <?php if(1 == $rainbow_course_details_card_social_show) : ?>
                            <div class="rbt-post-share d-flex align-items-center justify-content-center">
                                <?php if( ( 1 == $rainbow_course_details_card_fb_switch && !empty( $facebookShareUrl ) ) || ( 1 == $rainbow_course_details_card_twitter_switch && !empty( $twitterShareUrl ) ) || ( 1 == $rainbow_course_details_card_linkedin_switch && !empty( $linkedInShareUrl ) ) ) : ?>
                                <ul class="social-icon social-default transparent-with-border justify-content-center mb--20">
                                    <?php if(1 == $rainbow_course_details_card_fb_switch) : ?>
                                        <?php if(!empty($facebookShareUrl)) : ?>
                                        <li><a target="_blank" href="<?php echo esc_url($facebookShareUrl); ?>">
                                                <i class="feather-facebook"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if(1 == $rainbow_course_details_card_twitter_switch) : ?>
                                        <?php if(!empty($twitterShareUrl)) : ?>
                                        <li><a target="_blank" href="<?php echo esc_url($twitterShareUrl); ?>">
                                                <i class="feather-twitter"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if(1 == $rainbow_course_details_card_linkedin_switch) : ?>
                                        <?php if(!empty($linkedInShareUrl)) : ?>
                                        <li><a target="_blank" href="<?php echo esc_url($linkedInShareUrl); ?>">
                                                <i class="feather-linkedin"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </ul>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <hr class="">
                        <div class="contact-with-us text-center">
                            <?php if(!empty($rainbow_course_details_card_contact_label)) : ?>
                                <p><?php echo esc_html($rainbow_course_details_card_contact_label); ?></p>
                            <?php endif; ?>
                            <p class="rbt-badge-2 mt--10 justify-content-center w-100"><i class="feather-phone mr--5"></i> 
                            <?php if(!empty($rainbow_course_details_card_contact_number_before_text)) : ?>
                                <?php echo esc_html($rainbow_course_details_card_contact_number_before_text); ?>
                            <?php endif; ?>
                            <a
                            href="tel: <?php echo esc_attr($rainbow_course_details_card_contact_number_link) ? esc_attr($rainbow_course_details_card_contact_number_link): '#0'; ?>"><strong><?php echo esc_html($rainbow_course_details_card_contact_number_link); ?></strong></a></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>