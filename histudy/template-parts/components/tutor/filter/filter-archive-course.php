<?php
    $users_data = class_exists('Elementor_Helper') ? Elementor_Helper::get_instructor(): '';
    $rainbow_layout_tutor_settings                            = Rainbow_Helper::rainbow_layout_tutor_settings();
    $rainbow_tutor_archive_enable_filter_enable_sort_by       = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_sort_by'];
    $rainbow_tutor_archive_enable_filter_enable_author_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_author_sort'];
    $rainbow_tutor_archive_enable_filter_enable_rating_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_rating_sort'];
    $rainbow_tutor_archive_enable_filter_enable_offer_sort    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_offer_sort'];
    $rainbow_tutor_archive_enable_filter_enable_category_sort = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_category_sort'];
    $rainbow_tutor_archive_orderby_front                      = $rainbow_layout_tutor_settings['rainbow_tutor_archive_orderby_front'];
    $course_terms = class_exists('Elementor_Helper') ? Elementor_Helper::get_course_terms(): '';
?>
<div class="filter-inner">
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_rating_sort) : ?>
    <div class="filter-select-option">
        <div class="filter-select rbt-modern-select select-rating">
            <span class="select-label d-block"><?php echo esc_html__('SORT By Rating', 'histudy'); ?></span>
            <button class="rbt-filter-rating-toggle"><?php echo esc_html__('Rating', 'histudy'); ?></button>
            <div class="rbt-single-widget rbt-single-rating-widget-padding rbt-widget-rating">
                <div class="inner">
                    <ul class="rbt-sidebar-list-wrapper rating-list-check">
                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="0" id="cat-radio-0" type="radio" name="rbt-radio">
                            <label for="cat-radio-0">
                                <span class="rating">
                                    <i class="fas fa-star off"></i>
                                    <i class="fas fa-star off"></i>
                                    <i class="fas fa-star off"></i>
                                    <i class="fas fa-star off"></i>
                                    <i class="fas fa-star off"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( 'NOT RATED', 'histudy' ); ?></span>
                            </label>
                        </li>
                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="5" id="cat-radio-5" type="radio" name="rbt-radio">
                            <label for="cat-radio-5">
                                <span class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '5 STAR', 'histudy' ); ?></span>
                            </label>
                        </li>
                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="4" id="cat-radio-4" type="radio" name="rbt-radio">
                            <label for="cat-radio-4">
                                <span class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '4 STAR', 'histudy' ); ?></span>
                            </label>
                        </li>
                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="3" id="cat-radio-3" type="radio" name="rbt-radio">
                            <label for="cat-radio-3">
                                <span class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '3 STAR', 'histudy' ); ?></span>
                            </label>
                        </li>
                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="2" id="cat-radio-2" type="radio" name="rbt-radio">
                            <label for="cat-radio-2">
                                <span class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '2 STAR', 'histudy' ); ?></span>
                            </label>
                        </li>

                        <li class="rbt-check-group">
                            <input class="form-select empty-placeholder" data-var="sortrating" value="1" id="cat-radio-1" type="radio" name="rbt-radio">
                            <label for="cat-radio-1">
                                <span class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                    <i class="off fas fa-star"></i>
                                </span>
                                <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '1 STAR', 'histudy' ); ?></span>
                            </label>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php if( ( 'yes' == $rainbow_tutor_archive_enable_filter_enable_sort_by ) ) : ?>
    <div class="filter-select-option">
        <div class="filter-select rbt-modern-select">
            <span class="select-label d-block"><?php echo esc_html__('SORT By Order', 'histudy') ?></span>
            <select class="form-select" aria-label="<?php echo esc_attr__('Default select example', 'histudy'); ?>" data-var="sortby">
                <option selected><?php _e( 'Default', 'histudy' ); ?></option>
                <option value="DESC" name="Latest" ><?php _e( 'Latest', 'histudy' ); ?></option>
                <option value="ASC" name="Oldest"><?php _e( 'Oldest', 'histudy' ); ?></option>
            </select>
        </div>
    </div>
    <?php endif; ?>
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_author_sort) : ?>
    <div class="filter-select-option">
        <div class="filter-select rbt-modern-select">
            <span class="select-label d-block"><?php echo esc_html__('SORT By Author', 'histudy') ?></span>
            <?php if( !empty( $users_data ) ) : ?>
            <select class="form-select" multiple aria-label="multiple select example" data-live-search="true" data-size="7" data-selected-text-format="count > 2" data-actions-box="true" data-var="instructor">
                <?php
                    foreach ( $users_data as $instructor ) {
                        $instructor_metadata = get_user_meta( $instructor->ID ); ?>
                            <option value="<?php echo esc_attr( $instructor->ID ); ?>" name="<?php echo isset($instructor_metadata['username'][0]) ? ucwords( $instructor_metadata['username'][0] ): ''; ?>"><?php echo isset( $instructor_metadata['nickname'][0] ) ? ucwords( $instructor_metadata['nickname'][0] ): ''; ?> </option>
                        <?php
                    } ?>
            </select>
            <?php else: ?>
                <h5 class="alert alert-warning"><?php echo esc_html__('No User Found', 'histudy'); ?></h5>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_offer_sort) : ?>
    <div class="filter-select-option">
        <div class="filter-select rbt-modern-select">
            <span class="select-label d-block"><?php echo esc_html__('SORT By Price', 'histudy') ?></span>
            <select class="form-select" aria-label="<?php echo esc_attr__('Default select example', 'histudy'); ?>" data-var="price">
                <option value ="" selected><?php _e( 'All', 'histudy' ); ?></option>
                <option value="free" name="<?php echo esc_attr__('Free', 'histudy'); ?>"><?php _e( 'Free', 'histudy' ); ?></option>
                <option value="paid" name="<?php echo esc_attr__('Paid', 'histudy'); ?>"><?php _e( 'Paid', 'histudy' ); ?></option>
            </select>
        </div>
    </div>
    <?php endif; ?>
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_category_sort) : ?>
    <div class="filter-select-option">
        <div class="filter-select rbt-modern-select">
            <span class="select-label d-block"><?php echo esc_html__('SORT By Category', 'histudy') ?></span>
            <select data-live-search="true" class="form-select" multiple aria-label="<?php echo esc_attr__('multiple select example', 'histudy'); ?>" data-var="category">
                <option disabled><?php echo esc_html__('-- Select Category --', 'histudy') ?></option>
                <?php
                foreach ( $course_terms as $terms ) {
                    ?>
                        <option value="<?php echo esc_attr( $terms->slug ); ?>" name="<?php echo esc_attr( $terms->name ); ?>"><?php echo esc_html( $terms->name ); ?></label></option>
                    <?php
                } ?>
            </select>
        </div>
    </div>
    <?php endif; ?>
</div>