<?php
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$users_data = class_exists('Element<PERSON>_Helper') ? Elementor_Helper::get_instructor(): '';
$rainbow_layout_tutor_settings = Rainbow_Helper::rainbow_layout_tutor_settings();
$rainbow_tutor_archive_enable_filter_enable_sort_by       = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_sort_by'];
$rainbow_tutor_archive_enable_filter_enable_offer_sort    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_offer_sort'];
$rainbow_tutor_archive_enable_filter_difficulty    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_difficulty'];
$rainbow_tutor_archive_enable_filter_enable_author_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_author_sort'];
$rainbow_tutor_archive_enable_filter_enable_category_sort = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_category_sort'];    $rainbow_tutor_archive_enable_filter_enable_rating_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_rating_sort'];
$course_terms = class_exists('Elementor_Helper') ? Elementor_Helper::get_course_terms(): '';

$rainbow_tutor_category_archive_category_enable_filter    = $rainbow_layout_tutor_settings['rainbow_tutor_category_archive_category_enable_filter'];


?>
<div class="filter-inner d-block">
   <div class="rbt-sidebar-widget-wrapper filter-top-2">
      <div class="row g-5">
        <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_category_sort) : 

         if( !is_tax( 'course-category' ) ) {
         ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-categories has-show-more">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Category', 'histudy') ?></h4>
                  <ul class="rbt-sidebar-list-wrapper categories-list-check has-show-more-inner-content rbt-list">
                    <?php
                        foreach ( $course_terms as $terms ) {
                            $cat_count = $terms->count;
                            ?>
                            <li class="rbt-check-group">
                                <input data-var="category" value="<?php echo esc_attr( $terms->slug ); ?>" class="d-none" id="<?php echo esc_attr( $terms->slug ); ?>" type="checkbox" name="<?php echo esc_attr( $terms->slug ); ?>">
                                <label for="<?php echo esc_attr( $terms->slug ); ?>"><?php echo esc_html( $terms->name ); ?></label>
                            </li>
                            <?php
                        }
                    ?>
                  </ul>
               </div>
               <div class="rbt-show-more-btn"><?php echo esc_html( $rainbow_options['rainbow_show_more_text'] ); ?></div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php 
         }
         endif; ?>
         <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_rating_sort) : ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-ratings">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('Rating', 'histudy'); ?></h4>
                  <ul class="rbt-sidebar-list-wrapper rating-list-check">
                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="5" id="cat-radio-5" type="radio" name="rbt-radio">
                        <label for="cat-radio-5">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '5 STAR', 'histudy' ); ?></span>
                        </label>
                    </li>
                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="4" id="cat-radio-4" type="radio" name="rbt-radio">
                        <label for="cat-radio-4">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '4 STAR', 'histudy' ); ?></span>
                        </label>
                    </li>
                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="3" id="cat-radio-3" type="radio" name="rbt-radio">
                        <label for="cat-radio-3">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '3 STAR', 'histudy' ); ?></span>
                        </label>
                    </li>
                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="2" id="cat-radio-2" type="radio" name="rbt-radio">
                        <label for="cat-radio-2">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '2 STAR', 'histudy' ); ?></span>
                        </label>
                    </li>

                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="1" id="cat-radio-1" type="radio" name="rbt-radio">
                        <label for="cat-radio-1">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                                <i class="off fas fa-star"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '1 STAR', 'histudy' ); ?></span>
                        </label>
                    </li>
                    <li class="rbt-check-group">
                        <input class="form-select empty-placeholder" data-var="sortrating" value="" id="cat-radio-0" type="radio" name="rbt-radio">
                        <label for="cat-radio-0">
                            <span class="rating">
                                <i class="fas fa-star off"></i>
                                <i class="fas fa-star off"></i>
                                <i class="fas fa-star off"></i>
                                <i class="fas fa-star off"></i>
                                <i class="fas fa-star off"></i>
                            </span>
                            <span class="rbt-lable count bg-transparent"><?php echo esc_html__( 'No Rated', 'histudy' ); ?></span>
                        </label>
                    </li>
                </ul>
               </div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php endif; ?>
         <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_author_sort) : ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-instructor has-show-more">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Author', 'histudy') ?></h4>
                  <ul class="rbt-sidebar-list-wrapper has-show-more-inner-content instructor-list-check rbt-list">
                     <?php
                     foreach ( $users_data as $index => $instructor ) {
                        $instructor_metadata = get_user_meta( $instructor->ID );
                     ?>
                        <li class="rbt-check-group">
                           <input data-var="instructor" value="<?php echo esc_attr( $instructor->ID ); ?>" class="d-none" id="ins-list-<?php echo esc_attr( $index ); ?>" type="radio" name="ins-list">
                           <label for="ins-list-<?php echo esc_attr( $index ); ?>"><?php echo isset( $instructor_metadata['nickname'][0] ) ? ucwords( $instructor_metadata['nickname'][0] ): ''; ?></label>
                        </li>
                     <?php } ?>
                  </ul>
               </div>
               <div class="rbt-show-more-btn"><?php echo esc_html( $rainbow_options['rainbow_show_more_text'] ); ?></div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php endif; ?>
         <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_offer_sort) : ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-prices">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Price', 'histudy') ?></h4>
                  <ul class="rbt-sidebar-list-wrapper prices-list-check rbt-list" >
                     <li class="rbt-check-group">
                        <input data-var="price" checked class="d-none" value="" id="prices-list-1" type="radio" name="prices-list">
                        <label for="prices-list-1"><?php _e( 'All', 'histudy' ); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input data-var="price" class="d-none" value="free" id="prices-list-2" type="radio" name="prices-list">
                        <label for="prices-list-2"><?php echo esc_html__('Free', 'histudy'); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input data-var="price" class="d-none" value="paid" id="prices-list-3" type="radio" name="prices-list">
                        <label for="prices-list-3"><?php _e( 'Paid', 'histudy' ); ?></label>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php endif; ?>
         <?php if( 'yes' == $rainbow_tutor_archive_enable_filter_difficulty ) : ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-lavels">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('Levels', 'histudy'); ?></h4>
                  <ul class="rbt-sidebar-list-wrapper lavels-list-check rbt-list">
                     <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="all_levels" id="lavels-list-1" type="radio" name="lavels-list">
                        <label for="lavels-list-1"><?php echo esc_html__('All Levels', 'histudy'); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="beginner" id="lavels-list-2" type="radio" name="lavels-list">
                        <label for="lavels-list-2"><?php echo esc_html__('Beginner', 'histudy'); ?> </label>
                     </li>
                     <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="intermediate" id="lavels-list-3" type="radio" name="lavels-list">
                        <label for="lavels-list-3"><?php echo esc_html__('Intermediate', 'histudy'); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="expert" id="lavels-list-4" type="radio" name="lavels-list">
                        <label for="lavels-list-4"><?php echo esc_html__('Expert', 'histudy'); ?></label>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php endif; ?>
         <?php if( ( 'yes' == $rainbow_tutor_archive_enable_filter_enable_sort_by ) ) : ?>
         <!-- Start Widget Area  -->
         <div class="col-lg-2 col-md-4 col-sm-6 col-12">
            <div class="rbt-single-widget rbt-widget-features">
               <div class="inner">
                  <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Order', 'histudy') ?></h4>
                  <ul class="rbt-sidebar-list-wrapper features-list-check rbt-list">
                     <li class="rbt-check-group">
                        <input data-var="sortby" value="" class="d-none" id="sort-list-1" type="radio" name="sort-list">
                        <label for="sort-list-1"><?php _e( 'Default', 'histudy' ); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input data-var="sortby" value="DESC" class="d-none" id="sort-list-2" type="radio" name="sort-list">
                        <label for="sort-list-2"><?php _e( 'Latest', 'histudy' ); ?></label>
                     </li>
                     <li class="rbt-check-group">
                        <input data-var="sortby" value="ASC" class="d-none" id="sort-list-3" type="radio" name="sort-list">
                        <label for="sort-list-3"><?php _e( 'Oldest', 'histudy' ); ?></label>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
         <!-- End Widget Area  -->
         <?php endif; ?>
      </div>
   </div>
</div>