<?php
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$users_data = class_exists('Elementor_Helper') ? Elementor_Helper::get_instructor(): '';
$rainbow_layout_tutor_settings = Rainbow_Helper::rainbow_layout_tutor_settings();
$rainbow_tutor_archive_enable_filter_enable_sort_by       = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_sort_by'];
$rainbow_tutor_archive_enable_filter_enable_offer_sort    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_offer_sort'];
$rainbow_tutor_archive_enable_filter_difficulty    = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_difficulty'];
$rainbow_tutor_archive_enable_filter_enable_author_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_author_sort'];
$rainbow_tutor_archive_enable_filter_enable_category_sort = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_category_sort'];    $rainbow_tutor_archive_enable_filter_enable_rating_sort   = $rainbow_layout_tutor_settings['rainbow_tutor_archive_enable_filter_enable_rating_sort'];
$course_terms             = Elementor_Helper::get_course_terms();
$course_terms_tags        = Elementor_Helper::get_course_terms( false, 'course-tag' );
$users_data               = Elementor_Helper::get_instructor();
$rand = rand(300, 900);

$course_filter_layout = get_query_var('course_filter_layout');

$right_sidebar_class = ( $course_filter_layout =='layout-3' ) ? 'custom-course-details-right-sidebar': '';
?>
<div class="rbt-course-details-right-sidebar <?php echo esc_attr($right_sidebar_class); ?>">
<?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_category_sort) : 
    
endif; ?>
<?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_rating_sort) : ?>
    <!-- Start Widget Area  -->
    <div class="rbt-single-widget rbt-widget-ratings">
        <div class="inner">
            <h4 class="rbt-widget-title-2"><?php echo esc_html__('Rating', 'histudy'); ?></h4>
            <ul class="rbt-sidebar-list-wrapper rating-list-check">
                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="5" id="sidebar-cat-radio-5-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-5-<?php echo esc_attr( $rand ); ?>">
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '5 STAR', 'histudy' ); ?></span>
                    </label>
                </li>
                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="4" id="sidebar-cat-radio-4-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-4-<?php echo esc_attr( $rand ); ?>">
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '4 STAR', 'histudy' ); ?></span>
                    </label>
                </li>
                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="3" id="sidebar-cat-radio-3-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-3-<?php echo esc_attr( $rand ); ?>">
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '3 STAR', 'histudy' ); ?></span>
                    </label>
                </li>
                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="2" id="sidebar-cat-radio-2-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-2-<?php echo esc_attr($rand); ?>">
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '2 STAR', 'histudy' ); ?></span>
                    </label>
                </li>

                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="1" id="sidebar-cat-radio-1-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-1-<?php echo esc_attr($rand); ?>">
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                            <i class="off fas fa-star"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( '1 STAR', 'histudy' ); ?></span>
                    </label>
                </li>
                <li class="rbt-check-group">
                    <input class="form-select empty-placeholder" data-var="sortrating" value="0" id="sidebar-cat-radio-0-<?php echo esc_attr($rand); ?>" type="radio" name="rbt-radio">
                    <label for="sidebar-cat-radio-0-<?php echo esc_attr($rand); ?>">
                        <span class="rating">
                            <i class="fas fa-star off"></i>
                            <i class="fas fa-star off"></i>
                            <i class="fas fa-star off"></i>
                            <i class="fas fa-star off"></i>
                            <i class="fas fa-star off"></i>
                        </span>
                        <span class="rbt-lable count bg-transparent"><?php echo esc_html__( 'No Rated', 'histudy' ); ?></span>
                    </label>
                </li>
            </ul>
        </div>
    </div>
    <!-- End Widget Area  -->
    <?php endif; ?>
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_author_sort) : ?>
        <!-- Start Widget Area  -->
            <div class="rbt-single-widget rbt-widget-instructor has-show-more">
                <div class="inner">
                    <h4 class="rbt-widget-title-2"><?php echo esc_html__('Sort By Author', 'histudy') ?></h4>
                    <ul class="rbt-sidebar-list-wrapper instructor-list-check rbt-list">
                        <?php
                        foreach ( $users_data as $index => $instructor ) {
                            $instructor_metadata = get_user_meta( $instructor->ID );
                        ?>
                            <li class="rbt-check-group">
                            <input data-var="instructor" value="<?php echo esc_attr( $instructor->ID ); ?>" class="d-none" id="sidebar-ins-list-<?php echo esc_attr( $index ); ?>-<?php echo esc_attr($rand); ?>" type="radio" name="ins-list">
                            <label for="sidebar-ins-list-<?php echo esc_attr( $index ); ?>-<?php echo esc_attr($rand); ?>"><?php echo isset( $instructor_metadata['nickname'][0] ) ? ucwords( $instructor_metadata['nickname'][0] ): ''; ?></label>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
                <div class="rbt-show-more-btn"><?php echo esc_html( $rainbow_options['rainbow_show_more_text'] ); ?></div>
            </div>
        <!-- End Widget Area  -->
    <?php endif; ?>
    <?php if('yes' == $rainbow_tutor_archive_enable_filter_enable_offer_sort) : ?>
    <!-- Start Widget Area  -->
        <div class="rbt-single-widget rbt-widget-prices">
            <div class="inner">
                <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Price', 'histudy') ?></h4>
                <ul class="rbt-sidebar-list-wrapper prices-list-check rbt-list" >
                    <li class="rbt-check-group">
                        <input data-var="price" checked class="d-none" value="" id="sidebar-prices-list-1-<?php echo esc_attr($rand); ?>" type="radio" name="prices-list">
                        <label for="sidebar-prices-list-1-<?php echo esc_attr($rand); ?>"><?php _e( 'All', 'histudy' ); ?></label>
                    </li>
                    <li class="rbt-check-group">
                        <input data-var="price" class="d-none" value="free" id="sidebar-prices-list-2-<?php echo esc_attr($rand); ?>" type="radio" name="prices-list">
                        <label for="sidebar-prices-list-2-<?php echo esc_attr($rand); ?>"><?php echo esc_html__('Free', 'histudy'); ?></label>
                    </li>
                    <li class="rbt-check-group">
                        <input data-var="price" class="d-none" value="paid" id="sidebar-prices-list-3-<?php echo esc_attr($rand); ?>" type="radio" name="prices-list">
                        <label for="sidebar-prices-list-3-<?php echo esc_attr($rand); ?>"><?php _e( 'Paid', 'histudy' ); ?></label>
                    </li>
                </ul>
            </div>
        </div>
    <!-- End Widget Area  -->
    <?php endif; ?>
    <?php if( 'yes' == $rainbow_tutor_archive_enable_filter_difficulty ) : ?>
    <!-- Start Widget Area  -->
        <div class="rbt-single-widget rbt-widget-lavels">
            <div class="inner">
                <h4 class="rbt-widget-title-2"><?php echo esc_html__('Levels', 'histudy'); ?></h4>
                <ul class="rbt-sidebar-list-wrapper lavels-list-check rbt-list">
                    <li class="rbt-check-group">
                        <input checked data-var="difficulty" class="d-none" value="all_levels" id="sidebar-lavels-list-1-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-lavels-list">
                        <label for="sidebar-lavels-list-1-<?php echo esc_attr($rand); ?>"><?php echo esc_html__('All Levels', 'histudy'); ?></label>
                    </li>
                    <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="beginner" id="sidebar-lavels-list-2-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-lavels-list">
                        <label for="sidebar-lavels-list-2-<?php echo esc_attr($rand); ?>"><?php echo esc_html__('Beginner', 'histudy'); ?> </label>
                    </li>
                    <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="intermediate" id="sidebar-lavels-list-3-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-lavels-list">
                        <label for="sidebar-lavels-list-3-<?php echo esc_attr($rand); ?>"><?php echo esc_html__('Intermediate', 'histudy'); ?></label>
                    </li>
                    <li class="rbt-check-group">
                        <input  data-var="difficulty" class="d-none" value="expert" id="sidebar-lavels-list-4-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-lavels-list">
                        <label for="sidebar-lavels-list-4-<?php echo esc_attr($rand); ?>"><?php echo esc_html__('Expert', 'histudy'); ?></label>
                    </li>
                </ul>
            </div>
        </div>
    <!-- End Widget Area  -->
    <?php endif; ?>
    <?php if( ( 'yes' == $rainbow_tutor_archive_enable_filter_enable_sort_by ) ) : ?>
        <!-- Start Widget Area  -->
            <div class="rbt-single-widget rbt-widget-features">
                <div class="inner">
                    <h4 class="rbt-widget-title-2"><?php echo esc_html__('SORT By Order', 'histudy') ?></h4>
                    <ul class="rbt-sidebar-list-wrapper features-list-check rbt-list">
                        <li class="rbt-check-group">
                            <input checked data-var="sortby" value="" class="d-none" id="sidebar-sort-list-1-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-sort-list">
                            <label for="sidebar-sort-list-1-<?php echo esc_attr($rand); ?>"><?php _e( 'Default', 'histudy' ); ?></label>
                        </li>
                        <li class="rbt-check-group">
                            <input data-var="sortby" value="DESC" class="d-none" id="sidebar-sort-list-2-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-sort-list">
                            <label for="sidebar-sort-list-2-<?php echo esc_attr($rand); ?>"><?php _e( 'Latest', 'histudy' ); ?></label>
                        </li>
                        <li class="rbt-check-group">
                            <input data-var="sortby" value="ASC" class="d-none" id="sidebar-sort-list-3-<?php echo esc_attr($rand); ?>" type="radio" name="sidebar-sort-list">
                            <label for="sidebar-sort-list-3-<?php echo esc_attr($rand); ?>"><?php _e( 'Oldest', 'histudy' ); ?></label>
                        </li>
                    </ul>
                </div>
            </div>
        <!-- End Widget Area  -->
        <?php endif; ?>
</div>