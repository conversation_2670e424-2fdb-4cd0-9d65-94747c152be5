<?php $rainbow_options = Rainbow_Helper::rainbow_get_options();
$filter_layout =  $rainbow_options['rainbow_course_details_breadcrumb_filter_layout'];
$course_filter_layout = get_query_var( 'course_filter_layout' );
if( !empty($course_filter_layout) ) {
    $filter_layout = $course_filter_layout;
}

?>
<?php
if( 'layout-1' == $filter_layout ) {
   get_template_part( 'template-parts/components/tutor/filter/filter-archive-course' );
} elseif( 'layout-3' == $filter_layout ) {
   get_template_part( 'template-parts/components/tutor/filter/filter-archive-course', 3 );
} elseif( 'layout-2' == $filter_layout ) {
    get_template_part( 'template-parts/components/tutor/filter/filter-archive-course', 2 );
} else {
    get_template_part( 'template-parts/components/tutor/filter/filter-archive-course' );
}
?>
