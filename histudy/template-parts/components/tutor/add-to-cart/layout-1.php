<?php
use <PERSON><PERSON>\Models\CourseModel;
if(isset($args['course_id'])) {
    $course_id          = $args['course_id'];
} else {
    $course_id          = get_the_ID();
}
if( !class_exists( 'Elementor_Helper' ) && !function_exists( 'tutor' ) ) {
    return;
}
$enrolled  = tutor_utils()->is_enrolled( $course_id );
if( !$enrolled ) {
    if(tutor_utils()->is_course_purchasable($course_id)) :  
        $product_id      = tutor_utils()->get_course_product_id( $course_id );?>
        <?php if(Elementor_Helper::rbt_is_product_in_cart($product_id)): ?>
            <a class="mt--15 rbt-btn btn-gradient icon-hover w-100 d-block text-center" href="<?php echo esc_url(wc_get_cart_url()) ?>">
                <span class="btn-text"><?php echo esc_html__('View Cart', 'histudy'); ?></span>
                <span class="btn-icon"><i class="feather-arrow-right"></i></span>
            </a>
            <?php else: ?>
                <div class="mt--15">
                    <?php 
                    if ( is_singular('courses') ) {
                        Elementor_Helper::rbt_tutor_woo_add_to_cart_single_page();
                    } else {
                        Elementor_Helper::rbt_tutor_woo_add_to_cart(); 
                    }
                    
                    ?>
                </div>
        <?php endif; ?>
    <?php endif;
} else {
    $user_id = get_current_user_id();
    $lesson_url = CourseModel::get_review_progress_link( $course_id, $user_id );
    $is_public_course = \TUTOR\Course_List::is_public( $course_id );
    if( $is_public_course ) :
    ?>
    <a class="mt--15 rbt-btn btn-gradient icon-hover w-100 d-block text-center d-none" href="<?php echo esc_url($lesson_url) ?>">
        <span class="btn-text"><?php echo esc_html__('Start Learning', 'histudy'); ?></span>
        <span class="btn-icon"><i class="feather-arrow-right"></i></span>
    </a>
    <?php endif; ?>
<?php }

