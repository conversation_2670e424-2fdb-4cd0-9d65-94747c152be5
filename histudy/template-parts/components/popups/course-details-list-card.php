<?php
$course_id = get_the_ID();
$course_attributes      = get_post_meta($course_id);
if(tutor_utils()->is_course_purchasable($course_id)) {
    $course_product_id      = isset( $course_attributes['_tutor_course_product_id'][0] ) ? $course_attributes['_tutor_course_product_id'][0] : null;
} else {
    $course_product_id = null;
}

$product_prices = Rainbow_Helper::rb_get_product_prices($course_product_id);
$current_product_price = isset( $product_prices['current_price'] ) ? $product_prices['current_price'] : 0;
$regular_price = isset( $product_prices['regular_price'] ) ? $product_prices['regular_price'] : 0;

$user_id = get_current_user_id();

$is_enrolled = tutor_utils()->is_enrolled( $course_id, $user_id );
$price 	= tutor_utils()->get_course_price( get_the_ID() );
$redirect_url = '';
if ( class_exists( 'WooCommerce' ) ) { 
    $redirect_url = wc_get_checkout_url();
}

$price 	= tutor_utils()->get_course_price( $course_id );
$is_public            = get_post_meta( get_the_ID(), '_tutor_is_public_course', true ) == 'yes';

$first_lesson_url  = tutor_utils()->get_course_first_lesson( get_the_ID(), tutor()->lesson_post_type );
! $first_lesson_url ? $first_lesson_url = tutor_utils()->get_course_first_lesson( get_the_ID() ) : 0;

$tutor_options = get_option('tutor_option');
$monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
$buy_now_icon = 'buy-now-icon-active';
if( $monetize_by == 'tutor') {
    $buy_now_icon = '';
}

?>

<?php if ( tutor_utils()->is_course_purchasable($course_id) ) : ?>
<div class="rbt-course-action-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 col-md-6">
                <div class="section-title text-center text-md-start">
                    <h5 class="title mb--0"><?php the_title(); ?></h5>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 mt_sm--15">
                <div class="course-action-bottom-right rbt-single-group">
                    <div class="rbt-single-list rbt-price large-size justify-content-center">
                    <?php if(!empty($price) && !$is_enrolled ) : ?>
                        <span class="current-price"><?php echo wp_kses_post((string) $price); ?></span>
                    <?php endif; ?>
                    </div>
                    <div class="rbt-single-list action-btn <?php echo esc_attr($buy_now_icon);?>">
                        <?php 
                            if( $is_enrolled && !empty( $first_lesson_url )) { ?>
                            <a href="<?php echo esc_url( $first_lesson_url); ?>" class="rbt-btn btn-gradient icon-hover w-100 d-block text-center">
                                <span class="btn-text"><?php echo esc_html__('Start Learning', 'histudy'); ?></span>
                                <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                            </a>

                        <?php } elseif( $monetize_by == 'tutor') {
                            get_template_part('template-parts/components/tutor/add-to-cart/layout', '1'); 
                        } else { ?>
                       
                        <button data-redirect="<?php echo esc_url($redirect_url); ?>" data-product_id="<?php echo esc_attr( $course_product_id ); ?>" class="rbt-btn btn-gradient hover-icon-reverse btn-md ajax-buy-now-product px-3 ms-2 w-170">
                            <span class="btn-text"><?php echo esc_html__('Buy Now', 'histudy'); ?></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                        </button>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>