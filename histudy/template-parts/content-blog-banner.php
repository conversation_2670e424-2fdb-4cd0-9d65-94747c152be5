<?php
    $col_class = 'col-lg-12';
    if( 'blog-list' == get_query_var( 'blog_layout' ) ) {
        $col_class = 'col-lg-10 offset-lg-1 histudy-post-wrapper';
        
    }
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $banner_layout = Rainbow_Helper::rainbow_blog_banner_layout();
    $breadcrumb_enable = $banner_layout['banner_layout'];
    $rainbow_blog_title_layout = isset( $rainbow_options['rainbow_blog_title_layout'] ) ? $rainbow_options['rainbow_blog_title_layout']: 1;
    global $wp_query;
    $rainbow_blog_subtitle =  sanitize_text_field( $rainbow_options['rainbow_blog_subtitle'] );
    $published_post_count = $wp_query->found_posts;
    $allowed_tags = wp_kses_allowed_html('post');
    $rainbow_select_banner_style = function_exists( 'get_field' ) ? get_field('rainbow_select_banner_style', get_the_ID()): '';
    $overlap_class = isset($rainbow_options['rainbow_enable_blog_breadcrumb_overlap']) && 1 != $rainbow_options['rainbow_enable_blog_breadcrumb_overlap'] ? 'rbt-page-gradient-breadcrumb': '';
	$is_tutor_instructor_archive = isset($_GET['view']) ? $_GET['view']: '';
	if( 'instructor' == $is_tutor_instructor_archive ) {
		return;
	}
?>
<?php if( 2 == $rainbow_blog_title_layout ) : ?>
<div class="rbt-page-banner-wrapper <?php echo esc_attr( $overlap_class ); ?>">
    <!-- Start Banner BG Image  -->
    <div class="rbt-banner-image"></div>
    <!-- End Banner BG Image  -->
    <div class="rbt-banner-content">

        <!-- Start Banner Content Top  -->
        <div class="rbt-banner-content-top">
            <div class="container">
                <div class="row">
                    <div class="<?php echo esc_attr( $col_class ); ?>">
                        <!-- Start Breadcrumb Area  -->
                        <?php rainbow_breadcrumbs(); ?>
                        <!-- End Breadcrumb Area  -->
                        <div class="title-wrapper">
                            <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                            <a href="<?php echo get_post_type_archive_link( 'post' ); ?>" class="rbt-badge-2">
                                <div class="image">🎉</div> <?php echo sprintf('%d %s', $published_post_count, _nx('Article', 'Articles', $published_post_count, 'Total number of article', 'histudy')); ?>
                            </a>
                        </div>

                        <p class="description"><?php echo wp_kses( $rainbow_blog_subtitle, $allowed_tags ); ?>  </p>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Banner Content Top  -->

    </div>
</div>
<?php else: ?>
<div class="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="breadcrumb-inner text-center">
                    <h2 class="title"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h2>
                    <?php rainbow_breadcrumbs();?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif;