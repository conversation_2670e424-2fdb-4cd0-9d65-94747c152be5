<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$sidebar = Rainbow_Helper::rainbow_sidebar_options();
$rainbow_single_blog_sidebar_class = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? ' col-lg-12' : ' col-lg-7';
$alignwide = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? 'wp-block-image alignwide' : '';
$thumb_size = 'rainbow-thumbnail-single';
?>
<!-- End .content -->
<?php if (has_post_thumbnail()) { ?>
<div class="post-thumbnail mb--30 position-relative wp-block-image alignwide">
    <figure>
        <?php the_post_thumbnail($thumb_size, ['class' => ' ']) ?>
        <figcaption><?php the_title(); ?></figcaption>
    </figure>
</div>
<?php } ?>
<!-- End .thumbnail -->
<div class="rainbow-post-content-wrapper">
    <?php
    the_content();
    wp_link_pages(array(
        'before' => '<div class="rainbow-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'histudy') . '</span>',
        'after' => '</div>',
        'link_before' => '<span>',
        'link_after' => '</span>',
    ));
    ?>
</div>


<?php get_template_part('template-parts/single-post/tags');?>

<?php get_template_part('template-parts/single-post/social-share');?>

<?php get_template_part('template-parts/single-post/biography');?>

<?php get_template_part('template-parts/single-post/comment');?>

<?php if(true == $rainbow_options['show_related_post']){ 
    histudy_related_post_grid();
}