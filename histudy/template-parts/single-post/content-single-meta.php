<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */
  
$rainbow_options = Rainbow_Helper::rainbow_get_options();
?>
<?php $post_tags = get_the_tags();
    if ( $rainbow_options['rainbow_show_blog_details_tags_meta'] !== 'no' && $post_tags ) { ?>
        <div class="tagcloud">
            <?php foreach( $post_tags as $tag ) { ?>
                <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>"><?php echo esc_html($tag->name); ?></a>
            <?php } ?>
        </div>
    <?php } ?> 


    <?php
    if ($rainbow_options['rainbow_show_post_tags_meta']) {
        get_template_part('template-parts/single-post/social-share');
    }
    ?> 
    <?php
    if ($rainbow_options['rainbow_show_post_author_meta']) {
        get_template_part('template-parts/single-post/biography');
    }
    ?> 
    <?php   
    /**
     *  Output comments wrapper if it's a post, or if comments are open,
     * or if there's a comment number – and check for password.
     * */
    if ((is_single() || is_page()) && (comments_open() || get_comments_number()) && !post_password_required()) {
        ?>
        <div class="rainbow-comment-area">  
                <?php comments_template(); ?>  
        </div><!-- .comments-wrapper --> 
        <?php
    }