<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$name        = get_the_author_meta( 'display_name' ) ? '<h4 class="item-title">' . get_the_author_meta( 'display_name' ) . '</h4>' : '';
$description = get_the_author_meta( 'description' ) ? '<p>' . get_the_author_meta( 'description' ) . '</p>' : '';
$designation = get_the_author_meta( 'edhub_author_designation' ) ? '<div class="sub-title">' . get_the_author_meta( 'edhub_author_designation' ) . '</div>' : '';

$facebook  = get_the_author_meta( '_tutor_profile_facebook' ) ? '<li class="facebook"><a href="' . esc_url( get_the_author_meta( '_tutor_profile_facebook' ) ) . '"><i class="fab fa-facebook-f"></i></a></li>' : '';
$twitter   = get_the_author_meta( '_tutor_profile_twitter' ) ? '<li class="twitter"><a href="' . esc_url( get_the_author_meta( '_tutor_profile_twitter' ) ) . '"><i class="fab fa-twitter"></i></a></li>' : '';
$linkedin  = get_the_author_meta( '_tutor_profile_linkedin' ) ? '<li class="linkedin"><a href="' . esc_url( get_the_author_meta( '_tutor_profile_linkedin' ) ) . '"><i class="fab fa-linkedin-in"></i></a></li>' : '';
$instagram = get_the_author_meta( '_tutor_profile_github' ) ? '<li class="instagram"><a href="' . esc_url( get_the_author_meta( '_tutor_profile_github' ) ) . '"><i class="fab fa-instagram"></i></a></li>' : '';
?>

<div class="blog-author">
    <div class="media">
        <img src="<?php echo esc_url( get_avatar_url( get_the_author_meta( 'ID' ) ) ); ?>"
             alt="<?php echo esc_attr( $name ); ?>"/>
        <div class="media-body">
			<?php echo wp_kses( $name . $designation . $description, 'alltext_allow' ); ?>
            <ul class="inline-list item-social">
				<?php echo wp_kses( $facebook . $twitter . $linkedin . $instagram, 'alltext_allow' ); ?>
            </ul>
        </div>
    </div>
</div>