<?php
/**
 * The template part for displaying an Author biography
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package histudy
 */
$custom_link = rainbow_get_acf_data('rainbow_custom_link');
$link = !empty($custom_link) ? $custom_link : get_the_permalink();
$rainbow_options        = Rainbow_Helper::rainbow_get_options();
$meta_value = array_map( 'sanitize_text_field', get_post_meta( get_the_ID(), 'histudy_post_like' ) );
$like_count = isset($meta_value[0]) ? absint($meta_value[0]): 0;
?> 
<!-- Social Share Block  -->
<div class="social-share-block mt--30">
    <?php if('no' !== $rainbow_options['rainbow_blog_details_like_options'] || ($rainbow_options['rainbow_blog_details_social_share'] &&  function_exists('rainbow_sharing_icon_links_bottom') )){ ?>
       
            <?php 
            $likes_text = sprintf( 
    _nx( '%s Like', '%s Likes', $like_count, 'This will check like count', 'histudy' ), 
    number_format_i18n( $like_count ) 
);  ?>
            <?php if('no' !== $rainbow_options['rainbow_blog_details_like_options']){ ?>
                <div class="post-like pt-like-it rainbow-blog-details-like">
                    <button class="like-button"  data-id="<?php echo esc_attr(get_the_ID()); ?>"><i class="feather feather-thumbs-up"></i><span class="like-count"> <?php echo esc_html($likes_text); ?></span></button>
                </div>
            <?php  } ?>
        <?php } ?>

        <?php if ($rainbow_options['rainbow_blog_details_social_share'] && function_exists('rainbow_sharing_icon_links_bottom')) {
             rainbow_sharing_icon_links_bottom();
        }
    ?>
</div>