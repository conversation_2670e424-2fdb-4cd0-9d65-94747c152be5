<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$sidebar = Rainbow_Helper::rainbow_sidebar_options();
$rainbow_single_blog_sidebar_class = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? ' col-lg-12' : ' col-lg-7';
$alignwide = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? 'wp-block-image alignwide' : '';
$video_url = rainbow_get_acf_data("rainbow_video_link"); 
$convar_emb_link = '';
if (function_exists('rainbow_getEmbedUrl')) {
    $convar_emb_link = rainbow_getEmbedUrl($video_url);
}
?>
<div class="content">   
  <?php  if (!empty($convar_emb_link)) { ?> 
    <div class="ratio ratio-16x9 <?php echo esc_attr($alignwide); ?> mb--30">
        <iframe class="embed-responsive-item" src="<?php echo esc_url($convar_emb_link); ?>" frameborder="0"
                allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen=""></iframe>
    </div> 
    <?php } ?> 
    <div class="rainbow-post-content-wrapper">
        <?php 
        the_content(); 
        wp_link_pages(array(
            'before' => '<div class="rainbow-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'histudy') . '</span>',
            'after' => '</div>',
            'link_before' => '<span>',
            'link_after' => '</span>',
        ));
        ?>
    </div>  
    <?php get_template_part('template-parts/single-post/biography');?> 
</div> 
<?php if(true == $rainbow_options['show_related_post']){ 
    histudy_related_post_grid();
}