<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */



$post_id    = get_the_ID();
$cat_ids    = array();
$categories = get_the_category( $post_id );

if ( ! empty( $categories ) && ! is_wp_error( $categories ) ) {
	foreach ( $categories as $category ) {
		array_push( $cat_ids, $category->term_id );
	}
}

$current_post_type = get_post_type( $post_id );
$query_args        = [
	'category__in'   => $cat_ids,
	'post_type'      => $current_post_type,
	'post__not_in'   => [ $post_id ],
	'posts_per_page' => '3',
];
$related_cats_post = new WP_Query( $query_args );
if ( $related_cats_post->have_posts() ) {
	echo '<div class="blog-content-box"><h3 class="inner-title mb-4">' . esc_html__( 'Related Posts', 'histudy' ) . '</h3></div>';
}
$items   =  '2';
$options = [
	'items' => $items,
];

?>

<div data-options='<?php echo wp_json_encode( $options ); ?>' class="swiper-container related-post-slider">
    <div class="swiper-wrapper">
		<?php $related_cats_post = new WP_Query( $query_args );
		if ( $related_cats_post->have_posts() ) {
			while ( $related_cats_post->have_posts() ): $related_cats_post->the_post();
				$view_count = get_post_meta( get_the_ID(), 'edhub_post_view', true ) ? get_post_meta( get_the_ID(), 'post_view', true ) : '0';
				$view_text  = sprintf( _n( '%s View', '%s Views', $view_count, 'histudy' ), number_format_i18n( $view_count ) );
				?>

                <div class="swiper-slide">
                    <div class="blog-box style-1">
                        <div class="figure-box">
                            <a href="<?php the_permalink(); ?>"><?php the_post_thumbnail(); ?></a>
                        </div>
                        <div class="content-box">
                            <div class="category-name"><?php echo the_category( ', ' ); ?></div>
							<?php the_title( sprintf( '<h3 class="title"><a href="%s" rel="bookmark">', esc_attr( esc_url( get_permalink() ) ) ), '</a></h3>' ); ?>
                            <ul class="entry-meta inline-list">
                                <li><i class="fas fa-calendar-alt"></i><?php echo get_the_date(); ?></li>
                                <li><i class="far fa-eye"></i><?php echo esc_html( $view_text ); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>

			<?php endwhile;
			wp_reset_postdata();
		} ?>
    </div>
</div>