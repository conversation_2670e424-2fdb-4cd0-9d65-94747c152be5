<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$sidebar = Rainbow_Helper::rainbow_sidebar_options();
$rainbow_single_blog_sidebar_class = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? ' col-lg-12' : ' col-lg-7';
$alignwide = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? 'wp-block-image alignwide' : '';
$thumb_size = 'rainbow-thumbnail-single';
$author_name = rainbow_get_acf_data("rainbow_quote_author_name"); 
$designation = rainbow_get_acf_data("rainbow_quote_author_name_designation"); 
$quote_txt = rainbow_get_acf_data("rainbow_quote_txt"); 
?>
<div class="content">   
    <!-- End .content -->
    <?php if(!empty( $quote_txt ) ) : ?>
    <blockquote class="rbt-blockquote mt--0 alignwide square rbt-border-none bg-color-gray-light">
            <p><?php echo esc_html( $quote_txt ); ?></p>
        </blockquote>
    <?php endif; ?>
    <!-- End .thumbnail --> 
    <div class="rainbow-post-content-wrapper">
        <?php 
        the_content(); 
        wp_link_pages(array(
            'before' => '<div class="rainbow-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'histudy') . '</span>',
            'after' => '</div>',
            'link_before' => '<span>',
            'link_after' => '</span>',
        ));
        ?>
    </div>  
    <?php get_template_part('template-parts/single-post/biography');?> 
</div> 
<?php if(true == $rainbow_options['show_related_post']){ 
    histudy_related_post_grid();
}