<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$sidebar = Rainbow_Helper::rainbow_sidebar_options();
$rainbow_single_blog_sidebar_class = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? ' col-lg-12' : ' col-lg-7';
$alignwide = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? 'wp-block-image alignwide' : '';
$images = rainbow_get_acf_data('rainbow_gallery_image');
$thumb_size = 'rainbow-thumbnail-single';
?> 
<div class="content">    
   <?php if ($images) { ?> 
    <div class="post-thumbnail mb--30 position-relative wp-block-image blog-post-gallery-wrapper alignwide">
        <div class="swiper rbt-arrow-between blog-post-gallery-activation"> 
             <div class="swiper-wrapper">
            <?php foreach ($images as $image): ?> 
                <div class="swiper-slide">
                    <figure>
                       <img class="w-100" src="<?php echo esc_url($image['sizes'][$thumb_size]); ?>"
                         alt="<?php echo esc_attr($image['alt']); ?>"/>
                    </figure>
                </div> 
            <?php endforeach; ?>
        </div>
            <div class="rbt-swiper-arrow rbt-arrow-left">
                <div class="custom-overfolow">
                    <i class="rbt-icon feather-arrow-left"></i>
                    <i class="rbt-icon-top feather-arrow-left"></i>
                </div>
            </div> 
            <div class="rbt-swiper-arrow rbt-arrow-right">
                <div class="custom-overfolow">
                    <i class="rbt-icon feather-arrow-right"></i>
                    <i class="rbt-icon-top feather-arrow-right"></i>
                </div>
            </div>
        </div>
    </div>
    <?php } else {
        if (has_post_thumbnail()) { ?> 
            <div class="post-thumbnail">
                <?php the_post_thumbnail($thumb_size, ['class' => ' ']) ?>
            </div> 
        <?php }
    } ?> 

    <div class="rainbow-post-content-wrapper">
        <?php 
        the_content(); 
        wp_link_pages(array(
            'before' => '<div class="rainbow-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'histudy') . '</span>',
            'after' => '</div>',
            'link_before' => '<span>',
            'link_after' => '</span>',
        ));
        ?>
    </div>  
    <?php
        $post_tags = get_the_tags();
        if ( $rainbow_options['rainbow_show_blog_details_tags_meta'] !== 'no' && $post_tags ) { ?>
            <div class="tagcloud">
                <?php foreach( $post_tags as $tag ) { ?>
                    <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>"><?php echo esc_html($tag->name); ?></a>
                <?php } ?>
            </div>
        <?php } ?> 


        <?php
        if ($rainbow_options['rainbow_show_post_tags_meta']) {
            get_template_part('template-parts/single-post/social-share');
        }
        ?> 
        <?php
        if ($rainbow_options['rainbow_show_post_author_meta']) {
            get_template_part('template-parts/single-post/biography');
        }
        ?> 
        <?php   
        /**
         *  Output comments wrapper if it's a post, or if comments are open,
         * or if there's a comment number – and check for password.
         * */
        if ((is_single() || is_page()) && (comments_open() || get_comments_number()) && !post_password_required()) {
            ?>
            <div class="rainbow-comment-area">  
                    <?php comments_template(); ?>  
            </div><!-- .comments-wrapper --> 
            <?php
        }
    ?> 
</div> 
<?php if(true == $rainbow_options['show_related_post']){ 
    histudy_related_post_grid();
}