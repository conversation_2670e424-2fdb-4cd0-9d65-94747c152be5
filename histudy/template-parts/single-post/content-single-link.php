<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$sidebar = Rainbow_Helper::rainbow_sidebar_options();
$rainbow_single_blog_sidebar_class = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? ' col-lg-12' : ' col-lg-7';
$alignwide = ($sidebar === 'full') || !is_active_sidebar('sidebar-1') ? 'wp-block-image alignwide' : '';
$thumb_size = 'rainbow-thumbnail-single';
$audio_url = rainbow_get_acf_data('rainbow_upload_audio'); 
?>
<div class="content">   
<?php if ($audio_url) { ?> 
    <div class="mb--60 audio-player-wrapper">
        <audio controls>
            <source src="<?php echo esc_url($audio_url['url']); ?>" type="audio/ogg">
            <source src="<?php echo esc_url($audio_url['url']); ?>" type="audio/mpeg">
            <?php esc_html_e('Your browser does not support the audio tag.', 'histudy'); ?>
        </audio>
    </div>
    <?php if (has_post_thumbnail()) { ?>
        <div class="post-thumbnail">
            <?php the_post_thumbnail($thumb_size, ['class' => ' ']) ?>
        </div>
    <?php } ?>
<?php } ?>

    <div class="rainbow-post-content-wrapper">
        <?php 
        the_content(); 
        wp_link_pages(array(
            'before' => '<div class="rainbow-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'histudy') . '</span>',
            'after' => '</div>',
            'link_before' => '<span>',
            'link_after' => '</span>',
        ));
        ?>
    </div>  
    <?php get_template_part('template-parts/single-post/biography');?> 
</div> 
<?php if(true == $rainbow_options['show_related_post']){ 
    histudy_related_post_grid();
}