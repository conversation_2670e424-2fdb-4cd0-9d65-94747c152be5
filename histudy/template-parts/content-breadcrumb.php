
<?php 
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    
    $rainbow_event_details_brd_image = '';
    $rainbow_event_details_brd_color = '';
    $rainbow_event_details_gradient_color = '';
    if ( get_post_type() === 'course_event' && is_singular('course_event') ) {
        $rainbow_event_details_brd_image = isset( $rainbow_options['rainbow_event_details_brd_image']['background-image'] ) ? $rainbow_options['rainbow_event_details_brd_image']['background-image'] : '';
        $rainbow_event_details_brd_color = isset( $rainbow_options['rainbow_event_details_brd_image']['background-color'] ) ? $rainbow_options['rainbow_event_details_brd_image']['background-color'] : '';
        $rainbow_event_details_gradient_color = isset( $rainbow_options['rainbow_event_details_gradient_color'] ) ? $rainbow_options['rainbow_event_details_gradient_color'] : '';
    }
?>
<div class="rbt-breadcrumb-default rbt-breadcrumb-style-3">
    <div class="breadcrumb-inner">
        <?php if(!empty( $rainbow_event_details_brd_image ) ) { ?>
        <img src="<?php echo esc_url($rainbow_event_details_brd_image); ?>" alt="<?php echo esc_attr__( 'Education Images', 'histudy' ); ?>">
        <?php } else { 
            if(empty($rainbow_event_details_brd_color ) && empty($rainbow_event_details_gradient_color ) ) {
            ?>
            <img src="<?php echo esc_url(get_template_directory_uri(). '/assets/images/bg/bg-image-10.jpg'); ?>" alt="<?php echo esc_attr__( 'Education Images', 'histudy' ); ?>">
        <?php } } ?>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="content">
                    <div class="content text-start">
                        <?php rainbow_breadcrumbs();?>
                        <h2 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h2>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>