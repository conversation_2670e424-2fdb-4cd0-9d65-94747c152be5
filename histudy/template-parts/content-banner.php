<?php
/**
 * Template part for displaying page banner style one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package histudy
 */

// Get Value
if( is_post_type_archive('course_event') || (('course_event' == get_post_type()) && is_singular()) ) {
    return;
}

if( ('course-bundle' == get_post_type()) && is_singular() ) {
    return;
}

if( class_exists( 'LearnPress' ) ) return;

$rainbow_layout_tutor_settings                    = Rainbow_Helper::rainbow_layout_tutor_settings();
$rainbow_tutor_archive_banner_layout              = $rainbow_layout_tutor_settings['rainbow_tutor_archive_banner_layout'];

if( ('layout-1' == $rainbow_tutor_archive_banner_layout ) && 'courses' == get_post_type() && !is_tax('course-tag') && !is_tax('course-category') ) {
    return;
}

if (
    ('layout-1' == $rainbow_tutor_archive_banner_layout ) &&
    (
        ('courses' == get_post_type() && !is_tax('course-tag') && !is_tax('course-category')) ||
        ('academy_courses' == get_post_type() && !is_tax('academy_courses_tag') && !is_tax('academy_courses_category'))
    )
) {
    return;
}


$rainbow_options  = Rainbow_Helper::rainbow_get_options();
$shop_layout = Rainbow_Helper::rainbow_shop_banner_layout();
$single_product_banner_layout = isset( $shop_layout['single_product_banner_layout'] ) ? sanitize_text_field( $shop_layout['single_product_banner_layout'] ): '';

$rainbow_banner_enable = isset( $shop_layout['banner_area'] ) ? $shop_layout['banner_area']: '';
$rainbow_tutor_archive_banner_layout = (isset($rainbow_options['rainbow_tutor_archive_banner_layout']) && !empty($rainbow_options['rainbow_tutor_archive_banner_layout'])) ? $rainbow_options['rainbow_tutor_archive_banner_layout']: 'layout-1';
$rainbow_page_breadrumb_layout = isset( $rainbow_options['rainbow_page_breadrumb_layout'] ) ? $rainbow_options['rainbow_page_breadrumb_layout']: 1;
$page_breadcrumb           = Rainbow_Helper::rainbow_page_breadcrumb();
$page_breadcrumb_enable    = $page_breadcrumb['breadcrumbs'];
$allowed_tags              = wp_kses_allowed_html('post');
$banner_layout             = Rainbow_Helper::rainbow_banner_layout();
$sub_title                 = $banner_layout['sub_title'];
$banner_style              = $banner_layout['banner_style'];
$banner_area               = $banner_layout['banner_area'];
$rainbow_select_banner_style = function_exists( 'get_field' ) ? get_field('rainbow_select_banner_style'): '';
if( $rainbow_select_banner_style ) {
    switch( $rainbow_select_banner_style ) {
        case 1:
            $rainbow_page_breadrumb_layout = 'layout-1';
            break;
        case 2:
            $rainbow_page_breadrumb_layout = 'layout-2';
            break;
    }
}

// show banner for shop
if( WOOC_WOO_ACTIVED && is_shop() || WOOC_WOO_ACTIVED && is_archive() && 'product' == get_post_type() ) {
    $banner_area = 'yes';
}

$banner_layout = Rainbow_Helper::rainbow_blog_banner_layout();
$banner_enable = $banner_layout['banner_area'];
/**
 * Shop banner attributes
 */
$shop_banner_layout      = Rainbow_Helper::rainbow_shop_banner_layout();
$shop_page_layout         = $shop_banner_layout['template'];
$single_product_banner_layout         = $shop_banner_layout['single_product_banner_layout'];
$shop_page_url = class_exists('WooCommerce') ? wc_get_page_permalink( 'shop' ): null;

if('no' == $page_breadcrumb_enable) {
    return;
}

$col_class = 'col-lg-12';
$rainbow_enable_shop_banner_overlap = isset($rainbow_options['rainbow_enable_shop_banner_overlap']) ? sanitize_text_field( $rainbow_options['rainbow_enable_shop_banner_overlap'] ): '';
if('no' == $rainbow_enable_shop_banner_overlap) {
    $banner_shop_archive_class = 'rb-banner-no-shop-archive-overlap';
    $banner_shop_overlay_default_class = '';
} else {
    $banner_shop_overlay_default_class = 'rainbow-shop-overlap-class';
    $banner_shop_archive_class = 'rbt-banner-shop-archive';
}


if (
    ( is_post_type_archive('courses') || is_post_type_archive('academy_courses') ) ||
    ( is_tax('course-category') || is_tax('course-tag') || is_tax('academy_courses_category') || is_tax('academy_courses_tag') )
) {
    if( !is_search() ) {
        if( 'layout-3' == $rainbow_tutor_archive_banner_layout ) {
            get_template_part( 'template-parts/components/page-banners/course/layout', '3' );
            return;
        } elseif( 'layout-2' == $rainbow_tutor_archive_banner_layout ) {
            get_template_part( 'template-parts/components/page-banners/course/layout', '2' );
            return;
        } else {
            return;
        }
    }
}

if( 'blog-list' == get_query_var( 'blog_layout' ) ) {
    $col_class = 'col-lg-10 offset-lg-1 histudy-post-wrapper';

}

if( 'post' == get_post_type() && !is_single() && !is_front_page() ) {
    if( 'yes' == $banner_enable ) {
        ob_start();
        get_template_part( 'template-parts/content-blog-banner' );
        echo ob_get_clean();
    }
    return;
}
$count = Rainbow_Helper::get_total_product_count();
?>


<?php if ('yes' === $banner_area) {?>
    <?php if (WOOC_WOO_ACTIVED && is_shop() || WOOC_WOO_ACTIVED && is_archive() && 'product' == get_post_type() ) {
    if( 'yes' != $rainbow_banner_enable ) {
        return;
    }
    ?>
    <?php if( 2 == $shop_page_layout ) : ?>
    <div class="rbt-page-banner-wrapper <?php echo esc_attr($banner_shop_archive_class); ?>">
        <!-- Start Banner BG Image  -->
        <div class="rbt-banner-image"></div>
                <!-- Start Banner Content Top  -->
                <div class="rbt-banner-content-top">
                    <div class="container">
                        <div class="row">
                            <div class="<?php echo esc_attr( $col_class ); ?>">
                                <!-- Start Breadcrumb Area  -->
                                <?php rainbow_breadcrumbs(); ?>
                                <!-- End Breadcrumb Area  -->
                                <div class=" title-wrapper">
                                    <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                                    <?php if(!empty($shop_page_url)) : ?>
                                    <a href="<?php echo esc_url($shop_page_url); ?>" class="rbt-badge-2">
                                        <div class="image">🎉</div>
                                        <?php echo sprintf( _n( '%s Product', '%s Products', $count, 'histudy' ), $count ); ?>
                                    </a>
                                    <?php endif; ?>
                                </div>
                                <?php  if ( !empty($sub_title) ) { ?>
                                    <p class="description"><?php echo esc_html( $sub_title ); ?> </p>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Banner Content Top  -->
        </div>
    <?php else: ?>
        <div class="rbt-breadcrumb-default rbt-section-gap bg-gradient-1 <?php echo esc_attr( $banner_shop_overlay_default_class ); ?>">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="breadcrumb-inner text-center">
                            <h2 class="title"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h2>
                            <?php rainbow_breadcrumbs();?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php } elseif(TUTOR_ACTIVED && (tutor_utils()->is_tutor_frontend_dashboard())) {
        if( !is_user_logged_in(  ) ) {?>
            <div class="rbt-breadcrumb-default rbt-section-gap bg-gradient-1">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="breadcrumb-inner text-center">
                                <h2 class="title"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h2>
                                <?php rainbow_breadcrumbs();?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php return; }
        ?>
        <div class="rbt-page-banner-wrapper">
            <!-- Start Banner BG Image  -->
            <div class="rbt-banner-image"></div>
            <!-- End Banner BG Image  -->
        </div>
    <?php return;
    } else {
        ?>
        <?php if( 'layout-2' == $rainbow_page_breadrumb_layout ) : ?>
            <div class="rbt-page-banner-wrapper rbt-page-gradient-breadcrumb">
                <!-- Start Banner BG Image  -->
                <div class="rbt-banner-image"></div>
                <!-- End Banner BG Image  -->
                <div class="rbt-banner-content">
                    <!-- Start Banner Content Top  -->
                    <div class="rbt-banner-content-top">
                        <div class="container">
                            <div class="row">
                                <div class="col-12">
                                    <!-- Start Breadcrumb Area  -->
                                    <?php rainbow_breadcrumbs(); ?>
                                    <!-- End Breadcrumb Area  -->

                                    <div class="title-wrapper">
                                        <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Banner Content Top  -->
                </div>
            </div>
        <?php else: ?>
            <div class="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1 ">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="breadcrumb-inner text-center">
                                <h2 class="title"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h2>
                                <?php rainbow_breadcrumbs();?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php } ?>
<?php }