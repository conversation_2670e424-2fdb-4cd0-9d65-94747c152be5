<?php
    global $rainbow_options;
    $enable_breadcrumb    = $rainbow_options['generic_banner_breadcrumb_enable'];
    $enable_title         = $rainbow_options['generic_banner_title_enable'];
    $enable_badge         = $rainbow_options['generic_banner_badge_enable'];
    $badge_label_singular = $rainbow_options['generic_banner_badge_text_singular'];
    $badge_label_plural   = $rainbow_options['generic_banner_badge_text_plural'];
    $banner_subtitle      = $rainbow_options['generic_banner_subtitle'];
    $enable_subtitle = $rainbow_options['generic_banner_subtitle_enable'];
    $enable_grid_list_filter = $rainbow_options['generic_banner_layout_filter_enable'];

    $count = histudy_get_total_course_count(); 

    // Determine the singular or plural label based on the count
    $badge_label = ( $count == 1 ) ? $badge_label_singular : $badge_label_plural;
?>
<div class="rbt-page-banner-wrapper page-banner-layout-style-two-learnpress">
    <!-- Start Banner BG Image  -->
    <div class="rbt-banner-image"></div>
    <!-- End Banner BG Image  -->
    <div class="rbt-banner-content">
        <!-- Start Banner Content Top  -->
        <div class="rbt-banner-content-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <?php if(!empty($enable_breadcrumb)) : ?>
                        <!-- Start Breadcrumb Area  -->
                         <?php rainbow_lms_breadcrumb(); ?>
                        <!-- End Breadcrumb Area  -->
                         <?php endif; ?>
                        <div class=" title-wrapper">
                            <?php if(!empty($enable_title)) : ?>
                                <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                            <?php endif; ?>
                            <?php if(!empty($enable_badge)) : ?>
                            <a href="#" class="rbt-badge-2">
                                <div class="image">🎉</div> <?php echo sprintf( '%s %s', number_format_i18n( $count ), esc_html( $badge_label ) );  ?>
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($banner_subtitle) && !empty($enable_subtitle)) : ?>
                            <p class="description"><?php echo esc_html($banner_subtitle); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Banner Content Top  -->
        <!-- Start Course Top  -->
        <div class="rbt-course-top-wrapper mt--40 mt_sm--20 rbt-generic-banner-course-filter-banner">
            <div class="container">
            <?php if(class_exists( 'LearnPress' )) : ?>    
            <div class="rbt-lp-course-sidebar-filter">
            <button class="rbt-filter-toggle-btn rbt-btn btn-white btn-md radius-round"><?php echo esc_html__("Filter","histudy"); ?><i class="feather-filter"></i></button>
                <?php
                    if( is_active_sidebar( 'course-sidebar' ) ) {
                        dynamic_sidebar( 'course-sidebar' );
                    }
                ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <!-- End Course Top  -->

    </div>
</div>