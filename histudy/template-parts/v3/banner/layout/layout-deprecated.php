<?php
    global $rainbow_options;
    $enable_breadcrumb    = $rainbow_options['generic_banner_breadcrumb_enable'];
    $enable_title         = $rainbow_options['generic_banner_title_enable'];
    $enable_badge         = $rainbow_options['generic_banner_badge_enable'];
    $badge_label_singular = $rainbow_options['generic_banner_badge_text_singular'];
    $badge_label_plural   = $rainbow_options['generic_banner_badge_text_plural'];
    $banner_subtitle      = $rainbow_options['generic_banner_subtitle'];
    $enable_subtitle = $rainbow_options['generic_banner_subtitle_enable'];
    $enable_grid_list_filter = $rainbow_options['generic_banner_layout_filter_enable'];
?>
<div class="rbt-page-banner-wrapper">
    <!-- Start Banner BG Image  -->
    <div class="rbt-banner-image"></div>
    <!-- End Banner BG Image  -->
    <div class="rbt-banner-content">
        <!-- Start Banner Content Top  -->
        <div class="rbt-banner-content-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <?php if(!empty($enable_breadcrumb)) : ?>
                        <!-- Start Breadcrumb Area  -->
                         <?php rainbow_lms_breadcrumb(); ?>
                        <!-- End Breadcrumb Area  -->
                         <?php endif; ?>
                        <div class=" title-wrapper">
                            <?php if(!empty($enable_title)) : ?>
                                <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                            <?php endif; ?>
                            <?php if(!empty($enable_badge)) : ?>
                            <a href="#" class="rbt-badge-2">
                                <div class="image">🎉</div> <?php printf( _n( '%s %s', '%s %s', $histudy_get_total_course_count(), 'histudy' ), number_format_i18n( $count ), $badge_label_singular, histudy_get_total_course_count(), $badge_label_plural, histudy_get_total_course_count()  ); ?>
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($banner_subtitle) && !empty($enable_subtitle)) : ?>
                            <p class="description"><?php echo esc_html($banner_subtitle); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Banner Content Top  -->
        <!-- Start Course Top  -->
        <div class="rbt-course-top-wrapper mt--40 mt_sm--20 rbt-generic-banner-course-filter-banner">
            <div class="container">
                <div class="row g-5 align-items-center">

                    <div class="col-lg-5 col-md-12">
                        <div class="rbt-sorting-list d-flex flex-wrap align-items-center">
                            <?php if(!empty($enable_grid_list_filter)) : ?>
                            <div class="rbt-short-item switch-layout-container">
                                <ul class="course-switch-layout">
                                    <li class="course-switch-item"><button class="rbt-grid-view active" title="Grid Layout">
                                        <?php if(!empty($rainbow_options['generic_banner_grid_btn_icon'])) : ?>
                                            <i class="<?php echo esc_attr( $rainbow_options['generic_banner_grid_btn_icon'] ); ?>"></i> 
                                        <?php endif; ?>
                                        <?php if(!empty($rainbow_options['generic_banner_grid_btn_label'])) : ?>
                                            <span class="text"><?php echo esc_html($rainbow_options['generic_banner_grid_btn_label']); ?></span></button></li>
                                        <?php endif; ?>
                                    <li class="course-switch-item"><button class="rbt-list-view" title="List Layout">
                                        <?php if(!empty($rainbow_options['generic_banner_list_btn_icon'])) : ?>
                                        <i class="<?php echo esc_attr( $rainbow_options['generic_banner_list_btn_icon'] ); ?>"></i> 
                                        <?php endif; ?>
                                        <?php if(!empty($rainbow_options['generic_banner_list_btn_label'])) : ?>
                                            <span class="text"><?php echo esc_html($rainbow_options['generic_banner_list_btn_label']); ?></span></button></li>
                                        <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            <?php if( !empty($rainbow_options['generic_banner_enable_course_count_filter_description']) ) : ?>
                            <div class="rbt-short-item">
                                <span class="course-index">
                                    <?php if(!empty($rainbow_options['generic_banner_course_count_filter_label_start'])) : ?>
                                        <?php echo esc_html($rainbow_options['generic_banner_course_count_filter_label_start']); ?>
                                    <?php endif; ?>
                                     1-9 
                                     <?php if(!empty($rainbow_options['generic_banner_course_count_filter_label_middle'])) : ?>
                                        <?php echo esc_html($rainbow_options['generic_banner_course_count_filter_label_middle']); ?>
                                     <?php endif; ?>
                                      19 
                                      <?php if(!empty($rainbow_options['generic_banner_course_count_filter_label_end'])) : ?>
                                        <?php echo esc_html($rainbow_options['generic_banner_course_count_filter_label_end']); ?>
                                      <?php endif; ?>
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-lg-7 col-md-12">
                        <div class="rbt-sorting-list d-flex flex-wrap align-items-center justify-content-start justify-content-lg-end">
                            <?php if(!empty($rainbow_options['generic_banner_enable_front_serch'])) : ?>
                            <div class="rbt-short-item">
                                <form action="#" class="rbt-search-style me-0">
                                    <input type="text" placeholder="<?php echo esc_html($rainbow_options['generic_banner_front_search_label']); ?>">
                                    <button type="submit" class="rbt-search-btn rbt-round-btn">
                                        <i class="<?php echo esc_attr($rainbow_options['generic_banner_front_search_icon_feather']); ?>"></i>
                                    </button>
                                </form>
                            </div>
                            <?php endif; ?>
                            <?php if(!empty($rainbow_options['generic_banner_enable_filter_btn'])) : ?>
                            <div class="rbt-short-item">
                                <div class="view-more-btn text-start text-sm-end">
                                    <button class="discover-filter-button discover-filter-activation rbt-btn btn-white btn-md radius-round">
                                        <?php if(!empty($rainbow_options['generic_banner_filter_label'])) : ?>
                                            <?php echo esc_html( $rainbow_options['generic_banner_filter_label']); ?>
                                        <?php endif; ?>
                                        <?php if(!empty($rainbow_options['generic_banner_front_filter_icon_feather'])) : ?>
                                        <i class="<?php echo esc_html($rainbow_options['generic_banner_front_filter_icon_feather']); ?>"></i>
                                        <?php endif; ?>
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Start Filter Toggle  -->
                <div class="default-exp-wrapper default-exp-expand">
                    <div class="filter-inner">
                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_date'])) : ?>
                        <div class="filter-select-option">
                            <div class="filter-select rbt-modern-select">
                                <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_date_label'])) : ?>
                                    <span class="select-label d-block"><?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_date_label']) ?></span>
                                <?php endif; ?>
                                <select>
                                    <option>Default</option>
                                    <option>Latest</option>
                                    <option>Popularity</option>
                                    <option>Trending</option>
                                    <option>Price: low to high</option>
                                    <option>Price: high to low</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_author'])) : ?>
                        <div class="filter-select-option">
                            <div class="filter-select rbt-modern-select">
                                <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_author_label'])) : ?>
                                    <span class="select-label d-block"><?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_author_label']); ?></span>
                                <?php endif; ?>
                                <select data-live-search="true" title="Select Author" multiple data-size="7" data-actions-box="true" data-selected-text-format="count > 2">
                                    <option data-subtext="Experts">Janin Afsana</option>
                                    <option data-subtext="Experts">Joe Biden</option>
                                    <option data-subtext="Experts">Fatima Asrafy</option>
                                    <option data-subtext="Experts">Aysha Baby</option>
                                    <option data-subtext="Experts">Mohamad Ali</option>
                                    <option data-subtext="Experts">Jone Li</option>
                                    <option data-subtext="Experts">Alberd Roce</option>
                                    <option data-subtext="Experts">Zeliski Noor</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_offer'])) : ?>
                        <div class="filter-select-option">
                            <div class="filter-select rbt-modern-select">
                                <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_offer_label'])) : ?>
                                    <span class="select-label d-block"><?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_offer_label']); ?></span>
                                <?php endif; ?>
                                <select>
                                    <option>Free</option>
                                    <option>Paid</option>
                                    <option>Premium</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_category'])) : ?>
                        <div class="filter-select-option">
                            <div class="filter-select rbt-modern-select">
                                <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_category_label'])) : ?>
                                    <span class="select-label d-block"><?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_category_label']); ?></span>
                                <?php endif; ?>
                                <select data-live-search="true">
                                    <option>Web Design</option>
                                    <option>Graphic</option>
                                    <option>App Development</option>
                                    <option>Figma Design</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_price_range'])) : ?>
                        <div class="filter-select-option">
                            <div class="filter-select">
                                <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_price_range_label'])) : ?>
                                    <span class="select-label d-block"><?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_price_range_label']); ?></span>
                                <?php endif; ?>

                                <div class="price_filter s-filter clear">
                                    <form action="#" method="GET">
                                        <div id="slider-range"></div>
                                        <div class="slider__range--output">
                                            <div class="price__output--wrap">
                                                <div class="price--output">
                                                    <span>
                                                        <?php if(!empty($rainbow_options['generic_banner_enable_sort_by_price_before_price'])) : ?>
                                                        <?php echo esc_html($rainbow_options['generic_banner_enable_sort_by_price_before_price']); ?> :
                                                        <?php endif; ?>
                                                    </span><input type="text" id="amount">
                                                </div>
                                                <?php if(!empty($rainbow_options['generic_banner_sort_by_price_filter_btn_label'])) : ?>
                                                <div class="price--filter">
                                                    <a class="rbt-btn btn-gradient btn-sm" href="#"><?php echo esc_html( $rainbow_options['generic_banner_sort_by_price_filter_btn_label'] ); ?></a>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <!-- End Filter Toggle  -->
            </div>
        </div>
        <!-- End Course Top  -->
    </div>
</div>