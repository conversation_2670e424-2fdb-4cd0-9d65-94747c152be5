<?php
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $banner_layout = isset( $rainbow_options['generice_course_archive_banner_layout'] ) ? $rainbow_options['generice_course_archive_banner_layout'] : '';
    $generic_banner_breadcrumb_enable = isset( $rainbow_options['generic_banner_breadcrumb_enable'] ) ? $rainbow_options['generic_banner_breadcrumb_enable'] : ' ';
    do_action( 'rbt_before_archive_banner' );

    if( $banner_layout == 'layout-1' && $generic_banner_breadcrumb_enable == '1') {
        get_template_part( 'template-parts/v3/banner/layout/'. $banner_layout );
    }
    do_action( 'rbt_after_archive_banner' );