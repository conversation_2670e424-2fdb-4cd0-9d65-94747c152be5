<?php
$image_size = isset($rainbow_options['rainbow_blog_default_img_size']) ? sanitize_text_field( $rainbow_options['rainbow_blog_default_img_size'] ): 'full';
$args = array(
    'image_size' => $image_size
);
?>
<div class="row">
    <?php
        while(have_posts()) : the_post();
            /**
             * If featured post not enabled
             * 
             * Then load in default style
             */
            ob_start();
                echo "<div class='col-12'>";
                    echo '<div class="mt--30">';
                        get_template_part( 'template-parts/components/blog-card/card', 'layout', $args );
                    echo '</div>';
                echo '</div>';
            echo ob_get_clean();
        endwhile;
    ?>
</div>