<?php
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $blog_grid_enable_featured_blog = isset($rainbow_options['blog_grid_enable_featured_blog']) ? $rainbow_options['blog_grid_enable_featured_blog']: false;
    $blog_grid_select_col = isset($rainbow_options['blog_grid_select_col']) ? absint( $rainbow_options['blog_grid_select_col'] ): 1;
    $col_class = '';
    $row_class = 'row';
    $rainbow_blog_sidebar =  $rainbow_options['rainbow_blog_sidebar'];
    if( sanitize_text_field( get_query_var( 'blog_sidebar' ) ) ) {
        $rainbow_blog_sidebar = sanitize_text_field( get_query_var( 'blog_sidebar' ) );
    }
    $rainbow_blog_sidebar_class = (sanitize_text_field( $rainbow_options['rainbow_blog_sidebar'] ) === 'no') || !is_active_sidebar('sidebar-1') ? 'col-12 histudy-post-wrapper' : 'col-lg-8 histudy-post-wrapper';
    $featured_blog = ('' !== get_query_var( 'featured_blog' )) ? sanitize_text_field( get_query_var( 'featured_blog' ) ): null;
    $blog_column = sanitize_text_field( get_query_var( 'blog_column' ) );
    if( isset($featured_blog) ) {
        $blog_grid_enable_featured_blog = sanitize_text_field( $featured_blog );
    }
    if( ( isset( $blog_column ) && !empty( $blog_column ) ) ) {
        $blog_grid_select_col = sanitize_text_field( $blog_column );
    } else {
        $blog_grid_select_col = $rainbow_options['blog_grid_select_col'];
    }
    if( ('blog-grid' == get_query_var( 'blog_layout' )) && ( 'no' == get_query_var( 'blog_sidebar' ) ) ) {
        $rainbow_blog_sidebar_class = 'col-12 histudy-post-wrapper';
        $row_class = 'row g-5';
    }
    if( 'blog-grid' == get_query_var( 'blog_layout') ) {
        $row_class = 'row g-5';
    }
    if ('blog-grid' == get_query_var( 'blog_layout')   &&  'card-boxed' == get_query_var( 'card_layout'  )) {
    
        $row_class = 'row g-5 mt--15';
    }
    $image_size = isset($rainbow_options['rainbow_blog_grid_img_size']) ? sanitize_text_field( $rainbow_options['rainbow_blog_grid_img_size'] ): 'full';
    /**
     * Create a column class based on selected column from redux customizer
     */
    switch( $blog_grid_select_col ) {
        case 1:
            $col_class = 'col-12';
            break;
        case 2:
            $col_class = 'col-lg-6 col-md-6 col-12';
            break;
        case 3:
            $col_class = 'col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        case 4:
            $col_class = 'col-xl-3 col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        default:
            $col_class = 'col-12';
            break;
    }
?>
<?php
    $post_count = 1;
    /**
     * Load featured post if enabled
     * 
     * @since 1.0.0
     */
    if( $blog_grid_enable_featured_blog ) { ?>
        <div class="has-rainbow-grid-featured-blog-enabled">
            <div class="row g-5">
                <!-- Start Single Card  -->
                <div class="col-lg-6 col-md-12 col-sm-12 col-12 sal-animate" data-sal-delay="150" data-sal="slide-up" data-sal-duration="800">
                    <?php 
                        $post_count = 1;
                        while( have_posts() ) {
                            the_post();
                            if( $post_count == 1 ) {
                                echo '<div class="rbt-blog-grid-featured-img">';
                                get_template_part( 'template-parts/components/blog-card/card', 'full-image' );
                                echo '</div>';
                            }
                            $post_count++;
                        }
                        unset( $post_count );
                    ?>
                </div>
                <!-- End Single Card  -->

                <div class="col-lg-6 col-md-12 col-sm-12 col-12 sal-animate" data-sal-delay="150" data-sal="slide-up" data-sal-duration="800">
                    <?php 
                        $post_count = 1;
                        while( have_posts() ) {
                            the_post();
                            if( $post_count >= 2 && $post_count <= 4 ) {
                                echo '<div class="histudy-blog-featured-right-list">';
                                    get_template_part( 'template-parts/components/blog-card/card', 'list' , array( 'image_size' => $image_size ) );
                                echo '</div>';
                            }
                            $post_count++;
                        }
                        unset( $post_count );
                    ?>
                </div>
            </div>
        </div>
        <?php
        ob_start();
            $post_count = 1;
            echo '<div class="'.esc_attr( $row_class ).'">';
                // left sidebar
                if( is_active_sidebar('sidebar-1') && $rainbow_blog_sidebar == 'left' ) {
                    echo '<div class="col-lg-4">';
                        echo '<aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">';
                            dynamic_sidebar();
                        echo '</aside>';
                    echo '</div>';
                }
                echo "<div class='$rainbow_blog_sidebar_class'>";
                    echo '<div class="row g-5">';
                        while(have_posts()) {
                            the_post();
                            if( $post_count > 4 ) {
                                echo "<div class='$col_class'>";
                                    get_template_part( 'template-parts/components/blog-card/card', 'layout' , array( 'image_size' => $image_size ) );
                                echo '</div>';
                            }
                            $post_count++;
                        }
                    echo '</div>';
                echo '</div>';
                // right sidebar
                if( is_active_sidebar('sidebar-1') && $rainbow_blog_sidebar == 'right' ) {
                    echo '<div class="col-lg-4">';
                        echo '<aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">';
                            dynamic_sidebar();
                        echo '</aside>';
                    echo '</div>';
                }
            echo '</div>';
        echo ob_get_clean();
        ?>
    <?php } else {
        /**
         * If featured post not enabled
         * 
         * Then load in default style
         */
        ob_start();
            echo '<div class="row g-5">';
                // left sidebar
                if( is_active_sidebar('sidebar-1') && $rainbow_options['rainbow_blog_sidebar'] == 'left' ) {
                    echo '<div class="col-lg-4">';
                        echo '<aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">';
                            dynamic_sidebar();
                        echo '</aside>';
                    echo '</div>';
                }
                echo "<div class='$rainbow_blog_sidebar_class'>";
                    echo '<div class="row g-5">';
                        while(have_posts()) : the_post();
                            echo "<div class='$col_class'>";
                                get_template_part( 'template-parts/components/blog-card/card', 'layout' , array( 'image_size' => $image_size ) );
                            echo '</div>';
                        endwhile;
                    echo '</div>';
                echo '</div>';
                // right sidebar
                if( is_active_sidebar('sidebar-1') && $rainbow_options['rainbow_blog_sidebar'] == 'right' ) {
                    echo '<div class="col-lg-4">';
                        echo '<aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">';
                            dynamic_sidebar();
                        echo '</aside>';
                    echo '</div>';
                }
            echo '</div>';
        echo ob_get_clean();
    }
    // count post by 1
    $post_count++;