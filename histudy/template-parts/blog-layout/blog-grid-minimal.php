<?php
    global $rainbow_options;
    $blog_grid_minimal_select_col = isset($rainbow_options['blog_grid_minimal_select_col']) ? absint( $rainbow_options['blog_grid_minimal_select_col'] ): 1;
    $col_class = '';
    $featured_blog = ('' !== get_query_var( 'featured_blog' )) ? sanitize_text_field( get_query_var( 'featured_blog' ) ): null;
    $blog_column = sanitize_text_field( get_query_var( 'blog_column' ) );
    if( ( isset( $blog_column ) && !empty( $blog_column ) ) ) {
        $blog_grid_minimal_select_col = sanitize_text_field( $blog_column );
    } else {
        $blog_grid_minimal_select_col = $rainbow_options['blog_grid_minimal_select_col'];
    }
    $image_size = isset($rainbow_options['rainbow_blog_grid_minimal_img_size']) ? sanitize_text_field( $rainbow_options['rainbow_blog_grid_minimal_img_size'] ): 'full';
    $args = array(
        'image_size' => $image_size
    );
    /**
     * Create a column class based on selected column from redux customizer
     */
    switch( $blog_grid_minimal_select_col ) {
        case 1:
            $col_class = 'col-12';
            break;
        case 2:
            $col_class = 'col-lg-6 col-md-6 col-12';
            break;
        case 3:
            $col_class = 'col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        case 4:
            $col_class = 'col-xl-3 col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        default:
            $col_class = 'col-12';
            break;
    }
?>
<div class="row g-5">
    <?php
        /**
         * If featured post not enabled
         * 
         * Then load in default style
        */
        ob_start();
            while(have_posts()) {
                the_post();
                echo "<div class='$col_class'>";
                    get_template_part( 'template-parts/components/blog-card/card', 'layout', $args );
                echo '</div>';
            }
        echo ob_get_clean();
    ?>
</div>