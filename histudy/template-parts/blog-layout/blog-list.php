<?php
    global $rainbow_options;
    $blog_list_enable_featured_blog = isset($rainbow_options['blog_list_enable_featured_blog']) ? $rainbow_options['blog_list_enable_featured_blog']: false;
    $blog_list_select_col = isset($rainbow_options['blog_list_select_col']) ? absint( $rainbow_options['blog_list_select_col'] ): 1;
    $image_size = isset($rainbow_options['rainbow_blog_list_img_size']) ? sanitize_text_field( $rainbow_options['rainbow_blog_list_img_size'] ): 'full';
    /**
     * Check if exists in url
     */
    $featured_blog = ('' !== get_query_var( 'featured_blog' )) ? sanitize_text_field( get_query_var( 'featured_blog' ) ): null;
    $blog_column = sanitize_text_field( get_query_var( 'blog_column' ) );
    if( isset($featured_blog) && !empty( $featured_blog ) ) {
        $blog_list_enable_featured_blog = sanitize_text_field( $featured_blog );
    }
    if( isset($blog_column) && !empty( $blog_column ) ) {
        $blog_list_select_col = $blog_column;
    } else {
        $blog_list_select_col = $rainbow_options['blog_list_select_col'];
    }
    $col_class = '';
    /**
     * Create a column class based on selected column from redux customizer
     */
    
    switch( $blog_list_select_col ) {
        case 1:
            $col_class = 'col-12';
            break;
        case 2:
            $col_class = 'col-lg-6 col-md-6 col-12';
            break;
        case 3:
            $col_class = 'col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        case 4:
            $col_class = 'col-xl-3 col-lg-4 col-md-6 col-sm-12 col-12';
            break;
        default:
            $col_class = 'col-12';
            break;
    }
?>
<div class="row g-5">
    <?php
        $post_count = 1;
        while(have_posts()) : the_post();
        
            /**
             * Load featured post if enabled
             * 
             * @since 1.0.0
             */
            if( $blog_list_enable_featured_blog ) {
                if( 1 == $post_count ) {
                    ob_start();
                        echo '<div class="col-12">';
                            get_template_part( 'template-parts/components/blog-card/card', 'full-image' );
                        echo '</div>';
                    echo ob_get_clean();
                } else {
                    /**
                     * Load other posts
                     * 
                     * @since 1.0.0
                     */
                    ob_start();
                        echo "<div class='$col_class'>";
                            if( 2 == $blog_list_select_col ) {
                                echo '<div class="rainbow-blog-card-list-2-col">';
                                    get_template_part( 'template-parts/components/blog-card/card', 'layout' );
                                echo '</div>';
                            } else {
                                $args = array(
                                    'image_size' => $image_size
                                );
                                get_template_part( 'template-parts/components/blog-card/card', 'layout', $args );
                            }
                        echo '</div>';
                    echo ob_get_clean();
                }
            } else {
                /**
                 * If featured post not enabled
                 * 
                 * Then load in default style
                 */
                ob_start();
                    echo "<div class='$col_class'>";
                        echo '<div class="mt--30">';
                            if( 2 == $blog_list_select_col ) {
                                echo '<div class="rainbow-blog-card-list-2-col">';
                                    get_template_part( 'template-parts/components/blog-card/card', 'layout', array( 'image_size' => $image_size ) );
                                echo '</div>';
                            } else {
                                get_template_part( 'template-parts/components/blog-card/card', 'layout', array( 'image_size' => $image_size ) );
                            }
                        echo '</div>';
                    echo '</div>';
                echo ob_get_clean();
            }
            // count post by 1
            $post_count++;
        endwhile;
    ?>
</div>