<?php
/**
 * This partial is used for displaying the Breadcrumbs
 *
 * @package histudy
 *
 * Add this to any template file by calling rainbow_breadcrumbs()
 */
/**
 * Breadcrumb
 */
if(!function_exists('rainbow_breadcrumbs')){
    function rainbow_breadcrumbs()
    {

        $rainbow_option = rainbow_get_opt();
        $allowed_tags = wp_kses_allowed_html('post');

        /**
         * Settings
         */
        $separator = '<div class="icon-right"><i class="feather-chevron-right"></i></div>';
        $breadcrums_id = 'breadcrumbs';
        $breadcrums_class = 'page-list';
        $home_title = esc_html__('Home', 'histudy');

        $term_tv = get_term_by('slug', get_query_var('term'), get_query_var('taxonomy'));

        // If you have any custom post types with custom taxonomies, put the taxonomy name below (e.g. product_cat)
        $custom_taxonomy = '';

        $allowed_tags = wp_kses_allowed_html('post');

        // Get the query & post information
        global $post, $wp_query;
        // Do not display on the homepage
        if (!is_front_page()) {
            // Build the breadcrums
            echo '<ul id="' . esc_attr( $breadcrums_id ) . '" class="' . esc_attr( $breadcrums_class ) . '">';

            // Home page
            echo '<li class="item-home"><a class="histudy-breadcrumb-item bread-link bread-home" href="' . esc_url(get_home_url()) . '" title="' . esc_attr( $home_title ) . '">' . esc_html( $home_title ) . '</a></li>';
            echo '<li class="separator separator-home"> ' . wp_kses($separator, $allowed_tags) . ' </li>';

            if (is_archive() && !is_tax() && !is_category() && !is_tag()) {

                echo '<li class="histudy-breadcrumb-item active item-current item-archive"><span class="bread-current bread-archive">' . wp_title('', '') . '</span></li>';

            } else if(is_archive()) {
                echo '<li class="histudy-breadcrumb-item active item-current item-archive"><span class="bread-current bread-archive">' . get_post_type() . ' > ' .get_the_archive_title() . '</span></li>';
            } else if( 'courses' == get_post_Type() && is_single() ) {
                echo '<li class="histudy-breadcrumb-item item-current item-archive"><a href='.get_post_type_archive_link( 'courses' ).' class="histudy-breadcrumb-item">'.__('Courses', 'histudy').'</a><i class="feather-chevron-right"></i><span class="bread-current bread-archive"> ' . get_the_title() . '</span></li>';
            }
            else if( 'lp_course' == get_post_Type() && is_single() ) {
                echo '<li class="histudy-breadcrumb-item item-current item-archive"><a href='.get_post_type_archive_link( 'lp_course' ).' class="histudy-breadcrumb-item">'.__('Courses', 'histudy').'</a><i class="feather-chevron-right"></i><span class="bread-current bread-archive"> ' . get_the_title() . '</span></li>';
            }
            else if( 'course-bundle' == get_post_Type() && is_single() ) {
                echo '<li class="histudy-breadcrumb-item item-current item-archive"><a href='.get_post_type_archive_link( 'course-bundle' ).' class="histudy-breadcrumb-item">'.__('Course Bundle', 'histudy').'</a><i class="feather-chevron-right"></i><span class="bread-current bread-archive"> ' . get_the_title() . '</span></li>';
            }
            else if( 'job_listing' == get_post_Type() && is_single() ) {
                echo '<li class="histudy-breadcrumb-item item-current item-archive"><a href='.get_post_type_archive_link( 'job_listing' ).' class="histudy-breadcrumb-item">'.__('Job', 'histudy').'</a> ><span class="bread-current bread-archive"> ' . get_the_title() . '</span></li>';
            }

            else if (is_archive() && is_tax() && !is_category() && !is_tag()) {

                // If post is a custom post type
                $post_type = get_post_type();

                $custom_tax_name = get_queried_object()->name;
                echo '<li class="histudy-breadcrumb-item active item-current item-archive"><span class="bread-current bread-archive">' . esc_html($custom_tax_name) . '</span></li>';

            } else if (is_page()) {

                // Standard page
                if ($post->post_parent) {

                    // If child page, get parents
                    $anc = get_post_ancestors($post->ID);

                    // Get parents in the right order
                    $anc = array_reverse($anc);

                    // Parent page loop
                    foreach ($anc as $ancestor) {
                        $parents = '<li class="item-parent item-parent-' . esc_attr( $ancestor ) . '"><a class="bread-parent bread-parent-' . esc_attr( $ancestor ) . '" href="' . get_permalink($ancestor) . '" title="' . get_the_title($ancestor) . '">' . get_the_title($ancestor) . '</a></li>';
                        $parents .= '<li class="separator separator-' . esc_attr( $ancestor ) . '"> ' . wp_kses($separator, $allowed_tags) . ' </li>';
                    }

                    // Display parent pages
                    echo wp_kses($parents, $allowed_tags);

                    // Current page
                    echo '<li class="histudy-breadcrumb-item active item-current item-' . esc_attr( $post->ID ) . '"><span title="' . esc_attr(get_the_title()) . '"> ' . rainbow_short_title(get_the_title()) . '</span></li>';

                } else {
                    // Just display current page if not parents
                    echo '<li class="histudy-breadcrumb-item active item-current item-' . esc_attr( $post->ID ) . '"><span class="bread-current bread-' . esc_attr( $post->ID ) . '"> ' . rainbow_short_title(get_the_title()) . '</span></li>';

                }

            } else if (is_tag()) {

                // Tag page

                // Get tag information
                $term_id = get_query_var('tag_id');
                $taxonomy = 'post_tag';
                $args = 'include=' . $term_id;
                $terms = get_terms($taxonomy, $args);
                $get_term_id = $terms[0]->term_id;
                $get_term_slug = $terms[0]->slug;
                $get_term_name = $terms[0]->name;

                // Display the tag name
                echo '<li class="histudy-breadcrumb-item active item-current item-tag-' . esc_attr($get_term_id) . ' item-tag-' . esc_attr( $get_term_slug ) . '"><span class="bread-current bread-tag-' . esc_attr($get_term_id) . ' bread-tag-' . esc_attr( $get_term_slug ) . '">' . esc_html( $get_term_name ) . '</span></li>';

            } elseif (is_day()) {

                // Day archive

                // Year link
                echo '<li class="histudy-breadcrumb-item item-year item-year-' . get_the_time('Y') . '"><a class="bread-year bread-year-' . get_the_time('Y') . '" href="' . get_year_link(get_the_time('Y')) . '" title="' . get_the_time('Y') . '">' . get_the_time('Y') . esc_html__('Archives', 'histudy') . '</a></li>';
                echo '<li class="separator separator-' . get_the_time('Y') . '"> ' . wp_kses($separator, $allowed_tags) . ' </li>';

                // Month link
                echo '<li class="item-month item-month-' . get_the_time('m') . '"><a class="bread-month bread-month-' . get_the_time('m') . '" href="' . get_month_link(get_the_time('Y'), get_the_time('m')) . '" title="' . get_the_time('M') . '">' . get_the_time('M') . esc_html__('Archives', 'histudy') . '</a></li>';
                echo '<li class="separator separator-' . get_the_time('m') . '"> ' . wp_kses($separator, $allowed_tags) . ' </li>';

                // Day display
                echo '<li class="active item-current item-' . get_the_time('j') . '"><span class="bread-current bread-' . get_the_time('j') . '"> ' . get_the_time('jS') . ' ' . get_the_time('M') . esc_html__('Archives', 'histudy') . '</span></li>';

            } else if (is_month()) {

                // Month Archive

                // Year link
                echo '<li class="item-year item-year-' . get_the_time('Y') . '"><a class="bread-year bread-year-' . get_the_time('Y') . '" href="' . get_year_link(get_the_time('Y')) . '" title="' . get_the_time('Y') . '">' . get_the_time('Y') . esc_html__('Archives', 'histudy') . '</a></li>';
                echo '<li class="separator separator-' . get_the_time('Y') . '"> ' . wp_kses($separator, $allowed_tags) . ' </li>';

                // Month display
                echo '<li class="item-month item-month-' . get_the_time('m') . '"><span class="bread-month bread-month-' . get_the_time('m') . '" title="' . get_the_time('M') . '">' . get_the_time('M') . esc_html__('Archives', 'histudy') . '</span></li>';

            } else if (is_year()) {

                // Display year archive
                echo '<li class="active item-current item-current-' . get_the_time('Y') . '"><span class="bread-current bread-current-' . get_the_time('Y') . '" title="' . get_the_time('Y') . '">' . get_the_time('Y') . esc_html__('Archives', 'histudy') . '</span></li>';

            } else if (is_author()) {

                // Auhor archive

                // Get the author information
                global $author;
                $userdata = get_userdata($author);

                // Display author name
                echo '<li class="active item-current item-current-' . esc_attr($userdata->user_nicename) . '"><span class="bread-current bread-current-' . esc_attr($userdata->user_nicename) . '" title="' . esc_attr($userdata->display_name) . '">' . esc_html__('Author: ', 'histudy') . esc_html($userdata->display_name) . '</span></li>';
            } else if (get_query_var('paged')) {

                // Paginated archives
                echo '<li class="active item-current item-current-' . get_query_var('paged') . '"><span class="bread-current bread-current-' . get_query_var('paged') . '" title="Page ' . get_query_var('paged') . '">' . esc_html__('Page', 'histudy') . ' ' . get_query_var('paged') . '</span></li>';

            } else if (is_search()) {

                // Search results page
                echo '<li class="active item-current item-current-' . get_search_query() . '"><span class="bread-current bread-current-' . get_search_query() . '" title="Search results for: ' . get_search_query() . '">' . esc_html__('Search results for: ', 'histudy') . get_search_query() . '</span></li>';

            } elseif (is_404()) {

                // 404 page
                echo '<li>' . esc_html__('Error 404', 'histudy') . '</li>';
            } elseif( is_home() && !( 'post' == get_post_type() && is_archive() ) ) {
                $page_title = esc_html__("Blog ","histudy");
                echo '<li class="histudy-breadcrumb-item active item-current item-archive"><span class="bread-current bread-archive">' . $page_title . '</span></li>';
            }
            else if( class_exists('WooCommerce') && is_product() ) {
                $page_title = get_the_title();
                echo '<li class="histudy-breadcrumb-item active item-current item-archive"><a href='.get_post_type_archive_link( 'product' ).'>'.__( 'Shop', 'histudy' ).'</a> > <span class="bread-current bread-archive">' . $page_title . '</span></li>';
            }

            echo '</ul>';

        }

    }
}