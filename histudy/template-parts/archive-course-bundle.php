<?php
use MailPoetVendor\Doctrine\ORM\Query\Expr\Func;

get_header();

?>
<div class=" has-filter-layout rbt-section-gapBottom course-layout tutor-course-archive-page ptt-120 rbt-archive-course-bundle-main">
    <div class="container">
        <div class="row row--30 gy-5">
            <div class="col-lg-12 col-xl-12">
                <?php if($wp_query->have_posts()) : ?>
                    <div class="row g-5 rbt-archive-course-bundle">
                        <?php while($wp_query->have_posts()) : $wp_query->the_post();  ?>
                        <!-- Start Single Event  -->
                        <div class="col-xl-4 col-lg-6 col-md-6 col-12 course-grid-3">
                            <?php get_template_part('template-parts/components/card/layout','1' ); ?>
                        </div>
                        <!-- End Single Event  -->
                        <?php endwhile; wp_reset_postdata(); ?>
                    </div>
                <?php endif; ?>
                <?php
                    // Add pagination links
                    $pagination_args = array(
                        'total'        => $wp_query->max_num_pages,
                        'current'      => max(1, get_query_var('paged')),
                        'prev_text'    => '<i class="feather-chevron-left"></i>',
                        'next_text'    => '<i class="feather-chevron-right"></i>',
                        'type'         => 'list',
                        'post_type' => 'course_event'
                    );
                    $pagination_links = paginate_links($pagination_args);
                ?>
                <?php if(!empty($pagination_links)) : ?>
                <div class="row rbt-has-pagination">
                    <div class="col-lg-12 mt--60">
                        <nav>
                            <ul class="rbt-pagination">
                                <?php echo wp_kses_post($pagination_links); ?>
                            </ul>
                        </nav>
                    </div>
                </div>
                <?php endif; wp_reset_postdata(); ?>
            </div>
        </div>
    </div>
</div>
<?php get_footer();