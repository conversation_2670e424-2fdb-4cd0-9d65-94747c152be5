== Changelog ==

1.0.0
Initial release

1.1.0
new. Course left sidebar added.
new. Course right sidebar added.
fix. Course filter issue fixed.
new. Course builder design added on details.
new. Added condition My account register ( based on member access ).
new. Like feature added on blog details.
fix. Lesson page next button fixed.
new. Course details heading control added.
fix. Load more hidden issue fixed.
fix. Tutor LMS video player updated on course details.
fix. Breadcrumb issue on teacher profile fixed.
fix. Fixed Font-family issue on Frontend.
fix. Featured author position issue fixed.
fix. Course not found spacing solved on course archive.
fix. Course box space solved on course archive.
fix. Course details style issue fixed.

2.0
added. Rich theme dashboard
added. Custom cart label feature for external product
added. Quick access theme options and customizer
added. Activate/Deactivate/Install plugin from theme dashboard.
added. Theme documentation on dashboared.
added. Server configuration status
added. Seperate menu for mobile and dekstop
added. Template import by page or section.
added. RTL Support Added.
added. Removed buy now button from external product.
fixed. Change add to cart to view cart, after product added
fixed. Header 1 user button not showing on mobile fixed.
fixed. My account login url fixed.
fixed. Price formate added ( like for 1000, it will be 10,000 )
fixed. Fixed: All product price same after add specific product into cart.
fixed. Currency symbol added.
fixed. Login button not showing on mobile.
fixed. Course details small video preview.
fixed. Course details featured video.
fixed. Username added on header lms dashboard.
fixed. backtotop console issue fixed.
fixed. duplicate user issue fixed on header.
fixed. Course/Blog popup Search updated. ( changed event from keypress to input )
fixed. WooCommerce outdated template issue fixed.
fixed. Custom footer bottom text feature added ( from widget )


2.1
added. Sponsor section added on event details.
added: Review control from theme options.
added. Course archive append or override feature added ( on click load more ).
added. Course details style 2 added.
added. Course archive card ( 6 diffrent layout ) added.
fixed. Buy now button appear after purchase course, when it update from free to paid.
fixed. Section title widget style added.
fixed. List style typography, color padding control added.
fixed. Banner widget style updated.
fixed. Banner static widget style updated.
fixed. Custom menu widget style updated.
fixed. Banner slider widget style updated.
fixed. Banner widget style updated.
fixed. List style widget style updated.
fixed. List style widget style updated.
fixed. Section title widget style updated.
fixed. Event details banner.
fixed. Conditional featured course from course meta on course single.
fixed. All course appear issue on specific course archive ( category or taxonomy ).
fixed. Event content description fixed.
fixed. Event single breadcrumb issue fixed.
fixed. Theme core css and rtl css validation issue fixed.
fixed. Bestseller badge for each course issue fixed.
fixed. Announcement, review and q&a not removes from admin panel issue fixed.
fixed. Fixed add to cart redirect issue on course variation 2 (elementor widget).
fixed: Fixed scroll behavior issue.
fixed: Fixed review label based on count issue.
fixed: Course archive filter issue solved.

2.2.0
added: Teacher profile section hide based on condition in course details.
added: Condition based profile show/hide on course details.
added: All tutor player support added on feature course single widget.
fixed: Cart count issue fixed.
fixed: Comment count issue fixed on course details.
fixed: Rating navigation issue fixed.
fixed: Course free issue after select rating fixed.
fixed: Category based course show on course archive.
fixed: Author and category filter on course details.
fixed: Add to cart disappear issue on course details fixed.
fixed: Phone number required issue fixed on course details.
fixed: "Find with Us" static text converted to dynamic on header sidebar.
fixed: Added text field for "Find with us" text customize.
fixed: Selected category cross removed from course details filter bar.
fixed: Fixed rating dropdown remain open after select.
Fixed: One user can only 1 like at a time.
Fixed: Number of products count on shop archive badge.
Fixed: Blog default layout picture added.
Fixed: Event permalink rewrite feature added on theme options.
Update: Advance custom fields 6.3.0.1
Fixed: wc_get_page_id() issue fixed on user.php ( function check fixed ).

2.3.0
New: Load more feature added on elementor course widget.
New: Login/Registration popup added on header "my account" button.
New: "Continue learning" and "add to cart" button added based on course purchase condition.
New: Course not found alert added on empty author archive.
Fixed: Redux submenu position issue on rtl fixed.
Fixed: Fixed title issue on contact us widget
Fixed: Theme broken if no product are exists.
Fixed: Fixed broken issue on woocommerce uninstalled.