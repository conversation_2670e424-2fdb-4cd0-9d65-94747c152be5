<?php
trait SingleProductAttributes {
    public static function single_product_attributes() {
        if(!class_exists('WooCommerce')) {
            return;
        }
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $wc_cats = $rainbow_options['wc_cats'] ? $rainbow_options['wc_cats']: '';
        $wc_tags = $rainbow_options['wc_tags'] ? $rainbow_options['wc_tags']: '';
        $wc_related_before_title = $rainbow_options['wc_related_before_title'] ? $rainbow_options['wc_related_before_title']: '';
        $wc_related_title = $rainbow_options['wc_related_title'] ? $rainbow_options['wc_related_title']: '';
        $wc_description = $rainbow_options['wc_description'] ? $rainbow_options['wc_description']: '';
        $wc_reviews = $rainbow_options['wc_reviews'] ? $rainbow_options['wc_reviews']: '';
        $wc_additional_info = $rainbow_options['wc_additional_info'] ? $rainbow_options['wc_additional_info']: '';
        $rainbow_product_details_attributes = [
            'wc_cats' => $wc_cats,
            'wc_tags' => $wc_tags,
            'wc_related_before_title' => $wc_related_before_title,
            'wc_related_title' => $wc_related_title,
            'wc_description' => $wc_description,
            'wc_reviews' => $wc_reviews,
            'wc_additional_info' => $wc_additional_info,
        ];
        return $rainbow_product_details_attributes;
    }
}