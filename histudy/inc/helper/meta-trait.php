<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */


trait rainbowPostMeta
{

    public static function rainbow_postmeta()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        ?>
        <ul class="blog-meta">

            <?php
            if ($rainbow_options['rainbow_show_post_author_meta'] != 'no') { ?>
                <li class="single-post-meta-author"><i class="feather-user"></i><?php the_author(); ?></li>
            <?php } ?>
            <?php if ($rainbow_options['rainbow_show_post_publish_date_meta'] !== 'no') { ?>
                <li class="single-post-meta-date"><i
                            class="feather-clock"></i><?php echo get_the_time(get_option('date_format')); ?></li>
            <?php } ?>
            <?php if ($rainbow_options['rainbow_show_post_reading_time_meta'] !== 'no') { ?>
                <li class="single-post-meta-reading-time"><i
                            class="feather-watch"></i><?php echo rainbow_content_estimated_reading_time(get_the_content()); ?>
                </li>
            <?php } ?>
            <?php if ($rainbow_options['rainbow_show_post_view'] != 'no') { ?>
                <li class="single-post-meta-post-views"><i
                            class="feather-eye"></i> <?php echo Rainbow_Post_Views_number(__( 'Views', 'histudy' )); ?></li>
            <?php } ?>
            <?php if ($rainbow_options['rainbow_show_post_comments_meta'] !== 'no') { ?>
                <li class="single-post-meta-comment"><i
                            class="feather-message-circle"></i><?php comments_popup_link(esc_html__('No Comments', 'histudy'), esc_html__('1 Comment', 'histudy'), esc_html__('% Comments', 'histudy'), 'post-comment', esc_html__('Comments off', 'histudy')); ?>
                </li>
            <?php } ?>
            <?php if (($rainbow_options['rainbow_show_post_categories_meta'] !== 'no') && has_category()) { ?>
                <li class="single-post-meta-categories"><i class="feather-folder"></i><?php the_category(' '); ?></li>
            <?php } ?>
            <?php
            $tag_list = get_the_tag_list( '<ul><li>', '</li><li>', '</li></ul>' );
            if( $tag_list ) {
                if (($rainbow_options['rainbow_show_post_tags_meta'] !== 'no') && has_category()) { ?>
                    <li class="rainbow-post-meta-tag-box"><i class="feather-tag"></i><?php echo get_the_tag_list( '<ul><li>', ',</li><li>', '</li></ul>' ); ?></li>
                <?php }
            }?>
        </ul>
        <?php
    }

    // Single post meta
    public static function rainbow_singlepostmeta_top()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $author_id = get_the_author_meta('ID');

        $post_id = get_the_ID();
        $author_id = get_post_field('post_author', $post_id);
        $author_name = get_the_author_meta('display_name', $author_id);
        $author_archive_url = get_author_posts_url($author_id);
        $author_avatar = get_avatar($author_id, 45); // Change the size (64) as needed

        $top_meta_category_on_off = isset( $rainbow_options['rainbow_show_post_categories_meta_top'] ) ? $rainbow_options['rainbow_show_post_categories_meta_top'] : 'no';
        ?>

        <ul class="meta-list justify-content-center mb--10"> 
        <?php if ( ($rainbow_options['rainbow_show_blog_details_author_meta'] != 'no') || (($rainbow_options['rainbow_show_blog_details_categories_meta'] == 'yes') && has_category())) { 
           
            ?>
            <li class="list-item">
                <?php if ($rainbow_options['rainbow_show_blog_details_author_meta'] !== 'no') { ?>
                    <div class="author-thumbnail">
                       <?php echo get_avatar($author_id); ?>
                    </div>
                <?php } ?>
                <div class="author-info">
                    <?php if ($rainbow_options['rainbow_show_blog_details_author_meta'] !== 'no') { ?>
                         <a href="<?php echo esc_url($author_archive_url); ?>"><strong><?php echo esc_html($author_name); ?> </strong></a>
                         <?php 
                         if( ($top_meta_category_on_off == 'yes') && has_category()) {
                         echo esc_html__(' in', 'histudy');
                         }
                         ?>
                    <?php } ?>
                    <?php if ( ($top_meta_category_on_off == 'yes') && has_category()) { 
                        
                        ?>
                        <?php the_category(','); ?>
                    <?php } ?>
                </div>
            </li>
        <?php } ?>

        <?php if ($rainbow_options['rainbow_show_blog_details_publish_date_meta'] !== 'no') { ?>
             <li class="list-item"><i class="feather-clock"></i><?php echo get_the_time(get_option('date_format')); ?></li>
        <?php } ?>
        </ul>

    <?php }

    // Single post meta
    public static function rainbow_singlepostmeta()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        ?>
        <div class="histudy-post-meta">
            <div class="post-meta-content">
                <ul class="post-meta-list blog-meta">
                    <?php if ($rainbow_options['rainbow_show_blog_details_reading_time_meta'] !== 'no') { ?>
                        <li><i class="feather-watch"></i><?php echo rainbow_content_estimated_reading_time(get_the_content()); ?></li>
                    <?php } ?>
                    <?php if ($rainbow_options['rainbow_show_blog_details_post_view'] != 'no') { ?>
                        <li class="single-post-meta-post-views"><i class="feather-eye"></i><?php echo Rainbow_Post_Views_number(__( 'Views', 'histudy' )); ?></li>
                    <?php } ?>
                    <?php if ($rainbow_options['rainbow_show_blog_details_comments_meta'] !== 'no') { ?>
                        <li class="single-post-meta-comment"><i class="feather-message-circle"></i><?php comments_popup_link(esc_html__('No Comments', 'histudy'), esc_html__('1 Comment', 'histudy'), esc_html__('% Comments', 'histudy'), 'post-comment', esc_html__('Comments off', 'histudy')); ?></li>
                    <?php } ?>
                    <?php if (($rainbow_options['rainbow_show_blog_details_categories_meta'] !== 'no') && has_category()) { ?>
                    <li class="single-post-meta-category"><i class="feather-grid"></i><?php the_category(','); ?></li>
                    <?php } ?>

                </ul>
            </div>
        </div>

    <?php }

    /**
     * rainbow_post_category_meta
     */
    public static function rainbow_post_category_meta($show = true)
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        if ($show && $rainbow_options['rainbow_show_post_category'] !== 'no' && has_category()) {
            $categories = get_the_category();
            ?>
            <div class="category-list">

                <?php
                if (!empty($categories)) {
                    foreach ($categories as $category) { ?>
                        <a class="flip-item" href="<?php echo esc_url(get_category_link($category->term_id)) ?>">
                            <?php echo esc_html($category->name) ?>
                        </a> <?php
                    }
                }
                ?>
            </div>
            <?php
        }
    }

    public static function rainbow_read_more()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $rainbow_enable_readmore_btn = isset($rainbow_options) ? sanitize_text_field( $rainbow_options['rainbow_enable_readmore_btn'] ) : '';
        if( 'yes' !== $rainbow_enable_readmore_btn ) {
            return;
        }
        ?>
             <a class="transparent-button" href="<?php the_permalink(); ?>"><?php echo esc_html($rainbow_options['rainbow_readmore_text'], 'histudy') ?><i><svg width="17" height="12" xmlns="http://www.w3.org/2000/svg"><g stroke="#27374D" fill="none" fill-rule="evenodd"><path d="M10.614 0l5.629 5.629-5.63 5.629"/><path stroke-linecap="square" d="M.663 5.572h14.594"/></g></svg></i></a>
          
        <?php 
    }
    public static function rainbow_get_image( $image_size = 'full' ) {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        if( 'gallery' == get_post_format() ) {
            /**
             * If post format is gallery, then load this
             * 
             * @since 1.0.0
             */
            $images = rainbow_get_acf_data('rainbow_gallery_image');
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $image_size );
            if( $images ) {
                $thumbnail = '';
                $thumbnail .= '<div class="swiper rbt-arrow-between blog-post-gallery-activation">';
                $thumbnail .= '<div class="swiper-wrapper">';
                foreach( $images as $index => $image ) {
                    $image_url = $image['url'];
                    $image_alt = $image['alt'];
                    $thumbnail .= '<div class="swiper-slide">';
                    $thumbnail .= "<img src='$image_url' alt='$image_alt'>";
                    $thumbnail .= '</div>';
                }
                $thumbnail .= '</div>';
                $thumbnail .= <<<EOD
                <div class="rbt-swiper-arrow rbt-arrow-left">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-left"></i>
                        <i class="rbt-icon-top feather-arrow-left"></i>
                    </div>
                </div>
                <div class="rbt-swiper-arrow rbt-arrow-right">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-right"></i>
                        <i class="rbt-icon-top feather-arrow-right"></i>
                    </div>
                </div>
                EOD;
                $thumbnail .= '</div>';
            }
            echo wp_kses_post($thumbnail);
            return;
        } elseif( 'video' == get_post_format() ) {
            $video_url = rainbow_get_acf_data( "rainbow_video_link" );
            $videoId = self::getYouTubeVideoId($video_url);
            $thumbnail = '<iframe src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allowfullscreen></iframe>';
            echo wp_kses_post($thumbnail);
        } elseif( 'audio' == get_post_format() ) {
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $image_size );
            echo wp_kses_post($thumbnail);
            return;
        } else {
            the_post_thumbnail( $image_size );
            return;
        }
    }
    public static function rainbow_get_image_for_list( $image_size = 'full' ) {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $blog_list_card_image_size = sanitize_text_field( $rainbow_options['blog_list_card_image_size'] );
        $rainbow_blog_img_size = $image_size;
        if( 'gallery' == get_post_format() ) {
            /**
             * If post format is gallery, then load this
             * 
             * @since 1.0.0
             */
            $images = rainbow_get_acf_data('rainbow_gallery_image');
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $rainbow_blog_img_size );
            if( $images ) {
                $thumbnail = '';
                $thumbnail .= '<div class="swiper rbt-arrow-between blog-post-gallery-activation">';
                $thumbnail .= '<div class="swiper-wrapper">';
                foreach( $images as $index => $image ) {
                    $image_url = $image['url'];
                    $image_alt = $image['alt'];
                    $thumbnail .= '<div class="swiper-slide">';
                    $thumbnail .= "<img src='$image_url' alt='$image_alt'>";
                    $thumbnail .= '</div>';
                }
                $thumbnail .= '</div>';
                $thumbnail .= <<<EOD
                <div class="rbt-swiper-arrow rbt-arrow-left">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-left"></i>
                        <i class="rbt-icon-top feather-arrow-left"></i>
                    </div>
                </div>
                <div class="rbt-swiper-arrow rbt-arrow-right">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-right"></i>
                        <i class="rbt-icon-top feather-arrow-right"></i>
                    </div>
                </div>
                EOD;
                $thumbnail .= '</div>';
            }
            echo wp_kses_post($thumbnail);
            return;
        } elseif( 'video' == get_post_format() ) {
            $video_url = rainbow_get_acf_data( "rainbow_video_link" );
            $videoId = self::getYouTubeVideoId($video_url);
            $thumbnail = '<iframe src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allowfullscreen></iframe>';
            echo wp_kses_post($thumbnail);
        } elseif( 'audio' == get_post_format() ) {
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $rainbow_blog_img_size );
            echo wp_kses_post($thumbnail);
            return;
        } else {
            the_post_thumbnail( $rainbow_blog_img_size );
            return;
        }
    }
    public static function rainbow_get_image_full() {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $rainbow_blog_img_size = 'full';
        if( 'gallery' == get_post_format() ) {
            /**
             * If post format is gallery, then load this
             * 
             * @since 1.0.0
             */
            $images = rainbow_get_acf_data('rainbow_gallery_image');
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $rainbow_blog_img_size );
            if( $images ) {
                $thumbnail = '';
                $thumbnail .= '<div class="swiper rbt-arrow-between blog-post-gallery-activation">';
                $thumbnail .= '<div class="swiper-wrapper">';
                foreach( $images as $index => $image ) {
                    $image_url = $image['url'];
                    $image_alt = $image['alt'];
                    $thumbnail .= '<div class="swiper-slide">';
                    $thumbnail .= "<img src='$image_url' alt='$image_alt'>";
                    $thumbnail .= '</div>';
                }
                $thumbnail .= '</div>';
                $thumbnail .= <<<EOD
                <div class="rbt-swiper-arrow rbt-arrow-left">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-left"></i>
                        <i class="rbt-icon-top feather-arrow-left"></i>
                    </div>
                </div>
                <div class="rbt-swiper-arrow rbt-arrow-right">
                    <div class="custom-overfolow">
                        <i class="rbt-icon feather-arrow-right"></i>
                        <i class="rbt-icon-top feather-arrow-right"></i>
                    </div>
                </div>
                EOD;
                $thumbnail .= '</div>';
            }
            echo wp_kses_post($thumbnail);
            return;
        } elseif( 'video' == get_post_format() ) {
            $video_url = rainbow_get_acf_data( "rainbow_video_link" );
            $videoId = self::getYouTubeVideoId($video_url);
            $thumbnail = '<iframe src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allowfullscreen></iframe>';
            echo wp_kses_post($thumbnail);
        } elseif( 'audio' == get_post_format() ) {
            $thumbnail = get_the_post_thumbnail( get_the_ID(), $rainbow_blog_img_size );
            echo wp_kses_post($thumbnail);
            return;
        } else {
            the_post_thumbnail( $rainbow_blog_img_size );
            return;
        }
    }
    static function getYouTubeVideoId($url) {
        $videoId = '';
        $pattern = '/(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
        preg_match($pattern, $url, $matches);
        if (isset($matches[1])) {
            $videoId = $matches[1];
        }
        return $videoId;
    }
    static function rbt_hide_post() {
        if( 'quote' == get_post_format() ) {
            return false;
        }
        if( 'link' == get_post_format() ) {
            return false;
        }
        return true;
    }
}