<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait becomeInstructorTrait {
    public static function rainbow_become_instructor_settings(){
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        if( TUTOR_ACTIVED ){
            $rainbow_tutor_instructor_registration_repeater_title    =  $rainbow_options['rainbow_tutor_instructor_registration_repeater_title'];  
            $rainbow_tutor_instructor_registration_repeater_content  =  $rainbow_options['rainbow_tutor_instructor_registration_repeater_content'];  
            $rainbow_tutor_instructor_registration_badge_title       =  $rainbow_options['rainbow_tutor_instructor_registration_badge_title'];  
            $rainbow_tutor_instructor_registration_main_title       =  $rainbow_options['rainbow_tutor_instructor_registration_main_title'];  
            $rainbow_tutor_instructor_registration_main_desc       =  $rainbow_options['rainbow_tutor_instructor_registration_main_desc'];  
            $rainbow_tutor_instructor_registration_instructor_image       =  $rainbow_options['rainbow_tutor_instructor_registration_instructor_image'];  
            $instructor_image_show       =  $rainbow_options['instructor_image_show'];  
        }else{
            $rainbow_tutor_instructor_registration_repeater_title    =  array();
            $rainbow_tutor_instructor_registration_repeater_content  =  array();
            $rainbow_tutor_instructor_registration_badge_title       =  '';
            $rainbow_tutor_instructor_registration_main_title       =  '';
            $rainbow_tutor_instructor_registration_main_desc       =  '';
            $rainbow_tutor_instructor_registration_instructor_image       =  '';
            $instructor_image_show       =  '';
        }

        $becomeInstructorTrait                                       =  [
            'rainbow_tutor_instructor_registration_repeater_title'   => $rainbow_tutor_instructor_registration_repeater_title,
            'rainbow_tutor_instructor_registration_repeater_content' => $rainbow_tutor_instructor_registration_repeater_content,
            'rainbow_tutor_instructor_registration_badge_title'      => $rainbow_tutor_instructor_registration_badge_title,
            'rainbow_tutor_instructor_registration_main_title'      => $rainbow_tutor_instructor_registration_main_title,
            'rainbow_tutor_instructor_registration_main_desc'      => $rainbow_tutor_instructor_registration_main_desc,
            'rainbow_tutor_instructor_registration_instructor_image'      => $rainbow_tutor_instructor_registration_instructor_image,
            'instructor_image_show'      => $instructor_image_show,
        ];
        return $becomeInstructorTrait;
    }

}