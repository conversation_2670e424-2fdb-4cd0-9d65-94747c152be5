<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait rainbowBannerTrait
{

    public static function rainbow_shop_banner_layout()
    {
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        $size = 'full';  
        $single_product_banner_layout = '';
        if( WOOC_WOO_ACTIVED ){
            $banner_area        = $rainbow_options['rainbow_shop_banner_enable'];  
            $single_product_banner_layout        = $rainbow_options['rainbow_single_product_banner_layout'];  
            $banner_subtitle    = $rainbow_options['rainbow_shop_bannr_subtitle'];
            $template           = $rainbow_options['rainbow_shop_banner_template'];
        }else{
            $banner_area        = "";  
            $banner_subtitle    = "";
            $template           = "";
        }
        $banner_layout = [
            'banner_area'     => $banner_area,   
            'banner_subtitle' => $banner_subtitle,
            'template'        => $template,
            'single_product_banner_layout' => $single_product_banner_layout,
        ];

        return $banner_layout;

    }
    public static function rainbow_layout_tutor_settings() {
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        if( TUTOR_ACTIVED || function_exists( 'academy_start' ) ) {
            $rainbow_shop_banner_enable = (isset($rainbow_options['rainbow_shop_banner_enable']) && !empty($rainbow_options['rainbow_course_details_breadcrumb_overlap_switch'])) ? $rainbow_options['rainbow_course_details_breadcrumb_overlap_switch']: false;
            $rainbow_course_details_breadcrumb_overlap_switch = (isset($rainbow_options['rainbow_course_details_breadcrumb_overlap_switch']) && !empty($rainbow_options['rainbow_course_details_breadcrumb_overlap_switch'])) ? $rainbow_options['rainbow_course_details_breadcrumb_overlap_switch']: false;
            $rainbow_tutor_archive_title = (isset($rainbow_options['rainbow_tutor_archive_title']) && !empty($rainbow_options['rainbow_tutor_archive_title'])) ? $rainbow_options['rainbow_tutor_archive_title']: '';
            $rainbow_tutor_archive_banner_layout = (isset($rainbow_options['rainbow_tutor_archive_banner_layout']) && !empty($rainbow_options['rainbow_tutor_archive_banner_layout'])) ? $rainbow_options['rainbow_tutor_archive_banner_layout']: '';
            $rainbow_tutor_archive_orderby_front = (isset($rainbow_options['rainbow_tutor_archive_orderby_front']) && !empty($rainbow_options['rainbow_tutor_archive_orderby_front'])) ? $rainbow_options['rainbow_tutor_archive_orderby_front']: '';
            $rainbow_tutor_archive_subtitle = (isset($rainbow_options['rainbow_tutor_archive_subtitle']) && !empty($rainbow_options['rainbow_tutor_archive_subtitle'])) ? $rainbow_options['rainbow_tutor_archive_subtitle']: '';
            $rainbow_tutor_archive_enable_grid_list = (isset($rainbow_options['rainbow_tutor_archive_enable_grid_list']) && !empty($rainbow_options['rainbow_tutor_archive_enable_grid_list'])) ? $rainbow_options['rainbow_tutor_archive_enable_grid_list']: '';
            $rainbow_tutor_archive_filter_show_default = (isset($rainbow_options['rainbow_tutor_archive_filter_show_default']) && !empty($rainbow_options['rainbow_tutor_archive_filter_show_default'])) ? $rainbow_options['rainbow_tutor_archive_filter_show_default']: '';
            $rainbow_tutor_archive_enable_search = (isset($rainbow_options['rainbow_tutor_archive_enable_search']) && !empty($rainbow_options['rainbow_tutor_archive_enable_search'])) ? $rainbow_options['rainbow_tutor_archive_enable_search']: '';
            $rainbow_tutor_archive_enable_filter = (isset($rainbow_options['rainbow_tutor_archive_enable_filter']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter']: '';
            $rainbow_tutor_category_archive_enable_filter = (isset($rainbow_options['rainbow_tutor_category_archive_enable_filter']) && !empty($rainbow_options['rainbow_tutor_category_archive_enable_filter'])) ? $rainbow_options['rainbow_tutor_category_archive_enable_filter']: '';
            $rainbow_tutor_category_archive_category_enable_filter = (isset($rainbow_options['rainbow_tutor_category_archive_category_enable_filter']) && !empty($rainbow_options['rainbow_tutor_category_archive_category_enable_filter'])) ? $rainbow_options['rainbow_tutor_category_archive_category_enable_filter']: '';

            
            $rainbow_single_product_banner_enable = (isset($rainbow_options['rainbow_single_product_banner_enable']) && !empty($rainbow_options['rainbow_single_product_banner_enable'])) ? $rainbow_options['rainbow_single_product_banner_enable']: '';
            $rainbow_tutor_archive_enable_filter_enable_sort_by = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_sort_by']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_sort_by'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_sort_by']: '';
            $rainbow_tutor_archive_enable_filter_enable_author_sort = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_author_sort']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_author_sort'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_author_sort']: '';
            $rainbow_tutor_archive_enable_filter_enable_offer_sort = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_offer_sort']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_offer_sort'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_offer_sort']: '';
            $rainbow_tutor_archive_enable_filter_difficulty = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_difficulty']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_difficulty'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_difficulty']: '';
            $rainbow_tutor_archive_enable_filter_enable_rating_sort = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_rating_sort']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_rating_sort'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_rating_sort']: '';
            $rainbow_tutor_archive_enable_filter_toggle = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_toggle']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_toggle'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_toggle']: '';
            $rainbow_tutor_archive_enable_filter_enable_category_sort = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_category_sort']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_category_sort'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_category_sort']: '';
            $rainbow_tutor_archive_enable_filter_enable_price_range_sort = (isset($rainbow_options['rainbow_tutor_archive_enable_filter_enable_price_range_sort']) && !empty($rainbow_options['rainbow_tutor_archive_enable_filter_enable_price_range_sort'])) ? $rainbow_options['rainbow_tutor_archive_enable_filter_enable_price_range_sort']: '';
            $rainbow_tutor_course_badge_show_hide = (isset($rainbow_options['rainbow_tutor_course_badge_show_hide']) && !empty($rainbow_options['rainbow_tutor_course_badge_show_hide'])) ? $rainbow_options['rainbow_tutor_course_badge_show_hide']: '';
            $rainbow_tutor_course_toggle_tab = (isset($rainbow_options['rainbow_tutor_course_toggle_tab']) && !empty($rainbow_options['rainbow_tutor_course_toggle_tab'])) ? $rainbow_options['rainbow_tutor_course_toggle_tab']: '';
            $rainbow_tutor_course_badge_label = (isset($rainbow_options['rainbow_tutor_course_badge_label']) && !empty($rainbow_options['rainbow_tutor_course_badge_label'])) ? $rainbow_options['rainbow_tutor_course_badge_label']: '';
            $rainbow_tutor_course_select_column = (isset($rainbow_options['rainbow_tutor_course_select_column']) && !empty($rainbow_options['rainbow_tutor_course_select_column'])) ? $rainbow_options['rainbow_tutor_course_select_column']: 3;
        } else {
            $rainbow_tutor_archive_title                                 = '';
            $rainbow_tutor_course_select_column                          = '';
            $rainbow_shop_banner_enable                                  = '';
            $rainbow_tutor_archive_banner_layout                         = '';
            $rainbow_tutor_archive_subtitle                              = '';
            $rainbow_course_details_breadcrumb_overlap_switch            = '';
            $rainbow_tutor_archive_orderby_front                         = '';
            $rainbow_tutor_archive_enable_grid_list                      = '';
            $rainbow_single_product_banner_enable                        = '';
            $rainbow_tutor_course_toggle_tab                             = '';
            $rainbow_tutor_archive_enable_filter_difficulty                             = '';
            $rainbow_tutor_course_badge_label                            = '';
            $rainbow_tutor_archive_enable_search                         = '';
            $rainbow_tutor_course_badge_show_hide                        = '';
            $rainbow_tutor_archive_enable_filter                         = '';
            $rainbow_tutor_category_archive_enable_filter                         = '';
            $rainbow_tutor_archive_filter_show_default                   = '';
            $rainbow_tutor_archive_enable_filter_enable_sort_by          = '';
            $rainbow_tutor_archive_enable_filter_enable_author_sort      = '';
            $rainbow_tutor_archive_enable_filter_enable_offer_sort       = '';
            $rainbow_tutor_archive_enable_filter_enable_rating_sort      = '';
            $rainbow_tutor_archive_enable_filter_toggle                  = '';
            $rainbow_tutor_archive_enable_filter_enable_category_sort    = '';
            $rainbow_tutor_archive_enable_filter_enable_price_range_sort = '';
            $rainbow_tutor_category_archive_category_enable_filter = '';
        }
        $course_archive_banner_layout = [
            'rainbow_tutor_archive_title'                                 => $rainbow_tutor_archive_title,   
            'rainbow_tutor_course_select_column'                                 => $rainbow_tutor_course_select_column,   
            'rainbow_shop_banner_enable'                                  => $rainbow_shop_banner_enable,   
            'rainbow_tutor_archive_subtitle'                              => $rainbow_tutor_archive_subtitle,
            'rainbow_course_details_breadcrumb_overlap_switch'            => $rainbow_course_details_breadcrumb_overlap_switch,
            'rainbow_tutor_archive_enable_grid_list'                      => $rainbow_tutor_archive_enable_grid_list,
            'rainbow_tutor_archive_enable_search'                         => $rainbow_tutor_archive_enable_search,
            'rainbow_tutor_archive_enable_filter'                         => $rainbow_tutor_archive_enable_filter,
            'rainbow_tutor_category_archive_enable_filter'                => $rainbow_tutor_category_archive_enable_filter,
            'rainbow_tutor_archive_banner_layout'                         => $rainbow_tutor_archive_banner_layout,
            'rainbow_tutor_archive_enable_filter_toggle'                  => $rainbow_tutor_archive_enable_filter_toggle,
            'rainbow_tutor_course_toggle_tab'                  => $rainbow_tutor_course_toggle_tab,
            'rainbow_tutor_archive_filter_show_default'                   => $rainbow_tutor_archive_filter_show_default,
            'rainbow_single_product_banner_enable'                        => $rainbow_single_product_banner_enable,
            'rainbow_tutor_archive_orderby_front'                         => $rainbow_tutor_archive_orderby_front,
            'rainbow_tutor_archive_enable_filter_enable_sort_by'          => $rainbow_tutor_archive_enable_filter_enable_sort_by,
            'rainbow_tutor_archive_enable_filter_enable_author_sort'      => $rainbow_tutor_archive_enable_filter_enable_author_sort,
            'rainbow_tutor_archive_enable_filter_difficulty'      => $rainbow_tutor_archive_enable_filter_difficulty,
            'rainbow_tutor_archive_enable_filter_enable_offer_sort'       => $rainbow_tutor_archive_enable_filter_enable_offer_sort,
            'rainbow_tutor_archive_enable_filter_enable_category_sort'    => $rainbow_tutor_archive_enable_filter_enable_category_sort,
            'rainbow_tutor_archive_enable_filter_enable_price_range_sort' => $rainbow_tutor_archive_enable_filter_enable_price_range_sort,
            'rainbow_tutor_archive_enable_filter_enable_rating_sort'      => $rainbow_tutor_archive_enable_filter_enable_rating_sort,
            'rainbow_tutor_course_badge_show_hide'                        => $rainbow_tutor_course_badge_show_hide,
            'rainbow_tutor_course_badge_label'                            => $rainbow_tutor_course_badge_label,
            'rainbow_tutor_category_archive_category_enable_filter'       => $rainbow_tutor_category_archive_category_enable_filter,
        ];
        
        return $course_archive_banner_layout;
    }
    /** layout settings */
    public static function rainbow_layout_settings()
    {


        if (is_single() || is_page()) {
            $post_type = get_post_type();

            switch ($post_type) {
                case 'page':
                    $themepfix = 'page';
                break;
                case 'product':
                    $themepfix = 'single_product';
                break;      
                case 'post':
                    $themepfix = 'single_post';
                default:
                    $themepfix = 'single_post';
                break; 

            }

        } elseif (is_home() || is_archive() || is_search() || is_404()) {
            if (is_author()) {
                $themepfix = 'blog';
            } elseif (is_search()) {
                $themepfix = 'blog';
            } elseif (is_post_type_archive("product") || is_tax("product_cat")) {
                $themepfix = 'product_archive';
            } else {
                $themepfix = 'blog';
            }
        }
        return $themepfix;
    }


    /**
     * @return array
     * Banner Layout
     */
    public static function rainbow_banner_layout()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $condipfix = self::rainbow_layout_settings();
        $sub_title = '';
        $banner_area = '';
        if (is_single() || is_page()) { 
            $banner_area = rainbow_get_acf_data('rainbow_title_wrapper_show');
            $banner_area = (!empty($banner_area)) ? $banner_area : $rainbow_options['rainbow_single_product_banner_enable'];
            $banner_style = rainbow_get_acf_data('rainbow_select_banner_style');
            $sub_title        = rainbow_get_acf_data('rainbow_custom_sub_title');
            $sub_title        = isset($rainbow_options['rainbow_' . $condipfix . '_subtitle']) && ( !empty( $sub_title ) ) ? $rainbow_options['rainbow_' . $condipfix . '_subtitle'] : $sub_title;
        } 
        elseif(class_exists('WooCommerce') && is_shop()) {
            $sub_title      =  isset($rainbow_options['rainbow_shop_bannr_subtitle']) ? $rainbow_options['rainbow_shop_bannr_subtitle']: '';
        }
        elseif (is_home() || is_archive() || is_search() || is_404() || is_shop()) {  
            $banner_area    =  isset($rainbow_options['rainbow_single_product_banner_enable']) ? $rainbow_options['rainbow_single_product_banner_enable']: '';
            $sub_title      =  isset($rainbow_options['rainbow_' . $condipfix . '_subtitle']) ? $rainbow_options['rainbow_' . $condipfix . '_subtitle']: '';
            $banner_style   =  isset($rainbow_options['rainbow_' . $condipfix . '_title_layout']) ? $rainbow_options['rainbow_' . $condipfix . '_title_layout']: '';
        }
        $banner_layout = [
            'banner_area'   => $banner_area,
            'banner_style'  => isset($banner_style) ? $banner_style: '',
            'sub_title'     => $sub_title,

        ];
        return $banner_layout;
    }



    /**
     * @return array
     * Banner Layout
     */
    public static function rainbow_blog_banner_layout()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $condipfix = self::rainbow_layout_settings();

        $banner_area    =  isset($rainbow_options['rainbow_blog_banner_enable']) ? $rainbow_options['rainbow_blog_banner_enable']: '';
        $banner_layout    =  isset($rainbow_options['rainbow_blog_title_layout']) ? $rainbow_options['rainbow_blog_title_layout']: '';
        /**
         * Load Value
         */
        $banner_layout = [
            'banner_area' => $banner_area,
            'banner_layout' => $banner_layout,
        ];
        return $banner_layout;

    }

    /**
     * @return array
     * Banner Layout
     */
    public static function rainbow_page_breadcrumb()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        /**
         * Get Page Options value
         */
        $breadcrumbs = rainbow_get_acf_data('rainbow_breadcrumbs_enable');
        $condipfix = self::rainbow_layout_settings();
        /**
         * Set Condition
         */
        if(isset($rainbow_options['rainbow_' . $condipfix . '_breadcrumb_enable']) &&  !empty($rainbow_options['rainbow_' . $condipfix . '_breadcrumb_enable'])) {
            $breadcrumbs = (!empty($breadcrumbs)) ? $breadcrumbs : $rainbow_options['rainbow_' . $condipfix . '_breadcrumb_enable'];
        }

        /**
         * Load Value
         */
        $breadcrumbs_enable = [
            'breadcrumbs' => $breadcrumbs,
        ];
        return $breadcrumbs_enable;

    }

}
