<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait rainbowCourseCartTrait {
    public static function rainbow_caourse_cart_settings(){
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        if( TUTOR_ACTIVED ){
            $rainbow_tutor_card_layout               = $rainbow_options['rainbow_tutor_card_layout'];  
            $rainbow_course_card_author_switch      = $rainbow_options['rainbow_course_card_author_switch'];  
            $rainbow_course_card_image_switch       = $rainbow_options['rainbow_course_card_image_switch'];  
            $rainbow_course_card_title_switch = $rainbow_options['rainbow_course_card_title_switch'];  
            $rainbow_course_card_rating_switch      = $rainbow_options['rainbow_course_card_rating_switch'];  
            $rainbow_course_card_meta_switch      = $rainbow_options['rainbow_course_card_meta_switch'];  
            $rainbow_course_card_add_to_cart_switch = $rainbow_options['rainbow_course_card_add_to_cart_switch'];  
            $rainbow_course_card_pricing_switch = $rainbow_options['rainbow_course_card_pricing_switch'];  
            $rainbow_course_grid_archive_img_size = $rainbow_options['rainbow_course_grid_archive_img_size'];  
            $rainbow_course_list_archive_img_size = $rainbow_options['rainbow_course_list_archive_img_size'];  
            $rainbow_course_content_limit = $rainbow_options['rainbow_course_content_limit'];  
        }else{
            $rainbow_tutor_card_layout          = "";
            $rainbow_course_card_author_switch = "";
            $rainbow_course_card_image_switch  = "";
            $rainbow_course_card_title_switch  = "";
            $rainbow_course_card_rating_switch = "";
            $rainbow_course_card_meta_switch = "";
            $rainbow_course_card_add_to_cart_switch = "";
            $rainbow_course_card_pricing_switch = "";
            $rainbow_course_grid_archive_img_size = "";
            $rainbow_course_list_archive_img_size = "";
            $rainbow_course_content_limit = "";
        }

        $course_cart_layout = [
            'rainbow_tutor_card_layout'               => $rainbow_tutor_card_layout,
            'rainbow_course_card_author_switch'               => $rainbow_course_card_author_switch,
            'rainbow_course_card_image_switch'       => $rainbow_course_card_image_switch,
            'rainbow_course_card_title_switch'       => $rainbow_course_card_title_switch,
            'rainbow_course_card_rating_switch'      => $rainbow_course_card_rating_switch,
            'rainbow_course_card_meta_switch'      => $rainbow_course_card_meta_switch,
            'rainbow_course_card_add_to_cart_switch' => $rainbow_course_card_add_to_cart_switch,
            'rainbow_course_card_pricing_switch' => $rainbow_course_card_pricing_switch,
            'rainbow_course_grid_archive_img_size' => $rainbow_course_grid_archive_img_size,
            'rainbow_course_list_archive_img_size' => $rainbow_course_list_archive_img_size,
            'rainbow_course_content_limit' => $rainbow_course_content_limit,
        ];
        return $course_cart_layout;
    }

}