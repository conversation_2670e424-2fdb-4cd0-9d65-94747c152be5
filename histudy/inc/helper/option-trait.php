<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait rainbowOptionsTrait
{

    /**
     * @abstract get theme mod
     * return boolean
     */
    public static function get_rainbow_options($name)
    {
        $modval = get_theme_mod($name);
        if (!empty($modval)) {
            if (!is_array($modval)) {
                $newval = unserialize($modval);
            } else {
                $newval = $modval;
            }
            return $newval;
        }
        return false;
    }

    /**
     * @abstract get theme options
     * return object
     */
    public static function rainbow_get_options()
    {
        // Define the file path for the JSON file
        $file_path = RAINBOW_OPTIONS . 'predefined-data.json';

        global $wp_filesystem, $rainbow_optionss;

        // Initialize the WordPress filesystem
        if (empty($wp_filesystem)) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
            WP_Filesystem();
        }

        // Check if the file exists using WP_Filesystem
        if ($wp_filesystem->exists($file_path)) {
            // Get the file content using WP_Filesystem
            $predefined_data = $wp_filesystem->get_contents($file_path);

            if ($predefined_data) {
                $rainbow_optionss = json_decode($predefined_data, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    global $options;
                    $rainbow_optionss = isset($GLOBALS['rainbow_options']) ? wp_parse_args($GLOBALS['rainbow_options'], $options) : $rainbow_optionss;

                    return $rainbow_optionss;
                }
            }
        }

        return null; // Return null if the file doesn't exist or an error occurs
    }

    /**
     * @abstract get post object
     * return object
     */
    public static function rainbow_get_post_object()
    {
        global $post;
        return $post;
    }

    /**
     * @abstract get current user info
     * return array
     */

    public static function rainbow_get_current_user_var()
    {
        return wp_get_current_user();
    }

}