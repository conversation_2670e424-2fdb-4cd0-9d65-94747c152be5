<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
 
trait rainbowsocialTrait
{

    public static function rainbow_socials()
    {
        $rainbow_get_options = self::rainbow_get_options();
        $rainbow_social_attributes = $rainbow_get_options['histudy_social_icons'];
        $rainbow_socials = array(
            'social_facebook' => array(
                'icon' => 'feather-facebook',
                'url' => $rainbow_social_attributes['facebook-f'],
                'title' => __('Facebook', 'histudy'),
            ),
            'social_twitter' => array(
                'icon' => 'feather-twitter',
                'url' => $rainbow_social_attributes['twitter'],
                'title' => __('Twitter', 'histudy'),
            ),
            'social_linkedin' => array(
                'icon' => 'feather-linkedin',
                'url' => $rainbow_social_attributes['linkedin-in'],
                'title' => __('Linkedin', 'histudy'),
            ),
            'social_youtube' => array(
                'icon' => 'feather-youtube',
                'url' => $rainbow_social_attributes['youtube'],
                'title' => __('Youtube', 'histudy'),
            ),
            'social_instagram' => array(
                'icon' => 'feather-instagram',
                'url' => $rainbow_social_attributes['instagram'],
                'title' => __('Instagram', 'histudy'),
            ),
            'social_tiktok' => array(
                'icon' => 'fab fa-tiktok',
                'url' => $rainbow_social_attributes['tiktok'],
                'title' => __('Tiktok', 'histudy'),
            ),
            'social_snapchat' => array(
                'icon' => 'fab fa-snapchat',
                'url' => $rainbow_social_attributes['snapchat-ghost'],
                'title' => __('Snapchat', 'histudy'),
            ),
            'social_whatsapp' => array(
                'icon' => 'fab fa-whatsapp',
                'url' => $rainbow_social_attributes['whatsapp'],
                'title' => __('WhatsApp', 'histudy'),
            ),
            'social_pinterest' => array(
                'icon' => 'fab fa-pinterest',
                'url' => $rainbow_social_attributes['pinterest-p'],
                'title' => __('Pinterest', 'histudy'),
            ),
            'social_reddit' => array(
                'icon' => 'fab fa-reddit',
                'url' => $rainbow_social_attributes['reddit-alien'],
                'title' => __('Reddit', 'histudy'),
            ),
            'social_vimeo' => array(
                'icon' => 'fab fa-vimeo',
                'url' => $rainbow_social_attributes['vimeo-v'],
                'title' => __('Vimeo', 'histudy'),
            ),
            'social_qq' => array(
                'icon' => 'fab fa-qq',
                'url' => $rainbow_social_attributes['qq'],
                'title' => __('QQ', 'histudy'),
            ),
            'social_skype' => array(
                'icon' => 'fab fa-skype',
                'url' => $rainbow_social_attributes['skype'],
                'title' => __('Skype', 'histudy'),
            ),
            'social_wordpress' => array(
                'icon' => 'fab fa-wordpress',
                'url' => $rainbow_social_attributes['wordpress'],
                'title' => __('WordPress', 'histudy'),
            ),
            'social_discord' => array(
                'icon' => 'fab fa-discord',
                'url' => $rainbow_social_attributes['discord'],
                'title' => __('Discord', 'histudy'),
            ),
            'social_stack_overflow' => array(
                'icon' => 'fab fa-stack-overflow',
                'url' => $rainbow_social_attributes['stack-overflow'],
                'title' => __('Stack Overflow', 'histudy'),
            ),
            'social_stack_dribbble' => array(
                'icon' => 'fab fa-dribbble',
                'url' => $rainbow_social_attributes['dribbble'],
                'title' => __('Dribbble', 'histudy'),
            ),
            'social_stack_behance' => array(
                'icon' => 'fab fa-behance',
                'url' => $rainbow_social_attributes['behance'],
                'title' => __('Behance', 'histudy'),
            ),

        );
        return array_filter($rainbow_socials, array(__CLASS__, 'rainbow_filter_social'));
    }
    public static function rainbow_filter_social($args)
    {
        return ($args['url'] != '');
    }
}
