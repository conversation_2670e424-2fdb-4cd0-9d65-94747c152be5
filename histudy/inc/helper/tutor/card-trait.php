<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait rainbowTutorCardTrait
{
    /**
         * Rainbow Course Card Settings
         * @since 1.0.0
         * @version 1.0.0
         * @return $rbt_theme_course_card_settings
    */
    public static function rainbow_course_card_settings() {
        return;
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        if( TUTOR_ACTIVED ) {
            $rainbow_tutor_archive_card_layout = $rainbow_options['rainbow_tutor_archive_card_layout'];  
        } else {
            $rainbow_tutor_archive_card_layout = '';
        }
        $rbt_theme_course_card_settings = [
            'rainbow_tutor_archive_card_layout' => $rainbow_tutor_archive_card_layout
        ];
        return $rbt_theme_course_card_settings;
    }
}
