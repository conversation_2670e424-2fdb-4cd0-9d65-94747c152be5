<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */
trait becomeStudent {
    public static function rainbow_become_student_settings(){
        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        if( TUTOR_ACTIVED ){
            $rainbow_tutor_student_registration_repeater_title    =  $rainbow_options['rainbow_tutor_student_registration_repeater_title'];  
            $rainbow_tutor_student_registration_repeater_content  =  $rainbow_options['rainbow_tutor_student_registration_repeater_content'];  
            $rainbow_tutor_student_registration_badge_title       =  $rainbow_options['rainbow_tutor_student_registration_badge_title'];  
            $rainbow_tutor_student_registration_main_title       =  $rainbow_options['rainbow_tutor_student_registration_main_title'];  
            $rainbow_tutor_student_registration_main_desc       =  $rainbow_options['rainbow_tutor_student_registration_main_desc'];  
            $rainbow_tutor_student_registration_student_image       =  $rainbow_options['rainbow_tutor_student_registration_student_image'];  
            $student_image_show       =  $rainbow_options['student_image_show'];  
        }else{
            $rainbow_tutor_student_registration_repeater_title    =  array();
            $rainbow_tutor_student_registration_repeater_content  =  array();
            $rainbow_tutor_student_registration_badge_title       =  '';
            $rainbow_tutor_student_registration_main_title       =  '';
            $rainbow_tutor_student_registration_main_desc       =  '';
            $rainbow_tutor_student_registration_student_image       =  '';
            $student_image_show       =  '';
        }

        $becomeInstructorTrait                                       =  [
            'rainbow_tutor_student_registration_repeater_title'   => $rainbow_tutor_student_registration_repeater_title,
            'rainbow_tutor_student_registration_repeater_content' => $rainbow_tutor_student_registration_repeater_content,
            'rainbow_tutor_student_registration_badge_title'      => $rainbow_tutor_student_registration_badge_title,
            'rainbow_tutor_student_registration_main_title'      => $rainbow_tutor_student_registration_main_title,
            'rainbow_tutor_student_registration_main_desc'      => $rainbow_tutor_student_registration_main_desc,
            'rainbow_tutor_student_registration_student_image'      => $rainbow_tutor_student_registration_student_image,
            'student_image_show'      => $student_image_show,
        ];
        return $becomeInstructorTrait;
    }

}