<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */

trait rainbowPageTitleTrait
{
  // Page title
    public static function rainbow_get_page_title()
    {

        $rainbow_options    = Rainbow_Helper::rainbow_get_options();
        $rainbow_blog_details_text = (isset($rainbow_options['rainbow_blog_details_text']) && !empty($rainbow_options['rainbow_blog_details_text'])) ? $rainbow_options['rainbow_blog_details_text']: get_the_title(); 
        $rainbow_single_product_banner_title =  class_exists('WooCommerce') && isset( $rainbow_options['rainbow_single_product_banner_title'] ) && !empty($rainbow_options['rainbow_single_product_banner_title']) ? $rainbow_options['rainbow_single_product_banner_title'] : __( 'Product Details', 'histudy' );

        if (is_404()) {
            $rainbow_title = 'error_title';
        } elseif (is_search()) {
            $rainbow_title = esc_html__('Search Results for : ', 'histudy') . get_search_query();
        } elseif (is_home()) {
            if (get_option('page_for_posts')) {
                $rainbow_title = get_the_title(get_option('page_for_posts'));
            } else {
                $rainbow_title = apply_filters("rainbow_blog_title", esc_html__('All Posts', 'histudy'));
            }
        }
        elseif (is_archive()) {

            if (is_post_type_archive("rainbow_projects")) {
                $rainbow_title = get_the_archive_title();
            } else {
                $rainbow_title = get_the_archive_title();
            }
        } elseif (is_single()) {
            $rainbow_title = get_the_title(); 

        } else {
            $banner_title           = rainbow_get_acf_data("rainbow_custom_title");
            if ( $banner_title ) {
             $rainbow_title            = rainbow_get_acf_data("rainbow_custom_title");
         
            }else{
                $rainbow_title = get_the_title();

            }

        }
        return $rainbow_title;
    }

}
