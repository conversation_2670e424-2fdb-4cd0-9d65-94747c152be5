<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package histudy
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
 
/**
 * histudy_get_nav_menus
 */

 
if ( ! function_exists( 'histudy_related_post_grid' )) {
    function histudy_related_post_grid(){
        // Get Value
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $post_id                = get_the_id();
        $active_post            = array( $post_id );
        $related_post_count     = $rainbow_options['show_related_post_number'];
        $query_type = $rainbow_options['related_post_query'];
        $args = array(
            'post__not_in'           => $active_post,
            'posts_per_page'         => $related_post_count,
            'post_status'            => 'publish',
            'no_found_rows'          => true,
            'update_post_term_cache' => false,
            'ignore_sticky_posts'    => true,
        );
        if( !empty($rainbow_options['related_post_sort']) && isset($rainbow_options['related_post_sort']) ){
            $post_order = $rainbow_options['related_post_sort'];
            if( $post_order == 'rand' ){
                $args['orderby'] = 'rand';
            }elseif( $post_order == 'popular' ){
                $args['orderby'] = 'comment_count';
            }elseif( $post_order == 'modified' ){
                $args['orderby'] = 'modified';
                $args['order']   = 'ASC';
            }elseif( $post_order == 'recent' ){
                $args['orderby'] = '';
                $args['order']   = '';
            }
        }
        if( $query_type == 'author' ){
            $args['author'] = get_the_author_meta( 'ID' );
        }
        elseif( $query_type == 'tag' ){
            $tags_ids  = array();
            $post_tags = get_the_terms( $post_id, 'post_tag' );

            if( ! empty( $post_tags ) ){
                foreach( $post_tags as $individual_tag ){
                    $tags_ids[] = $individual_tag->term_id;
                }

                $args['tag__in'] = $tags_ids;
            }
        }
        else{
            $category_ids = array();
            $categories   = get_the_category( $post_id );
            foreach( $categories as $individual_category ){
                $category_ids[] = $individual_category->term_id;
            }
            $args['category__in'] = $category_ids;
        }

        $related_query = new \wp_query( $args );

        if( $related_query->have_posts() ) { ?>
            <div class="related-post pt--60 pb--30">
                <?php if ( !empty( $rainbow_options['related_post_area_title'] ) ): ?>
                <div class="section-title text-start mb--40">
                    <span class="subtitle bg-primary-opacity">
                        <?php echo esc_html( $rainbow_options['related_post_area_before_title'] ); ?>
                    </span>
                    <h2 class="title"><?php echo esc_html( $rainbow_options['related_post_area_title'] ); ?></h2>
                </div>
                <?php endif; ?>
                    <?php
                    while ( $related_query->have_posts() ) {
                        $related_query->the_post();
                        $title = get_the_title();
                        $title = wp_trim_words( $title,  $rainbow_options['related_title_limit'] );
                        ?>
                    <div class="rbt-card card-list variation-02 rbt-hover mt--30">
                      <?php if(has_post_thumbnail()){ ?>
                        <div class="rbt-card-img">
                              <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('grid-small-post-thumb'); ?>
                            </a>
                        </div>
                      <?php } ?>
                    <div class="rbt-card-body">
                        <h5 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php echo esc_html($title); ?></a>
                        </h5>
                        <div class="rbt-card-bottom">
                            <a class="transparent-button" href="<?php the_permalink(); ?>"><?php echo esc_html__( 'Read Article', 'histudy' ); ?><i><svg width="17" height="12" xmlns="http://www.w3.org/2000/svg"><g stroke="#27374D" fill="none" fill-rule="evenodd"><path d="M10.614 0l5.629 5.629-5.63 5.629"></path><path stroke-linecap="square" d="M.663 5.572h14.594"></path></g></svg></i></a>
                        </div>
                    </div>
                </div>
                    <?php } ?>
            </div>
    <?php }
        wp_reset_postdata();
    }
}

if (!function_exists('rainbow_get_nav_menus')) {
    function rainbow_get_nav_menus()
    {

        $menus = wp_get_nav_menus();
        $menus_data = array(
            'default' => esc_html__('Default', 'histudy')
        );
        if (!empty($menus) && !is_wp_error($menus)) {
            foreach ($menus as $item) {
                $menus_data[$item->slug] = $item->name;
            }
        }
        return $menus_data;
    }
}
 

/**
 * @param $classes
 * @return array
 */
if (!function_exists('rainbow_body_classes')) {
    function rainbow_body_classes($classes)
    {

        $rainbow_options = Rainbow_Helper::rainbow_get_options();


        global $post;

        if (isset($post)) {
            $classes[] = $post->post_type . '-' . $post->post_name;
        }

        // Adds a class of hfeed to non-singular pages.
        if (is_singular()) {
            $classes[] = ' ';
        } 

        // Run code only for Single post page
        if (is_single() && 'post' == get_post_type()) {
            $classes[] = ' ';
        }
 
        $active_dark_mode = '';


        // Header Sticky & Transparent Classes
        $header_layout = Rainbow_Helper::rainbow_header_layout();
        $header_transparent = $header_layout['header_transparent'];
        $header_sticky = $header_layout['header_sticky'];
        if(!empty($header_sticky) && "no" !== $header_sticky && "0" !== $header_sticky && false !== $header_sticky){
            $classes[] = 'rbt-header-sticky';
        }
        if(!empty($header_transparent) && "no" !== $header_transparent && "0" !== $header_transparent && false !== $header_transparent){
            $classes[] = 'rbt-transparent-header rbt-header-transpernt-active';
        }

        $global_dark_light_options = isset( $rainbow_options['active_dark_mode'] ) ? $rainbow_options['active_dark_mode'] : '';
        $client_cookie_key = $global_dark_light_options == 1 ? 'client_dark_mode_style_cookie' : 'client_light_mode_style_cookie';
        if (isset($_COOKIE[$client_cookie_key])) {
            $styleModeClass = $_COOKIE[$client_cookie_key] == 'dark' ? 'active-dark-mode':'active-light-mode';
        } else {
            $styleModeClass = $global_dark_light_options == 1 ? 'active-dark-mode':'active-light-mode';
        }
        $classes[] = $styleModeClass;

        return $classes;
    }
}

add_filter('body_class', 'rainbow_body_classes', 9999);

/**
 * @param $classes
 * @return string
 */
if (!function_exists('rainbow_admin_body_classes')) {
    function rainbow_admin_body_classes($classes)
    {
        global $post;
        if (isset($post)) {
            return $post->post_type . '-' . $post->post_name;
        }
    }
}

//add_filter('admin_body_class', 'rainbow_admin_body_classes');

/**
 * Get unique ID.
 */
if (!function_exists('rainbow_unique_id')) {
    function rainbow_unique_id($prefix = '')
    {
        static $id_counter = 0;
        if (function_exists('wp_unique_id')) {
            return wp_unique_id($prefix);
        }
        return $prefix . (string)++$id_counter;
    }
}

/**
 * Add a pingback url auto-discovery header for singularly identifiable articles.
 */
if (!function_exists('rainbow_pingback_header')) {
    function rainbow_pingback_header()
    {
        if (is_singular() && pings_open()) {
            printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
        }
    }
}

add_action('wp_head', 'rainbow_pingback_header');

/**
 * Comment navigation
 */
if (!function_exists('rainbow_get_post_navigation')) {
    function rainbow_get_post_navigation()
    {
        if (get_comment_pages_count() > 1 && get_option('page_comments')):
            require(get_template_directory() . '/inc/comment-nav.php');
        endif;
    }
}

require get_template_directory() . '/inc/comment-form.php';