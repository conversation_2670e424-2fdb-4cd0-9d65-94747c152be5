<?php
/**
 * ReduxFramework Sample Config File
 *
 * For full documentation, please visit: http://docs.reduxframework.com/
 */
if (!class_exists('Redux')) {
    return;
}
if ( !function_exists('awLoadPostTemplateHistudy') ){

    function awLoadPostTemplateHistudy($post_type){
        $awca = array();
        if(isset($_REQUEST['page']) && $_REQUEST['page'] == "histudy_options"){
            $args = array( 'post_type' => $post_type, 'posts_per_page' => -1 );

            $queryAnwar = new WP_Query( $args );
            $count = 0;
            if( $queryAnwar->have_posts() ) {
                while( $queryAnwar->have_posts() ): $queryAnwar->the_post();

                    $data=array();
                    $awca[get_the_ID()] = get_the_title();
                    $count++;
                endwhile;

                wp_reset_postdata();
                wp_reset_query();
            } else {
                $awca=array();
                $awca['title']= "No template found.";

            }

            return $awca;
        }
    }


}
$opt_name = 'rainbow_options';
$theme = wp_get_theme();
$menu_type = HiStudyEducationThemes::$licence_activated ? 'submenu': 'hidden';
$args = array(
    // TYPICAL -> Change these values as you need/desire
    'opt_name' => $opt_name,
    // This is where your data is stored in the database and also becomes your global variable name.
    'disable_tracking' => true,
    'display_name' => $theme->get('Name'),
    // Name that appears at the top of your panelr
    'display_version' => $theme->get('Version'),
    // Version that appears at the top of your panel
    'menu_type' => $menu_type,
    //Specify if the admin menu should appear or not. Options: menu or submenu (Under appearance only)
    'allow_sub_menu' => true,
    // Show the sections below the admin menu item or not
    'menu_title' => __('Theme Options', 'histudy'),
    'page_title' => __('Theme Options', 'histudy'),
    // You will need to generate a Google API key to use this feature.
    // Please visit: https://developers.google.com/fonts/docs/developer_api#Auth
    //'google_api_key'       => 'AIzaSyC2GwbfJvi-WnYpScCPBGIUyFZF97LI0xs',
    // Set it you want google fonts to update weekly. A google_api_key value is required.
    'google_update_weekly' => false,
    // Must be defined to add google fonts to the typography module
    'async_typography' => false,
    // Use a asynchronous font on the front end or font string
    //'disable_google_fonts_link' => true,                    // Disable this in case you want to create your own google fonts loader
    'admin_bar' => true,
    // Show the panel pages on the admin bar
    'admin_bar_icon' => 'dashicons-menu',
    // Choose an icon for the admin bar menu
    'admin_bar_priority' => 50,
    // Choose an priority for the admin bar menu
    'global_variable' => '',
    // Set a different name for your global variable other than the opt_name
    'dev_mode' => false,
    'forced_dev_mode_off' => false,
    // Show the time the page took to load, etc
    'update_notice' => false,
    // If dev_mode is enabled, will notify developer of updated versions available in the GitHub Repo
    'customizer' => false,
    // Enable basic customizer support
    //'open_expanded'     => true,                    // Allow you to start the panel in an expanded way initially.
    //'disable_save_warn' => true,                    // Disable the save warning when a user changes a field

    // OPTIONAL -> Give you extra features
    'page_priority' => null,
    // Order where the menu appears in the admin area. If there is any conflict, something will not show. Warning.
    'page_parent' => 'themes.php',
    // For a full list of options, visit: http://codex.wordpress.org/Function_Reference/add_submenu_page#Parameters
    'page_permissions' => 'manage_options',
    // Permissions needed to access the options panel.
    'menu_icon' => '',
    // Specify a custom URL to an icon
    'last_tab' => '',
    // Force your panel to always open to a specific tab (by id)
    'page_icon' => 'icon-themes',
    // Icon displayed in the admin panel next to your menu_title
    'page_slug' => RAINBOW_THEME_FIX . '_options',
    // Page slug used to denote the panel, will be based off page title then menu title then opt_name if not provided
    'save_defaults' => true,
    // On load save the defaults to DB before user clicks save or not
    'default_show' => true,
    // If true, shows the default value next to each field that is not the default value.
    'default_mark' => '',
    // What to print by the field's title if the value shown is default. Suggested: *
    'show_import_export' => true,
    // Shows the Import/Export panel when not used as a field.

    // CAREFUL -> These options are for advanced use only
    'transient_time' => 60 * MINUTE_IN_SECONDS,
    'output' => true,
    // Global shut-off for dynamic CSS output by the framework. Will also disable google fonts output
    'output_tag' => true,
    // Allows dynamic CSS to be generated for customizer and google fonts, but stops the dynamic CSS from going to the head
    'footer_credit' => '&nbsp;',
    // Disable the footer credit of Redux. Please leave if you can help it.

    // FUTURE -> Not in use yet, but reserved or partially implemented. Use at your own risk.
    'database' => '',
    // possible: options, theme_mods, theme_mods_expanded, transient. Not fully functional, warning!
    'use_cdn' => true,
    // If you prefer not to use the CDN for Select2, Ace Editor, and others, you may download the Redux Vendor Support plugin yourself and run locally or embed it in your code.
    'hide_expand' => true,
    // This variable determines if the ‘Expand Options’ buttons is visible on the options panel.
);
Redux::disable_demo();
Redux::setArgs($opt_name, $args);

/*
 * ---> END ARGUMENTS
 */

Redux::setSection($opt_name, array(
    'title' => __('General', 'histudy'),
    'id' => 'rainbow_general',
    'icon' => 'el el-cog',
));
Redux::setSection($opt_name, array(
    'title' => __('General Setting', 'histudy'),
    'id' => 'histudy-general-setting',
    'icon' => 'el el-adjust-alt',
    'subsection' => true,
    'fields' => array(

        array(
            'id' => 'active_dark_mode',
            'type' => 'switch',
            'title' => esc_html__('Switch to Dark Mode', 'histudy'),
            'on' => esc_html__('Yes', 'histudy'),
            'off' => esc_html__('No', 'histudy'),
            'default' => true,
        ),

        array(
            'id' => 'show_ld_switcher_form_user_end',
            'type' => 'switch',
            'title' => esc_html__('Enabled Dark/Light Switcher Form User End', 'histudy'),
            'on' => esc_html__('Enabled', 'histudy'),
            'off' => esc_html__('Disabled', 'histudy'),
            'default' => true,
        ),

        array(
            'id' => 'rainbow_logo_type',
            'type' => 'button_set',
            'title' => __('Select Logo Type', 'histudy'),
            'subtitle' => __('Select logo type, if the image is chosen the existing options of  image below will work, or text option will work. (Note: Used when Transparent Header is enabled.)', 'histudy'),
            'options' => array(
                'image' => 'Image',
                'text' => 'Text',
            ),
            'default' => 'image',
        ),

        array(
            'id' => 'rainbow_head_logo',
            'title' => __('Default Logo', 'histudy'),
            'subtitle' => __('Upload the main logo of your site. ( Recommended size: Width 267px and Height: 70px )', 'histudy'),
            'type' => 'media',
            'default' => array(
                'url' => RAINBOW_IMG_URL . 'logo/logo-black.png'
            ),
            'required' => array('rainbow_logo_type', 'equals', 'image'),
        ),
        array(
            'id' => 'rainbow_light_logo',
            'title' => __('Light Menu Logo', 'histudy'),
            'subtitle' => __('Upload the main logo of your site. ( Recommended size: Width 267px and Height: 70px )', 'histudy'),
            'type' => 'media',
            'default' => array(
                'url' => RAINBOW_IMG_URL . 'logo/logo.png'
            ),
            'required' => array('rainbow_logo_type', 'equals', 'image'),
        ),
        array(
            'id' => 'rainbow_gradient_logo',
            'title' => __('Gradient Menu Logo', 'histudy'),
            'subtitle' => __('Upload the main logo of your site. ( Recommended size: Width 267px and Height: 70px )', 'histudy'),
            'type' => 'media',
            'default' => array(
                'url' => RAINBOW_IMG_URL . 'logo/logo.png'
            ),
            'required' => array('rainbow_logo_type', 'equals', 'image'),
        ),

        array(
            'id' => 'rainbow_dark_mode_logo',
            'title' => __('Dark Mode  Logo', 'histudy'),
            'subtitle' => __('Upload the main logo of your site. ( Recommended size: Width 267px and Height: 70px )', 'histudy'),
            'type' => 'media',
            'default' => array(
                'url' => RAINBOW_IMG_URL . 'logo/logo.png'
            ),
            'required' => array('rainbow_logo_type', 'equals', 'image'),
        ),
        array(
            'id' => 'rainbow_logo_padding',
            'type' => 'spacing',
            'title' => __('Logo Padding', 'histudy'),
            'subtitle' => __('Controls the top, right, bottom and left padding of the logo. (Note: Used when Transparent Header is enabled.)', 'histudy'),
            'mode' => 'padding',
            'units' => array('em', 'px'),
            'default' => array(
                'padding-top' => 'px',
                'padding-right' => 'px',
                'padding-bottom' => 'px',
                'padding-left' => 'px',
                'units' => 'px',
            ),
            'output' => array('.header-left .logo a'),

            'required' => array('rainbow_logo_type', 'equals', 'image'),
        ),

        array(
            'id' => 'logo_height',
            'type' => 'text',
            'title' => __('Logo Height(PX)', 'histudy'),
            'subtitle' => __('Enter the height for the logo in pixels.example: 100px', 'histudy'),
            'min' => 1,
            'validate' => 'number',
            'max' => 1000,
            'step' => 1,
        ),

        array(
            'id' => 'logo_width',
            'type' => 'text',
            'title' => __('Logo Width(PX)', 'histudy'),
            'subtitle' => __('Enter the width for the logo in pixels. example: 150px', 'histudy'),
            'validate' => 'number',
            'min' => 1,
            'max' => 1000,
            'step' => 1,
        ),

        array(
            'id' => 'rainbow_logo_text_font',
            'type' => 'typography',
            'title' => __('Site Title Font Settings', 'histudy'),
            'required' => array('rainbow_logo_type', 'equals', 'text'),
            'google' => true,
            'subsets' => false,
            'line-height' => false,
            'text-transform' => true,
            'transition' => false,
            'text-align' => false,
            'preview' => false,
            'all_styles' => true,
            'output' => array('.header-left .logo a, .haeder-default .header-brand a'),
            'units' => 'px',
            'subtitle' => __('Controls the font settings of the site title. (Note: Used when Transparent Header is enabled.)', 'histudy'),
            'default' => array(
                'google' => true,
            )
        ),
        // End logo
        array(
            'id' => 'rainbow_scroll_to_top_enable',
            'type' => 'button_set',
            'title' => __('Enable Back To Top', 'histudy'),
            'subtitle' => __('Enable the back to top button that appears in the bottom right corner of the screen.', 'histudy'),
            'options' => array(
                'yes' => __('Yes', 'histudy'),
                'no' => __('No', 'histudy'),
            ),
            'default' => 'no'
        ),

        array(
            'id' => 'histudy_preloader_on_off',
            'type' => 'switch',
            'title' => esc_html__('Preloader on/off', 'histudy'),
            'on' => esc_html__('Enabled', 'histudy'),
            'off' => esc_html__('Disabled', 'histudy'),
            'default' => 'no',
        ),


    )
));

Redux::setSection($opt_name,
    array(
        'title' => __('Site Information', 'histudy'),
        'id' => 'site_information_section',
        'heading' => __('Site Information', 'histudy'),
        'icon' => 'el el-info-circle',
        'subsection' => true,
        'fields' => array(
            array(
                'id' => 'email',
                'type' => 'text',
                'title' => __('Email', 'histudy'),
                'default' => '<EMAIL>',
            ),
            array(
                'id' => 'phone',
                'type' => 'text',
                'title' => __('Phone', 'histudy'),
                'description' => 'Please enter your phone',
                'default' => '******-555-0174',
            ),
            array(
                'id' => 'followers_filed_1_icon',
                'type' => 'text',
                'title' => __('Followers Input 1 - Icon', 'histudy'),
                'subtitle' => __( 'Only apply on Header 10, Header 1 and Header 3 ', 'histudy' ),
                'description' => __('You can find icon class form here: https://fontawesome.com', 'histudy'),
                'default' => 'fab fa-facebook-square',
            ),
            array(
                'id' => 'followers_filed_1_label',
                'type' => 'text',
                'subtitle' => __( 'Only apply on Header 10, Header 1 and Header 3 ', 'histudy' ),
                'title' => __('Followers Input 1 - Label', 'histudy'),
                'default' => '500k Followers',
            ),
            array(
                'id' => 'followers_filed_1_link',
                'type' => 'text',
                'subtitle' => __( 'Only apply on Header 1 and Header 3 ', 'histudy' ),
                'title' => __('Followers Input 1 - Link', 'histudy'),
                'default' => '#',
            ),
            array(
                'id' => 'followers_filed_2_icon',
                'type' => 'text',
                'title' => __('Followers Input 2 - Icon', 'histudy'),
                'description' => __('You can find icon class form here: https://fontawesome.com', 'histudy'),
                'default' => 'fab fa-instagram',
            ),
            array(
                'id' => 'followers_filed_2_label',
                'type' => 'text',
                'subtitle' => __( 'Only apply on Header 1 and Header 3 ', 'histudy' ),
                'title' => __('Followers Input 2 - Label', 'histudy'),
                'default' => '100k Followers',
            ),
            array(
                'id' => 'followers_filed_2_link',
                'type' => 'text',
                'subtitle' => __( 'Only apply on Header 1 and Header 3 ', 'histudy' ),
                'title' => __('Followers Input 2 - Link', 'histudy'),
                'default' => '#',
            ),

            array(
                'id' => 'rainbow_mobile_header_message',
                'type' => 'editor',
                'subtitle' => __( 'Apply only mobile sidebar ', 'histudy' ),
                'title' => __('Mobile Header Message', 'histudy'),
                'default' => 'Histudy is a education website template. You can customize all.',
                'args' => array(
                    'teeny' => true,
                    'textarea_rows' => 2,
                ),
            ),
            array(
                'id' => 'rainbow_header_center_message',
                'subtitle' => __( 'Apply For Header 1 And Header 3 ', 'histudy' ),
                'type' => 'editor',
                'title' => __('Header Center Message', 'histudy'),
                'default' => 'Intro price. Get Histudy for Big Sale -95% off.',
                'args' => array(
                    'teeny' => true,
                    'textarea_rows' => 2,
                ),
            ),
            array(
            'id' => 'rainbow_show_more_text',
                'type' => 'text',
                'title' => __('Show More Text', 'histudy'),
                'default' => 'Show More',
            ),
            array(
            'id' => 'rainbow_show_less_text',
                'type' => 'text',
                'title' => __('Show Less Text', 'histudy'),
                'default' => 'Show Less',
            ),

        )
    )
);

Redux::setSection($opt_name,
    array(
        'title' => __('Social Networks', 'histudy'),
        'id' => 'socials_section',
        'heading' => __('Social Networks', 'histudy'),
        'icon' => 'el el-twitter',
        'subsection' => true,
        'fields' => array(
            array(
                'id' => 'histudy_social_label',
                'type' => 'text',
                'title' => __('Social Label', 'histudy'),
                'default' => __('Find With Us', 'histudy')
            ),
            array(
                'id' => 'histudy_social_icons',
                'type' => 'sortable',
                'title' => __('Social Icons', 'histudy'),
                'subtitle' => __('Enter social links to show the icons. In case you want to hide any field, just keep that field empty', 'histudy'),
                'mode' => 'text',
                'label' => true,
                'options' => array(
                    'facebook-f' => '',
                    'twitter' => '',
                    'pinterest-p' => '',
                    'linkedin-in' => '',
                    'instagram' => '',
                    'vimeo-v' => '',
                    'dribbble' => '',
                    'behance' => '',
                    'youtube' => '',
                    'github' => '',
                    'tiktok' => '',
                    'telegram-plane' => '',
                    'snapchat-ghost' => '',
                    'whatsapp' => '',
                    'reddit-alien' => '',
                    'qq' => '',
                    'skype' => '',
                    'stack-overflow' => '',
                    'discord' => '',
                    'wordpress' => '',
                ),
                'default' => array(
                    'facebook-f' => 'https://www.facebook.com/',
                    'twitter' => 'https://twitter.com/',
                    'pinterest-p' => '',
                    'linkedin-in' => 'https://linkedin.com/',
                    'instagram' => 'https://instagram.com/',
                    'vimeo-v' => '',
                    'dribbble' => '',
                    'behance' => '',
                    'youtube' => '',
                    'github' => '',
                ),
            )
        )
    )
);


/**
 * Menu
 */
Redux::setSection($opt_name, array(
    'title' => __('Mobile Bottom Navigation', 'histudy'),
    'id' => 'rainbow_menu',
    'icon' => 'el el-list-alt',
));
Redux::setSection($opt_name, array(
    'title'      => __('Bottom Navigation Menu', 'histudy'),
    'id'         => 'rainbow_menu_mobile',
    'icon'       => 'el el-list-alt',
    'subsection' => true,
    'fields'     => array(
        array(
            'id' => 'enable_mobile_menu',
            'type' => 'switch',
            'title' => __('Enable Mobile Menu', 'histudy'),
            'subtitle' => __('Enable/Disable mobile menu.', 'histudy'),
            'default' => false
        ),
        array(
            'id' => 'enable_home_menu',
            'type' => 'switch',
            'title' => __('Enable Home', 'histudy'),
            'subtitle' => __('Enable/Disable Home.', 'histudy'),
            'default' => true,
            'required' => array('enable_mobile_menu', 'equals', true),
        ),
        array(
            'id' => 'enable_course_menu',
            'type' => 'switch',
            'title' => __('Enable Course', 'histudy'),
            'subtitle' => __('Enable/Disable Course.', 'histudy'),
            'default' => true,
            'required' => array('enable_mobile_menu', 'equals', true),
        ),
        array(
            'id' => 'enable_myprofile_menu',
            'type' => 'switch',
            'title' => __('Enable Myprofile', 'histudy'),
            'subtitle' => __('Enable/Disable Profile.', 'histudy'),
            'default' => true,
            'required' => array('enable_mobile_menu', 'equals', true),
        ),
        array(
            'id' => 'enable_cart_menu',
            'type' => 'switch',
            'title' => __('Enable Cart', 'histudy'),
            'subtitle' => __('Enable/Disable Cart.', 'histudy'),
            'default' => false,
            'required' => array('enable_mobile_menu', 'equals', true),
        ),
        array(
            'id' => 'enable_category_menu',
            'type' => 'switch',
            'title' => __('Enable Category', 'histudy'),
            'subtitle' => __('Enable/Disable Category.', 'histudy'),
            'default' => false,
            'required' => array('enable_mobile_menu', 'equals', true),
        ),
        array(
            'id'            => 'histudy_mobile_menu_item',
            'type'          => 'repeater',
            'title'         => __('Menu Item', 'histudy'),
            'group_values' => true,
            'subtitle'      => __('Insert your mobile menu item', 'histudy'),
            'desc'          => __('Insert menu item icon and label', 'histudy'),
            'required' => array('enable_mobile_menu', 'equals', true),
            'fields'        => array(
                array(
                    'id'          => 'menu_title',
                    'type'        => 'text',
                    'title'       => __('Menu Title', 'histudy'),
                    'placeholder' => __('Enter menu title', 'histudy'),
                ),
                array(
                    'id'       => 'menu_icon',
                    'type'     => 'text',
                    'title'    => __('Menu Icon (Feather Icon)', 'histudy'),
                    'subtitle' => __('Enter the Menu icon name (e.g., "feather-list"). if using icon bottom image field is not working', 'histudy'),
                    'default'  => 'feather-list',
                    'class'    => 'histudy-sticky-menu', // Add your custom class here
                    'placeholder' => __('Click Here', 'histudy'),
                ),
                array(
                    'id'       => 'menu_image',
                    'type'     => 'media',
                    'subtitle' => __('Upload the menu Image. if using Image top icon field is not working', 'histudy'),
                    'title'    => __('Sticky Menu Image', 'histudy'),
                ),
                array(
                    'id'          => 'menu_url',
                    'type'        => 'text',
                    'default'  => '#',
                    'title'       => __('Menu URL', 'histudy'),
                    'placeholder' => __('Enter menu url', 'histudy'),
                ),
            ),
        ),
    )
));

/**
 * Header
 */
Redux::setSection($opt_name, array(
    'title' => __('Header', 'histudy'),
    'id' => 'header_id',
    'icon' => 'el el-minus',
    'fields' => array(
        array(
            'id' => 'rainbow_enable_header',
            'type' => 'switch',
            'title' => __('Header', 'histudy'),
            'subtitle' => __('Enable or disable the header area.', 'histudy'),
            'default' => true
        ),
        array(
            'id' => 'rainbow_header_sticky',
            'type' => 'switch',
            'title' => __('Enable Sticky Header ', 'histudy'),
            'subtitle' => __('Enable to activate the sticky header.', 'histudy'),
            'default' => true,
            'required' => array('rainbow_enable_header', 'equals', true),
        ),
        array(
            'id'                        => 'rainbow_header_type',
            'title'                     => esc_html__('Header Type','histudy'),
            'subtitle'                  => esc_html__('Select header type, if the default is chosen the existing options below will work, or choose the custom option to get headers from header post type.','histudy'),
            'type'                      => 'button_set',
            'options'                   => array(
                'default'                   => esc_html__('Default','histudy'),
                'custom'                    => esc_html__('Custom','histudy'),
            ),
            'default'                   => 'default',
        ),
        array(
            'id'       => 'rainbow_header_custom',
            'type'     => 'select',
            'title'                 => esc_html__('Select Header Template', 'histudy'),
            'subtitle'              => esc_html__('Choose the header template where you created headers from the header post type.', 'histudy'),
            'options'  => awLoadPostTemplateHistudy('rbt_header_builder'),
            'required'              => array('rainbow_header_type','equals', 'custom'),
        ),
        array(
            'id' => 'rainbow_select_header_template',
            'type' => 'image_select',
            'title' => __('Select Header Layout', 'histudy'),
            'options' => array(
                '1' => array(
                    'alt' => __('Header Layout 1', 'histudy'),
                    'title' => __('Header Layout 1', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/1.webp',
                ),
                '2' => array(
                    'alt' => __('Header Layout 2', 'histudy'),
                    'title' => __('Header Layout 2', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/2.webp',
                ),
                '3' => array(
                    'alt' => __('Header Layout 3', 'histudy'),
                    'title' => __('Header Layout 3', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/3.webp',
                ),
                '4' => array(
                    'alt' => __('Header Layout 4', 'histudy'),
                    'title' => __('Header Layout 4', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/4.webp',
                ),
                '5' => array(
                    'alt' => __('Header Layout 5', 'histudy'),
                    'title' => __('Header Layout 5', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/5.webp',
                ),
                '6' => array(
                    'alt' => __('Header Layout 6', 'histudy'),
                    'title' => __('Header Layout 6', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/6.webp',
                ),
                '7' => array(
                    'alt' => __('Header Layout 7', 'histudy'),
                    'title' => __('Header Layout 7', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/7.webp',
                ),
                '8' => array(
                    'alt' => __('Header Layout 8', 'histudy'),
                    'title' => __('Header Layout 8', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/8.webp',
                ),
                '9' => array(
                    'alt' => __('Header Layout 9', 'histudy'),
                    'title' => __('Header Layout 9', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/9.webp',
                ),
                '10' => array(
                    'alt' => __('Header Layout 10', 'histudy'),
                    'title' => __('Header Layout 10', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/10.webp',
                ),
                '11' => array(
                    'alt' => __('Header Layout 11', 'histudy'),
                    'title' => __('Header Layout 11', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/11.webp',
                ),
                '12' => array(
                    'alt' => __('Header Layout 12', 'histudy'),
                    'title' => __('Header Layout 12', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/12.webp',
                ),
                'none' => array(
                    'alt' => __('None', 'histudy'),
                    'title' => __('None', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/header/none.webp',
                ),

            ),
            'default' => '1',
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_header_type', 'equals', 'default'),
            ),

        ),
        array(
            'id' => 'rainbow_header_side_info',
            'type' => 'textarea',
            'title' => __('Side Info', 'histudy'),
            'default' => __('', 'histudy'),
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['12']),
            ),
        ),
        array(
            'id' => 'rainbow_header_transparent',
            'type' => 'switch',
            'title' => __('Enable Transparent Header ', 'histudy'),
            'subtitle' => __('Enable to activate the transparent header.', 'histudy'),
            'default' => false,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),

        // Header button
        array(
            'id' => 'rainbow_enable_button',
            'type' => 'switch',
            'title' => __('Header button', 'histudy'),
            'subtitle' => __('Enable or disable header button.', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_address',
            'type' => 'text',
            'title' => __('Address', 'histudy'),
            'default' => __('', 'histudy'),
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['6', '7']),
            ),
        ),
        array(
            'id' => 'header_address_url',
            'type' => 'text',
            'title' => __('Address URL', 'histudy'),
            'default' => __('', 'histudy'),
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['6']),
            ),
        ),
        array(
            'id' => 'header_button_txt',
            'type' => 'text',
            'title' => __('Button Text', 'histudy'),
            'default' => __('Enroll Now', 'histudy'),
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_button_url',
            'type' => 'text',
            'title' => __('Button Url', 'histudy'),
            'default' => '#',
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_button_target',
            'type' => 'select',
            'title' => __('Button Target', 'histudy'),
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '10', '11']),
            ),
            'options' => array(
                '_blank' => __('Blank', 'histudy'),
                '_self' => __('Self', 'histudy'),
                '_parent' => __('Parent', 'histudy'),
                '_top' => __('Top', 'histudy'),
            ),
            'default' => '_blank',
        ),
        array(
            'id' => 'header_button_type',
            'type' => 'select',
            'title' => __('Button Type', 'histudy'),
            'required' => array(
                array('rainbow_enable_button', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '10', '11']),
            ),
            'options' => array(
                'rbt-btn btn-primary radius-round btn-sm' => __('Primary Button', 'histudy'),
                'rbt-btn bg-primary-opacity radius-round btn-sm' => __('Primary Opacity Button', 'histudy'),
                'rbt-btn btn-border radius-round btn-sm' => __('Border Button', 'histudy'),
                'rbt-btn rbt-marquee-btn marquee-auto btn-border-gradient radius-round btn-sm hover-transform-none' => __('Gradient Border Marquee', 'histudy'),
                'rbt-btn btn-gradient radius-round btn-sm' => __('Primary Gradient Button', 'histudy'),
                'rbt-btn rbt-switch-btn btn-gradient btn-sm hover-transform-none' => __('Primary Gradient SM Radius', 'histudy'),
                'rbt-moderbt-btn' => __('Modern Button', 'histudy'),

            ),
            'default' => 'rbt-btn rbt-marquee-btn marquee-auto btn-border-gradient radius-round btn-sm hover-transform-none',
        ),
        // header button 2
        array(
            'id' => 'rainbow_enable_button_2',
            'type' => 'switch',
            'title' => __('Header Second Button', 'histudy'),
            'subtitle' => __('Enable or disable header button.', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => false,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_button_txt_2',
            'type' => 'text',
            'title' => __('Button Text', 'histudy'),
            'default' => __('', 'histudy'),
            'required' => array(
                array('rainbow_enable_button_2', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_button_url_2',
            'type' => 'text',
            'title' => __('Button Url', 'histudy'),
            'default' => '#',
            'required' => array(
                array('rainbow_enable_button_2', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '6', '7', '8', '9', '10', '11']),
            ),
        ),
        array(
            'id' => 'header_button_target_2',
            'type' => 'select',
            'title' => __('Button Target', 'histudy'),
            'required' => array(
                array('rainbow_enable_button_2', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '10', '11']),
            ),
            'options' => array(
                '_blank' => __('Blank', 'histudy'),
                '_self' => __('Self', 'histudy'),
                '_parent' => __('Parent', 'histudy'),
                '_top' => __('Top', 'histudy'),
            ),
            'default' => '_blank',
        ),
        array(
            'id' => 'header_button_type_2',
            'type' => 'select',
            'title' => __('Button Type', 'histudy'),
            'required' => array(
                array('rainbow_enable_button_2', 'equals', true),
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2', '3', '5', '10', '11']),
            ),
            'options' => array(
                'rbt-btn btn-primary radius-round btn-sm' => __('Primary Button', 'histudy'),
                'rbt-btn bg-primary-opacity radius-round btn-sm' => __('Primary Opacity Button', 'histudy'),
                'rbt-btn btn-border radius-round btn-sm' => __('Border Button', 'histudy'),
                'rbt-btn rbt-marquee-btn marquee-auto btn-border-gradient radius-round btn-sm hover-transform-none' => __('Gradient Border Marquee', 'histudy'),
                'rbt-btn btn-gradient radius-round btn-sm' => __('Primary Gradient Button', 'histudy'),
                'rbt-btn rbt-switch-btn btn-gradient btn-sm hover-transform-none' => __('Primary Gradient SM Radius', 'histudy'),
                'rbt-moderbt-btn' => __('Modern Button', 'histudy'),

            ),
            'default' => 'rbt-btn rbt-marquee-btn marquee-auto btn-border-gradient radius-round btn-sm hover-transform-none',
        ),

        array(
            'id' => 'rainbow_search_icon',
            'type' => 'switch',
            'title' => __('Header Search', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3', '4', '5', '6', '10', '11']),
            )
        ),
        array(
            'id' => 'rainbow_search_button_text',
            'type' => 'text',
            'title' => __('Search Button Text', 'histudy'),
            'default' => __('Search', 'histudy'),
            'required' => array(
                array('rainbow_search_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['55']),
            )
        ),
        array(
            'id' => 'rainbow_search_placeholder',
            'type' => 'text',
            'title' => __('Search Placeholder', 'histudy'),
            'default' => __('What are you looking for?', 'histudy'),
            'required' => array(
                array('rainbow_search_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['3','1','4','5','6','10','11'])
            ),
        ),
        array(
            'id' => 'rainbow_search_product_title',
            'type' => 'text',
            'title' => __('Search Product Heading', 'histudy'),
            'default' => __('Our Top Courses', 'histudy'),
            'required' => array(
                array('rainbow_search_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['3','1','4','5','6','10','11'])
            ),
        ),
        array(
            'id' => 'rainbow_search_courses',
            'type' => 'switch',
            'title' => __('Our Top Course form Search Wrapper', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_search_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1'])
            ),
        ),
        array(
            'id' => 'rainbow_admin_icon',
            'type' => 'switch',
            'title' => __('Admin User', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3','4', '6', '9', '10', '11'])
            ),
        ),
        array(
            'id' => 'rainbow_admin_icon_label',
            'type' => 'text',
            'title' => __('Admin User Label', 'histudy'),
            'default' => __('Admin', 'histudy'),
            'required' => array(
                array('rainbow_admin_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '4', '9', '10', '11'])
            ),
        ),
        array(
            'id' => 'rainbow_admin_view_profile_userlink',
            'type' => 'text',
            'title' => __('Admin View Profile Redirect Link', 'histudy'),
            'subtitle' => __('When the field  is empty, the default view profile and default profile link will work.', 'histudy'),
            'required' => array(
                array('rainbow_admin_icon', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '4', '9', '10', '11'])
            ),
        ),
        array(
            'id' => 'rainbow_admin_header_display_limit',
            'type' => 'text',
            'title' => __('Header User Display Name character Limit', 'histudy'),
            'default' => __('15', 'histudy'),
            'required' => array(
                array('rainbow_select_header_template', 'equals', ['1', '4', '9', '10', '11'])
            ),
        ),
        array(
            'id' => 'rainbow_minicart_icon',
            'type' => 'switch',
            'title' => __('Cart Icon', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_enable_header', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3', '4', '6', '9', '10', '11'])
            ),
        ),

        array(
            'id' => 'header_top_enable',
            'type' => 'switch',
            'title' => __('Header Top', 'histudy'),
            'subtitle' => __('Enable or disable the Header top area.', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array('rainbow_select_header_template', 'equals', ['1', '3', '4', '5', '6', '7', '8', '9', '10', '12'])
        ),

        array(
            'id'       => 'header_top_bg',
            'type'     => 'media',
            'url'      => true,
            'title'    => __('Header Top BG', 'histudy'),
            'desc'     => __('Upload a Header Top Background.', 'histudy'),
            'subtitle' => __('Upload any media using the WordPress header top bg', 'histudy'),
            'default'  => array(
                'url'=>'#'
            ),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['7'])
            ),
        ),

        array(
            'id' => 'header_top_intro_text',
            'type' => 'text',
            'title' => __('Header Top Intro Text', 'histudy'),
            'default' => __('Intro price. Get Histudy for Big Sale -95', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['7', '9', '10', '12'])
            ),
        ),
        array(
            'id' => 'header_top_question_url',
            'type' => 'text',
            'title' => __('Header Top Question URL', 'histudy'),
            'default' => __('https://www.example.com', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['5', '8'])
            ),
        ),
        array(
            'id' => 'header_top_question_text',
            'type' => 'text',
            'title' => __('Header Top Question Text', 'histudy'),
            'default' => __('Have any Question?', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['5', '8'])
            ),
        ),
        array(
            'id' => 'header_top_button_switch',
            'type' => 'switch',
            'title' => __('Header Top Button Switch', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['4','9', '12']),
            ),
        ),
        array(
            'id' => 'header_top_button_text',
            'type' => 'text',
            'title' => __('Header Top Button Text', 'histudy'),
            'default' => __('Purchase Now', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['4', '9', '12']),
            ),
        ),
        array(
            'id' => 'header_top_button_url',
            'type' => 'text',
            'title' => __('Header Top Button URL', 'histudy'),
            'default' => __('Purchase Now', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['4', '9', '12']),
            ),
        ),
        array(
            'id' => 'rainbow_language_switcher',
            'type' => 'switch',
            'title' => __('Language Switcher (if WPML Plugin installed)', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3', '8', '9']),
            ),
        ),
        array(
            'id' => 'rainbow_currency_switcher',
            'type' => 'switch',
            'title' => __('Currency Switcher (if WPML Plugin installed)', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3', '8', '9']),
            ),
        ),
        array(
            'id' => 'header_top_btn_badge',
            'type' => 'text',
            'title' => __('Header Top Button Badge', 'histudy'),
            'default' => __('Limited Time Offer', 'histudy'),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['3', '9', '12'])
            ),
        ),
        array(
            'id' => 'rainbow_message',
            'type' => 'switch',
            'title' => __('Header Center Message', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '3', '9']),
            ),
        ),
        array(
            'id' => 'rainbow_top_phone',
            'type' => 'switch',
            'title' => __('Phone Number', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '4', '5', '6', '8', '10']),
            ),
        ),
        array(
            'id' => 'rainbow_top_questions_switch',
            'type' => 'switch',
            'title' => __('Question Button', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['5', '8', '10']),
            ),
        ),
        array(
            'id' => 'rainbow_top_info_2',
            'type' => 'switch',
            'title' => __('Header Top Social Info', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
             'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['10', '1']),
            ),
        ),
        array(
            'id' => 'rainbow_top_social_icons',
            'type' => 'switch',
            'title' => __('Header Top Social icons', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => true,
             'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['4', '5', '6', '7', '10']),
            ),
        ),

        array(
            'id' => 'rainbow_header_offset',
            'type' => 'text',
            'title' => __('Header One Page Menu offset', 'histudy'),
            'default' => '90',
            'required' => array('rainbow_enable_header', 'equals', true),
            'required' => array(
                array('header_top_enable', 'equals', true),
                array('rainbow_select_header_template', 'equals', ['1', '2']),
            ),
        ),
        array(
            'id' => 'rainbow_header_cat_on__icons_off',
            'type' => 'switch',
            'title' => __('Header Category Icons On/Off', 'histudy'),
            'on' => __('Enabled', 'histudy'),
            'off' => __('Disabled', 'histudy'),
            'default' => 'off,',
            'required' => array('rainbow_select_header_template', 'equals', ['1', '10'])
        ),
        array(
            'id'       => 'rainbow_language_markup',
            'type'     => 'editor',
            'title'    => esc_html__('Language Markup', 'histudy'),
            'subtitle' => esc_html__('Paste HTML code here.', 'histudy'),
            'desc'     => 'Possible use this html markup and language image',
            'default'  => '<ul class="rbt-dropdown-menu switcher-language">
                            <li class="has-child-menu">
                                <a href="#">
                                    <img class="left-image" src="https://rainbowthemes.net/themes/histudy/wp-content/themes/histudy/assets/images/icons/de.png" alt="Language Images">
                                    <span class="menu-item">English</span>
                                    <i class="right-icon feather-chevron-down"></i>
                                </a>
                                <ul class="sub-menu">
                                    <li>
                                        <a href="#">
                                            <img class="left-image" src="https://rainbowthemes.net/themes/histudy/wp-content/themes/histudy/assets/images/icons/fr.png" alt="Language Images">
                                            <span class="menu-item">Français</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <img class="left-image" src="https://rainbowthemes.net/themes/histudy/wp-content/themes/histudy/assets/images/icons/de.png" alt="Language Images">
                                            <span class="menu-item">Deutsch</span>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>',
        ),
        array(
            'id'       => 'rainbow_header_currency_markup',
            'type'     => 'editor',
            'title'    => esc_html__('Currency Html markup', 'histudy'),
            'subtitle' => esc_html__('Paste HTML code here.', 'histudy'),
            'desc'     => 'Possible use this html markup and language image',
            'default'  => '<ul class="rbt-dropdown-menu currency-menu"><li class="has-child-menu">
                            <a href="#">
                                <span class="menu-item">USD</span>
                                <i class="right-icon feather-chevron-down"></i>
                            </a>
                            <ul class="sub-menu hover-reverse">
                                <li>
                                    <a href="#">
                                        <span class="menu-item">EUR</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="menu-item">GBP</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>',
            )

        ),





    ));



/**
 * Popup
 */
$rbt_page_ids = get_all_page_ids();
$rbt_pages_array = array();
if (!empty($rbt_page_ids) && is_array($rbt_page_ids)) {
    foreach ($rbt_page_ids as $page_id) {
        $rbt_pages_array[$page_id] = get_the_title($page_id);
    }
}
Redux::setSection( $opt_name, array(
    'title' => __( 'Account Settings', 'histudy' ),
    'id' => 'account_settings',
    'icon' => 'el el-zoom-in',
    'fields' => array(
        array(
            'id' => 'rainbow_account_enable_popup',
            'type' => 'switch',
            'title' => __('Enable Popup', 'histudy'),
            'subtitle' => __('Enable Popup', 'histudy'),
            'default' => true,
        ),
        array(
            'id'       => 'rainbow_account_url',
            'type'     => 'text',
            'title'    => __('Account URL', 'histudy'),
            'subtitle' => __('Account URL', 'histudy'),
            'default'  => '#',
            'required' => array('rainbow_account_enable_popup', 'equals', 'false'),
        ),
    )
) );

/**
 * LearnPress
 */
if( class_exists( 'LearnPress' ) ) {
    Redux::setSection( $opt_name, array(
        'title' => __( 'LearnPress Settings', 'histudy' ),
        'id' => 'learnpress_theme_options',
        'icon' => 'el el-zoom-in',
    ) );
}

/**
 * Generice Banner
 */
if( class_exists( 'LearnPress' ) ) {
    Redux::setSection($opt_name, array(
        'title' => __('Course Archive', 'histudy'),
        'id' => 'generice_banner_course_archive_control',
        'icon' => 'el el-folder-open',
        'subsection' => true,
        'fields' => array(

            array(
                'id' => 'generice_course_archive_banner_layout',
                'type' => 'image_select',
                'title' => __('Archive Banner Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'title' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/1.jpg',
                    ),
                    'layout-2' => array(
                        'alt' => __('Filter Open', 'histudy'),
                        'title' => __('Filter Open', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/v3/banner/archive/filter-open.jpg',
                    ),
                ),
                'default' => 'layout-1',
            ),
            array(
                'id' => 'histudy_learnpress_course_breadcrumb_filter_layout',
                'type' => 'image_select',
                'title' => __('Filter Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-1.jpg',
                        'title' => __('Layout 1', 'histudy'),
                    ),
                    'layout-2' => array(
                        'alt' => __('Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-3.png',
                        'title' => __('Layout 2', 'histudy'),
                    ),
                    'layout-3' => array(
                        'alt' => __('Layout 3', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-4.jpg',
                        'title' => __('Layout 3', 'histudy'),
                    ),
                ),
                'default' => 'layout-1',
            ),

            array(
                'id'       => 'rainbow_learnpress_course_archive_brd_color_image',
                'type'     => 'background',
                'title'    => esc_html__('Course archive Banner Image | background color', 'histudy'),
                'subtitle' => esc_html__('Course archive background with image, color, etc.', 'histudy'),
            ),

            array(
                'id'       => 'rainbow_learnpress_course_archive_brd_image',
                'type'     => 'color_gradient',
                'title'    => esc_html__('Course Archive banner gradient Color', 'histudy'),
                'validate' => 'color',
                'default'        => array(
                    'preview' => false,
                    'from'           => '',
                    'to'             => '',
                ),
            ),
            array(
                'id'       => 'rainbow_learnpress_course_details_brd_image',
                'type'     => 'background',
                'title'    => esc_html__('Course Details Banner Image | background color', 'histudy'),
                'subtitle' => esc_html__('Course Details background with image, color, etc.', 'histudy'),
            ),

            array(
                'id'       => 'rainbow_learnpress_course_details_gradient_image',
                'type'     => 'color_gradient',
                'title'    => esc_html__('Course details banner gradient Color', 'histudy'),
                'validate' => 'color',
                'default'        => array(
                    'preview' => false,
                    'from'           => '',
                    'to'             => '',
                ),
            ),

            array(
                'id'       => 'rainbow_learnpress_course_grid_archive_img_size',
                'type'     => 'select',
                'title'    => __('Course Grid Image Size', 'histudy'),
                'subtitle'    => __('Image size will apply on course image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
            ),

            array(
                'id'       => 'generic_banner_title_enable',
                'type'     => 'switch',
                'title'    => __('Enable Title?', 'histudy'),
                'subtitle' => __('Enable/disable your title.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Title Hint', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
            ),
            array(
                'id'       => 'generic_banner_breadcrumb_enable',
                'type'     => 'switch',
                'title'    => __('Enable Breadcrumb?', 'histudy'),
                'subtitle' => __('Enable/disable your breadcrumb.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Breadcrumb Hint', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
            ),
            array(
                'id'       => 'generic_banner_badge_enable',
                'type'     => 'switch',
                'title'    => __('Enable Badge?', 'histudy'),
                'subtitle' => __('Enable/disable your breadcrumb.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Badge Hint', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
            ),
            array(
                'id' => 'generic_banner_badge_text_singular',
                'type' => 'text',
                'title' => __('Badget Text Singular', 'histudy'),
                'default' => 'Course',
                'required' => array(
                    array('generic_banner_badge_enable', 'equals', true),
                ),
            ),
            array(
                'id' => 'generic_banner_badge_text_plural',
                'type' => 'text',
                'title' => __('Badget Text Plural', 'histudy'),
                'default' => 'Courses',
                'required' => array(
                    array('generic_banner_badge_enable', 'equals', true),
                ),
            ),
            array(
                'id'       => 'generic_banner_subtitle_enable',
                'type'     => 'switch',
                'title'    => __('Enable Subtitle?', 'histudy'),
                'subtitle' => __('Enable/disable your Subtitle.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Subtitle Switch Hint', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
            ),
            array(
                'id' => 'generic_banner_subtitle',
                'type' => 'text',
                'title' => __('Subtitle Text', 'histudy'),
                'default' => 'Courses that help beginner designers become true unicorns.',
                'required' => array(
                    array('generic_banner_subtitle_enable', 'equals', true),
                ),
            ),
            array(
                'id' => 'rainbow_course_details_title_switch',
                'type' => 'switch',
                'title' => __('Title', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_rating_switch',
                'type' => 'switch',
                'title' => __('Enable Rating', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_student_count_switch',
                'type' => 'switch',
                'title' => __('Enable Student Count', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id'       => 'generic_banner_layout_filter_enable',
                'type'     => 'switch',
                'title'    => __('Enable Grid/List Filter?', 'histudy'),
                'subtitle' => __('Enable/Disable grid/list filter.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable Grid/List Filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
            ),

            array(
                'id' => 'generic_banner_grid_btn_label',
                'type' => 'text',
                'title' => __('Grid Button Label', 'histudy'),
                'default' => 'Grid',
                'required' => array(
                    array('generic_banner_layout_filter_enable', 'equals', true),
                ),
            ),
            array(
                'id'       => 'generic_banner_list_btn_icon',
                'type'     => 'text',
                'title'    => __('List Icon (Feather Icon)', 'histudy'),
                'subtitle' => __('Enter the List icon name (e.g., "feather-List").', 'histudy'),
                'default'  => 'feather-List',
                'required' => array(
                    array('generic_banner_layout_filter_enable', 'equals', true),
                ),
                'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_list_btn_label',
                'type' => 'text',
                'title' => __('List Button Label', 'histudy'),
                'default' => 'List',
                'required' => array(
                    array('generic_banner_layout_filter_enable', 'equals', true),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_course_count_filter_description',
                'type'     => 'switch',
                'title'    => __('Enable Filter Course Count?', 'histudy'),
                'subtitle' => __('Enable/Disable course count filter description.', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable course filter count', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_course_count_filter_label_start',
                'type' => 'text',
                'title' => __('Count Filter Start Label', 'histudy'),
                'default' => 'Showing',
                'required' => array(
                    array('generic_banner_enable_course_count_filter_description', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_course_count_filter_label_middle',
                'type' => 'text',
                'title' => __('Count Filter Middle Label', 'histudy'),
                'default' => 'of',
                'required' => array(
                    array('generic_banner_enable_course_count_filter_description', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_course_count_filter_label_end',
                'type' => 'text',
                'title' => __('Count Filter End Label', 'histudy'),
                'default' => 'results',
                'required' => array(
                    array('generic_banner_enable_course_count_filter_description', 'equals', true),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_front_serch',
                'type'     => 'switch',
                'title'    => __('Enable Front Search?', 'histudy'),
                'subtitle' => __('Enable/Disable Front Search', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable front search', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_front_search_label',
                'type' => 'text',
                'title' => __('Search Label', 'histudy'),
                'default' => 'Search Your Course..',
                'required' => array(
                    array('generic_banner_enable_front_serch', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_front_search_icon_feather',
                'type'     => 'text',
                'title'    => __('Search Icon (Feather Icon)', 'histudy'),
                'subtitle' => __('Enter the search icon name (e.g., "feather-search").', 'histudy'),
                'default'  => 'feather-search',
                'required' => array(
                    array('generic_banner_enable_front_serch', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
                'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
            ),
            array(
                'id'       => 'generic_banner_enable_filter_btn',
                'type'     => 'switch',
                'title'    => __('Enable Filter Button?', 'histudy'),
                'subtitle' => __('Enable/Disable Filter Button', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable filter button', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_front_filter_icon_feather',
                'type'     => 'text',
                'title'    => __('Filter Icon (Feather Icon)', 'histudy'),
                'subtitle' => __('Enter the search icon name (e.g., "feather-filter").', 'histudy'),
                'default'  => 'feather-filter',
                'required' => array(
                    array('generic_banner_enable_filter_btn', 'equals', true),
                ),
                'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_filter_label',
                'type' => 'text',
                'title' => __('Filter Label', 'histudy'),
                'default' => 'Filter',
                'required' => array(
                    array('generic_banner_enable_filter_btn', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_sort_by_date',
                'type'     => 'switch',
                'title'    => __('Enable Sort By Filter Date?', 'histudy'),
                'subtitle' => __('Enable/Disable sort by date filter', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable sort by date filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_date_label',
                'type' => 'text',
                'title' => __('Sort By Date Label', 'histudy'),
                'default' => 'SORT BY DATE',
                'required' => array(
                    array('generic_banner_enable_sort_by_date', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_sort_by_author',
                'type'     => 'switch',
                'title'    => __('Enable Sort By Author?', 'histudy'),
                'subtitle' => __('Enable/Disable sort by author filter', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable sort by author filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_author_label',
                'type' => 'text',
                'title' => __('Sort By Author Label', 'histudy'),
                'default' => 'SORT BY AUTHOR',
                'required' => array(
                    array('generic_banner_enable_sort_by_author', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_sort_by_offer',
                'type'     => 'switch',
                'title'    => __('Enable Sort By Offer?', 'histudy'),
                'subtitle' => __('Enable/Disable sort by offer filter', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable sort by offer filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_offer_label',
                'type' => 'text',
                'title' => __('Sort By Offer Label', 'histudy'),
                'default' => 'SORT BY OFFER',
                'required' => array(
                    array('generic_banner_enable_sort_by_offer', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_sort_by_category',
                'type'     => 'switch',
                'title'    => __('Enable Sort By Category?', 'histudy'),
                'subtitle' => __('Enable/Disable sort by category filter', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable sort by category filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_category_label',
                'type' => 'text',
                'title' => __('Sort By Category Label', 'histudy'),
                'default' => 'SORT BY category',
                'required' => array(
                    array('generic_banner_enable_sort_by_category', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id'       => 'generic_banner_enable_sort_by_price_range',
                'type'     => 'switch',
                'title'    => __('Enable Sort By Price Range?', 'histudy'),
                'subtitle' => __('Enable/Disable sort by price range filter', 'histudy'),
                'default'  => true,
                'hint'     => array(
                    'title'   => __('Enable/Disable price range filter', 'histudy'),
                    'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                ),
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_price_range_label',
                'type' => 'text',
                'title' => __('Sort By Price Range Label', 'histudy'),
                'default' => 'SORT BY price range',
                'required' => array(
                    array('generic_banner_enable_sort_by_price_range', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_price_before_price',
                'type' => 'text',
                'title' => __('Sort By Before Price Label', 'histudy'),
                'default' => 'PRICE',
                'required' => array(
                    array('generic_banner_enable_sort_by_price_range', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_sort_by_price_filter_btn_label',
                'type' => 'text',
                'title' => __('Filter Button Label', 'histudy'),
                'default' => 'FILTER',
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),
        )
    ) );
}

/**
 * Generice Banner
 */
if( class_exists( 'LearnPress' ) ) {
    Redux::setSection($opt_name, array(
        'title' => __('Course Single', 'histudy'),
        'id' => 'generice_banner_course_single_control',
        'icon' => 'el el-folder-open',
        'subsection' => true,
        'fields' => array(
            array(
                'id'       => 'lp_rainbow_course_details_layout',
                'type'     => 'select',
                'title'    => __('Course Details Layout', 'histudy'),
                'subtitle'    => __('Please select a course details layout.', 'histudy'),
                'options'  => array(
                    'layout-1' => __( 'Layout 01', 'histudy' ),
                    'layout-2' => __( 'Layout 02', 'histudy' ),
                    'layout-3' => __( 'Layout 03', 'histudy' ),
                    'layout-4' => __( 'Layout 04', 'histudy' ),
                    'layout-5' => __( 'Layout 05', 'histudy' ),
                ),
                'default'  => 'layout-1',
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_show_hide',
                'type' => 'switch',
                'title' => __('Show Course Sidebar?', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_details_show_instructor_profile',
                'type' => 'switch',
                'title' => __('Show Instructor Tab Profile', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'generic_banner_enable_sort_by_price_before_price',
                'type' => 'text',
                'title' => __('Sort By Before Price Label', 'histudy'),
                'default' => 'PRICE',
                'required' => array(
                    array('generic_banner_enable_sort_by_price_range', 'equals', true),
                    array('always_false_condition', 'equals', false),
                ),
            ),
            array(
                'id' => 'generic_banner_sort_by_price_filter_btn_label',
                'type' => 'text',
                'title' => __('Filter Button Label', 'histudy'),
                'default' => 'FILTER',
                'required' => array(
                    array('always_false_condition', 'equals', false),
                ),
            ),

            array(
                'id' => 'lp_rainbow_course_details_card_bottom_show',
                'type' => 'switch',
                'title' => __('Card Course Bottom Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('lp_rainbow_course_details_card_show_hide', 'equals', 1)
            ),

            array(
                'id' => 'lp_rainbow_course_details_card_social_show',
                'type' => 'switch',
                'title' => __('Card Course Social Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('lp_rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_fb_switch',
                'type' => 'switch',
                'title' => __('Show Facebook ? ', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
                    array('lp_rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_twitter_switch',
                'type' => 'switch',
                'title' => __('Show Twitter? ', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
                    array('lp_rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_linkedin_switch',
                'type' => 'switch',
                'title' => __('Show Linkedin?', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
                    array('lp_rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),

            array(
                'id' => 'lp_rainbow_course_details_card_contact_label',
                'type' => 'text',
                'title' => __('Card contact label', 'histudy'),
                'default' => __('Card contact label', 'histudy'),
                'required' => array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_contact_number_before_text',
                'type' => 'text',
                'title' => __('Card contact Number Before Text', 'histudy'),
                'default' => __('Call Us:', 'histudy'),
                'required' => array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
            ),
            array(
                'id' => 'lp_rainbow_course_details_card_contact_number_link',
                'type' => 'text',
                'title' => __('Card contact Number Link', 'histudy'),
                'default' => __('444555666777', 'histudy'),
                'required' => array('lp_rainbow_course_details_card_show_hide', 'equals', 1),
            ),

            array(
                'id' => 'lp_rainbow_course_details_instructor_title',
                'type' => 'text',
                'title' => __('Course Single Instructor Title', 'histudy'),
                'default' => __('Instructor', 'histudy'),
                'required' => array('lp_rainbow_course_details_show_instructor_profile', 'equals', 1),
            ),
            array(
                'id' => 'lp_rainbow_course_details_overview_heading',
                'type' => 'text',
                'title' => __('Course Single Tab Overview bottom heading', 'histudy'),
                'default' => __('About Description', 'histudy'),
            ),
            array(
                'id' => 'lp_rainbow_course_details_course_curriculam_heading',
                'type' => 'text',
                'title' => __('Course Single Curriculum heading', 'histudy'),
                'default' => __('Course Curriculum', 'histudy'),
            ),

             /**
             * Author Course Controls
             */
            // author course
            array(
                'id' => 'lp_rainbow_course_details_author_course_toggle',
                'type' => 'switch',
                'title' => __('Author Course Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_details_author_course_query_count',
                'type' => 'text',
                'title' => __('Author Query Count ', 'histudy'),
                'subtitle' => __('Author Query Count', 'histudy'),
                'default' => __('3', 'histudy'),
                'required' => array(
                    'lp_rainbow_course_details_author_course_toggle', 'equals', 1
                )
            ),
            array(
                'id' => 'lp_rainbow_course_details_author_course_title_prefix',
                'type' => 'text',
                'title' => __('Author Course Title Prefix', 'histudy'),
                'subtitle' => __('Enter which want to show before author course title', 'histudy'),
                'default' => __('More Course By', 'histudy'),
                'required' => array('lp_rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'lp_rainbow_course_details_author_course_btn_toggle',
                'type' => 'switch',
                'title' => __('Author Course Button Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('lp_rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'lp_rainbow_course_details_author_course_btn_title',
                'type' => 'text',
                'title' => __('Author Course Button Text', 'histudy'),
                'subtitle' => __('Enter which want to show Author Course Button Text', 'histudy'),
                'default' => __('View All Course', 'histudy'),
                'required' => array(
                    array(
                        'lp_rainbow_course_details_author_course_toggle', 'equals', 1
                    ),
                    array(
                        'lp_rainbow_course_details_author_course_btn_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id' => 'lp_rainbow_course_details_author_course_badge_toggle',
                'type' => 'switch',
                'title' => __('Course Badge Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('lp_rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'lp_rainbow_course_author_course_badge_title',
                'type' => 'text',
                'title' => __('Author Course Badge Title', 'histudy'),
                'subtitle' => __('Enter which want to show author course title', 'histudy'),
                'default' => __('TOP COURSE', 'histudy'),
                'required' => array(
                    array(
                        'lp_rainbow_course_details_author_course_toggle', 'equals', 1
                    ),
                    array(
                        'lp_rainbow_course_details_author_course_badge_toggle', 'equals', 1
                    )
                )
            ),
            // related course
            array(
                'id' => 'lp_rainbow_course_details_related_course_toggle',
                'type' => 'switch',
                'title' => __('Related Course Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_details_related_course_query_count',
                'type' => 'text',
                'title' => __('Related Course Product Count', 'histudy'),
                'subtitle' => __('Related Course Product Count', 'histudy'),
                'default' => __('3', 'histudy'),
                'required' => array(
                    array(
                        'lp_rainbow_course_details_related_course_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id' => 'lp_rainbow_course_details_related_course_title',
                'type' => 'text',
                'title' => __('Related Course Title', 'histudy'),
                'subtitle' => __('Enter which want to show related course title', 'histudy'),
                'default' => __('Related Course', 'histudy'),
                'required' => array(
                    array(
                        'lp_rainbow_course_details_related_course_toggle', 'equals', 1
                    )
                )
            ),

            array(
                'id' => 'lp_rainbow_course_details_related_course_badge_toggle',
                'type' => 'switch',
                'title' => __('Related Course Badge Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('lp_rainbow_course_details_related_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'lp_rainbow_course_details_related_course_badge_title',
                'type' => 'text',
                'title' => __('Related Course Badge Title', 'histudy'),
                'subtitle' => __('Enter which want to show related course title', 'histudy'),
                'default' => __('Explore All Products', 'histudy'),
                'required' => array(
                    array(
                        'lp_rainbow_course_details_related_course_toggle', 'equals', 1
                    ),
                    array(
                        'lp_rainbow_course_details_related_course_badge_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id'       => 'lp_rainbow_single_course_student_enroll_image',
                'type'     => 'media',
                'url'      => true,
                'title'    => __('Enroll Student Image', 'histudy'),
                'desc'     => __('Upload a student enroll image.', 'histudy'),
                'subtitle' => __('Working this image when using course details layout 3', 'histudy'),
                'required' => array(
                    'lp_rainbow_course_details_layout', 'equals', ['layout-3','layout-4','layout-5' ],
                )
            ),

            array(
                'id'       => 'lp_rainbow_course_details_brd_image_layout4',
                'type'     => 'background',
                'title'    => esc_html__('Course Details Banner Image Style 4 | background color', 'histudy'),
                'subtitle' => esc_html__('Course Details background with image, color, etc.', 'histudy'),
            ),


        )
    ) );
}

// course single card
if( class_exists( 'LearnPress' ) ) {
    Redux::setSection($opt_name, array(
        'title' => __('Course Card LearnPress Single', 'histudy'),
        'id' => 'lp_section_rainbow_theme_tutor_lms_course_card',
        'icon' => 'el el-picture',
        'subsection' => true,
        'fields' => array(

            array(
                'id' => 'rainbow_learnpress_card_layout_course',
                'type' => 'image_select',
                'title' => __('Card Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'title' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-1.jpg',
                    ),
                    'layout-2' => array(
                        'alt' => __('Layout 2', 'histudy'),
                        'title' => __('Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-2.jpg',
                    ),
                    'layout-3' => array(
                        'alt' => __('Layout 3', 'histudy'),
                        'title' => __('Layout 3', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-3.jpg',
                    ),
                ),
                'default' => 'layout-1',
                'desc' => 'The course card layout functions correctly when the LearnPress Ajax layout option is not selected.',
            ),

            array(
                'id' => 'lp_rainbow_course_card_title_switch',
                'type' => 'switch',
                'title' => __('Title Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_card_meta_switch',
                'type' => 'switch',
                'title' => __('Meta Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_card_author_switch',
                'type' => 'switch',
                'title' => __('Author Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_card_image_switch',
                'type' => 'switch',
                'title' => __('Image Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,

            ),
            array(
                'id'       => 'lp_rainbow_course_grid_archive_img_size',
                'type'     => 'select',
                'title'    => __('Course Grid Image Size', 'histudy'),
                'subtitle'    => __('Image size will apply on course image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
            ),

            array(
                'id'       => 'lp_rainbow_course_list_archive_img_size',
                'type'     => 'select',
                'title'    => __('Course list Image Size', 'histudy'),
                'subtitle'    => __('Image size will apply on course image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
            ),

            array(
                'id' => 'lp_rainbow_course_card_add_to_cart_switch',
                'type' => 'switch',
                'title' => __('Button Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_card_pricing_switch',
                'type' => 'switch',
                'title' => __('Pricing Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'lp_rainbow_course_content_limit',
                'type' => 'text',
                'title' => __('Course Content Limit', 'histudy'),
                'default' => '10',
            ),
        )
    ));
}


/**
 * Blog Panel
 */
Redux::setSection($opt_name, array(
    'title' => __('Page', 'histudy'),
    'id' => 'rainbow_page',
    'icon' => 'el el-book',
    'fields' => array(
        array(
            'id' => 'rainbow_page_breadrumb_layout',
            'type' => 'image_select',
            'title' => __('Page Banner Layout', 'histudy'),
            'options' => array(
                'layout-1' => array(
                    'alt' => __('Layout 1', 'histudy'),
                    'title' => __('Layout 1', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/page/breadcrumb-1.jpg',
                ),
                'layout-2' => array(
                    'alt' => __('Layout 2', 'histudy'),
                    'title' => __('Layout 2', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/page/breadcrumb-2.jpg',
                )
            ),
            'default' => 'layout-1',
        ),
        array(
            'id'       => 'rainbow_global_page_gradient_color',
            'type'     => 'color_gradient',
            'title'    => esc_html__('Page banner gradient Color', 'histudy'),
            'validate' => 'color',
            'default'        => array(
                'preview' => false,
                'from'           => '',
                'to'             => '',
            ),
        ),
        array(
            'id'       => 'rainbow_global_page_image_or_color',
            'type'     => 'background',
            'title'    => esc_html__('Page Banner Image | background color', 'histudy'),
            'subtitle' => esc_html__('Page background with image, color, etc.', 'histudy'),
        ),
    )
));

/**
 * Blog Panel
 */
Redux::setSection($opt_name, array(
    'title' => __('Blog', 'histudy'),
    'id' => 'rainbow_blog',
    'icon' => 'el el-file-edit',
));

/**
 * Blog Options
 */


Redux::setSection($opt_name, array(
    'title' => __('Archive', 'histudy'),
    'id' => 'rainbow_blog_genaral',
    'icon' => 'el el-edit',
    'subsection' => true,
    'fields' => array(

        array(
            'id' => 'rainbow_blog_banner_enable',
            'type' => 'button_set',
            'title' => __(' Title banner', 'histudy'),
            'subtitle' => __('Show or hide  the Title banner area', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
             'id' => 'rainbow_blog_title_layout',
             'type' => 'image_select',
             'title' => __('Select Blog Breadcrumb Layout', 'histudy'),
             'subtitle' => __('Choose your favorite layout', 'histudy'),
             'options' => array(
                 '1' => array(
                     'alt' => __('Layout One', 'histudy'),
                     'img' => get_template_directory_uri() . '/assets/images/optionframework/banner/1.png',
                     'title' => __('Layout One', 'histudy'),
                 ),
                 '2' => array(
                     'alt' => __('Layout Two', 'histudy'),
                     'img' => get_template_directory_uri() . '/assets/images/optionframework/banner/2.png',
                     'title' => __('Layout Two', 'histudy'),
                 ),

             ),
             'default' => '1',
             'required' => array('rainbow_blog_banner_enable', 'equals', 'yes'),
        ),

        array(
            'id'       => 'rainbow_blog_details_brd_background',
            'type'     => 'background',
            'title'    => esc_html__('Blog Details Banner Image | background color', 'histudy'),
            'subtitle' => esc_html__('blog details background with image, color, etc.', 'histudy'),
        ),
        array(
            'id'       => 'histudy_blog_details_gradient_bg_color',
            'type'     => 'color_gradient',
            'title'    => esc_html__('Blog Details banner gradient Color', 'histudy'),
            'validate' => 'color',
            'default'        => array(
                'preview' => false,
                'from'           => '',
                'to'             => '',
            ),
        ),

        array(
            'id'       => 'rainbow_blog_archive_brd_background',
            'type'     => 'background',
            'title'    => esc_html__('Blog Archive Banner Image | background color', 'histudy'),
            'subtitle' => esc_html__('blog archive background with image, color, etc.', 'histudy'),
        ),

        array(
            'id'       => 'histudy_blog_page_bg_color',
            'type'     => 'color_gradient',
            'title'    => esc_html__('Blog Archive banner gradient Color', 'histudy'),
            'validate' => 'color',
            'default'        => array(
                'preview' => false,
                'from'           => '',
                'to'             => '',
            ),
        ),
        array(
            'id'       => 'histudy_blog_page_bg_color_top',
            'type'     => 'color_gradient',
            'title'    => esc_html__('Blog archive Banner Top gradient Color', 'histudy'),
            'validate' => 'color',
            'default'        => array(
                'preview' => false,
                'from'           => '',
                'to'             => '',
            ),
        ),

         array(
            'id' => 'rainbow_enable_blog_breadcrumb_overlap',
            'type' => 'switch',
            'title' => __('Enable Breadcrumb Overlap', 'histudy'),
            'subtitle' => __('enable breadcrumb overlap.', 'histudy'),
            'default' => true,
            'required' => array('rainbow_blog_banner_enable', 'equals', 'yes'),
        ),
        array(
                'id' => 'rainbow_blog_subtitle',
                'type' => 'text',
                'title' => __('Sub Title', 'histudy'),
                'default' => '',
                'required' => array(
                    array('rainbow_blog_banner_enable', 'equals', 'yes'),
                    array('rainbow_blog_title_layout', 'equals', '2'),
                ),
        ),

        array(
            'id' => 'rainbow_blog_sidebar',
            'type' => 'image_select',
            'title' => __('Select Blog Sidebar', 'histudy'),
            'subtitle' => __('Choose your favorite blog layout', 'histudy'),
            'options' => array(
                'left' => array(
                    'alt' => __('Left Sidebar', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/left-sidebar.png',
                    'title' => __('Left Sidebar', 'histudy'),
                ),
                'right' => array(
                    'alt' => __('Right Sidebar', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/right-sidebar.png',
                    'title' => __('Right Sidebar', 'histudy'),
                ),
                'no' => array(
                    'alt' => __('No Sidebar', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/no-sidebar.png',
                    'title' => __('No Sidebar', 'histudy'),
                ),
            ),
            'default' => 'right',
        ),
        array(
            'id' => 'rainbow_blog_layout',
            'type' => 'image_select',
            'title' => __('Select Blog Layout', 'histudy'),
            'subtitle' => __('Choose your favorite blog layout', 'histudy'),
            'options' => array(
                'blog-list' => array(
                    'alt' => __('Classic', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-list.jpg',
                    'title' => __('Classic', 'histudy'),
                ),
                'blog-grid' => array(
                    'alt' => __('Standard', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-grid.jpg',
                    'title' => __('Standard', 'histudy'),
                ),
                'blog-grid-minimal' => array(
                    'alt' => __('Minimal', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-grid-minimal.jpg',
                    'title' => __('Minimal', 'histudy'),
                ),
                'default' => array(
                    'alt' => __('Default', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-default.jpg',
                    'title' => __('Default', 'histudy'),
                ),
            ),
            'default' => 'default',
        ),

        /**
         * Select card style for list layout
         *
         * @since 1.0.0
         */
        array(
            'id'       => 'rainbow_blog_list_img_size',
            'type'     => 'select',
            'title'    => __('Image Size', 'histudy'),
            'subtitle'    => __('Image size will not apply on featured image', 'histudy'),
            'options'  => rainbow_get_thumbnail_sizes(),
            'default'  => 'full',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-list'),
        ),
        array(
            'id' => 'blog_list_enable_featured_blog',
            'type' => 'switch',
            'title' => __('Enable Focus Blog', 'histudy'),
            'subtitle' => __('Enable/Disable focus blog here.', 'histudy'),
            'default' => true,
            'required' => array('rainbow_blog_layout', 'equals', 'blog-list'),
        ),

        array(
            'id' => 'blog_list_card_layout',
            'type' => 'image_select',
            'title' => __('Select Card Layout', 'histudy'),
            'subtitle' => __('Choose your favorite blog layout', 'histudy'),
            'options' => array(
                'card-boxed' => array(
                    'alt' => __('Card Boxed', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-boxed.jpg',
                    'title' => __('Card Boxed', 'histudy'),
                ),
                'card-list' => array(
                    'alt' => __('Card List', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-list.jpg',
                    'title' => __('Card List', 'histudy'),
                ),
                'card-minimal' => array(
                    'alt' => __('Card Minimal', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-minimal.jpg',
                    'title' => __('Card Minimal', 'histudy'),
                ),
            ),
            'default' => 'card-list',
            // condition
            'required' => array('rainbow_blog_layout', 'equals', 'blog-list'),
        ),
        array(
            'id' => 'blog_minimal_img_on_off',
            'type' => 'switch',
            'title' => __('Minimal Grid Feature Image On/Off', 'histudy'),
            'subtitle' => __('Enable/Disable focus blog here.', 'histudy'),
            'default' => false,
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid-minimal'),
        ),
        array(
            'id' => 'blog_list_card_meta_toggle',
            'type' => 'switch',
            'title' => __('Card Meta On/Off', 'histudy'),
            'subtitle' => __('Card Meta on/off.', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_blog_layout', 'equals', 'blog-list'),
                array('blog_list_card_layout', 'equals', 'card-list')
            )
        ),
        array(
            'id' => 'blog_list_card_excerpt_toggle',
            'type' => 'switch',
            'title' => __('Card Excerpt On/Off', 'histudy'),
            'subtitle' => __('Card Excerpt on/off.', 'histudy'),
            'default' => true,
            'required' => array(
                array('rainbow_blog_layout', 'equals', 'blog-list'),
                array('blog_list_card_layout', 'equals', 'card-list')
            )
        ),
        array(
            'id'       => 'blog_list_card_image_size',
            'type'     => 'select',
            'title'    => __('Image Size', 'histudy'),
            'subtitle'    => __('Blog Image Size', 'histudy'),
            'options'  => array(
                'default'                      => __('Default', 'histudy'),
                'full'                      => __('Full', 'histudy'),
                'thumbnail'                 => __('Thumbnail', 'histudy'),
                'medium'                    => __('Medium', 'histudy'),
                'large'                     => __('Large', 'histudy'),
                'large'                     => __('Large', 'histudy'),
                'rainbow-thumbnail-md'      => __('Rainbow Medium Thumbnail', 'histudy'),
                'rainbow-thumbnail-sm'      => __('Rainbow Small Thumbnail', 'histudy'),
                'rainbow-thumbnail-lg'      => __('Rainbow Large Thumbnail', 'histudy'),
                'rainbow-thumbnail-archive' => __('Rainbow Archive Thumbnail', 'histudy'),
                'rainbow-thumbnail-single'  => __('Rainbow Single Thumbnail', 'histudy'),
            ),
            'default'  => 'full',
            'required' => array(
                array('rainbow_blog_layout', 'equals', 'blog-list'),
                array('blog_list_card_layout', 'equals', 'card-list')
            )
        ),
        array(
            'id'       => 'blog_list_select_col',
            'type'     => 'select',
            'title'    => __('Select Column', 'histudy'),
            'subtitle'    => __('You can select column for each row.', 'histudy'),
            'options'  => array(
                '1' => __('1 Column', 'histudy'),
                '2' => __('2 Column', 'histudy'),
            ),
            'default'  => '1',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-list'),
        ),
        /**
         * Select card style for grid layout
         *
         * @since 1.0.0
         */
        array(
            'id'       => 'rainbow_blog_grid_img_size',
            'type'     => 'select',
            'title'    => __('Image Size', 'histudy'),
            'subtitle'    => __('Blog Image Size', 'histudy'),
            'options'  => rainbow_get_thumbnail_sizes(),
            'default'  => 'full',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid'),
        ),
        array(
            'id' => 'blog_grid_enable_featured_blog',
            'type' => 'switch',
            'title' => __('Enable Focus Blog', 'histudy'),
            'subtitle' => __('Enable/Disable focus blog here.', 'histudy'),
            'default' => true,
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid'),
        ),
        array(
            'id' => 'blog_grid_card_layout',
            'type' => 'image_select',
            'title' => __('Select Card Layout', 'histudy'),
            'subtitle' => __('Choose your favorite blog layout', 'histudy'),
            'options' => array(
                'card-boxed' => array(
                    'alt' => __('Card Boxed', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-boxed.jpg',
                    'title' => __('Card Boxed', 'histudy'),
                ),
                'card-list' => array(
                    'alt' => __('Card List', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-list.jpg',
                    'title' => __('Card List', 'histudy'),
                ),
                'card-minimal' => array(
                    'alt' => __('Card Minimal', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-minimal.jpg',
                    'title' => __('Card Minimal', 'histudy'),
                ),
            ),
            'default' => 'card-boxed',
            // condition
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid'),
        ),
        array(
            'id'       => 'blog_grid_select_col',
            'type'     => 'select',
            'title'    => __('Select Column', 'histudy'),
            'subtitle'    => __('You can select column for each row.', 'histudy'),
            'options'  => array(
                '1' => __('1 Column', 'histudy'),
                '2' => __('2 Column', 'histudy'),
                '3' => __('3 Column', 'histudy'),
                '4' => __('4 Column', 'histudy'),
            ),
            'default'  => '3',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid'),
        ),
        /**
         * Select card style for grid minimal layout
         *
         * @since 1.0.0
         */
        array(
            'id'       => 'rainbow_blog_grid_minimal_img_size',
            'type'     => 'select',
            'title'    => __('Image Size', 'histudy'),
            'subtitle'    => __('Blog Image Size', 'histudy'),
            'options'  => rainbow_get_thumbnail_sizes(),
            'default'  => 'full',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid-minimal'),
        ),
        array(
            'id' => 'blog_grid_minimal_card_layout',
            'type' => 'image_select',
            'title' => __('Select Card Layout', 'histudy'),
            'subtitle' => __('Choose your favorite blog layout', 'histudy'),
            'options' => array(
                'card-boxed' => array(
                    'alt' => __('Card Boxed', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-boxed.jpg',
                    'title' => __('Card Boxed', 'histudy'),
                ),
                'card-list' => array(
                    'alt' => __('Card List', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-list.jpg',
                    'title' => __('Card List', 'histudy'),
                ),
                'card-minimal' => array(
                    'alt' => __('Card Minimal', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/card/card-minimal.jpg',
                    'title' => __('Card Minimal', 'histudy'),
                ),
            ),
            'default' => 'card-minimal',
            // condition
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid-minimal'),
        ),
        array(
            'id'       => 'blog_grid_minimal_select_col',
            'type'     => 'select',
            'title'    => __('Select Column', 'histudy'),
            'subtitle'    => __('You can select column for each row.', 'histudy'),
            'options'  => array(
                '1' => __('1 Column', 'histudy'),
                '2' => __('2 Column', 'histudy'),
                '3' => __('3 Column', 'histudy'),
                '4' => __('4 Column', 'histudy'),
            ),
            'default'  => '3',
            'required' => array('rainbow_blog_layout', 'equals', 'blog-grid-minimal'),
        ),
        array(
            'id'       => 'rainbow_blog_default_img_size',
            'type'     => 'select',
            'title'    => __('Image Size', 'histudy'),
            'subtitle'    => __('Blog Image Size', 'histudy'),
            'options'  => rainbow_get_thumbnail_sizes(),
            'default'  => 'full',
            'required' => array('rainbow_blog_layout', 'equals', 'default'),
        ),
        /**
         * Other common styles for blog
         *
         * @since 1.0.0
         */
         array(
            'id' => 'rainbow_post_content_limit',
            'type' => 'text',
            'title' => __('Post Content Limit', 'histudy'),
            'default' => '24',
        ),
        array(
            'id' => 'rainbow_show_post_author_meta',
            'type' => 'button_set',
            'title' => __('Author', 'histudy'),
            'subtitle' => __('Show or hide the author of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_post_publish_date_meta',
            'type' => 'button_set',
            'title' => __('Publish Date', 'histudy'),
            'subtitle' => __('Show or hide the publish date of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_post_reading_time_meta',
            'type' => 'button_set',
            'title' => __('Reading Time', 'histudy'),
            'subtitle' => __('Show or hide the publish content reading time.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_post_view',
            'type' => 'button_set',
            'title' => __('Post View', 'histudy'),
            'subtitle' => __('Show or hide the post view of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_post_comments_meta',
            'type' => 'button_set',
            'title' => __('Comments', 'histudy'),
            'subtitle' => __('Show or hide the comments of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_post_categories_meta',
            'type' => 'button_set',
            'title' => __('Categories', 'histudy'),
            'subtitle' => __('Show or hide the categories of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),

        array(
            'id' => 'rainbow_show_post_tags_meta',
            'type' => 'button_set',
            'title' => __('Tags', 'histudy'),
            'subtitle' => __('Show or hide the tags of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_enable_readmore_btn',
            'type' => 'button_set',
            'title' => __('Read More Button', 'histudy'),
            'subtitle' => __('Show or hide the read more button of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_readmore_text',
            'type' => 'text',
            'title' => __('Read More Text', 'histudy'),
            'subtitle' => __('Set the Default title of read more button.', 'histudy'),
            'default' => __('Read More', 'histudy'),
            'required' => array('rainbow_enable_readmore_btn', 'equals', 'yes'),
        ),
    )
));

/**
 * Single Post
 */
Redux::setSection($opt_name, array(
    'title' => __('Single', 'histudy'),
    'id' => 'rainbow_blog_details_id',
    'icon' => 'el el-website',
    'subsection' => true,
    'fields' => array(
        array(
            'id' => 'rainbow_show_blog_details_author_meta',
            'type' => 'button_set',
            'title' => __('Author', 'histudy'),
            'subtitle' => __('Show or hide the author of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_blog_details_publish_date_meta',
            'type' => 'button_set',
            'title' => __('Publish Date', 'histudy'),
            'subtitle' => __('Show or hide the publish date of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_blog_details_reading_time_meta',
            'type' => 'button_set',
            'title' => __('Reading Time', 'histudy'),
            'subtitle' => __('Show or hide the publish content reading time.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_show_blog_details_comments_meta',
            'type' => 'button_set',
            'title' => __('Comments', 'histudy'),
            'subtitle' => __('Show or hide the comments of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_blog_details_categories_meta',
            'type' => 'button_set',
            'title' => __('Categories', 'histudy'),
            'subtitle' => __('Show or hide the categories of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_post_categories_meta_top',
            'type' => 'button_set',
            'title' => __('Categories Top Meta', 'histudy'),
            'subtitle' => __('Show or hide the Author with top categories of blog Single post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_blog_details_tags_meta',
            'type' => 'button_set',
            'title' => __('Tags', 'histudy'),
            'subtitle' => __('Show or hide the tags of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),
        array(
            'id' => 'rainbow_show_blog_details_post_view',
            'type' => 'button_set',
            'title' => __('View Post', 'histudy'),
            'subtitle' => __('Show or hide the View Post of blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'rainbow_blog_details_like_options',
            'type' => 'button_set',
            'title' => __('Likes', 'histudy'),
            'subtitle' => __('Show or hide the Likes of single blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'no',
        ),

        array(
            'id' => 'rainbow_blog_details_social_share',
            'type' => 'switch',
            'title' => __('Social Link', 'histudy'),
            'subtitle' => __('Show or hide the social share of single post.', 'histudy'),
            'default' => false,
        ),
        array(
            'id' => 'rainbow_blog_details_author_biography',
            'type' => 'button_set',
            'title' => __('Author Biography', 'histudy'),
            'subtitle' => __('Show or hide the author biography of single blog post.', 'histudy'),
            'options' => array(
                'yes' => __('Show', 'histudy'),
                'no' => __('Hide', 'histudy'),
            ),
            'default' => 'yes',
        ),
        // Show Related post
        array(
            'id'      => 'show_related_post',
            'type'    => 'switch',
            'title'   => __( 'Show Related post', 'histudy' ),
            'on'      => __( 'On', 'histudy' ),
            'off'     => __( 'Off', 'histudy' ),
            'default' => false,
        ),
        array(
            'id'       => 'related_post_area_before_title',
            'type'     => 'text',
            'title'    => __( 'Related Post Area before Title', 'histudy' ),
            'default'  => __( 'RELATED POST', 'histudy' ),
            'required' => array('show_related_post', 'equals', true),
        ),
        array(
            'id'       => 'related_post_area_title',
            'type'     => 'text',
            'title'    => __( 'Related Post Area Title', 'histudy' ),
            'default'  => __( 'Similar Post', 'histudy' ),
            'required' => array('show_related_post', 'equals', true),
        ),

        array(
            'id'       => 'show_related_post_number',
            'type'     => 'text',
            'title'    => __( 'Show Related Post Number', 'histudy' ),
            'required' => array( 'show_related_post', 'equals', true ),
            'default'  =>  '4',
        ),

        array(
            'id'       => 'related_post_query',
            'type'     => 'radio',
            'title'    => __('Query Type', 'histudy'),
            'subtitle' => __('Post Query', 'histudy'),
            'required' => array( 'show_related_post', 'equals', true ),
            'options'  => array(
                'cat'       => __( 'Posts in the same Categories', 'histudy' ),
                'tag'       => __( 'Posts in the same Tags', 'histudy' ),
                'author'    => __( 'Posts by the same Author', 'histudy' ),
            ),
            'default'   => 'cat'
        ),

        array(
            'id'       => 'related_post_sort',
            'type'     => 'radio',
            'title'    => __('Sort Order', 'histudy'),
            'subtitle' => __('Display post Order', 'histudy'),
            'required' => array( 'show_related_post', 'equals', true ),
            'options'  => array(
                'recent'    => __( 'Recent Posts', 'histudy' ),
                'rand'      => __( 'Random Posts', 'histudy' ),
                'modified'  => __( 'Last Modified Posts', 'histudy' ),
                'popular'   => __( 'Most Commented posts', 'histudy' ),
            ),
            'default'   => 'recent'
        ),
        array(
            'id'       => 'related_title_limit',
            'type'     => 'text',
            'required' => array( 'show_related_post', 'equals', true ),
            'title'    => __( 'Related Post Title Length', 'histudy' ),
            'default'  => '15',
        ),

    )
));

/**
 * Typography
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Typography', 'histudy'),
    'id' => 'rainbow_fonts',
    'icon' => 'el el-fontsize',
    'fields' => array(
        array(
            'id' => 'rainbow_p_typography',
            'type' => 'typography',
            'title' => esc_html__('Body Typography (Paragraph)', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the body.', 'histudy'),
            'google' => true,
            'color' => false,
            'subsets' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'text-align' => false,
            'all_styles' => false,
            'output' => array('body, em, p'),
            'units' => 'px',
        ),
        array(
            'id' => 'rainbow_header_typography',
            'type' => 'typography',
            'title' => esc_html__('Header Typography (Menu)', 'histudy'),
            'subtitle' => esc_html__('Change header menu typography based on your requirements.', 'histudy'),
            'google' => true,
            'color' => false,
            'subsets' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'text-align' => false,
            'all_styles' => false,
            'output' => array('body .rbt-header .mainmenu-nav .mainmenu li a, .rbt-header .mainmenu-nav .mainmenu > li > a, .rbt-header .mainmenu-nav .mainmenu li.with-megamenu .rbt-megamenu .wrapper .mega-menu-item li a'),
            'units' => 'px',
        ),
        array(
            'id' => 'rainbow_footer_typography_heading',
            'type' => 'typography',
            'title' => esc_html__('Footer Typography (Heading)', 'histudy'),
            'subtitle' => esc_html__('Change footer heading typography based on your requirements.', 'histudy'),
            'google' => true,
            'color' => false,
            'subsets' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'text-align' => false,
            'all_styles' => false,
            'output' => array('.rbt-footer .footer-widget .ft-title'),
            'units' => 'px',
        ),
        array(
            'id' => 'rainbow_footer_typography_menu',
            'type' => 'typography',
            'title' => esc_html__('Footer Typography (Menu)', 'histudy'),
            'subtitle' => esc_html__('Change footer menu typography based on your requirements.', 'histudy'),
            'google' => true,
            'color' => false,
            'subsets' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'text-align' => false,
            'all_styles' => false,
            'output' => array('.footer-widget .ft-link li a'),
            'units' => 'px',
        ),
        array(
            'id' => 'rainbow_h1_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H1 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H1 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h1, .h1'),
        ),
        array(
            'id' => 'rainbow_h2_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H2 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H2 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'letter-spacing' => false,
            'word-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h2, .h2'),
        ),
        array(
            'id' => 'rainbow_h3_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H3 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H3 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'letter-spacing' => false,
            'word-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h3, .h3'),
        ),
        array(
            'id' => 'rainbow_h4_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H4 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H4 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h4, .h4'),
        ),
        array(
            'id' => 'rainbow_h5_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H5 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H5 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h5, .h5'),
        ),
        array(
            'id' => 'rainbow_h6_typography',
            'type' => 'typography',
            'always_display' => false,
            'color' => false,
            'title' => esc_html__('H6 Heading Typography', 'histudy'),
            'subtitle' => esc_html__('Controls the typography settings of the H6 heading.', 'histudy'),
            'google' => true,
            'text-transform' => false,
            'word-spacing' => false,
            'letter-spacing' => false,
            'subsets' => false,
            'text-align' => false,
            'all_styles' => false,
            'units' => 'px',
            'output' => array('h6, .h6'),
        ),

    )
));

/**
 * Footer section
 */
Redux::setSection($opt_name, array(
    'title' => __('Footer', 'histudy'),
    'id' => 'rainbow_footer_section',
    'icon' => 'el el-photo',
    'fields' => array(
        array(
            'id' => 'rainbow_footer_enable',
            'type' => 'switch',
            'title' => __('Footer', 'histudy'),
            'subtitle' => __('Enable or disable the footer area.', 'histudy'),
            'default' => true,
        ),
        array(
            'id'                        => 'rainbow_footer_type',
            'title'                     => esc_html__('Footer Type','histudy'),
            'subtitle'                  => esc_html__('Select footer type, if the default is chosen the existing options below will work, or choose the custom option to get footers from footer post type.','histudy'),
            'type'                      => 'button_set',
            'options'                   => array(
                'default'                   => esc_html__('Default','histudy'),
                'custom'                    => esc_html__('Custom','histudy'),
            ),
            'default'                   => 'default',
        ),

        array(
            'id'       => 'rainbow_select_footer_template_custom',
            'type'     => 'select',
            'title'                 => esc_html__('Select Footer Template', 'histudy'),
            'subtitle'              => esc_html__('Select the footer template that you made in the footer post type.', 'histudy'),
            'options'  => awLoadPostTemplateHistudy('rbt_footer_builder'),
            'required' => array(
                array('rainbow_footer_enable', 'equals', true),
                array('rainbow_footer_type','equals', 'custom'),
            ),
        ),

        // Footer Custom Style
        array(
            'id' => 'rainbow_select_footer_template',
            'type' => 'image_select',
            'title' => __('Select Footer Layout', 'histudy'),
            'options' => array(
                '1' => array(
                    'alt' => __('Footer Layout 1', 'histudy'),
                    'title' => __('Footer Layout 1', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/1.png',
                ),
                '2' => array(
                    'alt' => __('Footer Layout 2', 'histudy'),
                    'title' => __('Footer Layout 2', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/2.png',
                ),
                '3' => array(
                    'alt' => __('Footer Layout 3', 'histudy'),
                    'title' => __('Footer Layout 3', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/3.png',
                ),
                '4' => array(
                    'alt' => __('Footer Layout 4', 'histudy'),
                    'title' => __('Footer Layout 4', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/4.png',
                ),
                'none' => array(
                    'alt' => __('None', 'histudy'),
                    'title' => __('None', 'histudy'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/none.png',
                ),
            ),
            'default' => '4',
            'required' => array(
                array('rainbow_footer_enable', 'equals', true),
                array('rainbow_footer_type','equals', 'default'),
            ),
        ),

        array(
            'id' => 'rainbow_copyright_contact',
            'type' => 'editor',
            'title' => __('Copyright Content', 'histudy'),
            'args' => array(
                'teeny' => true,
                'textarea_rows' => 5,
            ),
            'default' => '©2024. All rights reserved by <a href="#" target="_blank" rel="noopener">Your Company.</a>',
            'required' => array(
                array('rainbow_footer_enable', 'equals', true),
                array('rainbow_footer_type','equals', 'default'),
            ),
        ),

           array(
            'id' => 'rainbow_footer_footerbottom',
            'type' => 'switch',
            'title' => __('Footer Bottom Menu', 'histudy'),
            'subtitle' => __('Enable or disable the footer Menu.', 'histudy'),
            'default' => false,
            'required' => array(
                array('rainbow_footer_enable', 'equals', true),
                array('rainbow_footer_type','equals', 'default'),
            ),
        ),



    )
));


/**
 * 404 error page
 */
Redux::setSection($opt_name, array(
    'title' => __('404 Page', 'histudy'),
    'id' => 'rainbow_error_page',
    'icon' => 'el el-eye-close',
    'fields' => array(
        array(
            'id' => 'rainbow_404_title',
            'type' => 'text',
            'title' => __('Title', 'histudy'),
            'subtitle' => __('Add your Default title.', 'histudy'),
            'value' => '404!',
            'default' => __('404!', 'histudy'),
        ),

        array(
            'id' => 'rainbow_404_subtitle',
            'type' => 'text',
            'title' => __('Sub Title', 'histudy'),
            'subtitle' => __('Add your custom subtitle.', 'histudy'),
            'default' => __('Page not found', 'histudy'),
        ),
        array(
            'id' => 'rainbow_404_content',
            'type' => 'textarea',
            'rows' => 2,
            'title' => __('Content', 'histudy'),
            'subtitle' => __('Add your custom Content.', 'histudy'),
            'default' => __('The page you were looking for could not be found.', 'histudy'),
        ),

        array(
            'id' => 'rainbow_enable_go_back_btn',
            'type' => 'button_set',
            'title' => __('Button', 'histudy'),
            'subtitle' => __('Enable or disable the go to home page button.', 'histudy'),
            'options' => array(
                'yes' => 'Enable',
                'no' => 'Disable'
            ),
            'default' => 'yes'
        ),
        array(
            'id' => 'rainbow_button_text',
            'type' => 'text',
            'title' => __('Button Text', 'histudy'),
            'subtitle' => __('Set the custom text of go to home page button.', 'histudy'),
            'default' => __('Back to Homepage', 'histudy'),
            'required' => array('rainbow_enable_go_back_btn', 'equals', 'yes'),
        )
    )
));

/**
 * Tutor LMS
 */
if(true) {
    Redux::setSection($opt_name,
        array(
            'title' => __('Tutor LMS', 'histudy'),
            'id' => 'rainbow_theme_tutor_lms_settings',
            'icon' => 'el el-book',
            'fields' => array(
                array(
                    'id' => 'rainbow_tutor_default_order',
                    'type' => 'select',
                    'title' => __('Default Order', 'histudy'),
                    'options'  => array(
                        'asc' => 'Old Posts First',
                        'desc' => 'New Posts First',
                    ),
                    'default'  => 'desc',
                ),
            )
        )
    );
    Redux::setSection($opt_name, array(
        'title' => __('Course Archive Banner', 'histudy'),
        'id' => 'rainbow_theme_tutor_lms_course_archive_banner',
        'icon' => 'el el-folder-open',
        'subsection' => true,
        'fields' => array(
            array(
                'id' => 'rainbow_tutor_archive_banner_layout',
                'type' => 'image_select',
                'title' => __('Archive Banner Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'title' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/1.jpg',
                    ),
                    'layout-2' => array(
                        'alt' => __('Layout 2', 'histudy'),
                        'title' => __('Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/3.jpg',
                    ),
                    'layout-3' => array(
                        'alt' => __('Layout 3', 'histudy'),
                        'title' => __('Layout 3', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/2.jpg',
                    ),
                ),
                'default' => 'layout-1',
            ),
            array(
                'id'       => 'rainbow_course_archive_brd_color_image',
                'type'     => 'background',
                'title'    => esc_html__('Course archive Banner Image | background color', 'histudy'),
                'subtitle' => esc_html__('Course archive background with image, color, etc.', 'histudy'),
            ),

            array(
                'id'       => 'rainbow_course_archive_brd_image',
                'type'     => 'color_gradient',
                'title'    => esc_html__('Course Archive banner gradient Color', 'histudy'),
                'validate' => 'color',
                'default'        => array(
                    'preview' => false,
                    'from'           => '',
                    'to'             => '',
                ),
            ),
            array(
                'id'       => 'rainbow_course_details_brd_image',
                'type'     => 'background',
                'title'    => esc_html__('Course Details Banner Image | background color', 'histudy'),
                'subtitle' => esc_html__('Course Details background with image, color, etc.', 'histudy'),
            ),
            array(
                'id'       => 'rainbow_course_details_brd_image_layout4',
                'type'     => 'background',
                'title'    => esc_html__('Course Details Banner Image Style 4 | background color', 'histudy'),
                'subtitle' => esc_html__('Course Details background with image, color, etc.', 'histudy'),
            ),
            array(
                'id'       => 'rainbow_course_details_gradient_image',
                'type'     => 'color_gradient',
                'title'    => esc_html__('Course details banner gradient Color', 'histudy'),
                'validate' => 'color',
                'default'        => array(
                    'preview' => false,
                    'from'           => '',
                    'to'             => '',
                ),
            ),
            array(
                'id' => 'rainbow_course_details_breadcrumb_overlap_switch',
                'type' => 'switch',
                'title' => __('Enable Overlap', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-2', 'layout-3']),
            ),
            array(
                'id' => 'rainbow_course_enable_ajax',
                'type' => 'switch',
                'title' => __('Enable Ajax', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-2', 'layout-3']),
            ),
            array(
                'id' => 'rainbow_override_ajax_archive',
                'type' => 'switch',
                'title' => __('Replace Existing Courses on Load More', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-2', 'layout-3']),
            ),
            array(
                'id' => 'rainbow_course_details_breadcrumb_filter_layout',
                'type' => 'image_select',
                'title' => __('Filter Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-1.jpg',
                        'title' => __('Layout 1', 'histudy'),
                    ),
                    'layout-2' => array(
                        'alt' => __('Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-2.jpg',
                        'title' => __('Layout 2', 'histudy'),
                    ),
                    'layout-3' => array(
                        'alt' => __('Layout 3', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-3.png',
                        'title' => __('Layout 3', 'histudy'),
                    ),
                    'layout-4' => array(
                        'alt' => __('Layout 4', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-4.jpg',
                        'title' => __('Layout 4', 'histudy'),
                    ),
                ),
                'default' => 'layout-1',
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-2', 'layout-3']),
            ),
            array(
                'id' => 'rainbow_tutor_archive_course_per_page',
                'type' => 'text',
                'title' => __('Course Per Page', 'histudy'),
                'subtitle' => __('"You can set how many courses you want to show on the default course page."', 'histudy'),
                'default' => __('6', 'histudy'),
            ),
            array(
                'id' => 'rainbow_tutor_archive_course_per_click',
                'type' => 'text',
                'title' => __('Course Per Click append item', 'histudy'),
                'subtitle' => __('"You can set how many items will be added per click on the Load More button on the course archive page."', 'histudy'),
                'default' => __('6', 'histudy'),
            ),
            array(
                'id' => 'rainbow_tutor_single_brd_language_text',
                'type' => 'text',
                'title' => __('Course Single banner Language text', 'histudy'),
                'subtitle' => __('"You can change banner language text."', 'histudy'),
                'default' => __('english,arabic', 'histudy'),
            ),
            array(
                'id' => 'rainbow_tutor_archive_title',
                'type' => 'text',
                'title' => __('Archive Title', 'histudy'),
                'subtitle' => __('Controls the Default title of the page which is displayed on the archive course are on the course page.', 'histudy'),
                'default' => __('All Courses', 'histudy'),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-1', 'layout-2','layout-3']),
            ),
            array(
                'id' => 'rainbow_tutor_archive_subtitle',
                'type' => 'text',
                'title' => __('Archive SubTitle', 'histudy'),
                'subtitle' => __('Controls the Default title of the page which is displayed on the archive course subtitle are on the course page.', 'histudy'),
                'default' => __('Courses that help beginner designers become true unicorns.', 'histudy'),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
            ),
            array(
                'id' => 'rainbow_tutor_course_badge_show_hide',
                'type' => 'button_set',
                'title' => __(' Course Badge Show/Hide', 'histudy'),
                'subtitle' => __('Course Badge Show/Hide feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                'default' => 'yes',
            ),
            array(
                'id'       => 'rainbow_tutor_course_select_column',
                'type'     => 'select',
                'title'    => __('Select Column', 'histudy'),
                'subtitle'    => __('Please select column for course archive', 'histudy'),
                'options'  => array(
                    1 => __('Column 1', 'histudy'),
                    2 => __('Column 2', 'histudy'),
                    3 => __('Column 3', 'histudy'),
                ),
                'default'  => 3,
            ),
            array(
                'id' => 'rainbow_tutor_course_toggle_tab',
                'type' => 'button_set',
                'title' => __(' Course Tab Show/Hide', 'histudy'),
                'subtitle' => __('Course Tab Show/Hide on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                'default' => 'yes',
            ),

            array(
                'id' => 'rainbow_tutor_course_badge_label',
                'type' => 'text',
                'title' => __(' Course Badge Label', 'histudy'),
                'subtitle' => __('Enable Search feature on course archive', 'histudy'),
                'default' => __('Courses', 'histudy'),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                'required' => array(
                    array('rainbow_tutor_course_badge_show_hide', 'equals', 'yes')
                )
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_grid_list',
                'type' => 'button_set',
                'title' => __(' Enable Grid/List', 'histudy'),
                'subtitle' => __('Enable Grid/List feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_search',
                'type' => 'button_set',
                'title' => __(' Enable Search', 'histudy'),
                'subtitle' => __('Enable Search feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'required' => array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter',
                'type' => 'button_set',
                'title' => __('Enable Filter', 'histudy'),
                'subtitle' => __('Enable filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                ),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_category_archive_enable_filter',
                'type' => 'button_set',
                'title' => __('Enable Filter course Category', 'histudy'),
                'subtitle' => __('Enable filter feature on course Category archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                ),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_category_archive_category_enable_filter',
                'type' => 'button_set',
                'title' => __('Enable Category Filter course Category', 'histudy'),
                'subtitle' => __('Enable Category filter Option on Category Page', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                ),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_archive_orderby_front',
                'type' => 'button_set',
                'title' => __('Front Orderby', 'histudy'),
                'subtitle' => __('Set orderby to your filter front', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                ),
                'default' => 'no',
            ),
            array(
                'id' => 'rainbow_tutor_archive_filter_show_default',
                'type' => 'button_set',
                'title' => __('Filter Show?', 'histudy'),
                'subtitle' => __('Enable filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array('rainbow_tutor_archive_enable_filter', 'equals', 'yes'),
                ),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_enable_sort_by',
                'type' => 'button_set',
                'title' => __('Enable Sort By', 'histudy'),
                'subtitle' => __('Sort by filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                ),
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_enable_author_sort',
                'type' => 'button_set',
                'title' => __('Enable Author Sort', 'histudy'),
                'subtitle' => __('Enable Author sort filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array(
                        'rainbow_tutor_archive_enable_filter', 'equals', 'yes'
                    )
                ),
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_enable_offer_sort',
                'type' => 'button_set',
                'title' => __('Enable Offer Sort', 'histudy'),
                'subtitle' => __('Enable Offer sort filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array(
                        'rainbow_tutor_archive_enable_filter', 'equals', 'yes'
                    )
                ),
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_difficulty',
                'type' => 'button_set',
                'title' => __('Enable Difficulty Sort?', 'histudy'),
                'subtitle' => __('Enable difficulty sort filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array(
                        'rainbow_tutor_archive_enable_filter', 'equals', 'yes'
                    )
                ),
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_enable_category_sort',
                'type' => 'button_set',
                'title' => __('Enable Category Sort', 'histudy'),
                'subtitle' => __('Enable Category sort filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array(
                        'rainbow_tutor_archive_enable_filter', 'equals', 'yes'
                    )
                ),
            ),
            array(
                'id' => 'rainbow_tutor_archive_enable_filter_enable_rating_sort',
                'type' => 'button_set',
                'title' => __('Enable Rating Sort', 'histudy'),
                'subtitle' => __('Enable Rating sort filter feature on course archive', 'histudy'),
                'options' => array(
                    'yes' => __('Enable', 'histudy'),
                    'no' => __('Disable', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array(
                    array('rainbow_tutor_archive_banner_layout', 'equals', ['layout-3', 'layout-2']),
                    array(
                        'rainbow_tutor_archive_enable_filter', 'equals', 'yes'
                    )
                ),
            ),
        )
    ));
    Redux::setSection($opt_name, array(
        'title' => __('Course Details', 'histudy'),
        'id' => '_section_raibow_course_details',
        'icon' => 'el el-folder-open',
        'subsection' => true,
        'fields' => array(
            /**
             * Course Details banner
             */
            array(
                'id'       => 'rainbow_course_details_layout',
                'type'     => 'select',
                'title'    => __('Course Details Layout', 'histudy'),
                'subtitle'    => __('Please select a course details layout.', 'histudy'),
                'options'  => array(
                    'layout-1' => __( 'Layout 01', 'histudy' ),
                    'layout-2' => __( 'Layout 02', 'histudy' ),
                    'layout-3' => __( 'Layout 03', 'histudy' ),
                    'layout-4' => __( 'Layout 04', 'histudy' ),
                    'layout-5' => __( 'Layout 05', 'histudy' ),
                ),
                'default'  => 'layout-1',
                'required' => array( 'rainbow_course_details_thumbnail_switch', 'equals', 1 ),
            ),
            array(
                'id' => 'rainbow_course_details_banner_switch',
                'type' => 'switch',
                'title' => __('Bannner', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id'       => 'rainbow_single_course_student_enroll_image',
                'type'     => 'media',
                'url'      => true,
                'title'    => __('Enroll Student Image', 'histudy'),
                'desc'     => __('Upload a student enroll image.', 'histudy'),
                'subtitle' => __('Working this image when using course details layout 3', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_breadcrumb_switch',
                'type' => 'switch',
                'title' => __('Breadcrumb', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_show_instructor_profile',
                'type' => 'switch',
                'title' => __('Show Instructor Profile', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_title_switch',
                'type' => 'switch',
                'title' => __('Title', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_desc_switch',
                'type' => 'switch',
                'title' => __('Description', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_badge_switch',
                'type' => 'switch',
                'title' => __('Bestsellar Badge', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_rating_switch',
                'type' => 'switch',
                'title' => __('Enable Rating', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_student_count_switch',
                'type' => 'switch',
                'title' => __('Enable Student Count', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_author_meta_switch',
                'type' => 'switch',
                'title' => __('Enable Author Meta', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_bestsellar_banner_price_switch',
                'type' => 'switch',
                'title' => __('Enable Banner Price', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            /**
             * Course Details Content
             */
            array(
                'id' => 'rainbow_course_details_thumbnail_switch',
                'type' => 'switch',
                'title' => __('Enable/Disable Thumbnail', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id'       => 'rainbow_course_details_img_size',
                'type'     => 'select',
                'title'    => __('Image Size Video Thumbnail', 'histudy'),
                'subtitle'    => __('Image size will not apply on featured image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
                'required' => array( 'rainbow_course_details_thumbnail_switch', 'equals', 1 ),
            ),
            array(
                'id'       => 'rainbow_course_details_img_detail_size',
                'type'     => 'select',
                'title'    => __('Image Size Details Thumbnail', 'histudy'),
                'subtitle'    => __('Image size will not apply on featured image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
                'required' => array( 'rainbow_course_details_thumbnail_switch', 'equals', 1 ),
            ),

            /**
             * Author Course Controls
             */
            // author course
            array(
                'id' => 'rainbow_course_details_author_course_toggle',
                'type' => 'switch',
                'title' => __('Author Course Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_author_course_query_count',
                'type' => 'text',
                'title' => __('Author Query Count ', 'histudy'),
                'subtitle' => __('Author Query Count', 'histudy'),
                'default' => __('3', 'histudy'),
                'required' => array(
                    'rainbow_course_details_author_course_toggle', 'equals', 1
                )
            ),
            array(
                'id' => 'rainbow_course_details_author_course_title_prefix',
                'type' => 'text',
                'title' => __('Author Course Title Prefix', 'histudy'),
                'subtitle' => __('Enter which want to show before author course title', 'histudy'),
                'default' => __('More Course By', 'histudy'),
                'required' => array('rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_author_course_btn_toggle',
                'type' => 'switch',
                'title' => __('Author Course Button Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_author_course_btn_title',
                'type' => 'text',
                'title' => __('Author Course Button Text', 'histudy'),
                'subtitle' => __('Enter which want to show Author Course Button Text', 'histudy'),
                'default' => __('View All Course', 'histudy'),
                'required' => array(
                    array(
                        'rainbow_course_details_author_course_toggle', 'equals', 1
                    ),
                    array(
                        'rainbow_course_details_author_course_btn_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id' => 'rainbow_course_details_author_course_badge_toggle',
                'type' => 'switch',
                'title' => __('Course Badge Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_author_course_toggle', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_author_course_badge_title',
                'type' => 'text',
                'title' => __('Author Course Badge Title', 'histudy'),
                'subtitle' => __('Enter which want to show author course title', 'histudy'),
                'default' => __('TOP COURSE', 'histudy'),
                'required' => array(
                    array(
                        'rainbow_course_details_author_course_toggle', 'equals', 1
                    ),
                    array(
                        'rainbow_course_details_author_course_badge_toggle', 'equals', 1
                    )
                )
            ),
            // related course
            array(
                'id' => 'rainbow_course_details_related_course_toggle',
                'type' => 'switch',
                'title' => __('Related Course Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_related_course_query_count',
                'type' => 'text',
                'title' => __('Related Course Product Count', 'histudy'),
                'subtitle' => __('Related Course Product Count', 'histudy'),
                'default' => __('3', 'histudy'),
                'required' => array(
                    array(
                        'rainbow_course_details_related_course_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id' => 'rainbow_course_details_related_course_title',
                'type' => 'text',
                'title' => __('Related Course Title', 'histudy'),
                'subtitle' => __('Enter which want to show related course title', 'histudy'),
                'default' => __('Related Course', 'histudy'),
                'required' => array(
                    array(
                        'rainbow_course_details_related_course_toggle', 'equals', 1
                    )
                )
            ),
            array(
                'id' => 'rainbow_course_details_related_course_badge_title',
                'type' => 'text',
                'title' => __('Related Course Badge Title', 'histudy'),
                'subtitle' => __('Enter which want to show related course title', 'histudy'),
                'default' => __('Explore All Products', 'histudy'),
                'required' => array(
                    array(
                        'rainbow_course_details_related_course_toggle', 'equals', 1
                    ),
                    array(
                        'rainbow_course_details_related_course_badge_toggle', 'equals', 1
                    )
                )
            ),

            array(
                'id' => 'rainbow_course_details_related_course_badge_toggle',
                'type' => 'switch',
                'title' => __('Related Course Badge Show/Hide', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_related_course_toggle', 'equals', 1)
            ),

            /**
             * Single Course Card
             */
            array(
                'id' => 'rainbow_course_details_card_show_hide',
                'type' => 'switch',
                'title' => __('Show Course Sidebar?', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
            ),
            array(
                'id' => 'rainbow_course_details_card_position',
                'type' => 'image_select',
                'title' => __('Sidebar Position', 'histudy'),
                'options' => array(
                    'left' => array(
                        'alt' => __('Left Sidebar', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/left-sidebar.png',
                        'title' => __('Left Sidebar', 'histudy'),
                    ),
                    'right' => array(
                        'alt' => __('Right Sidebar', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/right-sidebar.png',
                        'title' => __('Right Sidebar', 'histudy'),
                    ),
                    'no' => array(
                        'alt' => __('No Sidebar', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/no-sidebar.png',
                        'title' => __('No Sidebar', 'histudy'),
                    ),
                ),
                'default' => 'right',
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'enable_course_details_sidebar_show_more',
                'type' => 'switch',
                'title' => __('Enable Sidebar Show More', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_position', 'equals', ['left', 'right'])
            ),

            array(
                'id' => 'rainbow_course_details_card_video_thumbnail_show',
                'type' => 'switch',
                'title' => __('Show Card Video Thumbnail', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_price_show',
                'type' => 'switch',
                'title' => __('Card Price Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_progress_show',
                'type' => 'switch',
                'title' => __('Card Progress Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_mony_back_badge_show',
                'type' => 'switch',
                'title' => __('Card Course Money Back Badge Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_last_udpate_show',
                'type' => 'switch',
                'title' => __('Card Course Last Update Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_enrolled_show',
                'type' => 'switch',
                'title' => __('Card Course Total Enrolled Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_lecture_count_show',
                'type' => 'switch',
                'title' => __('Card Course Lecture Count Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_skill_level_show',
                'type' => 'switch',
                'title' => __('Card Course Skill Level Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_language_show',
                'type' => 'switch',
                'title' => __('Card Course Language Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_quizzes_show',
                'type' => 'switch',
                'title' => __('Card Course Quizzes Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_duration_show',
                'type' => 'switch',
                'title' => __('Card Course Course Duration Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_instructore_show',
                'type' => 'switch',
                'title' => __('Card Course Instructor Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_requirement_show',
                'type' => 'switch',
                'title' => __('Card Course Requirement Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_tags_show',
                'type' => 'switch',
                'title' => __('Card Course Tags Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_target_audience_show',
                'type' => 'switch',
                'title' => __('Card Course Target audience Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_bottom_show',
                'type' => 'switch',
                'title' => __('Card Course Bottom Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_social_show',
                'type' => 'switch',
                'title' => __('Card Course Social Show', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1)
            ),
            array(
                'id' => 'rainbow_course_details_card_fb_switch',
                'type' => 'switch',
                'title' => __('Show Facebook ? ', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('rainbow_course_details_card_show_hide', 'equals', 1),
                    array('rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),
            array(
                'id' => 'rainbow_course_details_card_twitter_switch',
                'type' => 'switch',
                'title' => __('Show Twitter? ', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('rainbow_course_details_card_show_hide', 'equals', 1),
                    array('rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),
            array(
                'id' => 'rainbow_course_details_card_linkedin_switch',
                'type' => 'switch',
                'title' => __('Show Linkedin?', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array(
                    array('rainbow_course_details_card_show_hide', 'equals', 1),
                    array('rainbow_course_details_card_social_show', 'equals', 1),
                )
            ),

            array(
                'id' => 'rainbow_course_details_card_contact_label',
                'type' => 'text',
                'title' => __('Card contact label', 'histudy'),
                'default' => __('Card contact label', 'histudy'),
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1),
            ),
            array(
                'id' => 'rainbow_course_details_card_contact_number_before_text',
                'type' => 'text',
                'title' => __('Card contact Number Before Text', 'histudy'),
                'default' => __('Call Us:', 'histudy'),
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1),
            ),
            array(
                'id' => 'rainbow_course_details_card_contact_number_link',
                'type' => 'text',
                'title' => __('Card contact Number Link', 'histudy'),
                'default' => __('444555666777', 'histudy'),
                'required' => array('rainbow_course_details_card_show_hide', 'equals', 1),
            ),
            array(
                'id' => 'rainbow_course_details_about_course_heading',
                'type' => 'text',
                'title' => __('About course heading', 'histudy'),
                'default' => __('About Course', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_benifit_course_heading',
                'type' => 'text',
                'title' => __('Benefits Course heading', 'histudy'),
                'default' => __('Benefits of the course', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_course_content_heading',
                'type' => 'text',
                'title' => __('Course Content Heading', 'histudy'),
                'default' => __('Course Content', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_faq_heading',
                'type' => 'text',
                'title' => __('Faq Heading', 'histudy'),
                'default' => __('Question & Answer', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_student_review_heading',
                'type' => 'text',
                'title' => __('Student Review Heading', 'histudy'),
                'default' => __('Student Ratings & Reviews', 'histudy'),
            ),
            array(
                'id' => 'rainbow_course_details_instructor_box_heading',
                'type' => 'text',
                'title' => __('Instructor box heading', 'histudy'),
                'default' => __('Instructor', 'histudy'),
            ),
        ),
    ));
    Redux::setSection($opt_name, array(
        'title' => __('Course Card', 'histudy'),
        'id' => '_section_rainbow_theme_tutor_lms_course_card',
        'icon' => 'el el-picture',
        'subsection' => true,
        'fields' => array(
            array(
                'id' => 'rainbow_tutor_card_layout',
                'type' => 'image_select',
                'title' => __('Card Layout', 'histudy'),
                'options' => array(
                    'layout-1' => array(
                        'alt' => __('Layout 1', 'histudy'),
                        'title' => __('Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-1.jpg',
                    ),
                    'layout-2' => array(
                        'alt' => __('Layout 2', 'histudy'),
                        'title' => __('Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-2.jpg',
                    ),
                    'layout-3' => array(
                        'alt' => __('Layout 3', 'histudy'),
                        'title' => __('Layout 3', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-3.jpg',
                    ),
                    'layout-4' => array(
                        'alt' => __('Layout 4', 'histudy'),
                        'title' => __('Layout 4', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-4.jpg',
                    ),
                    'layout-5' => array(
                        'alt' => __('Layout 5', 'histudy'),
                        'title' => __('Layout 5', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-5.jpg',
                    ),
                    'layout-6' => array(
                        'alt' => __('Layout 6', 'histudy'),
                        'title' => __('Layout 6', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/card/card-layout-6.jpg',
                    )
                ),
                'default' => 'layout-1'
            ),
            array(
                'id' => 'rainbow_course_card_title_switch',
                'type' => 'switch',
                'title' => __('Title Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_card_meta_switch',
                'type' => 'switch',
                'title' => __('Meta Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_card_author_switch',
                'type' => 'switch',
                'title' => __('Author Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_card_image_switch',
                'type' => 'switch',
                'title' => __('Image Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id'       => 'rainbow_course_grid_archive_img_size',
                'type'     => 'select',
                'title'    => __('Course Grid Image Size', 'histudy'),
                'subtitle'    => __('Image size will apply on course image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
                'required' => array('rainbow_tutor_card_layout', 'equals', array('layout-1','layout-2','layout-5','layout-6')),
            ),
            array(
                'id'       => 'rainbow_course_list_archive_img_size',
                'type'     => 'select',
                'title'    => __('Course List Image Size', 'histudy'),
                'subtitle'    => __('Image size will apply on course image', 'histudy'),
                'options'  => rainbow_get_thumbnail_sizes(),
                'default'  => 'full',
                'required' => array('rainbow_tutor_card_layout', 'equals', array('layout-3','layout-4')),
            ),
            array(
                'id' => 'rainbow_course_card_rating_switch',
                'type' => 'switch',
                'title' => __('Rating Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_card_add_to_cart_switch',
                'type' => 'switch',
                'title' => __('Add To Cart Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_card_pricing_switch',
                'type' => 'switch',
                'title' => __('Pricing Switch', 'histudy'),
                'on' => __('Enabled', 'histudy'),
                'off' => __('Disabled', 'histudy'),
                'default' => true,
                'required' => array('rainbow_tutor_card_layout', 'equals', 'layout-1')
            ),
            array(
                'id' => 'rainbow_course_content_limit',
                'type' => 'text',
                'title' => __('Course Content Limit', 'histudy'),
                'default' => '10',
            ),
        )
    ));

    Redux::setSection($opt_name, array(
        'title' => __('Instructor Registration', 'histudy'),
        'id' => '_section_rainbow_theme_tutor_instructor_registration',
        'icon' => 'el el-user',
        'subsection' => true,
        'fields' => array(
            array(
                'id'         => '_rainbow_tutor_instructor_register_repeater',
                'type'       => 'repeater',
                'group_values' => false,
                'title'      => __( 'Instructor Registration Intro Repeater', 'histudy' ),
                'subtitle'   => __( 'Set some info for studets', 'histudy' ),
                'desc'       => __( 'Provide Better UX For Teachers', 'histudy' ),
                'fields'     => array(
                    array(
                        'id'          => 'rainbow_tutor_instructor_registration_repeater_title',
                        'type'        => 'text',
                        'placeholder' => __( 'Repeater Title', 'histudy' ),
                    ),
                    array(
                        'id'          => 'rainbow_tutor_instructor_registration_repeater_content',
                        'type'        => 'textarea',
                        'placeholder' => __( 'Repeater Content', 'histudy' ),
                    ),
                )
            ),
            array(
                'id'          => 'rainbow_tutor_instructor_registration_badge_title',
                'type'        => 'text',
                'placeholder' => __( 'Badge Title', 'histudy' ),
                'default'     => __( 'FOR BECOME A INSTRUCTOR', 'histudy' )
            ),
            array(
                'id'          => 'rainbow_tutor_instructor_registration_main_title',
                'type'        => 'text',
                'placeholder' => __( 'Main Title', 'histudy' ),
                'default'     => __( 'Instructor Registration', 'histudy' )
            ),
            array(
                'id'          => 'rainbow_tutor_instructor_registration_main_desc',
                'type'        => 'text',
                'placeholder' => __( 'Title Description', 'histudy' ),
            ),
            array(
                'id'       => 'instructor_image_show',
                'type'     => 'switch',
                'title'    => __( 'Show Instructor Image?', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                    'id'       => 'rainbow_tutor_instructor_registration_instructor_image',
                    'type'     => 'media',
                    'url'      => true,
                    'title'    => __('Instructor Image', 'histudy'),
                    'desc'     => __('Upload a generic instructor image for instructor registration page.', 'histudy'),
                    'subtitle' => __('Upload any media using the WordPress native uploader', 'histudy'),
                    'default'  => array(
                        'url'=>'https://rainbowit.net/html/histudy/assets/images/tab/tabs-10.jpg'
                    ),
                    'required' => array('instructor_image_show', 'equals', 1),
                )
            )
    ));
    Redux::setSection($opt_name, array(
        'title' => __('Student Registration', 'histudy'),
        'id' => '_section_rainbow_theme_tutor_student_registration',
        'icon' => 'el el-user',
        'subsection' => true,
        'fields' => array(
            array(
                'id'         => '_rainbow_tutor_student_register_repeater',
                'type'       => 'repeater',
                'group_values' => false,
                'title'      => __( 'Student Registration Intro Repeater', 'histudy' ),
                'subtitle'   => __( 'Set some info for studets', 'histudy' ),
                'desc'       => __( 'Provide Better UX For Students', 'histudy' ),
                'fields'     => array(
                    array(
                        'id'          => 'rainbow_tutor_student_registration_repeater_title',
                        'type'        => 'text',
                        'placeholder' => __( 'Repeater Title', 'histudy' ),
                    ),
                    array(
                        'id'          => 'rainbow_tutor_student_registration_repeater_content',
                        'type'        => 'textarea',
                        'placeholder' => __( 'Repeater Content', 'histudy' ),
                    ),
                )
            ),
            array(
                'id'          => 'rainbow_tutor_student_registration_badge_title',
                'type'        => 'text',
                'placeholder' => __( 'Badge Title', 'histudy' ),
                'default'     => __( 'FOR BECOME A STUDENT', 'histudy' )
            ),
            array(
                'id'          => 'rainbow_tutor_student_registration_main_title',
                'type'        => 'text',
                'placeholder' => __( 'Main Title', 'histudy' ),
                'default'     => __( 'Student Registration', 'histudy' )
            ),
            array(
                'id'          => 'rainbow_tutor_student_registration_main_desc',
                'type'        => 'text',
                'placeholder' => __( 'Title Description', 'histudy' ),
            ),
            array(
                'id'       => 'student_image_show',
                'type'     => 'switch',
                'title'    => __( 'Show Student Image?', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                    'id'       => 'rainbow_tutor_student_registration_student_image',
                    'type'     => 'media',
                    'url'      => true,
                    'title'    => __('student Image', 'histudy'),
                    'desc'     => __('Upload a generic student image for Student registration page.', 'histudy'),
                    'subtitle' => __('Upload any media using the WordPress native uploader', 'histudy'),
                    'default'  => array(
                        'url'=>'https://rainbowit.net/html/histudy/assets/images/tab/tabs-10.jpg'
                    ),
                    'required' => array('student_image_show', 'equals', 1),
            )
        )
    ));
}

/**
 * event Panel
 */
// Redux::setSection($opt_name, array(
//     'title' => __('Events', 'histudy'),
//     'id' => 'rainbow_event',
//     'icon' => 'el el-file-edit'
// ));
// /**
//  * Page Banner/Title section
//  */

// Redux::setSection($opt_name, array(
//     'title' => __('Events Banner', 'histudy'),
//     'id' => 'rainbow_event_banner',
//     'icon' => 'el el-website',
//     'subsection' => true,
//     'fields' => array(
//         array(
//             'id' => 'rainbow_event_breadcrumb_enable',
//             'type' => 'button_set',
//             'title' => __('Breadcrumb', 'histudy'),
//             'subtitle' => __('Show or hide  the Breadcrumb area', 'histudy'),
//             'options' => array(
//                 'yes' => __('Show', 'histudy'),
//                 'no' => __('Hide', 'histudy'),
//             ),
//             'default' => 'yes',
//         ),
//         array(
//             'id' => 'rainbow_event_archive_banner_layout',
//             'type' => 'image_select',
//             'title' => __('Archive Banner Layout', 'histudy'),
//             'options' => array(
//                 'layout-1' => array(
//                     'alt' => __('Layout 1', 'histudy'),
//                     'title' => __('Layout 1', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/event/event-breadcrumb-1.jpg',
//                 ),
//                 'layout-2' => array(
//                     'alt' => __('Layout 2', 'histudy'),
//                     'title' => __('Layout 2', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/event/event-breadcrumb-2.png',
//                 )
//             ),
//             'default' => 'layout-1',
//             'required' => array('rainbow_event_breadcrumb_enable', 'equals', 'yes'),
//         ),

//         array(
//             'id'       => 'rainbow_events_archive_brd_background',
//             'type'     => 'background',
//             'title'    => esc_html__('Events archive Banner Image | background color', 'histudy'),
//             'subtitle' => esc_html__('Events archive background with image, color, etc.', 'histudy'),
//         ),

//         array(
//             'id'       => 'histudy_event_page_bg_color',
//             'type'     => 'color_gradient',
//             'title'    => esc_html__('Event Archive Banner Gradient Color', 'histudy'),
//             'validate' => 'color',
//             'default'        => array(
//                 'preview' => false,
//                 'from'           => '',
//                 'to'             => '',
//             ),
//         ),
//         array(
//             'id'       => 'histudy_evnet_page_bg_color_top',
//             'type'     => 'color_gradient',
//             'title'    => esc_html__('Event Archive Banner Top Gradient Color', 'histudy'),
//             'validate' => 'color',
//             'default'        => array(
//                 'preview' => false,
//                 'from'           => '',
//                 'to'             => '',
//             ),
//         ),

//         array(
//             'id'       => 'rainbow_event_details_brd_image',
//             'type'     => 'background',
//             'title'    => esc_html__('Events Details Banner Image | background color', 'histudy'),
//             'subtitle' => esc_html__('Events Details background with image, color, etc.', 'histudy'),
//         ),
//         array(
//             'id'       => 'rainbow_event_details_gradient_color',
//             'type'     => 'color_gradient',
//             'title'    => esc_html__('Event details banner Gradient Color', 'histudy'),
//             'validate' => 'color',
//             'default'        => array(
//                 'preview' => false,
//                 'from'           => '',
//                 'to'             => '',
//             ),
//         ),

//         array(
//             'id' => 'rainbow_event_archive_overlap',
//             'type' => 'switch',
//             'title' => __('Enable Overlap Content', 'histudy'),
//             'on' => __('Enabled', 'histudy'),
//             'off' => __('Disabled', 'histudy'),
//             'default' => true,
//             'required' => array('rainbow_event_archive_banner_layout', 'equals', 'layout-2'),
//         ),

//         array(
//             'id' => 'rainbow_event_title_text',
//             'type' => 'text',
//             'title' => __(' Title', 'histudy'),
//             'subtitle' => __('Controls the Default title of the page which is displayed on the page title are on the blog details page.', 'histudy'),
//             'default' => __('All Event', 'histudy'),
//             'required' => array('rainbow_event_breadcrumb_enable', 'equals', 'yes'),
//         ),
//         array(
//             'id' => 'rainbow_event_desc_text',
//             'type' => 'text',
//             'title' => __('Description', 'histudy'),
//             'subtitle' => __('Select the event description.', 'histudy'),
//             'default' => __('Event that help beginner designers become true unicorns.', 'histudy'),
//             'required' => array(
//                 array('rainbow_event_breadcrumb_enable', 'equals', 'yes'),
//                 array('rainbow_event_archive_banner_layout', 'equals', 'layout-2'),
//             ),
//         ),
//         array(
//             'id' => 'rainbow_event_archive_slug',
//             'type' => 'text',
//             'title' => __('Permalink Rewrite', 'histudy'),
//             'default' => 'course_event',
//         ),
//         array(
//             'id' => 'rainbow_event_archive_btn_label',
//             'type' => 'text',
//             'title' => __('Archive Button Label', 'histudy'),
//             'default' => 'See Details',
//         ),
//     )
// ));

// Redux::setSection($opt_name, array(
//     'title' => __('Events Layout', 'histudy'),
//     'id' => 'rainbow_event_layout',
//     'icon' => 'el el-website',
//     'subsection' => true,
//     'fields' => array(
//         array(
//             'id' => 'rainbow_event_sidebar',
//             'type' => 'image_select',
//             'title' => __('Select Event Sidebar', 'histudy'),
//             'subtitle' => __('Choose your favorite event layout', 'histudy'),
//             'options' => array(
//                 'left' => array(
//                     'alt' => __('Left Sidebar', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/left-sidebar.png',
//                     'title' => __('Left Sidebar', 'histudy'),
//                 ),
//                 'right' => array(
//                     'alt' => __('Right Sidebar', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/right-sidebar.png',
//                     'title' => __('Right Sidebar', 'histudy'),
//                 ),
//                 'no' => array(
//                     'alt' => __('No Sidebar', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/sidebar/no-sidebar.png',
//                     'title' => __('No Sidebar', 'histudy'),
//                 ),
//             ),
//             'default' => 'left',
//         ),
//         array(
//             'id' => 'rainbow_event_layout',
//             'type' => 'image_select',
//             'title' => __('Select Event Layout', 'histudy'),
//             'subtitle' => __('Choose your favorite event layout', 'histudy'),
//             'options' => array(
//                 'list' => array(
//                     'alt' => __('Event List', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-list.jpg',
//                     'title' => __('Select List Style', 'histudy'),
//                 ),
//                 'grid' => array(
//                     'alt' => __('Event Grid', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-grid-minimal.jpg',
//                     'title' => __('Select Grid Style', 'histudy'),
//                 ),
//                 'default' => array(
//                     'alt' => __('Default', 'histudy'),
//                     'img' => get_template_directory_uri() . '/assets/images/optionframework/blog/layout/blog-grid-minimal.jpg',
//                     'title' => __('Default', 'histudy'),
//                 ),
//             ),
//             'default' => 'grid',
//         ),

//         array(
//             'id'       => 'rainbow_event_archive_col_extra_grid',
//             'type'     => 'select',
//             'title'    => __('Select Column for Extra Device Grid', 'histudy'),
//             'subtitle' => __('Desktops: ≥ 1400px', 'histudy'),
//             'options'  => array(
//                 '12' => __('1 Col', 'histudy'),
//                 '6' => __('2 Col', 'histudy'),
//                 '4' => __('3 Col', 'histudy'),
//                 '3' => __('4 Col', 'histudy'),
//             ),
//             'default'  => '4',
//         ),

//         array(
//             'id'       => 'rainbow_event_archive_col_extra_list',
//             'type'     => 'select',
//             'title'    => __('Select Column for Extra Device List', 'histudy'),
//             'subtitle' => __('Desktops: ≥ 1400px', 'histudy'),
//             'options'  => array(
//                 '12' => __('1 Col', 'histudy'),
//                 '6' => __('2 Col', 'histudy'),
//                 '4' => __('3 Col', 'histudy'),
//                 '3' => __('4 Col', 'histudy'),
//             ),
//             'default'  => '6',
//         ),


//         array(
//             'id'       => 'rainbow_event_archive_col_lg',
//             'type'     => 'select',
//             'title'    => __('Select Column for Desktops', 'histudy'),
//             'subtitle' => __('Desktops: ≥ 1200px', 'histudy'),
//             'options'  => array(
//                 '12' => __('1 Col', 'histudy'),
//                 '6' => __('2 Col', 'histudy'),
//                 '4' => __('3 Col', 'histudy'),
//                 '3' => __('4 Col', 'histudy'),
//             ),
//             'default'  => '4',
//         ),
//         array(
//             'id'       => 'rainbow_event_archive_col_md',
//             'type'     => 'select',
//             'title'    => __('Select Column for Tabs', 'histudy'),
//             'subtitle'    => __('Tabs: ≥ 992px', 'histudy'),
//             'options'  => array(
//                 '12' => __('1 Col', 'histudy'),
//                 '6' => __('2 Col', 'histudy'),
//                 '4' => __('3 Col', 'histudy'),
//                 '3' => __('4 Col', 'histudy'),
//             ),
//             'default'  => '6',
//         ),
//         array(
//             'id'       => 'rainbow_event_archive_col',
//             'type'     => 'select',
//             'title'    => __('Select Column for Small Mobiles', 'histudy'),
//             'subtitle'    => __('Small Mobiles: < 576px', 'histudy'),
//             'options'  => array(
//                 '12' => __('1 Col', 'histudy'),
//                 '6' => __('2 Col', 'histudy'),
//                 '4' => __('3 Col', 'histudy'),
//                 '3' => __('4 Col', 'histudy'),
//             ),
//             'default'  => '12',
//         ),


//     )
// ));

// Redux::setSection($opt_name, array(
//     'title' => __('Events Single', 'histudy'),
//     'id' => 'rainbow_event_single',
//     'icon' => 'el el-website',
//     'subsection' => true,
//     'fields' => array(
//         array(
//             'id' => 'rainbow_popup_video_bottom_text_on_off',
//             'type' => 'button_set',
//             'title' => __(' Sidebar video Preview text On/off', 'histudy'),
//             'subtitle' => __('Show or hide banner area', 'histudy'),
//             'options' => array(
//                 'yes' => __('Show', 'histudy'),
//                 'no' => __('Hide', 'histudy'),
//             ),
//             'default' => 'yes',
//         ),
//         array(
//             'id'       => 'event_single_preview_text_change',
//             'type'     => 'text',
//             'title'    => __( 'Sidebar Preview Popup bottom video text change', 'histudy' ),
//             'default'  => 'Preview this event',
//         ),

//     )
// ));


/**
 * WooCommerce
 */

    Redux::setSection($opt_name, array(
        'title' => __('WooCommerce', 'histudy'),
        'id' => 'woo_Settings_section',
        'icon' => 'el el-shopping-cart',
    ));
    /**
     * WooCommerce Archive
     */
    Redux::setSection($opt_name, array(
        'title' => __('General', 'histudy'),
        'id' => 'wc_sec_general',
        'icon' => 'el el-folder-open',
        'subsection' => true,

        'fields' => array(

            array(
                'id' => 'rainbow_shop_banner_enable',
                'type' => 'button_set',
                'title' => __(' Shop Banner Enable', 'histudy'),
                'subtitle' => __('Show or hide banner area', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'default' => 'yes',
            ),
            array(
                'id' => 'rainbow_enable_shop_banner_overlap',
                'type' => 'button_set',
                'title' => __(' Enable Banner Overlap', 'histudy'),
                'subtitle' => __('Enable or disable shop banner overlap', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'default' => 'yes',
            ),

            array(
                'id' => 'rainbow_shop_banner_template',
                'type' => 'image_select',
                'hidden' => false,
                'title' => __('Select banner Layout', 'histudy'),
                'options' => array(
                    '1' => array(
                        'alt' => __('Bnnaer Layout 1', 'histudy'),
                        'title' => __('Bnnaer Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/banner/shop/banner-1.jpg',
                    ),
                    '2' => array(
                        'alt' => __('Bnnaer Layout 2', 'histudy'),
                        'title' => __('Bnnaer Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/banner/shop/banner-2.jpg',
                    ),
                ),
                'default' => '1',
                'required' => array('rainbow_shop_banner_enable', 'equals', 'yes'),
            ),
            array(
                'id' => 'rainbow_shop_bannr_subtitle',
                'type' => 'text',
                'title' => __('Shop Banner Sub Title', 'histudy'),
                'default' => '',
                'required' => array(
                    array('rainbow_shop_banner_enable', 'equals', 'yes'),
                    array('rainbow_shop_banner_template', 'equals', '2'),
                )
            ),


            array(
                'id'       => 'wc_num_product_per_row',
                'type'     => 'text',
                'title'    => __( 'Number of Products Per Row', 'histudy' ),
                'default'  => '3',
            ),
            array(
                'id'       => 'wc_num_product',
                'type'     => 'text',
                'title'    => __( 'Number of Products Per Page', 'histudy' ),
                'default'  => '12',
            ),
        )
    ));
    /**
     * LearnPress category
     */
    if( class_exists( 'LearnPress' ) ) {
        add_action( 'acf/include_fields', function() {
            if ( ! function_exists( 'acf_add_local_field_group' ) ) {
                return;
            }

            acf_add_local_field_group( array(
            'key' => 'group_668bd614e0c0e',
            'title' => 'Category LearnPress',
            'fields' => array(
                array(
                    'key' => 'field_668bd6155b774',
                    'label' => 'Category Icon Image',
                    'name' => 'category_image',
                    'aria-label' => '',
                    'type' => 'image',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_668bd6155b774_thumbnail_img',
                    'label' => 'Category Feature Image',
                    'name' => 'category_feature_image',
                    'aria-label' => '',
                    'type' => 'image',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                    'preview_size' => 'medium',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'taxonomy',
                        'operator' => '==',
                        'value' => 'course_category',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
            'show_in_rest' => 0,
        ) );
        } );
    }
    /**
     * LearnPress Course Additional Info
     */
    if( class_exists( 'LearnPress' ) ) {
        add_action( 'acf/include_fields', function() {
            if ( ! function_exists( 'acf_add_local_field_group' ) ) {
                return;
            }

            acf_add_local_field_group( array(
            'key' => 'group_668e209dd267d',
            'title' => 'Course Additional Info',
            'fields' => array(
                array(
                    'key' => 'field_668e209e89f7a',
                    'label' => 'Media',
                    'name' => 'media',
                    'aria-label' => '',
                    'type' => 'textarea',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'maxlength' => '',
                    'rows' => '',
                    'placeholder' => '',
                    'new_lines' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'lp_course',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
            'show_in_rest' => 0,
            ) );
        } );
    }
    /**
     * WooCommerce Single Page
     */
    Redux::setSection($opt_name, array(
        'title' => __('Product Single Page', 'histudy'),
        'id' => 'wc_sec_product',
        'icon' => 'el el-folder-open',
        'subsection' => true,
        'fields' => array(
                array(
                    'id' => 'rainbow_single_product_banner_enable',
                    'type' => 'button_set',
                    'title' => __(' Banner Enable', 'histudy'),
                    'subtitle' => __('Show or hide  the Banner Enable area', 'histudy'),
                    'options' => array(
                        'yes' => __('Show', 'histudy'),
                        'no' => __('Hide', 'histudy'),
                    ),
                    'default' => 'yes',
                ),

            array(
                'id' => 'rainbow_single_product_banner_layout',
                'type' => 'image_select',
                'title' => __('Select banner Layout', 'histudy'),
                'options' => array(
                    '1' => array(
                        'alt' => __('Banner Layout 1', 'histudy'),
                        'title' => __('Banner Layout 1', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/1.png',
                    ),
                    '2' => array(
                        'alt' => __('Banner Layout 2', 'histudy'),
                        'title' => __('Banner Layout 2', 'histudy'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/2.png',
                    ),
                ),
                'default' => '1',
                'required' => array('rainbow_single_product_banner_enable', 'equals', 'asd'),
            ),

            array(
                'id' => 'rainbow_single_product_banner_title',
                'type' => 'text',
                'title' => __('Default Title', 'histudy'),
                'subtitle' => __('Controls the Default title of the page which is displayed on the page title are on the shop page.', 'histudy'),
                'default' => __('Product Details', 'histudy'),
                'required' => array('rainbow_single_product_banner_enable', 'equals', 'asd'),
            ),
            array(
                'id' => 'rainbow_single_product_bannr_subtitle',
                'type' => 'text',
                'title' => __('Shop Banner Sub Title', 'histudy'),
                'default' => '',
                'required' => array('rainbow_single_product_banner_enable', 'equals', 'asd'),
            ),

            array(
                'id' => 'rainbow_single_product_breadcrumb_enable',
                'type' => 'button_set',
                'title' => __('Breadcrumb', 'histudy'),
                 'required' => array('rainbow_single_product_banner_enable', 'equals', 'yes'),
                'subtitle' => __('Show or hide  the Breadcrumb area', 'histudy'),
                'options' => array(
                    'yes' => __('Show', 'histudy'),
                    'no' => __('Hide', 'histudy'),
                ),
                'default' => 'yes',
                'required' => array('rainbow_single_product_banner_enable', 'equals', 'asd'),
            ),



            array(
                'id'       => 'wc_cats',
                'type'     => 'switch',
                'title'    => __( 'Categories', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wc_tags',
                'type'     => 'switch',
                'title'    => __( 'Tags', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wc_related',
                'type'     => 'switch',
                'title'    => __( 'Related Products', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wc_related_before_title',
                'type'     => 'text',
                'title'    => __( 'Related before Title', 'histudy' ),
                'default'  => 'RELATED BOOK',
                'required' => array('wc_related', 'equals', true),
            ),
            array(
                'id'       => 'wc_related_title',
                'type'     => 'text',
                'title'    => __( 'Related Title', 'histudy' ),
                'default'  => 'Similar Books.',
                'required' => array('wc_related', 'equals', true),
            ),


            array(
                'id'       => 'wc_description',
                'type'     => 'switch',
                'title'    => __( 'Description Tab', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wc_reviews',
                'type'     => 'switch',
                'title'    => __( 'Reviews Tab', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wc_additional_info',
                'type'     => 'switch',
                'title'    => __( 'Additional Information Tab', 'histudy' ),
                'on'       => __( 'Show', 'histudy' ),
                'off'      => __( 'Hide', 'histudy' ),
                'default'  => true,
            ),
        )
    ));

/**
 * LearnPress
 */
if(function_exists('academy_start' )) {
    Redux::setSection( $opt_name, array(
        'title' => __( 'Academy Settings', 'histudy' ),
        'id' => 'academy_lms_theme_options',
        'icon' => 'el el-zoom-in',
    ) );
}
    // academy course archive
    if(function_exists('academy_start' )) {
        Redux::setSection($opt_name, array(
            'title' => __('Academy Course Archive', 'histudy'),
            'id' => 'academy_banner_course_archive_control',
            'icon' => 'el el-folder-open',
            'subsection' => true,
            'fields' => array(

                array(
                    'id' => 'academy_course_archive_banner_layout',
                    'type' => 'image_select',
                    'title' => __('Archive Banner Layout', 'histudy'),
                    'options' => array(
                        'layout-1' => array(
                            'alt' => __('Layout 1', 'histudy'),
                            'title' => __('Layout 1', 'histudy'),
                            'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/1.jpg',
                        ),
                        'layout-2' => array(
                            'alt' => __('Filter Open', 'histudy'),
                            'title' => __('Filter Open', 'histudy'),
                            'img' => get_template_directory_uri() . '/assets/images/optionframework/v3/banner/archive/filter-open.jpg',
                        ),
                    ),
                    'default' => 'layout-1',
                ),
                array(
                    'id' => 'histudy_academy_course_breadcrumb_filter_layout',
                    'type' => 'image_select',
                    'title' => __('Filter Layout', 'histudy'),
                    'options' => array(
                        'layout-1' => array(
                            'alt' => __('Layout 1', 'histudy'),
                            'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-1.jpg',
                            'title' => __('Layout 1', 'histudy'),
                        ),
                        'layout-2' => array(
                            'alt' => __('Layout 2', 'histudy'),
                            'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-3.png',
                            'title' => __('Layout 2', 'histudy'),
                        ),
                        'layout-3' => array(
                            'alt' => __('Layout 3', 'histudy'),
                            'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/course/filter/layout-4.jpg',
                            'title' => __('Layout 3', 'histudy'),
                        ),
                    ),
                    'default' => 'layout-1',
                ),

                array(
                    'id'       => 'rainbow_academy_course_archive_brd_color_image',
                    'type'     => 'background',
                    'title'    => esc_html__('Course archive Banner Image | background color', 'histudy'),
                    'subtitle' => esc_html__('Course archive background with image, color, etc.', 'histudy'),
                ),

                array(
                    'id'       => 'rainbow_academy_course_archive_brd_image',
                    'type'     => 'color_gradient',
                    'title'    => esc_html__('Course Archive banner gradient Color', 'histudy'),
                    'validate' => 'color',
                    'default'        => array(
                        'preview' => false,
                        'from'           => '',
                        'to'             => '',
                    ),
                ),
                array(
                    'id'       => 'rainbow_academy_course_details_brd_image',
                    'type'     => 'background',
                    'title'    => esc_html__('Course Details Banner Image | background color', 'histudy'),
                    'subtitle' => esc_html__('Course Details background with image, color, etc.', 'histudy'),
                ),

                array(
                    'id'       => 'rainbow_academy_course_grid_archive_img_size',
                    'type'     => 'select',
                    'title'    => __('Course Grid Image Size', 'histudy'),
                    'subtitle'    => __('Image size will apply on course image', 'histudy'),
                    'options'  => rainbow_get_thumbnail_sizes(),
                    'default'  => 'full',
                ),

                array(
                    'id'       => 'academy_generic_banner_title_enable',
                    'type'     => 'switch',
                    'title'    => __('Enable Title?', 'histudy'),
                    'subtitle' => __('Enable/disable your title.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Title Hint', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_breadcrumb_enable',
                    'type'     => 'switch',
                    'title'    => __('Enable Breadcrumb?', 'histudy'),
                    'subtitle' => __('Enable/disable your breadcrumb.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Breadcrumb Hint', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_badge_enable',
                    'type'     => 'switch',
                    'title'    => __('Enable Badge?', 'histudy'),
                    'subtitle' => __('Enable/disable your breadcrumb.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Badge Hint', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_badge_text_singular',
                    'type' => 'text',
                    'title' => __('Badget Text Singular', 'histudy'),
                    'default' => 'Course',
                    'required' => array(
                        array('generic_banner_badge_enable', 'equals', true),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_badge_text_plural',
                    'type' => 'text',
                    'title' => __('Badget Text Plural', 'histudy'),
                    'default' => 'Courses',
                    'required' => array(
                        array('generic_banner_badge_enable', 'equals', true),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_subtitle_enable',
                    'type'     => 'switch',
                    'title'    => __('Enable Subtitle?', 'histudy'),
                    'subtitle' => __('Enable/disable your Subtitle.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Subtitle Switch Hint', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_subtitle',
                    'type' => 'text',
                    'title' => __('Subtitle Text', 'histudy'),
                    'default' => 'Courses that help beginner designers become true unicorns.',
                    'required' => array(
                        array('generic_banner_subtitle_enable', 'equals', true),
                    ),
                ),
                array(
                    'id' => 'academy_rainbow_course_details_title_switch',
                    'type' => 'switch',
                    'title' => __('Title', 'histudy'),
                    'on' => __('Enabled', 'histudy'),
                    'off' => __('Disabled', 'histudy'),
                    'default' => true,
                ),
                array(
                    'id' => 'academy_rainbow_course_details_bestsellar_rating_switch',
                    'type' => 'switch',
                    'title' => __('Enable Rating', 'histudy'),
                    'on' => __('Enabled', 'histudy'),
                    'off' => __('Disabled', 'histudy'),
                    'default' => true,
                ),
                array(
                    'id' => 'academy_rainbow_course_details_bestsellar_student_count_switch',
                    'type' => 'switch',
                    'title' => __('Enable Student Count', 'histudy'),
                    'on' => __('Enabled', 'histudy'),
                    'off' => __('Disabled', 'histudy'),
                    'default' => true,
                ),
                array(
                    'id'       => 'academy_generic_banner_layout_filter_enable',
                    'type'     => 'switch',
                    'title'    => __('Enable Grid/List Filter?', 'histudy'),
                    'subtitle' => __('Enable/Disable grid/list filter.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable Grid/List Filter', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                ),

                array(
                    'id' => 'academy_generic_banner_grid_btn_label',
                    'type' => 'text',
                    'title' => __('Grid Button Label', 'histudy'),
                    'default' => 'Grid',
                    'required' => array(
                        array('generic_banner_layout_filter_enable', 'equals', true),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_list_btn_icon',
                    'type'     => 'text',
                    'title'    => __('List Icon (Feather Icon)', 'histudy'),
                    'subtitle' => __('Enter the List icon name (e.g., "feather-List").', 'histudy'),
                    'default'  => 'feather-List',
                    'required' => array(
                        array('generic_banner_layout_filter_enable', 'equals', true),
                    ),
                    'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_list_btn_label',
                    'type' => 'text',
                    'title' => __('List Button Label', 'histudy'),
                    'default' => 'List',
                    'required' => array(
                        array('generic_banner_layout_filter_enable', 'equals', true),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_course_count_filter_description',
                    'type'     => 'switch',
                    'title'    => __('Enable Filter Course Count?', 'histudy'),
                    'subtitle' => __('Enable/Disable course count filter description.', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable course filter count', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_banner_course_count_filter_label_start',
                    'type' => 'text',
                    'title' => __('Count Filter Start Label', 'histudy'),
                    'default' => 'Showing',
                    'required' => array(
                        array('generic_banner_enable_course_count_filter_description', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_banner_course_count_filter_label_middle',
                    'type' => 'text',
                    'title' => __('Count Filter Middle Label', 'histudy'),
                    'default' => 'of',
                    'required' => array(
                        array('generic_banner_enable_course_count_filter_description', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_banner_course_count_filter_label_end',
                    'type' => 'text',
                    'title' => __('Count Filter End Label', 'histudy'),
                    'default' => 'results',
                    'required' => array(
                        array('generic_banner_enable_course_count_filter_description', 'equals', true),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_front_serch',
                    'type'     => 'switch',
                    'title'    => __('Enable Front Search?', 'histudy'),
                    'subtitle' => __('Enable/Disable Front Search', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable front search', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_front_search_label',
                    'type' => 'text',
                    'title' => __('Search Label', 'histudy'),
                    'default' => 'Search Your Course..',
                    'required' => array(
                        array('generic_banner_enable_front_serch', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_front_search_icon_feather',
                    'type'     => 'text',
                    'title'    => __('Search Icon (Feather Icon)', 'histudy'),
                    'subtitle' => __('Enter the search icon name (e.g., "feather-search").', 'histudy'),
                    'default'  => 'feather-search',
                    'required' => array(
                        array('generic_banner_enable_front_serch', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                    'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_filter_btn',
                    'type'     => 'switch',
                    'title'    => __('Enable Filter Button?', 'histudy'),
                    'subtitle' => __('Enable/Disable Filter Button', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable filter button', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_front_filter_icon_feather',
                    'type'     => 'text',
                    'title'    => __('Filter Icon (Feather Icon)', 'histudy'),
                    'subtitle' => __('Enter the search icon name (e.g., "feather-filter").', 'histudy'),
                    'default'  => 'feather-filter',
                    'required' => array(
                        array('generic_banner_enable_filter_btn', 'equals', true),
                    ),
                    'desc'     => __('For Feather Icons, visit <a href="https://feathericons.com/" target="_blank">Feather Icons</a> for available icon names.', 'histudy'),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_filter_label',
                    'type' => 'text',
                    'title' => __('Filter Label', 'histudy'),
                    'default' => 'Filter',
                    'required' => array(
                        array('generic_banner_enable_filter_btn', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_sort_by_date',
                    'type'     => 'switch',
                    'title'    => __('Enable Sort By Filter Date?', 'histudy'),
                    'subtitle' => __('Enable/Disable sort by date filter', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable sort by date filter', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_enable_sort_by_date_label',
                    'type' => 'text',
                    'title' => __('Sort By Date Label', 'histudy'),
                    'default' => 'SORT BY DATE',
                    'required' => array(
                        array('generic_banner_enable_sort_by_date', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_sort_by_author',
                    'type'     => 'switch',
                    'title'    => __('Enable Sort By Author?', 'histudy'),
                    'subtitle' => __('Enable/Disable sort by author filter', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable sort by author filter', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_enable_sort_by_author_label',
                    'type' => 'text',
                    'title' => __('Sort By Author Label', 'histudy'),
                    'default' => 'SORT BY AUTHOR',
                    'required' => array(
                        array('generic_banner_enable_sort_by_author', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_sort_by_offer',
                    'type'     => 'switch',
                    'title'    => __('Enable Sort By Offer?', 'histudy'),
                    'subtitle' => __('Enable/Disable sort by offer filter', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable sort by offer filter', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_enable_sort_by_offer_label',
                    'type' => 'text',
                    'title' => __('Sort By Offer Label', 'histudy'),
                    'default' => 'SORT BY OFFER',
                    'required' => array(
                        array('generic_banner_enable_sort_by_offer', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id'       => 'academy_generic_banner_enable_sort_by_category',
                    'type'     => 'switch',
                    'title'    => __('Enable Sort By Category?', 'histudy'),
                    'subtitle' => __('Enable/Disable sort by category filter', 'histudy'),
                    'default'  => true,
                    'hint'     => array(
                        'title'   => __('Enable/Disable sort by category filter', 'histudy'),
                        'content' => "<img src='https://sample-videos.com/gif/3.gif' style='max-height: 150px;' />",
                    ),
                    'required' => array(
                        array('always_false_condition', 'equals', false),
                    ),
                ),
                array(
                    'id' => 'academy_generic_banner_enable_sort_by_category_label',
                    'type' => 'text',
                    'title' => __('Sort By Category Label', 'histudy'),
                    'default' => 'SORT BY category',
                    'required' => array(
                        array('generic_banner_enable_sort_by_category', 'equals', true),
                        array('always_false_condition', 'equals', false),
                    ),
                ),
            )
        ) );
   }