<?php

/**
 * Adds a submenu page under a custom post type parent.
 */
add_action('admin_menu', 'rbt_inactive_theme_options_page');

function rbt_inactive_theme_options_page() {
    add_submenu_page(
        'histudy',
        __( 'Theme Options', 'histudy' ),
        __( 'Theme Options', 'histudy' ),
        'manage_options',
        'inactive-theme-options',
        'rbt_inactive_theme_options_page_callback'
    );
}

/**
 * Display callback for the submenu page.
 */
function rbt_inactive_theme_options_page_callback() { 
    ?>
    <div class="wrap">
        <h1><?php _e( 'Theme Options', 'histudy' ); ?></h1>
        <h4><?php _e( 'Please activate your theme and utilize the theme options.', 'histudy' ); ?></h4>

        <div class="image--box">
            <a class="rbt-image-link" href="<?php echo esc_url(home_url()); ?>/wp-admin/admin.php?page=histudy"><img style="width: 100%;" src="<?php echo esc_url( get_template_directory_uri().'/assets/images/histudy-option-sh.png');?>" alt="Inactive Options"></a>
        </div>

    </div>
    <?php
}


