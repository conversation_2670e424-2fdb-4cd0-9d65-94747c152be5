<?php
if (function_exists('acf_add_local_field_group')):
    // acf_add_local_field_group( array(
    //     'key' => 'group_6666a60485bb9',
    //     'title' => 'Event Description',
    //     'fields' => array(
    //         array(
    //             'key' => 'field_6666a604ed0f6',
    //             'label' => 'Event Description',
    //             'name' => 'event_description',
    //             'aria-label' => '',
    //             'type' => 'textarea',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'default_value' => '',
    //             'maxlength' => '',
    //             'rows' => '',
    //             'placeholder' => '',
    //             'new_lines' => '',
    //         ),

    //         array(
    //             'key' => 'field_event_badge',
    //             'label' => 'Event Badge',
    //             'name' => 'event_badge',
    //             'aria-label' => '',
    //             'type' => 'text',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'default_value' => '',
    //             'maxlength' => '',
    //             'placeholder' => '',
    //             'prepend' => '',
    //             'append' => '',
    //         ),
    //     ),
    //     'location' => array(
    //         array(
    //             array(
    //                 'param' => 'post_type',
    //                 'operator' => '==',
    //                 'value' => 'course_event',
    //             ),
    //         ),
    //     ),
    //     'menu_order' => 0,
    //     'position' => 'normal',
    //     'style' => 'default',
    //     'label_placement' => 'top',
    //     'instruction_placement' => 'label',
    //     'hide_on_screen' => '',
    //     'active' => true,
    //     'description' => '',
    //     'show_in_rest' => 0,
    // ) );

    // acf_add_local_field_group( array(
    //     'key' => 'group_658660de4aef0',
    //     'title' => 'Event Additional Options',
    //     'fields' => array(
    //         array(
    //             'key' => 'field_658661264c684',
    //             'label' => 'Event Features',
    //             'name' => 'event_features',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'table',
    //             'pagination' => 0,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Event Feature',
    //             'rows_per_page' => 20,
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_658661434c685',
    //                     'label' => 'Feature Text',
    //                     'name' => 'feature_text',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658661264c684',
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_658661774c686',
    //             'label' => 'Event Faq',
    //             'name' => 'event_faq',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'block',
    //             'pagination' => 0,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Faq',
    //             'rows_per_page' => 20,
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_6586618a4c687',
    //                     'label' => 'Faq Title',
    //                     'name' => 'faq_title',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '50',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658661774c686',
    //                 ),
    //                 array(
    //                     'key' => 'field_658661984c688',
    //                     'label' => 'Faq Content',
    //                     'name' => 'faq_content',
    //                     'aria-label' => '',
    //                     'type' => 'textarea',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '50',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'rows' => 4,
    //                     'placeholder' => '',
    //                     'new_lines' => '',
    //                     'parent_repeater' => 'field_658661774c686',
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_658661d04c689',
    //             'label' => 'Event Participants',
    //             'name' => 'event_participants',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'block',
    //             'pagination' => 0,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Participants',
    //             'rows_per_page' => 20,
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_658661d84c68a',
    //                     'label' => 'Participants Image',
    //                     'name' => 'participants_image',
    //                     'aria-label' => '',
    //                     'type' => 'image',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'return_format' => 'array',
    //                     'library' => 'all',
    //                     'min_width' => '',
    //                     'min_height' => '',
    //                     'min_size' => '',
    //                     'max_width' => '',
    //                     'max_height' => '',
    //                     'max_size' => '',
    //                     'mime_types' => '',
    //                     'preview_size' => 'medium',
    //                     'parent_repeater' => 'field_658661d04c689',
    //                 ),
    //                 array(
    //                     'key' => 'field_658661fe4c68b',
    //                     'label' => 'Participants Name',
    //                     'name' => 'participants_name',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658661d04c689',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586620b4c68c',
    //                     'label' => 'Participants Designation',
    //                     'name' => 'participants_designation',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658661d04c689',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586621a4c68d',
    //                     'label' => 'Partifipants Location',
    //                     'name' => 'partifipants_location',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658661d04c689',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586624286734',
    //                     'label' => 'Partifipants Info',
    //                     'name' => 'partifipants_info',
    //                     'aria-label' => '',
    //                     'type' => 'textarea',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'rows' => 4,
    //                     'placeholder' => '',
    //                     'new_lines' => '',
    //                     'parent_repeater' => 'field_658661d04c689',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586626386735',
    //                     'label' => 'Participants Social',
    //                     'name' => 'partifipants_social',
    //                     'aria-label' => '',
    //                     'type' => 'repeater',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'layout' => 'table',
    //                     'min' => 0,
    //                     'max' => 0,
    //                     'collapsed' => '',
    //                     'button_label' => 'Add Social Participants',
    //                     'rows_per_page' => 20,
    //                     'parent_repeater' => 'field_658661d04c689',
    //                     'sub_fields' => array(
    //                         array(
    //                             'key' => 'field_658676969044c',
    //                             'label' => 'Social Icon HTML',
    //                             'name' => 'social_icon_html',
    //                             'aria-label' => '',
    //                             'type' => 'text',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'default_value' => '',
    //                             'maxlength' => '',
    //                             'placeholder' => '',
    //                             'prepend' => '',
    //                             'append' => '',
    //                             'parent_repeater' => 'field_6586626386735',
    //                         ),
    //                         array(
    //                             'key' => 'field_65867aba2dcb3',
    //                             'label' => 'Social URL',
    //                             'name' => 'social_url',
    //                             'aria-label' => '',
    //                             'type' => 'text',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'default_value' => '',
    //                             'maxlength' => '',
    //                             'placeholder' => '',
    //                             'prepend' => '',
    //                             'append' => '',
    //                             'parent_repeater' => 'field_6586626386735',
    //                         ),
    //                     ),
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_658663ed86739',
    //             'label' => 'Additional Attributes',
    //             'name' => 'additional_attributes',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'block',
    //             'pagination' => 0,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Additional Attributes',
    //             'rows_per_page' => 20,
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_658664018673a',
    //                     'label' => 'Start Date',
    //                     'name' => 'start_date',
    //                     'aria-label' => '',
    //                     'type' => 'date_picker',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'display_format' => 'd/m/Y',
    //                     'return_format' => 'd/m/Y',
    //                     'first_day' => 1,
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_6587b9a41f632',
    //                     'label' => 'Start Time',
    //                     'name' => 'start_time',
    //                     'aria-label' => '',
    //                     'type' => 'time_picker',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'display_format' => 'g:i a',
    //                     'return_format' => 'g:i a',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586641b8673b',
    //                     'label' => 'End Date',
    //                     'name' => 'end_date',
    //                     'aria-label' => '',
    //                     'type' => 'date_picker',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'display_format' => 'd/m/Y',
    //                     'return_format' => 'd/m/Y',
    //                     'first_day' => 1,
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_6587b9c69157c',
    //                     'label' => 'End Time',
    //                     'name' => 'end_time',
    //                     'aria-label' => '',
    //                     'type' => 'time_picker',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'display_format' => 'g:i a',
    //                     'return_format' => 'g:i a',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_658664278673c',
    //                     'label' => 'Skill Level',
    //                     'name' => 'skill_level',
    //                     'aria-label' => '',
    //                     'type' => 'radio',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'choices' => array(
    //                         '' => 'None',
    //                         'beginner' => __( 'Beginner', 'histudy' ),
    //                         'intermediate' => __( 'Intermediate', 'histudy' ),
    //                         'advance' => __( 'Advance', 'histudy' ),
    //                     ),
    //                     'default_value' => '',
    //                     'return_format' => 'value',
    //                     'allow_null' => 0,
    //                     'other_choice' => 0,
    //                     'layout' => 'vertical',
    //                     'save_other_choice' => 0,
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_658664f68673d',
    //                     'label' => 'Location',
    //                     'name' => 'location',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_658664fc8673e',
    //                     'label' => 'Certificate',
    //                     'name' => 'certificate',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_6586650a8673f',
    //                     'label' => 'Language',
    //                     'name' => 'language',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_65866db640841',
    //                     'label' => 'Map Iframe',
    //                     'name' => 'map_iframe',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_65866e0640843',
    //                     'label' => 'Button Label',
    //                     'name' => 'button_label',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => 'Buy Now',
    //                     'min' => '',
    //                     'max' => '',
    //                     'placeholder' => '',
    //                     'step' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_65866e2640844',
    //                     'label' => 'Full Address',
    //                     'name' => 'full_address',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_65866e3640845',
    //                     'label' => 'PHONE',
    //                     'name' => 'phone',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'maxlength' => '',
    //                     'placeholder' => '',
    //                     'prepend' => '',
    //                     'append' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //                 array(
    //                     'key' => 'field_6587b86c32fdb',
    //                     'label' => 'External Link',
    //                     'name' => 'external_link',
    //                     'aria-label' => '',
    //                     'type' => 'text',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '20',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'placeholder' => '',
    //                     'parent_repeater' => 'field_658663ed86739',
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_65866b3240830',
    //             'label' => 'Schedule',
    //             'name' => 'schedule',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'table',
    //             'pagination' => 0,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Schedule',
    //             'rows_per_page' => 20,
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_65866b4540831',
    //                     'label' => 'Insert Event Day',
    //                     'name' => 'insert_event_day',
    //                     'aria-label' => '',
    //                     'type' => 'repeater',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'layout' => 'block',
    //                     'min' => 0,
    //                     'max' => 0,
    //                     'collapsed' => '',
    //                     'button_label' => 'Add Event Day',
    //                     'rows_per_page' => 20,
    //                     'parent_repeater' => 'field_65866b3240830',
    //                     'sub_fields' => array(
    //                         array(
    //                             'key' => 'field_65866b7d40832',
    //                             'label' => 'Event Date',
    //                             'name' => 'event_date',
    //                             'aria-label' => '',
    //                             'type' => 'date_picker',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '20',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'display_format' => 'd/m/Y',
    //                             'return_format' => 'd/m/Y',
    //                             'first_day' => 1,
    //                             'parent_repeater' => 'field_65866b4540831',
    //                         ),
    //                         array(
    //                             'key' => 'field_65866b9340834',
    //                             'label' => 'Event Time',
    //                             'name' => 'event_time',
    //                             'aria-label' => '',
    //                             'type' => 'time_picker',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '20',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'display_format' => 'g:i a',
    //                             'return_format' => 'g:i a',
    //                             'parent_repeater' => 'field_65866b4540831',
    //                         ),
    //                         array(
    //                             'key' => 'field_65866bd340835',
    //                             'label' => 'Event Name',
    //                             'name' => 'event_name',
    //                             'aria-label' => '',
    //                             'type' => 'text',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '20',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'default_value' => '',
    //                             'maxlength' => '',
    //                             'placeholder' => '',
    //                             'prepend' => '',
    //                             'append' => '',
    //                             'parent_repeater' => 'field_65866b4540831',
    //                         ),
    //                         array(
    //                             'key' => 'field_65866bf640837',
    //                             'label' => 'Event Speakers',
    //                             'name' => 'event_speakers',
    //                             'aria-label' => '',
    //                             'type' => 'repeater',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '40',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'layout' => 'table',
    //                             'min' => 0,
    //                             'max' => 0,
    //                             'collapsed' => '',
    //                             'button_label' => 'Add Speakers',
    //                             'rows_per_page' => 20,
    //                             'parent_repeater' => 'field_65866b4540831',
    //                             'sub_fields' => array(
    //                                 array(
    //                                     'key' => 'field_65866c0440838',
    //                                     'label' => 'Speaker Image',
    //                                     'name' => 'speaker_image',
    //                                     'aria-label' => '',
    //                                     'type' => 'image',
    //                                     'instructions' => '',
    //                                     'required' => 0,
    //                                     'conditional_logic' => 0,
    //                                     'wrapper' => array(
    //                                         'width' => '',
    //                                         'class' => '',
    //                                         'id' => '',
    //                                     ),
    //                                     'return_format' => 'array',
    //                                     'library' => 'all',
    //                                     'min_width' => '',
    //                                     'min_height' => '',
    //                                     'min_size' => '',
    //                                     'max_width' => '',
    //                                     'max_height' => '',
    //                                     'max_size' => '',
    //                                     'mime_types' => '',
    //                                     'preview_size' => 'medium',
    //                                     'parent_repeater' => 'field_65866bf640837',
    //                                 ),
    //                                 array(
    //                                     'key' => 'field_65866c1e40839',
    //                                     'label' => 'Speaker Name',
    //                                     'name' => 'speaker_name',
    //                                     'aria-label' => '',
    //                                     'type' => 'text',
    //                                     'instructions' => '',
    //                                     'required' => 0,
    //                                     'conditional_logic' => 0,
    //                                     'wrapper' => array(
    //                                         'width' => '',
    //                                         'class' => '',
    //                                         'id' => '',
    //                                     ),
    //                                     'default_value' => '',
    //                                     'maxlength' => '',
    //                                     'placeholder' => '',
    //                                     'prepend' => '',
    //                                     'append' => '',
    //                                     'parent_repeater' => 'field_65866bf640837',
    //                                 ),
    //                                 array(
    //                                     'key' => 'field_65866c394083a',
    //                                     'label' => 'Speaker Designation',
    //                                     'name' => 'speaker_designation',
    //                                     'aria-label' => '',
    //                                     'type' => 'text',
    //                                     'instructions' => '',
    //                                     'required' => 0,
    //                                     'conditional_logic' => 0,
    //                                     'wrapper' => array(
    //                                         'width' => '',
    //                                         'class' => '',
    //                                         'id' => '',
    //                                     ),
    //                                     'default_value' => '',
    //                                     'maxlength' => '',
    //                                     'placeholder' => '',
    //                                     'prepend' => '',
    //                                     'append' => '',
    //                                     'parent_repeater' => 'field_65866bf640837',
    //                                 ),
    //                             ),
    //                         ),
    //                         array(
    //                             'key' => 'field_65866be840836',
    //                             'label' => 'Event Content',
    //                             'name' => 'event_content',
    //                             'aria-label' => '',
    //                             'type' => 'textarea',
    //                             'instructions' => '',
    //                             'required' => 0,
    //                             'conditional_logic' => 0,
    //                             'wrapper' => array(
    //                                 'width' => '',
    //                                 'class' => '',
    //                                 'id' => '',
    //                             ),
    //                             'default_value' => '',
    //                             'tabs' => 'all',
    //                             'toolbar' => 'full',
    //                             'media_upload' => 1,
    //                             'delay' => 0,
    //                             'parent_repeater' => 'field_65866b4540831',
    //                         ),
    //                     ),
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_65866c954083c',
    //             'label' => 'Event Sponsors',
    //             'name' => 'event_sponsors',
    //             'aria-label' => '',
    //             'type' => 'repeater',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'layout' => 'table',
    //             'pagination' => 1,
    //             'rows_per_page' => 20,
    //             'min' => 0,
    //             'max' => 0,
    //             'collapsed' => '',
    //             'button_label' => 'Add Sponsor',
    //             'sub_fields' => array(
    //                 array(
    //                     'key' => 'field_65866cab4083d',
    //                     'label' => 'Sponsor Logo',
    //                     'name' => 'sponsor_logo',
    //                     'aria-label' => '',
    //                     'type' => 'image',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'return_format' => 'array',
    //                     'library' => 'all',
    //                     'min_width' => '',
    //                     'min_height' => '',
    //                     'min_size' => '',
    //                     'max_width' => '',
    //                     'max_height' => '',
    //                     'max_size' => '',
    //                     'mime_types' => '',
    //                     'preview_size' => 'medium',
    //                     'parent_repeater' => 'field_65866c954083c',
    //                 ),
    //                 array(
    //                     'key' => 'field_65866cb74083e',
    //                     'label' => 'Sponsor URL',
    //                     'name' => 'sponsor_url',
    //                     'aria-label' => '',
    //                     'type' => 'url',
    //                     'instructions' => '',
    //                     'required' => 0,
    //                     'conditional_logic' => 0,
    //                     'wrapper' => array(
    //                         'width' => '',
    //                         'class' => '',
    //                         'id' => '',
    //                     ),
    //                     'default_value' => '',
    //                     'placeholder' => '',
    //                     'parent_repeater' => 'field_65866c954083c',
    //                 ),
    //             ),
    //         ),
    //         array(
    //             'key' => 'field_65866e4240846',
    //             'label' => 'EMAIL',
    //             'name' => 'email',
    //             'aria-label' => '',
    //             'type' => 'email',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '33',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'default_value' => '',
    //             'placeholder' => '',
    //             'prepend' => '',
    //             'append' => '',
    //         ),
    //         array(
    //             'key' => 'field_65866e4f40847',
    //             'label' => 'FAX',
    //             'name' => 'fax',
    //             'aria-label' => '',
    //             'type' => 'text',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '33',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'default_value' => '',
    //             'maxlength' => '',
    //             'placeholder' => '',
    //             'prepend' => '',
    //             'append' => '',
    //         ),
    //         array(
    //             'key' => 'field_6587b71f8f6ba',
    //             'label' => 'Event Video',
    //             'name' => 'event_video',
    //             'aria-label' => '',
    //             'type' => 'url',
    //             'instructions' => '',
    //             'required' => 0,
    //             'conditional_logic' => 0,
    //             'wrapper' => array(
    //                 'width' => '33',
    //                 'class' => '',
    //                 'id' => '',
    //             ),
    //             'default_value' => '',
    //             'placeholder' => '',
    //         ),
    //     ),
    //     'location' => array(
    //         array(
    //             array(
    //                 'param' => 'post_type',
    //                 'operator' => '==',
    //                 'value' => 'course_event',
    //             ),
    //         ),
    //     ),
    //     'menu_order' => 0,
    //     'position' => 'normal',
    //     'style' => 'default',
    //     'label_placement' => 'top',
    //     'instruction_placement' => 'label',
    //     'hide_on_screen' => '',
    //     'active' => true,
    //     'description' => '',
    //     'show_in_rest' => 0,
    // ) );

        acf_add_local_field_group( array(
        'key' => 'group_65fc0453d3459',
        'title' => 'Tutor Course Meta',
        'fields' => array(
            array(
                'key' => 'rbt_exclude_course_layout',
                'label' => 'Exclude Layout',
                'name' => 'exclude_layout',
                'aria-label' => '',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'layout-1' => 'Layout 1',
                    'layout-2' => 'Layout 2',
                    'layout-3' => 'Layout 3',
                    'layout-4' => 'Layout 4',
                    'layout-5' => 'Layout 5',
                    'layout-6' => 'Layout 6',
                ),
                'default_value' => array(),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'multiple' => 1,
                'ui' => 1,
                'ajax' => 0,
                'placeholder' => '',
            ),
            array(
                'key' => 'field_65fc0453d9fc8',
                'label' => 'Thumbnail Image',
                'name' => 'thumbnail_image',
                'aria-label' => '',
                'type' => 'image',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'url',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
                'preview_size' => 'medium',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'courses',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ) );
    acf_add_local_field_group( array(
        'key' => 'group_659bd96d09e4b',
        'title' => 'Course Categories Attributes',
        'fields' => array(
            array(
                'key' => 'field_659bd96d295ed',
                'label' => 'Category Featured Image',
                'name' => 'category_featured_image',
                'aria-label' => '',
                'type' => 'image',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'url',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
                'preview_size' => 'medium',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'course-category',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ) );
    acf_add_local_field_group( array(
        'key' => 'group_655446923e40d',
        'title' => 'Course Additional Options',
        'fields' => array(
            array(
                'key' => 'field_65544692c8329',
                'label' => 'Language',
                'name' => 'rbt_course_language',
                'aria-label' => '',
                'type' => 'text',
                'instructions' => 'Comma for separate ( like English, Spanish )',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'English',
                'maxlength' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),
            array(
                'key' => 'field_66668bd97650b',
                'label' => 'Featured Course',
                'name' => 'featured_course',
                'aria-label' => '',
                'type' => 'true_false',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => 'Is this course featured?',
                'default_value' => 0,
                'ui' => 0,
                'ui_on_text' => '',
                'ui_off_text' => '',
            ),
        ),
        'location' => array(
            // Target 'courses' post type
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'courses',
                ),
            ),
            // Target 'lp_course' post type
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'lp_course',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ) );
    acf_add_local_field_group(array(
        'key' => 'group_5e4b96c080917',
        'title' => __( 'User Social Media', 'histudy' ),
        'fields' => array(
            array(
                'key' => 'user_designation_key',
                'label' => __( 'Designation', 'histudy' ),
                'name' => 'user_designation',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => __( 'Developer', 'histudy' ),
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_5e4b96f6dc7f8',
                'label' => __( 'Add Social Icons', 'histudy' ),
                'name' => 'rainbow_add_social_icons',
                'type' => 'repeater',
                'instructions' => __( 'Choose your icon markup here: https://fontawesome.com/icons', 'histudy' ),
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'collapsed' => 'field_5e4bbd75dc7fa',
                'min' => 0,
                'max' => 0,
                'layout' => 'table',
                'button_label' => __( 'Add New Network', 'histudy' ),
                'sub_fields' => array(
                    array(
                        'key' => 'field_5e4bbcaddc7f9',
                        'label' => __( 'Enter Social Icon Markup', 'histudy' ),
                        'name' => 'rainbow_enter_social_icon_markup',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 1,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '<i class="fab fa-facebook-f"></i>',
                        'prepend' => '',
                        'append' => '',
                        'maxlength' => '',
                    ),
                    array(
                        'key' => 'field_5e4bbd75dc7fa',
                        'label' => __( 'Enter Social Icon Link', 'histudy' ),
                        'name' => 'rainbow_enter_social_icon_link',
                        'type' => 'url',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '',
                    ),
                ),
            ),
            array(
                'key' => 'field_6607a301a4eca',
                'label' => 'Featured Author',
                'name' => 'featured_author',
                'aria-label' => '',
                'type' => 'true_false',
                'instructions' => 'Select if author is bestsellar',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 0,
                'ui_on_text' => '',
                'ui_off_text' => '',
                'ui' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'user_form',
                    'operator' => '==',
                    'value' => 'all',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'seamless',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

endif;

add_action( 'acf/include_fields', function() {
    if ( ! function_exists( 'acf_add_local_field_group' ) ) {
        return;
    }

    acf_add_local_field_group( array(
    'key' => 'group_64501ec753b7d',
    'title' => 'Instructor',
    'fields' => array(
        array(
            'key' => 'field_64501ec782bac',
            'label' => __( 'Phone', 'histudy' ),
            'name' => 'phone',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
        ),
        array(
            'key' => 'field_64501f8982bad',
            'label' => __( 'Location', 'histudy' ),
            'name' => 'location',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
        ),
    ),
    'location' => array(
        array(
            array(
                'param' => 'user_role',
                'operator' => '==',
                'value' => 'tutor_instructor',
            ),
        ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
    'show_in_rest' => 0,
) );
} );


add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_66f5112089e05',
	'title' => 'Course Single Extra Option Fields',
	'fields' => array(
		array(
			'key' => 'field_66f511219535b',
			'label' => 'Course Single Video',
			'name' => 'course_single_video',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
        array(
            'key' => 'field_money_back_guarntee',
            'label' => 'Money Back Guarantee',
            'name' => 'money_back_text',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => esc_html__('30-Day Money-Back Guarantee','histudy'),
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
        ),
        array(
			'key' => 'field_66f5492024935',
			'label' => 'Offer Price Date',
			'name' => 'offer_price_date_',
			'aria-label' => '',
			'type' => 'date_time_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'Y-m-d H:i:s',
			'return_format' => 'd/m/Y g:i a',
			'first_day' => 1,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'lp_course',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );





add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_66cc66265cbd4',
	'title' => 'Course LIst Image',
	'fields' => array(
		array(
			'key' => 'field_66cc66272fca7',
			'label' => '',
			'name' => 'learnpress_course_list_image',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'url',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'preview_size' => 'medium',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'lp_course',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );

add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_66cc66265cbd4_lp_banner_unique_img',
	'title' => 'Course Single Banenr Image',
	'fields' => array(
		array(
			'key' => 'field_66cc66272fca7_lp_banner_img',
			'label' => '',
			'name' => 'learnpress_course_baner_image',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'url',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'preview_size' => 'medium',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'lp_course',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );



