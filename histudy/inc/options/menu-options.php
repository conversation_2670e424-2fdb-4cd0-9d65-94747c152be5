<?php
if( function_exists('acf_add_local_field_group') ):

    acf_add_local_field_group(array(
        'key' => 'group_5c3de7e54eb56',
        'title' => __( 'Menu Option', 'histudy' ),
        'fields' => array(

            array(
                'key' => 'field_6431239009afb',
                'label' => __( 'Badge', 'histudy' ),
                'name' => 'badge',
                'aria-label' => '',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'maxlength' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),

            array(
                'key' => 'field_5c3de88e37696',
                'label' => __( 'Enable Mega Menu', 'histudy' ),
                'name' => 'rainbow_enable_mega_menu',
                'type' => 'true_false',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => '',
                'ui_off_text' => '',
            ),
            array(
                'key' => 'field_5c3de8217d5eb',
                'label' => __( 'Select Mega Menu', 'histudy' ),
                'name' => 'rainbow_select_mega_menu',
                'type' => 'post_object',
                'instructions' => '<a href="edit.php?post_type=rainbow_megamenu">Create / Edit Mega Menu</a>',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c3de88e37696',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'post_type' => array(
                    0 => 'rainbow_megamenu',
                ),
                'taxonomy' => '',
                'allow_null' => 0,
                'multiple' => 0,
                'return_format' => 'id',
                'ui' => 1,
            ),
            array(
                'key' => 'field_64cd45e1adfb2',
                'label' => __( 'Select Width', 'histudy' ),
                'name' => 'rainbow_select_mega_menu_width',
                'aria-label' => '',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c3de88e37696',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    '2' => __( '2 Column', 'histudy' ),
                    '3' => __( '3 Column', 'histudy' ),
                    '4' => __( '4 Column', 'histudy' ),
                    'full' => __( 'Full Width', 'histudy' ),
                    'custom' => __( 'Custom Width (Work form elementor)', 'histudy' ),
                ),
                'default_value' => 'full',
                'return_format' => 'value',
                'multiple' => 0,
                'allow_null' => 0,
                'ui' => 0,
                'ajax' => 0,
                'placeholder' => '',
            ),
            array(
                'key' => 'field_64cd465aadfb3',
                'label' => __( 'Menu Position', 'histudy' ),
                'name' => 'rainbow_select_mega_menu_position',
                'aria-label' => '',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c3de88e37696',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'select' => __( 'Select', 'histudy' ),
                    'left' => __( 'Left', 'histudy' ),
                    'right' => __( 'Right', 'histudy' ),
                ),
                'default_value' => 'select',
                'return_format' => 'value',
                'multiple' => 0,
                'allow_null' => 0,
                'ui' => 0,
                'ajax' => 0,
                'placeholder' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'nav_menu_item',
                    'operator' => '==',
                    'value' => 'all',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => 1,
        'description' => '',
    ));

endif;