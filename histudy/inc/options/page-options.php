<?php
if (function_exists('acf_add_local_field_group')):
    acf_add_local_field_group(array(
        'key' => 'group_5bf3bc1b4e26c_test',
        'title' => __( 'Page Options', 'histudy' ),
        'fields' => array(
            array(
                'key' => 'field_5bf3c134a081e',
                'label' => __( 'Header', 'histudy' ),
                'name' => '',
                'type' => 'tab',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'placement' => 'left',
                'endpoint' => 0,
            ),

            array(
                'key' => 'field_5c387546a3e4c',
                'label' => __( 'Show Header', 'histudy' ),
                'name' => 'rainbow_show_header',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
            ),
            
            array(
                'key' => 'field_5c38759fa3e4d',
                'label' => 'Header Type',
                'name' => 'rainbow_header_type',
                'type' => 'radio',
                'instructions' => 'Select header type, if the default is chosen the existing design will work, or choose the custom option to get headers from header post type.',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'default' => 'Theme Option',
                    'custom' => 'Custom',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => 'default',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
            ),
            array(
                'key' => 'histudy_header_template_custom',
                'label' => 'Select Header Template',
                'name' => 'rainbow_select_header_style_custom',
                'type' => 'post_object',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c38759fa3e4d',
                            'operator' => '==',
                            'value' => 'custom',
                        ),
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'post_type' => array(
                    0 => 'rbt_header_builder',
                ),
                'taxonomy' => '',
                'allow_null' => 0,
                'multiple' => 0,
                'return_format' => 'id',
                'ui' => 1,
            ),
            array(
                'key' => 'field_5c3875f7a3e4e',
                'label' => __( 'Select Header Template', 'histudy' ),
                'name' => 'rainbow_select_header_style',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    0 => __( 'Default', 'histudy' ),
                    1 => __( 'Header Layout 1', 'histudy' ),
                    2 => __( 'Header Layout 2', 'histudy' ),
                    3 => __( 'Header Layout 3', 'histudy' ),
                    4 => __( 'Header Layout 4', 'histudy' ),
                    5 => __( 'Header Layout 5', 'histudy' ),
                    6 => __( 'Header Layout 6', 'histudy' ),
                    7 => __( 'Header Layout 7', 'histudy' ),
                    8 => __( 'Header Layout 8', 'histudy' ),
                    9 => __( 'Header Layout 9', 'histudy' ),
                    10 => __( 'Header Layout 10', 'histudy' ),
                    11 => __( 'Header Layout 11', 'histudy' ),
                    12 => __( 'Header Layout 12', 'histudy' ),
                ),
                'default_value' => array(),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'ajax' => 0,
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c38759fa3e4d',
                            'operator' => '==',
                            'value' => 'default',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_5f3c1ed52db7e_nav_menu',
                'label' => __('Select Menu', 'histudy'),
                'name' => 'rainbow_select_nav_menu',
                'type' => 'select',
                'instructions' => __('By default works primary location menu.', 'histudy'),
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => rainbow_get_nav_menus(),
                'default_value' => false,
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'ajax' => 0,
                'return_format' => 'value',
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c38759fa3e4d',
                            'operator' => '==',
                            'value' => 'default',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_menu_type',
                'label' => __('Menu Type', 'histudy'),
                'name' => 'histudy_menu_type',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'multipage' => __( 'Multi Page', 'histudy' ),
                    'onepage' => __( 'One Page', 'histudy' ),
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c38759fa3e4d',
                            'operator' => '==',
                            'value' => 'default',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_5c52c42f6fdfc',
                'label' => __( 'Header Sticky', 'histudy' ),
                'name' => 'rainbow_header_sticky',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_5c52c47d6fdfd',
                'label' => __('Header Transparent', 'histudy'),
                'name' => 'rainbow_header_transparent',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_5c52c42f6fdfbob',
                'label' => __( 'Menu Offset Enabled', 'histudy' ),
                'name' => 'rainbow_header_offset_on',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => 'no',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_5bf3f6fc0509hsr',
                'label' => __( 'Header One Page Menu offset', 'histudy' ),
                'name' => 'rainbow_header_offset',
                'type' => 'range',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'min' => 0,
                'max' => 1000,
                'step' => '',
                'prepend' => '',
                'append' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbob',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),


            array(
                'key' => 'field_5c52c42f6fdfbb',
                'label' => __( 'Header Button', 'histudy' ),
                'name' => 'rainbow_header_button',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => 'no',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c387546a3e4c',
                            'operator' => '==',
                            'value' => 'yes',
                        )
                    ),
                ),
            ),
            array(
                'key' => 'field_button_target',
                'label' => __( 'Button Target', 'histudy' ),
                'name' => 'rainbow_button_target',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    '_blank' => __('Blank', 'histudy'),
                    '_self' => __('Self', 'histudy'),
                    '_parent' => __('Parent', 'histudy'),
                    '_top' => __('Top', 'histudy'),
                ),
                'default_value' => array(),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'ajax' => 0,
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c3875f7a3e4e',
                            'operator' => '==',
                            'value' => [1, 3],
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_5c3875f7a3e4bb',
                'label' => __( 'Button Type', 'histudy' ),
                'name' => 'rainbow_button_type',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'rbt-btn btn-primary radius-round btn-sm' => __('Primary Button', 'histudy'),
                    'rbt-btn bg-primary-opacity radius-round btn-sm' => __('Primary Opacity Button', 'histudy'),
                    'rbt-btn btn-border radius-round btn-sm' => __('Border Button', 'histudy'),
                    'rbt-btn rbt-marquee-btn marquee-auto btn-border-gradient radius-round btn-sm hover-transform-none' => __('Gradient Border Marquee', 'histudy'),
                    'rbt-btn btn-gradient radius-round btn-sm' => __('Primary Gradient Button', 'histudy'),
                    'rbt-moderbt-btn radius-round btn-sm' => __('Modern Button', 'histudy'),
                ),
                'default_value' => array(),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'ajax' => 0,
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_5bf3f6fc0509cbu',
                'label' => __( 'Button Url', 'histudy' ),
                'name' => 'rainbow_header_button_url',
                'type' => 'text',
                'instructions' => __( 'If this field is empty, then default Header button url will be showed', 'histudy' ),
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),

                ),
            ),

            array(
                'key' => 'field_5bf3f6fc0509cbt',
                'label' => __( 'Button Text', 'histudy' ),
                'name' => 'rainbow_header_button_txt',
                'type' => 'text',
                'instructions' => __( 'If this field is empty, then default Header button Text will be showed', 'histudy' ),
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),

                ),
            ),
            array(
                'key' => 'field_5bf3f6fc0509cbt1a',
                'label' => 'Address',
                'name' => 'rainbow_header_address',
                'type' => 'text',
                'instructions' => __( 'If this field is empty, then default Header address will be showed', 'histudy' ),
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => __( '20th New York, ND 8545, USA', 'histudy' ),
                'placeholder' => __( 'Please enter your address', 'histudy' ),
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_5bf3f6fc0509cbt1a12',
                'label' => __( 'Address URL', 'histudy' ),
                'name' => 'rainbow_header_address_url',
                'type' => 'text',
                'instructions' => __( 'If this field is empty, then default Header address URL will be showed', 'histudy' ),
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '#',
                'placeholder' => __( 'Please enter your address URL', 'histudy' ),
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5c52c42f6fdfbb',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c3875f7a3e4e',
                            'operator' => '==',
                            'value' => ['6'],
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_5bf3c14ba081f',
                'label' => __( 'Page Banner Area', 'histudy' ),
                'name' => '',
                'type' => 'tab',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'placement' => 'top',
                'endpoint' => 0,
            ),

            array(
                'key' => 'field_5bf3f6b20509a',
                'label' => __( 'Page Banner Area', 'histudy' ),
                'name' => 'rainbow_title_wrapper_show',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
            ),

            array(
                'key' => 'field_5c3875f7a3e4bl',
                'label' => __( 'Select Banner Template', 'histudy' ),
                'name' => 'rainbow_select_banner_style',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    0 => __( 'Default', 'histudy' ),
                    1 => __( 'Banner Layout 1', 'histudy' ),
                    2 => __( 'Banner Layout 2', 'histudy' ),
                     
                ),
                'default_value' => array(),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'ajax' => 0,
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bf3f6b20509a',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),
            array(
            'key' => 'field_5bf3f6fc0509c',
            'label' => __( 'Custom Title', 'histudy' ),
            'name' => 'rainbow_custom_title',
            'type' => 'text',
            'instructions' => __( 'If this field is empty, then default page/post title will be showed', 'histudy' ),
            'required' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
            'maxlength' => '',
            'conditional_logic' => array(
                array(
                    array(
                        'field' => 'field_5bf3f6b20509a',
                        'operator' => '==',
                         'value' => 'yes',
                    ),
                ),
               
            ),
        ),


        array(
            'key' => 'field_page_banner_sub_title_text',
            'label' => __( 'Custom Sub Title', 'histudy' ),
            'name' => 'rainbow_custom_sub_title',
            'type' => 'text',
            'required' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
            'maxlength' => '',
            'conditional_logic' => array(
                array(
                    array(
                        'field' => 'field_5bf3f6b20509a',
                        'operator' => '==',
                         'value' => 'yes',
                    ),
                ),
            ),
        ),
            array(
                'key' => 'field_5bf655966ed4b',
                'label' => __( 'Breadcrumbs Enable', 'histudy' ),
                'name' => 'rainbow_breadcrumbs_enable',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'save_other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                
            ),

            // Footer
            array(
                'key' => 'field_5bf3c169a0820',
                'label' => __( 'Footer', 'histudy' ),
                'name' => '',
                'type' => 'tab',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'placement' => 'top',
                'endpoint' => 0,
            ),

            array(
                'key' => 'field_5bfe771692a07',
                'label' => __( 'Show Footer', 'histudy' ),
                'name' => 'rainbow_show_footer',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'save_other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
            ),
            array(
                'key' => 'field_5bfe77e7cdd9b',
                'label' => 'Footer Type',
                'name' => 'histudy_footer_type',
                'type' => 'radio',
                'instructions' => 'Select footer type, if the default is chosen the existing design will work, or choose the custom option to get headers from footer post type.',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'default' => 'Default',
                    'custom' => 'Custom',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'save_other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
            ),
            array(
                'key' => 'field_5bfe785dcdd9c',
                'label' => 'Select Footer Style',
                'name' => 'histudy_select_footer_style_custom',
                'type' => 'post_object',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5bfe77e7cdd9b',
                            'operator' => '==',
                            'value' => 'custom',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'post_type' => array(
                    0 => 'rbt_footer_builder',
                ),
                'taxonomy' => '',
                'allow_null' => 1,
                'multiple' => 0,
                'return_format' => 'id',
                'ui' => 1,
            ),
            array(
                'key' => 'field_5c3875f7a3e4eS',
                'label' => __( 'Select Footer Template', 'histudy' ),
                'name' => 'rainbow_select_footer_style',
                'type' => 'select',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    0 => __( 'Default', 'histudy' ),
                    1 => __( 'Footer Layout 1', 'histudy' ),
                    2 => __( 'Footer Layout 2', 'histudy' ),
                    3 => __( 'Footer Layout 3', 'histudy' ),
                    4 => __( 'Footer Layout 4', 'histudy' ),
                ),
                'default_value' => array(4),
                'allow_null' => 0,
                'multiple' => 0,
                'ui' => 0,
                'return_format' => 'value',
                'ajax' => 0,
                'placeholder' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5bfe77e7cdd9b',
                            'operator' => '==',
                            'value' => 'default',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_social_on_off',
                'label' => __( 'Footer About Social ON/Off', 'histudy' ),
                'name' => 'rainbow_show_footer_social',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'save_other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_get_contact_social_on_off',
                'label' => __( 'Footer Get Contact Social ON/Off', 'histudy' ),
                'name' => 'rainbow_show_footer_social_get_contact',
                'type' => 'radio',
                'instructions' => '',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'allow_null' => 1,
                'other_choice' => 0,
                'save_other_choice' => 0,
                'default_value' => '',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_page_get_contact_address_label',
                'label' => __( 'Footer Get Contact Address Label Title', 'histudy' ),
                'name' => 'rainbow_get_contact_address_label_title',
                'type' => 'text',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c3875f7a3e4eS',
                            'operator' => '==',
                            'value' => 1,
                        ),
                    ),
                ),
            ),

            array(
                'key' => 'field_page_get_contact_description_text',
                'label' => __( 'Footer Get Contact Address Label Text', 'histudy' ),
                'name' => 'rainbow_get_contact_desc_text',
                'type' => 'textarea',
                'required' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_5bfe771692a07',
                            'operator' => '==',
                            'value' => 'yes',
                        ),
                        array(
                            'field' => 'field_5c3875f7a3e4eS',
                            'operator' => '==',
                            'value' => 1,
                        ),
                    ),
                ),
            ),

        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => 1,
        'description' => '',
    ));
    acf_add_local_field_group( array(
        'key' => 'group_65cb075362915',
        'title' => __( 'Footer Widget Full Info', 'histudy' ),
        'fields' => array(
            array(
                'key' => 'field_65cb0753a2765',
                'label' => __( 'Logo', 'histudy' ),
                'name' => 'logo',
                'aria-label' => '',
                'type' => 'image',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'url',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
                'preview_size' => 'medium',
            ),
            array(
                'key' => 'field_65cb07b1a2766',
                'label' => __( 'is social enabled', 'histudy' ),
                'name' => 'is_social_enabled',
                'aria-label' => '',
                'type' => 'true_false',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 1,
                'ui_on_text' => '',
                'ui_off_text' => '',
                'ui' => 1,
            ),
            array(
                'key' => 'field_65cb07e8a2767',
                'label' => __( 'Copyright Text', 'histudy' ),
                'name' => 'copyright_text',
                'aria-label' => '',
                'type' => 'textarea',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'maxlength' => '',
                'rows' => '',
                'placeholder' => '',
                'new_lines' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'widget',
                    'operator' => '==',
                    'value' => 'footer_widget_full_info',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ) );
endif;
