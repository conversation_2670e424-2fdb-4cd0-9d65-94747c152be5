<?php
if (function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_5e8e345b7998e',
        'title' => __( 'Team Options', 'histudy' ),
        'fields' => array(
            array(
                'key' => 'field_5e8e346373580',
                'label' => __( 'Designation', 'histudy' ),
                'name' => 'team_designation',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => __( 'Developer', 'histudy' ),
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'team_social_icons_field_5e4b96f6dc7f8',
                'label' => __( 'Add Social Network', 'histudy' ),
                'name' => 'rainbow_team_social_icons',
                'type' => 'repeater',
                'instructions' => __( 'Choose your icon markup here: https://fontawesome.com/icons', 'histudy' ),
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'collapsed' => 'field_5e4bbd75dc7fa',
                'min' => 0,
                'max' => 0,
                'layout' => 'table',
                'button_label' => __( 'Add New Network', 'histudy' ),
                'sub_fields' => array(
                    array(
                        'key' => 'team_enter_social_icon_markup_field_5e4bbcaddc7f9',
                        'label' => __( 'Enter Social Icon Markup', 'histudy' ),
                        'name' => 'team_enter_social_icon_markup',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 1,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '<i class="fab fa-facebook-f"></i>',
                        'prepend' => '',
                        'append' => '',
                        'maxlength' => '',
                    ),
                    array(
                        'key' => 'team_enter_social_icon_link_field_5e4bbd75dc7fa',
                        'label' => __( 'Enter Social Icon Link', 'histudy' ),
                        'name' => 'team_enter_social_icon_link',
                        'type' => 'url',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '',
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'rainbow_team',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

endif;