<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package histudy
 */

class Rainbow_Tgm_Config
{
    public $rainbow_theme_fix = RAINBOW_THEME_FIX;
    public $path = "https://rainbowthemes.net/themes/histudy/demo/plugins/";

    public $bundled_path = "https://rainbowthemes.net/resource/bundled/";
    public function __construct()
    {
        add_action('tgmpa_register', array($this, 'rainbow_tgm_plugins'));

    }

    public function rainbow_tgm_plugins()
    {
        $plugins = array(
            array(
                'name' => esc_html__('Contact Form 7', 'histudy'),
                'slug' => 'contact-form-7',
                'required' => true,
            ),
            array(
                'name'     => esc_attr__('WooCommerce', 'histudy'),
                'slug'     => 'woocommerce',
                'required' => true,
               // 'force_activation' => true
            ),
            array(
                'name' => esc_html__('<PERSON><PERSON>', 'histudy'),
                'slug' => 'tutor',
                'required' => true,
            ),
            array(
                'name' => esc_html__('One Click Demo Import', 'histudy'),
                'slug' => 'one-click-demo-import',
                'required' => true,
            ),
            array(
                'name'      => esc_html__('Advanced Custom Fields Pro', 'histudy'),
                'slug'      => 'advanced-custom-fields-pro',
                'source' => $this->bundled_path . 'advanced-custom-fields-pro.zip',
                'required'  => true,
            ),

            // Repository
            array(
                'name' => esc_html__('Redux Framework', 'histudy'),
                'slug' => 'redux-framework',
                'required' => true,
            ),
            array(
                'name' => esc_html__('Elementor Page Builder', 'histudy'),
                'slug' => 'elementor',
                'required' => true,
            ),
            array(
                'name'     => esc_html__('MailChimp for WordPress','histudy'),
                'slug'     => 'mailchimp-for-wp',
                'required' => true,
            ),
            array(
                'name' => esc_html__('Rainbow Elements', 'histudy'),
                'slug' => 'rainbow-elements',
                'source' => 'rainbow-elements-3.0.4.zip',
                'required' => true,
                'version' => '3.0.4'
            ),
        );

        $config = array(
            'id' => $this->rainbow_theme_fix,            // Unique ID for hashing notices for multiple instances of TGMPA.
            'default_path' => $this->path,              // Default absolute path to bundled plugins.
            'menu' => $this->rainbow_theme_fix . '-install-plugins', // Menu slug.
            'has_notices' => true,                    // Show admin notices or not.
            'dismissable' => true,                    // If false, a user cannot dismiss the nag message.
            'dismiss_msg' => '',                      // If 'dismissable' is false, this message will be output at top of nag.
            'is_automatic' => false,                    // Automatically activate plugins after installation or not.
            'message' => '',                      // Message to output right before the plugins table.
        );

        tgmpa($plugins, $config);
    }
}

new Rainbow_Tgm_Config;