<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

use <PERSON>ementor\Plugin;

class Rainbow_Scripts
{

    public $version;
    protected static $instance = null;
    public  $current_url_path;

    public function __construct()
    {
        add_action('wp_enqueue_scripts', array($this, 'rainbow_register_scripts'), 12);
        add_action('wp_enqueue_scripts', array($this, 'rainbow_enqueue_scripts'), 15);
        add_action('admin_enqueue_scripts', array($this, 'rainbow_admin_scripts'), 15);
        if( class_exists( 'LearnPress' ) ) {
            add_action('wp_enqueue_scripts', array($this, 'learnpress_css'), 10);
        }


        add_action('wp', array($this, 'get_current_url_path'));


    }

    public function get_current_url_path() {
        global $wp;
        $this->current_url_path = wp_parse_url(home_url(add_query_arg(array(), $wp->request)), PHP_URL_PATH);
        $this->current_url_path = is_string($this->current_url_path) ? $this->current_url_path : '';

    }

    public static function instance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }
    public function learnpress_css() {

        if( class_exists( 'LearnPress' ) ) {
            wp_register_style( 'histudy-learnpress', Rainbow_Helper::get_css('learnpress'), array('learnpress'), time(), false );
            wp_enqueue_style( 'histudy-learnpress' );
        }
    }

    // academy lms css
    public function academy_lms_css() {

        if (  function_exists( 'academy_start' )) {
            wp_register_style( 'academy-lms-css2', Rainbow_Helper::get_css('academy-lms'), array(''), time(), false );
            wp_enqueue_style( 'academy-lms-css2' );
        }
    }

    public function rainbow_admin_scripts($screen)
    {
        $allowed_page = array(
            'toplevel_page_histudy-dashboard',
            'histudy_page_histudy-dashboard_license'
        );
        if( in_array($screen, $allowed_page) ) {
            // do whatever you want
        }
        wp_enqueue_style('histudy-lic-style', Rainbow_Helper::get_admin_css('lic-style'), array(), RAINBOW_VERSION);

        wp_enqueue_media();
        wp_enqueue_script( 'jquery-ui-tabs' );
        wp_enqueue_style('magnafic-popup', Rainbow_Helper::get_admin_css('jquery.magnafic-popup.min'), null, '1.0.0');
        wp_enqueue_script('magnafic-js-popup', Rainbow_Helper::get_admin_js('jquery.magnafic-popup.min'), array('jquery'), '1.0.0', true);
        wp_enqueue_script( 'histudy-admin-main', Rainbow_Helper::get_admin_js('admin-main'), array( 'jquery', 'magnafic-js-popup' ), time(), true );
        wp_enqueue_style('histudy-wp-admin', Rainbow_Helper::get_admin_css('admin-style'), array(), RAINBOW_VERSION);
        $license = HiStudyEducationThemes::$licence_activated ? 'yes': 'no';
        wp_localize_script( 'histudy-admin-main', 'histudy_license', array(
            'license' => $license,
            'license_redirect_url' => admin_url('admin.php?page=histudy-dashboard')
        ) );
        /**
         * Enqueue script for redux
         */
        wp_enqueue_script( 'histudy-redux-customize', Rainbow_Helper::get_admin_js('redux-customize'), array( 'jquery' ), RAINBOW_VERSION, true );
    }
    public function rainbow_register_scripts()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();
        $show_more_text = isset($rainbow_options['rainbow_show_more_text']) ? sanitize_text_field( $rainbow_options['rainbow_show_more_text'] ): __( 'Show More', 'histudy' );
        $show_less_text = isset($rainbow_options['rainbow_show_less_text']) ? sanitize_text_field( $rainbow_options['rainbow_show_less_text'] ): __( 'Show Less', 'histudy' );


        if(is_rtl()) {
            wp_register_style('bootstrap-min', Rainbow_Helper::get_vendor_css('bootstrap.rtl.min'), array(), RAINBOW_VERSION);
        } else {
            wp_register_style('bootstrap-min', Rainbow_Helper::get_vendor_css('bootstrap.min'), array(), RAINBOW_VERSION);
        }

        wp_register_style('sal', Rainbow_Helper::get_plugins_css('sal'), array(), RAINBOW_VERSION);
        wp_register_style('feather', Rainbow_Helper::get_plugins_css('feather'), array(), RAINBOW_VERSION);
        wp_register_style('fontawesome-all', Rainbow_Helper::get_plugins_css('all.min'), array(), RAINBOW_VERSION);
        wp_register_style('euclid-circulara', Rainbow_Helper::get_plugins_css('euclid-circulara'), array(), RAINBOW_VERSION);
        wp_register_style('swiper2', Rainbow_Helper::get_plugins_css('swiper'), array(), RAINBOW_VERSION);
        wp_register_style('magnify', Rainbow_Helper::get_plugins_css('magnify'), array(), RAINBOW_VERSION);
        wp_register_style('odometer', Rainbow_Helper::get_plugins_css('odometer'), array(), RAINBOW_VERSION);
        wp_register_style('animation', Rainbow_Helper::get_plugins_css('animation'), array(), RAINBOW_VERSION);
        wp_register_style('backtotop', Rainbow_Helper::get_plugins_css('backtotop'), array(), RAINBOW_VERSION);

        wp_register_style('bootstrap-select', Rainbow_Helper::get_plugins_css('bootstrap-select.min'), array(), RAINBOW_VERSION);
        wp_register_style('jquery-ui', Rainbow_Helper::get_plugins_css('jquery-ui'), array(), RAINBOW_VERSION);
        wp_register_style('magnigy-popup-min', Rainbow_Helper::get_plugins_css('magnigy-popup.min'), array(), RAINBOW_VERSION);

        wp_register_style( 'academy-lms-css2', Rainbow_Helper::get_css('academylms'), array(), RAINBOW_VERSION );

        wp_register_style('main-style', Rainbow_Helper::get_css('style'), array(), time());
        wp_register_style('main-rtl-style', Rainbow_Helper::get_css('rtl'), array(), time() );

        wp_register_script('bootstrap-min', Rainbow_Helper::get_vendor_js('bootstrap.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('sal', Rainbow_Helper::get_vendor_js('sal'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('swiper', Rainbow_Helper::get_vendor_js('swiper'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('magnify-min', Rainbow_Helper::get_vendor_js('magnify-popup.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('jquery-appear', Rainbow_Helper::get_vendor_js('jquery-appear'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('wow', Rainbow_Helper::get_vendor_js('wow'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('odometer', Rainbow_Helper::get_vendor_js('odometer'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('jquery-style-switcher', Rainbow_Helper::get_vendor_js('jquery.style.switcher'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('js-cookie', Rainbow_Helper::get_vendor_js('js.cookie.min'), array('jquery'), RAINBOW_VERSION, true);

        if (isset($rainbow_options['rainbow_scroll_to_top_enable']) && $rainbow_options['rainbow_scroll_to_top_enable'] == 'yes' ) {
            if (!isset($_GET['course_ID']) && empty($_GET['course_ID'])) {
                if( strpos( $this->current_url_path, '/courses/') === false )  {
                    wp_register_script('backtotop', Rainbow_Helper::get_vendor_js('backtotop'), array('jquery'), RAINBOW_VERSION, true);
                 }
            }
        }

        global $wp;
        $current_url_path = wp_parse_url(home_url(add_query_arg(array(), $wp->request)), PHP_URL_PATH);
        $current_url_path = is_string($current_url_path) ? $current_url_path : '';

        wp_register_script('isotop', Rainbow_Helper::get_vendor_js('isotop'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('slick-min', Rainbow_Helper::get_vendor_js('slick.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('bootstrap-select.min', Rainbow_Helper::get_vendor_js('bootstrap-select.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('jquery-one-page-nav', Rainbow_Helper::get_vendor_js('jquery-one-page-nav'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('text-type', Rainbow_Helper::get_vendor_js('text-type'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('waypoint-min', Rainbow_Helper::get_vendor_js('waypoint.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('easypie', Rainbow_Helper::get_vendor_js('easypie'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('paralax-min', Rainbow_Helper::get_vendor_js('paralax.min'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('countdown', Rainbow_Helper::get_vendor_js('countdown'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('paralax-scroll', Rainbow_Helper::get_vendor_js('paralax-scroll'), array('jquery'), RAINBOW_VERSION, true);

        if (is_singular() && comments_open() && get_option('thread_comments')) {
            wp_enqueue_script('comment-reply');
        }

        wp_register_script('histudy-post-like', Rainbow_Helper::get_js( 'post-like'), array('jquery'), RAINBOW_VERSION, true);
        wp_register_script('histudy-login', Rainbow_Helper::get_js( 'login'), array('jquery'), RAINBOW_VERSION, true);


        wp_register_script('histudy-main', Rainbow_Helper::get_js('main'), array('jquery'), RAINBOW_VERSION, true);

        wp_localize_script('histudy-main', 'ajax_object', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'show_more_text' => $show_more_text,
            'show_less_text' =>  $show_less_text,
            'nothing_selected' => esc_html__('Nothing selected', 'histudy'),
            'select_all' => esc_html__('Select All', 'histudy'),
            'deselect_all' => esc_html__('Deselect All', 'histudy'),
        ));

        wp_register_script('histudy-has-elementor', Rainbow_Helper::get_js('has-elementor'), array('jquery'), RAINBOW_VERSION, true);


        ?>

        <?php
    }

    public function rainbow_enqueue_scripts()
    {
        $rainbow_options = Rainbow_Helper::rainbow_get_options();

        wp_enqueue_style('bootstrap-min');

        wp_enqueue_style('sal');
        wp_enqueue_style('swiper2');
        wp_enqueue_style('feather');
        wp_enqueue_style('fontawesome-all');
        wp_enqueue_style('euclid-circulara');

        wp_enqueue_style('magnify-min');
        wp_enqueue_style('jquery-appear');
        wp_enqueue_style('animation');
        wp_enqueue_style('bootstrap-select');
        wp_enqueue_style('jquery-ui-core');
        wp_enqueue_style('odometer');

        wp_enqueue_style('magnigy-popup-min');
        $this->rainbow_fonts_url();
        $this->rainbow_elementor_scripts();
        wp_enqueue_style( 'academy-lms-css2' );
        if ( !is_admin()    ) {
            wp_enqueue_style('main-style');
        }

        if( is_rtl() ) {
            wp_enqueue_style( 'main-rtl-style' );
        }

        wp_enqueue_script('bootstrap-min');
        wp_enqueue_script('sal');
        wp_enqueue_script('swiper');
        wp_enqueue_script('magnify-min');
        wp_enqueue_script('jquery-appear');
        wp_enqueue_script('wow');
        wp_enqueue_script('odometer');

        global $wp;
        $current_url_path = wp_parse_url(home_url(add_query_arg(array(), $wp->request)), PHP_URL_PATH);
        $current_url_path = is_string($current_url_path) ? $current_url_path : '';

        if (isset($rainbow_options['rainbow_scroll_to_top_enable']) && $rainbow_options['rainbow_scroll_to_top_enable'] == 'yes' ) {
            $query_params = $_GET;
            if (strpos($current_url_path, '/courses/') === false && !isset($query_params['course_ID']) && empty($query_params['course_ID'])) {
                if( strpos( $this->current_url_path, '/courses/') === false )  {
                    wp_enqueue_script('backtotop');
                }

           }
        }

        if (
            !isset($_GET['course_ID']) &&
            empty($_GET['course_ID']) &&
            strpos($current_url_path, '/dashboard/settings/') === false
        ) {
            wp_enqueue_script('jquery-ui');
        }

        if (strpos($current_url_path, '/qa-support/') === false) {
            // Dequeue jQuery UI scripts
            wp_dequeue_script('jquery-ui');
        }


        wp_enqueue_script('isotop');

        wp_enqueue_script('bootstrap-select.min');
        wp_enqueue_script('jquery-one-page-nav');
        wp_enqueue_script('text-type');
        wp_enqueue_script('waypoint-min');
        wp_enqueue_script('histudy-login');
        wp_enqueue_script('easypie');

        if (is_singular('courses') && strpos($current_url_path, 'zoom-lessons') !== false) {
            wp_enqueue_script('countdown');
        }

        wp_enqueue_script('paralax-scroll');
        wp_enqueue_script('imagesloaded');
        wp_enqueue_script('histudy-post-like');
        wp_enqueue_script('histudy-main');
        wp_enqueue_script('histudy-cart');
        wp_enqueue_script('jquery-style-switcher');
        wp_enqueue_script('histudy-has-elementor');

        wp_enqueue_script('js-cookie');
        $monetize_by  = '';
        if( function_exists('tutor')) {
            $tutor_options = get_option('tutor_option');
            $monetize_by = isset($tutor_options['monetize_by']) ? $tutor_options['monetize_by'] : '';
        }


        $this->rainbow_localized_scripts();
        $this->rainbow_localized_scripts2();
        wp_localize_script(
            'histudy-post-like',
            'histudy_ajax_object',
            array(
                'nonce'   => wp_create_nonce('histudy_like_post'),
                'monetize_tutor_lms' => $monetize_by,
                'ajax_url' => admin_url('admin-ajax.php')
            )
        );

        wp_localize_script('histudy-login', 'ajax_login_object', array(
            'nonce'   => wp_create_nonce('histudy_form_account'),
            'ajax_url' => admin_url('admin-ajax.php'),
            'template_path' => get_template_directory_uri()
        ));

        // Retrieve the gradient value
        $histudy_blog_page_bg_color = isset($rainbow_options['histudy_blog_page_bg_color']) ? $rainbow_options['histudy_blog_page_bg_color'] : '';
        $rainbow_course_archive_brd_image = isset($rainbow_options['rainbow_course_archive_brd_image']) ? $rainbow_options['rainbow_course_archive_brd_image'] : '';
        $histudy_blog_page_bg_color_top = isset($rainbow_options['histudy_blog_page_bg_color_top']) ? $rainbow_options['histudy_blog_page_bg_color_top'] : '';
        $histudy_event_page_bg_color = isset($rainbow_options['histudy_event_page_bg_color']) ? $rainbow_options['histudy_event_page_bg_color'] : '';
        $histudy_evnet_page_bg_color_top = isset($rainbow_options['histudy_evnet_page_bg_color_top']) ? $rainbow_options['histudy_evnet_page_bg_color_top'] : '';
        $rainbow_blog_details_brd_background = isset($rainbow_options['rainbow_blog_details_brd_background']) ? $rainbow_options['rainbow_blog_details_brd_background'] : '';
        $histudy_blog_details_gradient_bg_color = isset($rainbow_options['histudy_blog_details_gradient_bg_color']) ? $rainbow_options['histudy_blog_details_gradient_bg_color'] : '';

        $rainbow_blog_archive_brd_background = isset($rainbow_options['rainbow_blog_archive_brd_background']) ? $rainbow_options['rainbow_blog_archive_brd_background'] : '';
        $rainbow_course_details_brd_color = isset( $rainbow_options['rainbow_course_details_brd_image'] ) ? $rainbow_options['rainbow_course_details_brd_image'] : '';
        $rainbow_course_details_gradient_image = isset( $rainbow_options['rainbow_course_details_gradient_image'] ) ? $rainbow_options['rainbow_course_details_gradient_image'] : '';
        $rainbow_course_archive_brd_color_image = isset( $rainbow_options['rainbow_course_archive_brd_color_image'] ) ? $rainbow_options['rainbow_course_archive_brd_color_image'] : '';

        $rainbow_global_page_gradient_color = isset( $rainbow_options['rainbow_global_page_gradient_color'] ) ? $rainbow_options['rainbow_global_page_gradient_color'] : '';
        $rainbow_global_page_image_or_color = isset( $rainbow_options['rainbow_global_page_image_or_color'] ) ? $rainbow_options['rainbow_global_page_image_or_color'] : '';




        // Check if the value is not empty and contains valid gradient data
        $custom_style = '';

        // global pages

        if ( isset($rainbow_global_page_gradient_color['from'] ) && isset( $rainbow_global_page_gradient_color['to']) && !empty($rainbow_global_page_gradient_color)) {
            $custom_style .= sprintf(
                '.page .rbt-breadcrumb-default { background: linear-gradient(%s, %s) !important; }',
                esc_attr($rainbow_global_page_gradient_color['from']),
                esc_attr($rainbow_global_page_gradient_color['to'])
            );
        }

        if ( isset($rainbow_global_page_image_or_color['background-image']) && !empty($rainbow_global_page_image_or_color['background-image'])) {
            $custom_style .= sprintf('.page .rbt-breadcrumb-default {
                background:url(%s)!important;
                background-repeat: %s;
                background-size: %s;
                background-position: %s
            }', esc_attr( $rainbow_global_page_image_or_color['background-image']),esc_attr( $rainbow_global_page_image_or_color['background-repeat']),esc_attr( $rainbow_global_page_image_or_color['background-size']),esc_attr( $rainbow_global_page_image_or_color['background-position']) );
        }

        if ( isset($rainbow_global_page_image_or_color['background-color']) && !empty($rainbow_global_page_image_or_color['background-color'])) {
            $custom_style .= sprintf('.page .rbt-breadcrumb-default {
            background:%s!important;}', esc_attr( $rainbow_global_page_image_or_color['background-color']) );
        }

        // course archive

        if ( isset($rainbow_course_archive_brd_color_image['background-image']) && !empty($rainbow_course_archive_brd_color_image['background-image'])) {
            $custom_style .= sprintf('.post-type-archive-courses .bg-gradient-1.rainbow-tutor-lms-breadcrumb-center-content,
                .post-type-archive-courses .rbt-page-banner-wrapper .rbt-banner-image,
                .post-type-archive-courses .rbt-breadcrumb-default,
                .post-type-archive-courses .rbt-page-banner-wrapper .rbt-banner-image::after {
                background:url(%s)!important;
                background-repeat: %s;
                background-size: %s;
                background-position: %s
            }', esc_attr( $rainbow_course_archive_brd_color_image['background-image']),esc_attr( $rainbow_course_archive_brd_color_image['background-repeat']),esc_attr( $rainbow_course_archive_brd_color_image['background-size']),esc_attr( $rainbow_course_archive_brd_color_image['background-position']) );
        }

        if ( isset($rainbow_course_archive_brd_color_image['background-color']) && !empty($rainbow_course_archive_brd_color_image['background-color'])) {
            $custom_style .= sprintf('.post-type-archive-courses .bg-gradient-1.rainbow-tutor-lms-breadcrumb-center-content,
                .post-type-archive-courses .rbt-page-banner-wrapper .rbt-banner-image,
                .post-type-archive-courses .rbt-breadcrumb-default,
                .post-type-archive-courses .rbt-page-banner-wrapper .rbt-banner-image::after {
            background:%s!important;}', esc_attr( $rainbow_course_archive_brd_color_image['background-color']) );
        }

        if ( isset($rainbow_course_details_brd_color['background-color']) && !empty($rainbow_course_details_brd_color['background-color'])) {
            $custom_style .= sprintf('.single-course-bundle .rbt-breadcrumb-default ,.single-courses .rbt-breadcrumb-default{
            background:%s;}', esc_attr( $rainbow_course_details_brd_color['background-color']) );
        }

        if ( isset($rainbow_course_details_gradient_image['from'] ) && isset( $rainbow_course_details_gradient_image['to']) && !empty($rainbow_course_details_gradient_image)) {
            $custom_style .= sprintf(
                '.single-course-bundle .rbt-breadcrumb-default,.single-courses .rbt-breadcrumb-default { background: linear-gradient(%s, %s) !important; }',
                esc_attr($rainbow_course_details_gradient_image['from']),
                esc_attr($rainbow_course_details_gradient_image['to'])
            );
        }

        if ( isset($histudy_event_page_bg_color['from'] ) && isset( $histudy_event_page_bg_color['to']) && !empty($histudy_event_page_bg_color)) {
            $custom_style .= sprintf(
                '.post-type-archive-course_event .rbt-page-banner-wrapper .rbt-banner-image { background: linear-gradient(%s, %s) !important; }',
                esc_attr($histudy_event_page_bg_color['from']),
                esc_attr($histudy_event_page_bg_color['to'])
            );
        }

        if ( isset($rainbow_event_details_gradient_color['from'] ) && isset( $rainbow_event_details_gradient_color['to']) && !empty($rainbow_event_details_gradient_color)) {
            $custom_style .= sprintf(
                '.single-course_event .rbt-breadcrumb-default,.single-course_event .rbt-breadcrumb-style-3 { background: linear-gradient(%s, %s) !important; }',
                esc_attr($rainbow_event_details_gradient_color['from']),
                esc_attr($rainbow_event_details_gradient_color['to'])
            );
        }

        if ( isset($histudy_evnet_page_bg_color_top['from'] ) && isset( $histudy_evnet_page_bg_color_top['to']) && !empty($histudy_evnet_page_bg_color_top)) {
            $custom_style .= sprintf(
                '.post-type-archive-course_event .rbt-page-banner-wrapper .rbt-banner-image:after,
                .tax-event_tag .rbt-page-banner-wrapper .rbt-banner-image:after { background: linear-gradient(%s, %s) !important; }',
                esc_attr($histudy_evnet_page_bg_color_top['from']),
                esc_attr($histudy_evnet_page_bg_color_top['to'])
            );
        }

        if ( isset($rainbow_events_archive_brd_background['background-color']) && !empty($rainbow_events_archive_brd_background['background-color'])) {
            $custom_style .= sprintf('.post-type-archive-course_event .rbt-page-banner-wrapper .rbt-banner-image:after,
            .tax-event_tag .rbt-page-banner-wrapper .rbt-banner-image:after {
            background:%s;}', esc_attr( $rainbow_events_archive_brd_background['background-color']) );
        }

        // events details
        if ( isset($rainbow_event_details_brd_image['background-color']) && !empty($rainbow_event_details_brd_image['background-color'])) {
            $custom_style .= sprintf('.single-course_event .rbt-breadcrumb-default,.single-course_event .rbt-breadcrumb-style-3 {
            background:%s;}', esc_attr( $rainbow_event_details_brd_image['background-color']) );
        }



        if ( isset($rainbow_events_archive_brd_background['background-image']) && !empty($rainbow_events_archive_brd_background['background-image'])) {
            $custom_style .= sprintf('.post-type-archive-course_event .rbt-page-banner-wrapper .rbt-banner-image:after,.tax-event_tag .rbt-page-banner-wrapper .rbt-banner-image:after{
                background:url(%s);
                background-repeat: %s;
                background-size: %s;
                background-position: %s
            }', esc_attr( $rainbow_events_archive_brd_background['background-image']),esc_attr( $rainbow_events_archive_brd_background['background-repeat']),esc_attr( $rainbow_events_archive_brd_background['background-size']),esc_attr( $rainbow_events_archive_brd_background['background-position']) );
        }


        if ( isset($histudy_blog_page_bg_color['from'] ) && isset( $histudy_blog_page_bg_color['to']) && !empty($histudy_blog_page_bg_color) ) {
            $custom_style .= sprintf(
                '.blog .rbt-page-banner-wrapper .rbt-banner-image { background: linear-gradient(%s, %s) !important; }',
                esc_attr($histudy_blog_page_bg_color['from']),
                esc_attr($histudy_blog_page_bg_color['to'])
            );
        }

        if ( isset($histudy_blog_page_bg_color_top['from'] ) && isset( $histudy_blog_page_bg_color_top['to']) && !empty($histudy_blog_page_bg_color_top) ) {
            $custom_style .= sprintf(
                '.blog .rbt-page-banner-wrapper .rbt-banner-image:after { background: linear-gradient(%s, %s) !important; }',
                esc_attr($histudy_blog_page_bg_color_top['from']),
                esc_attr($histudy_blog_page_bg_color_top['to'])
            );
        }

        if (!empty($rainbow_course_archive_brd_image)) {
            $custom_style .= sprintf(
                '.post-type-archive-courses .bg-gradient-1.rainbow-tutor-lms-breadcrumb-center-content,
                .post-type-archive-courses .rbt-page-banner-wrapper .rbt-banner-image,
                .post-type-archive-courses .rbt-breadcrumb-default { background: linear-gradient(%s, %s) !important; }',
                esc_attr($rainbow_course_archive_brd_image['from']),
                esc_attr($rainbow_course_archive_brd_image['to'])
            );
        }

        // blog archive page

        if ( isset($rainbow_blog_archive_brd_background['background-color']) && !empty($rainbow_blog_archive_brd_background['background-color'])) {
            $custom_style .= sprintf('.blog .rbt-page-banner-wrapper .rbt-banner-image::after,.archive.category .rbt-page-banner-wrapper .rbt-banner-image::after,
            .archive.tag .rbt-page-banner-wrapper .rbt-banner-image::after {
            background:%s;}', esc_attr( $rainbow_blog_archive_brd_background['background-color']) );
        }

        if ( isset($rainbow_blog_archive_brd_background['background-image']) && !empty($rainbow_blog_archive_brd_background['background-image'])) {
            $custom_style .= sprintf('.blog .rbt-page-banner-wrapper .rbt-banner-image::after,.archive.category .rbt-page-banner-wrapper .rbt-banner-image::after,
            .archive.tag .rbt-page-banner-wrapper .rbt-banner-image::after {
                background:url(%s);
                background-repeat: %s;
                background-size: %s;
                background-position: %s
            }', esc_attr( $rainbow_blog_archive_brd_background['background-image']),esc_attr( $rainbow_blog_archive_brd_background['background-repeat']),esc_attr( $rainbow_blog_archive_brd_background['background-size']),esc_attr( $rainbow_blog_archive_brd_background['background-position']) );
        }

        // blog details

        if ( isset($rainbow_blog_details_brd_background['background-color']) && !empty($rainbow_blog_details_brd_background['background-color'])) {
            $custom_style .= sprintf('.single-post .rbt-overlay-page-wrapper .breadcrumb-image-container .breadcrumb-image-wrapper { background:%s;}', esc_attr( $rainbow_blog_details_brd_background['background-color']) );
        }

        if ( isset($histudy_blog_details_gradient_bg_color['from'] ) && isset( $histudy_blog_details_gradient_bg_color['to']) && !empty($histudy_blog_details_gradient_bg_color)) {
            $custom_style .= sprintf(
                '.single-post .rbt-overlay-page-wrapper .breadcrumb-image-container .breadcrumb-image-wrapper { background: linear-gradient(%s, %s) !important; }',
                esc_attr($histudy_blog_details_gradient_bg_color['from']),
                esc_attr($histudy_blog_details_gradient_bg_color['to'])
            );
        }


        // Add the inline style to the main stylesheet
        wp_add_inline_style('main-style', $custom_style);
    }

    public function rainbow_elementor_scripts()
    {
        if (!did_action('elementor/loaded')) {
            return;
        }
        if (Plugin::$instance->preview->is_preview_mode()) {
            wp_enqueue_script('bootstrap-min');
            wp_enqueue_script('sal');
            wp_enqueue_script('swiper');
            wp_enqueue_script('magnify-min');
            wp_enqueue_script('jquery-appear');
            wp_enqueue_script('wow');
            wp_enqueue_script('odometer');
            wp_enqueue_script('jquery-style-switcher');
            wp_enqueue_script('isotop');
            wp_enqueue_script('countdown');
            wp_enqueue_script('waypoint-min');
            wp_enqueue_script('easypie');
            wp_enqueue_script('text-type');
            wp_enqueue_script('bootstrap-select.min');
            wp_enqueue_script('jquery-one-page-nav');
            wp_enqueue_script('waypoint-min');
            wp_enqueue_script('imagesloaded');
        }
    }

    private function rainbow_fonts_url2()
    {
        $fonts_url = '';
        $fonts = array();
        $subsets = 'latin,latin-ext';
        /* translators: If there are characters in your language that are not supported by Poppins, translate this to 'off'. Do not translate into your own language. */
        if ('off' !== _x('on', 'Poppins font: on or off', 'histudy')) {
            $fonts[] = 'Poppins:wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,700&display=swap';

        }

        /* translators: If there are characters in your language that are not supported by Yantramanav, translate this to 'off'. Do not translate into your own language. */


        if ('off' !== esc_attr_x('on', 'Montserrat font: on or off', 'histudy')) {

            $fonts[] = 'Montserrat:wght@400,500,600,700,800&display=swap';
        }

        if ($fonts) {
            $fonts_url = add_query_arg(array(
                'family' => urlencode(implode('|', $fonts)),
                'subset' => urlencode($subsets),
            ), 'https://fonts.googleapis.com/css');
        }
        return $fonts_url;
    }


    private function rainbow_fonts_url()
    {
        $fonts_url = '';
        $fonts = array();
        $subsets = 'latin,latin-ext';
        /* translators: If there are characters in your language that are not supported by Poppins, translate this to 'off'. Do not translate into your own language. */
        if ('off' !== _x('on', 'Source font: on or off', 'histudy')) {
            $fonts[] = 'Source+Serif+Pro:ital,wght@0,400;0,600;0,700;0,900;1,400&display=swap';

        }


        if ($fonts) {
            $fonts_url = add_query_arg(array(
                'family' => urlencode(implode('|', $fonts)),
                'subset' => urlencode($subsets),
            ), 'https://fonts.googleapis.com/css');
        }
        return $fonts_url;
    }


    private function rainbow_localized_scripts()
    {

        $localize_data = array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'hasAdminBar' => is_admin_bar_showing() ? 1 : 0,
            'rtl' => is_rtl() ? 'yes' : 'no', //@rtl

        );
        wp_localize_script('histudy-has-elementor', 'RainbowObj', $localize_data);

    }

    private function rainbow_localized_scripts2()
    {
        $rainbow_options        = Rainbow_Helper::rainbow_get_options();
        $rainbow_header_offset  = rainbow_get_acf_data("rainbow_header_offset");
        $rainbow_header_offset_on  = rainbow_get_acf_data("rainbow_header_offset_on");

        if ($rainbow_header_offset_on) {
            $rainbow_header_offset  = $rainbow_header_offset;

        } else {
            $rainbow_header_offset  = $rainbow_options['rainbow_header_offset'];
        }

        $localize_data2 = array(
            'header_offset' => $rainbow_header_offset,

        );
        wp_localize_script('histudy-has-elementor', 'RainbowBavObj', $localize_data2 );
    }


}

Rainbow_Scripts::instance();