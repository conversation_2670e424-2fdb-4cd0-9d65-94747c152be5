<?php
/**
 * @param $options
 * dialog
 */
function histudy_confirmation_dialog_options($options)
{
    return array_merge($options, array(
        'width' => 500,
        'dialogClass' => 'wp-dialog',
        'resizable' => false,
        'height' => 'auto',
        'modal' => true,
    ));
}

add_filter('pt-ocdi/confirmation_dialog_options', 'histudy_confirmation_dialog_options', 10, 1);

/**
 * histudy_import_files
 * @return array
 */
function histudy_import_files()
{
    $import_notice = esc_html__('Importing may take 5-10 minutes.', 'histudy');

    if( function_exists('tutor') ) {
        $demo_location = 'https://rainbowthemes.net/themes/histudy/demo/';
        $demo_content = 'https://rainbowthemes.net/themes/histudy/demo/demo-content/';
        $preview_url = 'https://rainbowthemes.net/themes/histudy';
    } else{
        $demo_location = 'https://rainbowthemes.net/themes/histudy-learnpress/demo/';
        $demo_content = 'https://rainbowthemes.net/themes/histudy-learnpress/demo/learnpress-demo-content/';
        $preview_url = 'https://rainbowthemes.net/themes/histudy-learnpress';
    }


    $demos = array(
        array(
            'import_file_name' => 'Home',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/main-demo.webp',
            'preview_url' => $preview_url,
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Marketplace',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/marketplace.webp',
            'preview_url' => $preview_url . '/home-marketplace/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'kindergarten',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array( 
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/kindergarden.webp',
            'preview_url' => $preview_url . '/home-kindergarden/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'University Classic',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/university-classic.webp',
            'preview_url' => $preview_url . '/home-university-classic/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Home Elegant',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/home-elegent.webp',
            'preview_url' => $preview_url . '/home-elegant/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Gym Coaching',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/gym-coaching.webp',
            'preview_url' => $preview_url . '/home-gym-coachings/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Online School',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/online-course.webp',
            'preview_url' => $preview_url . '/home-online-school/',
            'import_notice' => $import_notice,
        ),


        array(
            'import_file_name' => 'University Status',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/university-status.webp',
            'preview_url' => $preview_url . '/home-university-about/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Home Technology',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/home-technology.webp',
            'preview_url' => $preview_url . '/home-technology/',
            'import_notice' => $import_notice,
        ),


        array(
            'import_file_name' => 'Instructor Portfolio',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/instructor-portfolio.webp',
            'preview_url' => $preview_url . '/instructor-portfolio/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Language Academy',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/language-academy.webp',
            'preview_url' => $preview_url . '/home-language-academy/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Single Course',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/single-course.webp',
            'preview_url' => $preview_url . '/home-single-courses/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Online Course',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/online-course.webp',
            'preview_url' => $preview_url . '/home-online-courses/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Classic LMS',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/classic-lms.webp',
            'preview_url' => $preview_url . '/home-classic-lms/',
            'import_notice' => $import_notice,
        ),
        array(
            'import_file_name' => 'Course School',
            'import_file_url' => $demo_content . 'content.xml',
            'import_widget_file_url' => $demo_content . 'widgets.wie',
            'import_customizer_file_url' => $demo_content . 'customizer.dat',
            'import_redux' => array(
                array(
                    'file_url' => $demo_content . 'options.json',
                    'option_name' => 'rainbow_options',
                )
            ),
            'import_preview_image_url' => $demo_location . 'preview/course-school.webp',
            'preview_url' => $preview_url . '/home-online-course-education/',
            'import_notice' => $import_notice,
        ),

    );

    $additional_demos = array();

    if ( function_exists('tutor') ) {
        $additional_demos = array(
            array(
                'import_file_name' => 'Online Academy',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/online_academy.webp',
                'preview_url' => $preview_url . '/online-academy/',
                'import_notice' => $import_notice,
            ),
            array(
                'import_file_name' => 'Instructors & Coaches',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/instructor_and_coaches.webp',
                'preview_url' => $preview_url . '/instructor-coaches/',
                'import_notice' => $import_notice,
            ),
            array(
                'import_file_name' => 'Modern University',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/modern_university.webp',
                'preview_url' => $preview_url . '/modern-university/',
                'import_notice' => $import_notice,
            ),
            array(
                'import_file_name' => 'Multilingual',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/multilangual_demo.webp',
                'preview_url' => $preview_url . '/multilingual/',
                'import_notice' => $import_notice,
            ),
            array(
                'import_file_name' => 'Art & Design School',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/art_and_design_school.webp',
                'preview_url' => $preview_url . '/art-design-school/',
                'import_notice' => $import_notice,
            ),

            array(
                'import_file_name' => 'Life Coach',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/life-coach.webp',
                'preview_url' => $preview_url . '/life-coach/',
                'import_notice' => $import_notice,
            ),

            array(
                'import_file_name' => 'Coaching',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/coaching.webp',
                'preview_url' => $preview_url . '/coaching/',
                'import_notice' => $import_notice,
            ),

            array(
                'import_file_name' => 'Islamic Center',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/islamic-center.webp',
                'preview_url' => $preview_url . '/islamic-center/',
                'import_notice' => $import_notice,
            ),

            array(
                'import_file_name' => 'Health Wellness Institute',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/health-institute.webp',
                'preview_url' => $preview_url . '/health-wellness-institute/',
                'import_notice' => $import_notice,
            ),

            array(
                'import_file_name' => 'Wishlist',
                'import_file_url' => $demo_content . 'content.xml',
                'import_widget_file_url' => $demo_content . 'widgets.wie',
                'import_customizer_file_url' => $demo_content . 'customizer.dat',
                'import_redux' => array(
                    array(
                        'file_url' => $demo_content . 'options.json',
                        'option_name' => 'rainbow_options',
                    )
                ),
                'import_preview_image_url' => $demo_location . 'preview/Wislist.webp',
                'preview_url' => $preview_url . '/wishlist/',
                'import_notice' => $import_notice,
            ),
        );
    }

    $demos = array_merge($demos, $additional_demos);

    return $demos;
}

add_filter('pt-ocdi/import_files', 'histudy_import_files');

/**
 * histudy_before_widgets_import
 * @param $selected_import
 */
function histudy_before_widgets_import($selected_import)
{

    // Remove 'Hello World!' post
    wp_delete_post(1, true);
    // Remove 'Sample page' page
    wp_delete_post(2, true);

    $sidebars_widgets = get_option('sidebars_widgets');
    $sidebars_widgets['sidebar'] = array();
    update_option('sidebars_widgets', $sidebars_widgets);

}

add_action('pt-ocdi/before_widgets_import', 'histudy_before_widgets_import');

/*
 * Automatically assign
 * "Front page",
 * "Posts page" and menu
 * locations after the importer is done
 */
function histudy_after_import_setup($selected_import)
{

    $demo_imported = get_option('histudy_demo_imported');

    $cpt_support = get_option('elementor_cpt_support');
    $elementor_disable_color_schemes = get_option('elementor_disable_color_schemes');
    $elementor_disable_typography_schemes = get_option('elementor_disable_typography_schemes');
    $elementor_container_width = get_option('elementor_container_width');


    //check if option DOESN'T exist in db
    if (!$cpt_support) {
        $cpt_support = ['page', 'post', 'portfolio', 'elementor_disable_color_schemes']; //create array of our default supported post types
        update_option('elementor_cpt_support', $cpt_support); //write it to the database
    }
    if (empty($elementor_disable_color_schemes)) {
        update_option('elementor_disable_color_schemes', 'yes'); //update database
    }
    if (empty($elementor_disable_typography_schemes)) {
        update_option('elementor_disable_typography_schemes', 'yes'); //update database
    }
    if (empty($elementor_container_width)) {
        update_option('elementor_container_width', '1260'); //update database
    }

    $elementor_general_settings = array(
        'container_width' => (!empty($elementor_container_width)) ? $elementor_container_width : '1260',
    );
    update_option('_elementor_general_settings', $elementor_general_settings); //update database

    // Update Global Css Options For Elementor
    $currentTime = strtotime("now");
    $elementor_global_css = array(
        'time' => $currentTime,
        'fonts' => array()
    );
    update_option('_elementor_global_css', $elementor_global_css); //update database

    update_option('histudy_elementor_custom_setting_imported', 'elementor_custom_setting_imported');


    //  Update URL 
    $rbt_options_old_url = get_option('rainbow_options');

    if( function_exists( 'tutor' ) ) { 

        $site_url_histudy = 'https://rainbowthemes.net/themes/histudy';

    } else {

        $site_url_histudy = 'https://rainbowthemes.net/themes/histudy-learnpress';
    }


    $site_url = get_site_url();
    foreach($rbt_options_old_url as $key => $val) {
        if(isset($rbt_options_old_url[$key]['url'])) {
            if (str_contains($rbt_options_old_url[$key]['url'], $site_url_histudy )) {
                $rbt_options_old_url[$key]['url'] = str_replace( $site_url_histudy, $site_url, $rbt_options_old_url[$key]['url']);
            }
        }
    }
    
    update_option('rainbow_options', $rbt_options_old_url); //update database

    if (empty($demo_imported)) {

        // Home page selected
        if ('Home' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home  - Marketplace' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home  - Marketplace');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home Kindergarden' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home Kindergarden');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - University Classic' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - University Classic');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home Elegant' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home Elegant');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Gym Coachings' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Gym Coachings');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Online School' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Online School');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - University About' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - University About');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Technology' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Technology');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Instructor Portfolio' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Instructor Portfolio');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Language Academy' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Language Academy');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - single courses' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - single courses');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Online Course' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Online Course');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Classic LMS' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Classic LMS');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Online Course & Education' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Online Course & Education');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } 
        elseif ('Online Academy' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Online Academy');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } 
        elseif ('Instructor & Coaches' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Instructor & Coaches');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } 
        elseif ('Modern University' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Modern University');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        } 
        elseif ('Multilingual' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Multilingual');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        elseif ('Art & Design School' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Art & Design School');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        elseif ('Life Coach' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Life Coach');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        elseif ('Coaching' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Coaching');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        elseif ('Islamic Center' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Islamic Center');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        elseif ('Health Wellness Institute' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Health Wellness Institute');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        
        elseif ('Wishlist' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Wishlist');
            update_option('histudy_theme_active_demo', $selected_import['import_file_name']);
        }
        

        $blog_page_id = get_page_by_title('Blog');
        update_option('show_on_front', 'page');
        update_option('page_on_front', $front_page_id->ID);

        update_option('page_for_posts', $blog_page_id->ID);

        update_option('histudy_demo_imported', 'imported');
    }

    // Set Menu As Primary && Off Canvus Menu
    $main_menu = get_term_by('name', 'Primary', 'nav_menu');
    set_theme_mod('nav_menu_locations', array(
        'primary' => $main_menu->term_id
    ));

}


add_action('pt-ocdi/after_import', 'histudy_after_import_setup');


/**
 * time_for_one_ajax_call
 * @return int
 */
function histudy_change_time_of_single_ajax_call()
{
    return 20;
}

add_action('pt-ocdi/time_for_one_ajax_call', 'histudy_change_time_of_single_ajax_call');


// To make demo imported items selected
add_action('admin_footer', 'histudy_pt_ocdi_add_scripts');
function histudy_pt_ocdi_add_scripts()
{
    $demo_imported = get_option('histudy_theme_active_demo');
    if (!empty($demo_imported)) {
        ?>
        <script>
            jQuery(document).ready(function ($) {
                $('.ocdi__gl-item.js-ocdi-gl-item').each(function () {
                    var ocdi_theme_title = $(this).data('name');
                    var current_ocdi_theme_title = '<?php echo strtolower($demo_imported); ?>';
                    if (ocdi_theme_title == current_ocdi_theme_title) {
                        $(this).addClass('active_demo');
                        return false;
                    }
                });
            });
        </script>
        <?php
    }
}
/**
 * Remove ads
 */
add_filter('pt-ocdi/disable_pt_branding', '__return_true');