<?php
/**
 * Contains methods for customizing the theme customization screen.
 *
 * @link http://codex.wordpress.org/Theme_Customization_API
 * @since histudy 1.0
 */
/**
 * rainbow_custom_customize_register
 */
if (!function_exists('rainbow_custom_customize_register')) {
    function rainbow_custom_customize_register()
    {
        /**
         * Custom Separator
         */
        class histudy_Separator_Custom_control extends WP_Customize_Control
        {
            public $type = 'separator';

            public function render_content()
            {
                ?>
                <p>
                <hr></p>
                <?php
            }
        }
    }

    add_action('customize_register', 'rainbow_custom_customize_register');
}

/**
 * Start Rainbow_Customize
 */
class Rainbow_Customize
{
    /**
     * This hooks into 'customize_register' (available as of WP 3.4) and allows
     * you to add new sections and controls to the Theme Customize screen.
     *
     * Note: To enable instant preview, we have to actually write a bit of custom
     * javascript. See rainbow_live_preview() for more.
     *
     * @see add_action('customize_register',$func)
     * @param \WP_Customize_Manager $wp_customize
     * @link http://ottopress.com/2012/how-to-leverage-the-theme-customizer-in-your-own-themes/
     * @since histudy 1.0
     */
    public static function register($wp_customize)
    {

        //1. Define a new section (if desired) to the Theme Customizer
        $wp_customize->add_panel('rainbow_colors_options',
            array(
                'title' => esc_html__('Histudy Colors Options', 'histudy'), //Visible title of section
                'priority' => 35, //Determines what order this appears in
                'capability' => 'edit_theme_options', //Capability needed to tweak
                'description' => esc_html__('Allows you to customize some example settings for histudy.', 'histudy'), //Descriptive tooltip
            )
        );

        $wp_customize->add_section('rainbow_colors_main_options',
            array(
                'title' => esc_html__('Global Colors', 'histudy'), //Visible title of section
                'priority' => 10, //Determines what order this appears in
                'capability' => 'edit_theme_options', //Capability needed to tweak
                'panel' => 'rainbow_colors_options',
            )
        );

        /*************************
         * Primary
         ************************/
        $wp_customize->add_setting('color_primary',
            array(
                'default' => '#2f57ef',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_primary',
            array(
                'label' => esc_html__('Primary Color', 'histudy'),
                'settings' => 'color_primary',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

        /*************************
         * Secondary
         ************************/
        $wp_customize->add_setting('color_secondary',
            array(
                'default' => '#b966e7',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_secondary',
            array(
                'label' => esc_html__('Secondary Color', 'histudy'),
                'settings' => 'color_secondary',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

        /*************************
         * color coral
         ************************/
        $wp_customize->add_setting('color_coral',
            array(
                'default' => '#E9967A',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_coral',
            array(
                'label' => esc_html__('Coral Color', 'histudy'),
                'settings' => 'color_coral',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

        /*************************
         * color violet
         ************************/
        $wp_customize->add_setting('color_violet',
            array(
                'default' => '#800080',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_violet',
            array(
                'label' => esc_html__('Colar Violet', 'histudy'),
                'settings' => 'color_violet',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

        /*************************
         * color violet
         ************************/
        $wp_customize->add_setting('color_pink',
            array(
                'default' => '#DB7093',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_pink',
            array(
                'label' => esc_html__('Color Pink', 'histudy'),
                'settings' => 'color_pink',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

        /*************************
         * Gradient 
         ************************/
        $wp_customize->add_setting('color_gradient_1',
            array(
                'default' => '#CFA2E8',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_gradient_1',
            array(
                'label' => esc_html__('Button Border Style Gradient -1', 'histudy'),
                'settings' => 'color_gradient_1',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));

         /*************************
         * Gradient 2
         ************************/
        $wp_customize->add_setting('color_gradient_2',
            array(
                'default' => '#637FEA',
                'type' => 'theme_mod',
                'capability' => 'edit_theme_options',
                'transport' => 'refresh',
                'sanitize_callback' => 'sanitize_hex_color'
            )
        );
        $wp_customize->add_control(new WP_Customize_Color_Control(
            $wp_customize,
            'rainbow_color_gradient_2',
            array(
                'label' => esc_html__('Button Border Style Gradient -2', 'histudy'),
                'settings' => 'color_gradient_2',
                'priority' => 10,
                'section' => 'rainbow_colors_main_options',
            )
        ));


    }

    /**
     * This will output the custom WordPress settings to the live theme's WP head.
     *
     * Used by hook: 'wp_head'
     *
     * @see add_action('wp_head',$func)
     * @since histudy 1.0
     */
    public static function rainbow_custom_color_output()
    {
        $color_primary = get_theme_mod('color_primary', '#2f57ef');
        $color_secondary = get_theme_mod('color_secondary', '#b966e7');
        $primary_rgba_01 = rainbow_hex2rgba($color_primary, '0.1');
        ?>
        <!--Customizer CSS-->
        <style type="text/css">

            /************************************************************************************
             * General
             ************************************************************************************/
            /* Primary [#ff014f] */
            <?php self::rainbow_generate_css(':root', '--color-primary', 'color_primary'); ?>  
            <?php self::rainbow_generate_css(':root', '--color-secondary', 'color_secondary'); ?> 
            <?php self::rainbow_generate_gradient_angle('.rbt-btn.btn-border-gradient, .rbt-counterup.border-bottom-gradient::before', '90deg', 'color_gradient_1',  'color_gradient_2'); ?> 
        </style>
        <!--/Customizer CSS-->
        <?php
    }

    /**
     * This will generate a line of CSS for use in header output. If the setting
     * ($mod_name) has no defined value, the CSS will not be output.
     *
     * @uses get_theme_mod()
     * @param string $selector CSS selector
     * @param string $style The name of the CSS *property* to modify
     * @param string $mod_name The name of the 'theme_mod' option to fetch
     * @param string $prefix Optional. Anything that needs to be output before the CSS property
     * @param string $postfix Optional. Anything that needs to be output after the CSS property
     * @param bool $echo Optional. Whether to print directly to the page (default: true).
     * @return string Returns a single line of CSS with selectors and a property.
     * @since histudy 1.0
     */
    public static function rainbow_generate_css($selector, $style, $mod_name, $prefix = '', $postfix = '', $echo = true)
    {
        $return = '';
        $mod = get_theme_mod($mod_name);
        if (!empty($mod)) {
            $return = sprintf('%s { %s:%s; }',
                $selector,
                $style,
                $prefix . $mod . $postfix
            );
            if ($echo) {
                echo rainbow_awescapeing($return);
            }
        }
        return $return;
    }

    /***
     * @param $selector
     * @param $angle
     * @param $from_color
     * @param $to_color
     * @param bool $echo
     * @return string
     */
    public static function rainbow_generate_gradient_angle($selector, $angle, $from_color, $to_color, $echo = true)
    {
        $return = '';
        $from_color = get_theme_mod($from_color, '#CFA2E8');
        $to_color = get_theme_mod($to_color, '#637FEA');
        
        if ($from_color || $to_color) {
            $return = sprintf('%s { background: linear-gradient(%s, %s, %s); }',
                $selector,
                $angle,
                $from_color,
                $to_color
            );
            if ($echo) {
                echo rainbow_awescapeing($return);
            }
        }
        return $return;
    }

    /**
     * @param $selector
     * @param $from_color
     * @param $from_color_default
     * @param $from
     * @param $to_color
     * @param $to_color_default
     * @param $to
     * @param bool $echo
     * @return string
     */
    public static function rainbow_generate_gradient_percentage($selector, $from_color, $from_color_default, $from,  $to_color, $to_color_default, $to, $echo = true)
    {
        $return = '';
        $from_color = get_theme_mod($from_color, $from_color_default);
        $to_color = get_theme_mod($to_color, $to_color_default);
        if ($from_color || $to_color) {
            $return = sprintf('%s { background-image: linear-gradient(%s %s, %s %s); }',
                $selector,
                $from_color,
                $from,
                $to_color,
                $to
            );
            if ($echo) {
                echo rainbow_awescapeing($return);
            }
        }
        return $return;
    }

    /**
     * @param $selector
     * @param $angle
     * @param $from_color
     * @param $from_color_default
     * @param $from
     * @param $to_color
     * @param $to_color_default
     * @param $to
     * @param bool $echo
     * @return string
     */
    public static function rainbow_generate_gradient_angle_percentage($selector, $angle, $from_color, $from_color_default, $from,  $to_color, $to_color_default, $to, $echo = true)
    {
        $return = '';
        $from_color = get_theme_mod($from_color, $from_color_default);
        $to_color = get_theme_mod($to_color, $to_color_default);
        if ($from_color || $to_color) {
            $return = sprintf('%s { background-image: linear-gradient(%s, %s %s, %s %s); }',
                $selector,
                $angle,
                $from_color,
                $from,
                $to_color,
                $to
            );
            if ($echo) {
                echo rainbow_awescapeing($return);
            }
        }
        return $return;
    }

    /**
     * @param $selector
     * @param $attributes
     * @param bool $echo
     * @return string
     */
    public static function rainbow_generate_box_shadow($selector, $attributes, $echo = true)
    {
        $return = '';
        if ($attributes) {
            $return = sprintf('%s { box-shadow: %s; }',
                $selector,
                $attributes
            );
            if ($echo) {
                echo rainbow_awescapeing($return);
            }
        }
        return $return;
    }

}

// Setup the Theme Customizer settings and controls...
add_action('customize_register', array('Rainbow_Customize', 'register'));

// Output custom CSS to live site
add_action('wp_head', array('Rainbow_Customize', 'rainbow_custom_color_output'));