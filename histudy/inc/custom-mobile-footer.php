<?php
function histudy_course_fixed_mobile_menu() {
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $menu_item_labels = isset($rainbow_options['histudy_mobile_menu_item']['menu_title']) ? $rainbow_options['histudy_mobile_menu_item']['menu_title']: array();
    $menu_item_icons = isset($rainbow_options['histudy_mobile_menu_item']['menu_icon']) ? $rainbow_options['histudy_mobile_menu_item']['menu_icon']: array();
    $menu_urls = isset($rainbow_options['histudy_mobile_menu_item']['menu_url']) ? $rainbow_options['histudy_mobile_menu_item']['menu_url']: array();
    $sticky_menu_image = isset($rainbow_options['histudy_mobile_menu_item']['menu_image']) ? $rainbow_options['histudy_mobile_menu_item']['menu_image']: array();

    $enable_mobile_menu = isset( $rainbow_options['enable_mobile_menu'] ) ? $rainbow_options['enable_mobile_menu'] : ' ';
    $enable_home_menu = isset( $rainbow_options['enable_home_menu'] ) ? $rainbow_options['enable_home_menu'] : ' ';
    $enable_course_menu = isset( $rainbow_options['enable_course_menu'] ) ? $rainbow_options['enable_course_menu'] : ' ';
    $enable_myprofile_menu = isset( $rainbow_options['enable_myprofile_menu'] ) ? $rainbow_options['enable_myprofile_menu'] : ' ';
    $enable_cart_menu = isset( $rainbow_options['enable_cart_menu'] ) ? $rainbow_options['enable_cart_menu'] : ' ';
    $enable_category_menu = isset( $rainbow_options['enable_category_menu'] ) ? $rainbow_options['enable_category_menu'] : ' ';

   
    $grouped_menu_items = array();
    for ($i = 0; $i < count($menu_item_labels); $i++) {
        $grouped_menu_items[] = array(
            'menu_title' => $menu_item_labels[$i],
            'menu_icon' => $menu_item_icons[$i],
            'menu_url' => $menu_urls[$i],
           'sticky_menu_image' => $sticky_menu_image[$i]
        );
       
    }
   
    if( $enable_mobile_menu =='1') {
        echo '<ul class="rbt-course-menu-fixed-pos-bottom d-xl-none">';
    }
    global $wp;
    $current_url_path = wp_parse_url(home_url(add_query_arg(array(), $wp->request)), PHP_URL_PATH);
    $current_url_path = is_string($current_url_path) ? $current_url_path : '';
    
    $home_url_path = parse_url(home_url('/'), PHP_URL_PATH);

    $home_url_path = trim($home_url_path, '/');

    if ($enable_home_menu == '1') {
        $home_active = ( $current_url_path == $home_url_path ) ? 'active' : '';
        echo '<li class="' . $home_active . '"><a href="' . home_url('/') . '"><i class="feather-home"></i><span>'.esc_html__("Home","histudy").'</span></a></li>';
    }

    if ($enable_course_menu == '1') {
        $courses_active = (is_string($current_url_path) && strpos($current_url_path, '/courses') !== false) ? 'active' : '';
        echo '<li class="' . $courses_active . '"><a href="' . home_url('/courses') . '"><i class="feather-book"></i><span>'.esc_html__("Courses","histudy").'</span></a></li>';
    }

    if ($enable_cart_menu == '1') {
        $cart_url = '';
        if( class_exists('WooCommerce') ) {
            $cart_url = wc_get_cart_url();
            $cart_active = (strpos($current_url_path, 'cart') !== false) ? 'active' : '';
            echo '<li class="' . $cart_active . '"><a href="' . $cart_url . '"><i class="feather-shopping-cart"></i><span>'.esc_html__("Cart","histudy").'</span></a></li>';
        }
    }

    if ($enable_myprofile_menu == '1') {
        $profile_active = (is_string($current_url_path) && strpos($current_url_path, '/dashboard/my-profile') !== false) ? 'active' : '';
        echo '<li class="' . $profile_active . '"><a href="' . home_url('/dashboard/my-profile/') . '"><i class="feather-user"></i><span>'.esc_html__("Profile","histudy").'</span></a></li>';
    }

    if ($enable_category_menu == '1') {
        $category_active = (strpos($current_url_path, '/course-category') !== false) ? 'active' : '';
        echo '<li class="' . $category_active . '"><a href="' . home_url('/course-category') . '"><i class="feather-inbox"></i><span>'.esc_html__("Category","histudy").'</span></a></li>';
    }

   

    if (!empty($grouped_menu_items)) {
        foreach ($grouped_menu_items as $index => $item) {
            // echo "<pre>";
            // print_r($item);
            // echo "</pre>";
            if(!empty($item['menu_title'])) {
                $active = '';
                $current_url = home_url("/$current_url_path");
                // Check if the current URL matches the menu URL
                if (strpos($current_url, $item['menu_url']) !== false) {
                    $active = 'active';
                }
                if ($index == 0 && $enable_home_menu != 1 && $enable_course_menu != 1  && $enable_myprofile_menu != 1 && $enable_cart_menu != 1) {
                    $active = 'active';
                }

                $menu_image = $item['sticky_menu_image']['url'];
                
                if (isset($item['menu_icon']) && !empty($item['menu_icon']) && empty($menu_image)) {
                    echo '<li class="' . $active . '"><a href="' . $item['menu_url'] . '"><i class="' . $item['menu_icon'] . '"></i><span>' . $item['menu_title'] . '</span></a></li>';
                } elseif (isset($item['menu_icon']) && empty($item['menu_icon']) && !empty($menu_image)) {
                    echo '<li class="' . $active . '"><a href="' . $item['menu_url'] . '"><img src="' . $menu_image . '" alt="menu image" /><span>' . $item['menu_title'] . '</span></a></li>';
                }
            }
        }
    }

    if( $enable_mobile_menu =='1') {
        echo '</ul>';
    }
}
$rainbow_options = Rainbow_Helper::rainbow_get_options();
if(!empty($rainbow_options['enable_mobile_menu'])) {
    add_action( 'wp_footer', 'histudy_course_fixed_mobile_menu' );
}
