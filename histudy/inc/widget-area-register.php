<?php
/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */

if (!function_exists('rainbow_widgets_init')) {
    function rainbow_widgets_init()
    {
        register_sidebar(array(
            'name' => __('Sidebar', 'histudy'),
            'id' => 'sidebar-1',
            'description' => __('Add widgets here.', 'histudy'),
            'before_widget' => '<div id="%1$s" class="footer-widget %2$s mb--40">',
            'after_widget' => '</div>',
            'before_title' => '<h4 class="rbt-widget-title">',
            'after_title' => '</h4>',
        ));

        register_sidebar(array(
            'name' => __('Sidebar Shop', 'histudy'),
            'id' => 'sidebar-shop',
            'description' => __('Add widgets here.', 'histudy'),
            'before_widget' => '<div class="%1$s histudy-single-widget %2$s mb--40">',
            'after_widget' => '</div>',
            'before_title' => '<h2 class="widget-title">',
            'after_title' => '</h2>',
        ));
        register_sidebar(array(
            'name' => __('Sidebar Event', 'histudy'),
            'id' => 'sidebar-event',
            'description' => __('Add widgets here.', 'histudy'),
            'before_widget' => '<div class="%1$s rbt-single-widget  %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h4 class="rbt-widget-title">',
            'after_title' => '</h4>',
        ));

        register_sidebar(array(
            'name' => __('Tutor/LearnPress Course Single Sidebar', 'histudy'),
            'id' => 'sidebar-tutor-course',
            'description' => __('Add widgets here.', 'histudy'),
            'before_widget' => '<div class="%1$s histudy-course-single-widget %2$s mb--40 rbt-shadow-box rbt-gradient-border">',
            'after_widget' => '</div>',
            'before_title' => '<h2 class="widget-title">',
            'after_title' => '</h2>',
        ));

        $number_of_widget = 4;
        $rainbow_widget_titles = array(
            '1' => __('Footer 1', 'histudy'),
            '2' => __('Footer 2', 'histudy'),
            '3' => __('Footer 3', 'histudy'),
            '4' => __('Footer 4', 'histudy'),
        );
        for ($i = 1; $i <= $number_of_widget; $i++) {
            register_sidebar(array(
                'name' => $rainbow_widget_titles[$i],
                'id' => 'footer-' . $i,
                'before_widget' => '<div id="%1$s" class="footer-widget widget %2$s">',
                'after_widget' => '</div>',
                'before_title' => '<h5 class="ft-title">',
                'after_title' => '</h5>',
            ));
        }
        // footer 2
        $histudy_widget_titles = array(
            '1' => __( 'Footer Widget Area 2:1', 'histudy' ),
            '2' => __( 'Footer Widget Area 2:2', 'histudy' ),
            '3' => __( 'Footer Widget Area 2:3', 'histudy' ),
            '4' => __( 'Footer Widget Area 2:4', 'histudy' ),
        );
        for ( $i = 1; $i <= $number_of_widget; $i++ ) {
            register_sidebar( array(
                'name'          => $histudy_widget_titles[$i],
                'id'            => 'footer-2-'. $i,
                'before_widget' => '<div id="%1$s" class="footer-widget widget %2$s">',
                'after_widget'  => '</div>',
                'before_title'  => '<h5 class="ft-title">',
                'after_title'   => '</h5>',
            ) );
        }
        // footer 4
        register_sidebar( array(
            'name'          => __('Footer widget 4', 'histudy'),
            'id'            => 'footer-4-1',
            'before_widget' => '<div id="%1$s" class="footer-widget widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h5 class="ft-title">',
            'after_title'   => '</h5>',
        ) );
        register_sidebar( array(
            'name'          => __('Footer Bottom Additional Data', 'histudy'),
            'id'            => 'footer-bottom-additional-data',
            'before_widget' => '<div id="%1$s" class="footer-widget widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h5 class="ft-title">',
            'after_title'   => '</h5>',
        ) );
    }
}
add_action('widgets_init', 'rainbow_widgets_init');