<?php
/**
 * Instructor List Item
 * Portrait/Default layout
 *
 * @package Tutor\Templates
 * @subpackage Instructor
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 2.0.2
 */

    $instructor = isset( $instructor ) ? $instructor : array();
    $instructor_id = $instructor->ID;
    $instructor_designation = get_the_author_meta( '_tutor_profile_job_title', $instructor_id );
    $instructor_language = function_exists('get_field') ? get_field('location', 'user_'. $instructor_id ): '';
?>
<div class="rbt-team team-style-default style-three small-layout rbt-hover">
    <div class="inner">
        <div class="thumbnail">
            <a href="<?php echo esc_url( tutor_utils()->profile_url( $instructor->ID, true ) ); ?>"><img class="tutor-instructor-cover-photo" src="<?php echo esc_url( get_avatar_url( $instructor->ID, array( 'size' => 500 ) ) ); ?>" alt="<?php echo esc_attr( $instructor->display_name ); ?>" loading="lazy"></a>
        </div>
        <div class="content">
            <h4 class="title"><a href="<?php echo esc_url( tutor_utils()->profile_url( $instructor->ID, true ) ); ?>"><?php echo esc_html( $instructor->display_name ); ?></a></h4>
            <?php if(!empty($instructor_designation)): ?>
            <h6 class="subtitle theme-gradient"><?php echo esc_html($instructor_designation); ?></h6>
            <?php endif; ?>
            <?php if(!empty($instructor_language)) : ?>
            <span class="team-form">
                <i class="feather-map-pin"></i>
                <span class="location"><?php echo esc_html($instructor_language); ?></span>
            </span>
            <?php endif; ?>
        </div>
    </div>
</div>