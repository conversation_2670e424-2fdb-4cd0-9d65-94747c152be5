<?php
/**
 * Template for displaying frontend dashboard
 *
 * @package Tu<PERSON>\Templates
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */
$user_id                   = get_current_user_id();
$user                      = get_user_by( 'ID', $user_id );
$is_by_short_code = isset( $is_shortcode ) && true === $is_shortcode;
if ( ! $is_by_short_code && ! defined( 'OTLMS_VERSION' ) ) {
	tutor_utils()->tutor_custom_header();
}

global $wp_query;
/**
 * Profile photo
 */
$profile_placeholder = apply_filters( 'tutor_login_default_avatar', tutor()->url . 'assets/images/profile-photo.png' );
$profile_photo_src   = $profile_placeholder;
$profile_photo_id    = get_user_meta( $user->ID, '_tutor_profile_photo', true );
if ( $profile_photo_id ) {
	$url                                 = wp_get_attachment_image_url( $profile_photo_id, 'full' );
	! empty( $url ) ? $profile_photo_src = $url : 0;
}
/**
 * Cover photo
 */
$cover_placeholder = tutor()->url . 'assets/images/cover-photo.jpg';
$cover_photo_src   = $cover_placeholder;
$cover_photo_id    = get_user_meta( $user->ID, '_tutor_cover_photo', true );
if ( $cover_photo_id ) {
	$url                               = wp_get_attachment_image_url( $cover_photo_id, 'full' );
	! empty( $url ) ? $cover_photo_src = $url : 0;
}

$dashboard_page_slug = '';
$dashboard_page_name = '';
if ( isset( $wp_query->query_vars['tutor_dashboard_page'] ) && $wp_query->query_vars['tutor_dashboard_page'] ) {
	$dashboard_page_slug = $wp_query->query_vars['tutor_dashboard_page'];
	$dashboard_page_name = $wp_query->query_vars['tutor_dashboard_page'];
}
/**
 * Getting dashboard sub pages
 */
if ( isset( $wp_query->query_vars['tutor_dashboard_sub_page'] ) && $wp_query->query_vars['tutor_dashboard_sub_page'] ) {
	$dashboard_page_name = $wp_query->query_vars['tutor_dashboard_sub_page'];
	if ( $dashboard_page_slug ) {
		$dashboard_page_name = $dashboard_page_slug . '/' . $dashboard_page_name;
	}
}
$dashboard_page_name = apply_filters( 'tutor_dashboard_sub_page_template', $dashboard_page_name );


$enable_profile_completion = tutor_utils()->get_option( 'enable_profile_completion' );
$is_instructor             = tutor_utils()->is_instructor();

// URLS
$current_url  = tutor()->current_url;
$footer_url_1 = trailingslashit( tutor_utils()->tutor_dashboard_url( $is_instructor ? 'my-courses' : '' ) );
$footer_url_2 = trailingslashit( tutor_utils()->tutor_dashboard_url( $is_instructor ? 'question-answer' : 'my-quiz-attempts' ) );

// Footer links
$footer_links = array(
	array(
		'title'      => $is_instructor ? __( 'My Courses', 'histudy' ) : __( 'Dashboard', 'histudy' ),
		'url'        => $footer_url_1,
		'is_active'  => $footer_url_1 == $current_url,
		'icon_class' => 'ttr tutor-icon-dashboard',
	),
	array(
		'title'      => $is_instructor ? __( 'Q&A', 'histudy' ) : __( 'Quiz Attempts', 'histudy' ),
		'url'        => $footer_url_2,
		'is_active'  => $footer_url_2 == $current_url,
		'icon_class' => $is_instructor ? 'ttr  tutor-icon-question' : 'ttr tutor-icon-quiz-attempt',
	),
	array(
		'title'      => __( 'Menu', 'histudy' ),
		'url'        => '#',
		'is_active'  => false,
		'icon_class' => 'ttr tutor-icon-hamburger-o tutor-dashboard-menu-toggler',
	),
);

do_action( 'tutor_dashboard/before/wrap' );
?>
<div class="tutor-wrap tutor-wrap-parent tutor-dashboard tutor-frontend-dashboard tutor-dashboard-student tutor-pb-80 rbt-dashboard-area pt-120 rbt-section-gapTop rbt-section-gapBottom rbt-section-overlayping-top">
	<div class="tutor-container">

    <!-- Start Dashboard Top  -->
    <div class="rbt-dashboard-content-wrapper">
        <div class="tutor-bg-photo bg_image bg_image--22 height-350" data-background="<?php echo esc_url( $cover_photo_src ); ?>"></div>
		<div class="rbt-tutor-information">
			<div class="rbt-tutor-information-left">
				<div class="thumbnail rbt-avatars size-lg">
					<img src="<?php echo esc_attr( $profile_photo_src ); ?>" alt="">
				</div>
				<div class="tutor-content">
					<?php if ( current_user_can( tutor()->instructor_role ) ) :
						$instructor_rating = tutor_utils()->get_instructor_ratings( $user->ID );
					?>
					<h5 class="title"><?php echo esc_html( $user->display_name ); ?></h5>
					<div class="rbt-review">
						<div class="rating">
							<?php tutor_utils()->star_rating_generator_v2( $instructor_rating->rating_avg, $instructor_rating->rating_count, true ); ?>
						</div>
					</div>
					<?php endif; ?>
				</div>
			</div>
			<div class="rbt-tutor-information-right">
				<div class="tutor-btn">
					<?php
						do_action( 'tutor_dashboard/before_header_button' );
						$instructor_status  = tutor_utils()->instructor_status( 0, false );
						$instructor_status  = is_string( $instructor_status ) ? strtolower( $instructor_status ) : '';
						$rejected_on        = get_user_meta( $user->ID, '_is_tutor_instructor_rejected', true );
						$info_style         = 'vertical-align: middle; margin-right: 7px;';
						$info_message_style = 'display:inline-block; color:#7A7A7A; font-size: 15px;';

						ob_start();
						if ( tutor_utils()->get_option( 'enable_become_instructor_btn' ) ) {
							?>
							<div class="tutor-btn">
								<a id="tutor-become-instructor-button" class="rbt-btn btn-md hover-icon-reverse"  href="<?php echo esc_url( tutor_utils()->instructor_register_url() ); ?>">
									<span class="icon-reverse-wrapper">
										<span class="btn-text"><?php echo esc_html__('Create a New Course', 'histudy'); ?></span>
										<span class="btn-icon"><i class="feather-arrow-right"></i></span>
										<span class="btn-icon"><i class="feather-arrow-right"></i></span>
									</span>
								</a>
							</div>
							<?php
						}
						$become_button = ob_get_clean();

						if ( current_user_can( tutor()->instructor_role ) ) {
							$course_type = tutor()->course_post_type;
							?>
							<?php
							/**
							 * Render create course button based on free & pro
							 *
							 * @since v2.0.7
							 */
							if ( function_exists( 'tutor_pro' ) ) :
								?>
								<?php do_action( 'tutor_course_create_button' ); ?>
								<?php else : ?>
									<a id="tutor-become-instructor-button" class="rbt-btn btn-md hover-icon-reverse tutor-btn tutor-btn-outline-primary tutor-create-new-course"  href="#">
										<span class="icon-reverse-wrapper">
											<span class="btn-text"><?php echo esc_html__('Create a New Course', 'histudy'); ?></span>
											<span class="btn-icon"><i class="feather-arrow-right"></i></span>
											<span class="btn-icon"><i class="feather-arrow-right"></i></span>
										</span>
									</a>
						<?php endif; ?>
							<?php
						} elseif ( 'pending' == $instructor_status ) {
							$on = get_user_meta( $user->ID, '_is_tutor_instructor', true );
							$on = date( 'd F, Y', $on );
							echo '<span style="' . esc_attr( $info_message_style ) . '">
										<i class="dashicons dashicons-info tutor-color-warning" style=" ' . esc_attr( $info_style ) . '"></i>',
							esc_html__( 'Your Application is pending as of', 'histudy' ), ' <b>', esc_html( $on ), '</b>',
							'</span>';
						} elseif ( $rejected_on || $instructor_status !== 'blocked' ) {
							echo wp_kses_post($become_button); //phpcs:ignore --data escaped above
						}
					?>
				</div>
			</div>
		</div>
    </div>
    <!-- End Dashboard Top  -->
		<div class="tutor-row tutor-frontend-dashboard-maincontent">
			<div class="tutor-col-12 tutor-col-md-4 tutor-col-lg-3 tutor-dashboard-left-menu">
				<div class="rbt-default-sidebar sticky-top rbt-shadow-box rbt-gradient-border rbt-teacher-details-sidebar-layout-1">
					<div class="inner">
						<div class="content-item-content rbt-default-sidebar-wrapper">
							<div class="rbt-default-sidebar-wrapper">
								<div class="section-title mb--20">
									<h6 class="rbt-title-style-2"><?php echo esc_html__('Welcome', 'histudy'); ?>, <?php echo esc_html( $user->display_name ); ?></h6>
								</div>
								<nav class="mainmenu-nav">
									<ul >
										<?php
										$dashboard_pages = tutor_utils()->tutor_dashboard_nav_ui_items();
										// get reviews settings value.
										$disable = ! get_tutor_option( 'enable_course_review' );
										foreach ( $dashboard_pages as $dashboard_key => $dashboard_page ) {
											/**
											 * If not enable from settings then quit
											 *
											 *  @since v2.0.0
											 */
											if ( $disable && 'reviews' === $dashboard_key ) {
												continue;
											}

											$menu_title = $dashboard_page;
											$menu_link  = tutor_utils()->get_tutor_dashboard_page_permalink( $dashboard_key );
											$separator  = false;
											$menu_icon  = '';

											if ( is_array( $dashboard_page ) ) {
												$menu_title     = tutor_utils()->array_get( 'title', $dashboard_page );
												$menu_icon_name = tutor_utils()->array_get( 'icon', $dashboard_page, ( isset( $dashboard_page['icon'] ) ? $dashboard_page['icon'] : '' ) );
												if ( $menu_icon_name ) {
													$menu_icon = "<span class='{$menu_icon_name} tutor-dashboard-menu-item-icon'></span>";
												}
												// Add new menu item property "url" for custom link
												if ( isset( $dashboard_page['url'] ) ) {
													$menu_link = $dashboard_page['url'];
												}
												if ( isset( $dashboard_page['type'] ) && $dashboard_page['type'] == 'separator' ) {
													$separator = true;
												}
											}
											if ( $separator ) {
												echo '<li class="tutor-dashboard-menu-divider"></li>';
												if ( $menu_title ) {
													?>
													<li class='tutor-dashboard-menu-divider-header'>
														<?php echo esc_html( $menu_title ); ?>
													</li>
													<?php
												}
											} else {
												$li_class = "tutor-dashboard-menu-{$dashboard_key}";
												if ( 'index' === $dashboard_key ) {
													$dashboard_key = '';
												}
												$active_class    = $dashboard_key == $dashboard_page_slug ? 'active' : '';
												$data_no_instant = 'logout' == $dashboard_key ? 'data-no-instant' : '';
												$menu_link = apply_filters( 'tutor_dashboard_menu_link', $menu_link, $menu_title );
												?>
												<li class='tutor-dashboard-menu-item <?php echo esc_attr( $li_class . ' ' . $active_class ); ?>'>
													<a <?php echo esc_html( $data_no_instant ); ?> href="<?php echo esc_url( $menu_link ); ?>" class='tutor-dashboard-menu-item-link tutor-fs-6 tutor-color-black'>
														<?php
														echo wp_kses(
															$menu_icon,
															tutor_utils()->allowed_icon_tags()
														);
														?>
														<span class='tutor-dashboard-menu-item-text tutor-ml-12'>
															<?php echo esc_html( $menu_title ); ?>
														</span>
													</a>
												</li>
												<?php
											}
										}
										?>
									</ul>
								</nav>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="tutor-col-12 tutor-col-md-8 tutor-col-lg-9">
				<div class="rbt-dashboard-content bg-color-white rbt-shadow-box mb--60">
					<div class="content tutor-dashboard-content">
					<?php
					if ( $dashboard_page_name ) {
						do_action( 'tutor_load_dashboard_template_before', $dashboard_page_name );

						/**
						 * Load dashboard template part from other location
						 *
						 * This filter is basically added for adding templates from respective addons
						 *
						 * @since version 1.9.3
						 */
						$other_location      = '';
						$from_other_location = apply_filters( 'load_dashboard_template_part_from_other_location', $other_location );

						if ( '' == $from_other_location ) {
							tutor_load_template( 'dashboard.' . $dashboard_page_name );
						} else {
							// Load template from other location full abspath
							include_once $from_other_location;
						}

						do_action( 'tutor_load_dashboard_template_before', $dashboard_page_name );
					} else {
						tutor_load_template( 'dashboard.dashboard' );
					}
					?>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="tutor-dashboard-footer-mobile">
		<div class="tutor-container">
			<div class="tutor-row">
				<?php foreach ( $footer_links as $link ) : ?>
					<a class="tutor-col-4 <?php echo esc_url($link['is_active']) ? 'active' : ''; ?>" href="<?php echo esc_url( $link['url'] ); ?>">
						<i class="<?php echo esc_attr( $link['icon_class'] ); ?>"></i>
						<span><?php echo esc_html( $link['title'] ); ?></span>
					</a>
				<?php endforeach; ?>
			</div>
		</div>
	</div>
</div>

<?php do_action( 'tutor_dashboard/after/wrap' ); ?>

<?php
if ( ! $is_by_short_code && ! defined( 'OTLMS_VERSION' ) ) {
	tutor_utils()->tutor_custom_footer();
}