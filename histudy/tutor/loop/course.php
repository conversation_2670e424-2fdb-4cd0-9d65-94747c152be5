<?php
/**
 * A single course loop
 *
 * @package Tutor\Templates
 * @subpackage CourseLoopPart
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_tutor_card_layout = $rainbow_options['rainbow_tutor_card_layout'];
if( 'layout-1' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/tutor/loop/card/layout', 1 );
} elseif( 'layout-2' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/card/layout', 2 );
} elseif( 'layout-3' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/card/layout', 3 );
} elseif( 'layout-4' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/card/layout', 4 );
} elseif( 'layout-5' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/card/layout', 5 );
} elseif( 'layout-6' == $rainbow_tutor_card_layout ) {
    echo get_template_part( 'template-parts/components/card/layout', 6 );
} else {
    echo get_template_part( 'template-parts/components/tutor/loop/card/layout', 1 );
}