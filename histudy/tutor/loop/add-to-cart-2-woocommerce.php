<?php
/**
 * A single course loop add to cart
 *
 * @package Tu<PERSON>\Templates
 * @subpackage WooCommerceIntegration
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$product_id = tutor_utils()->get_course_product_id();
$product    = ( class_exists( 'WooCommerce' ) ) ? wc_get_product( $product_id ) : '';

if ( ! $product_id || ! $product ) {
	return;
}

/**
 * Add required logged-in class
 *
 * @since 1.5.5
 */
$isLoggedIn               = is_user_logged_in();
$enable_guest_course_cart = tutor_utils()->get_option( 'enable_guest_course_cart' );
$required_loggedin_class  = '';
$page_target = '_self';
$ajax_add_to_cart_class = $product->supports( 'ajax_add_to_cart' ) ? 'ajax_add_to_cart' : '';
// if ( ! $isLoggedIn && ! $enable_guest_course_cart ) {
// 	$required_loggedin_class = apply_filters( 'tutor_enroll_required_login_class', 'tutor-open-login-modal' );
// } else {
// 	if($product->get_type() == 'external') {
// 		$ajax_add_to_cart_class = '';
// 		$page_target = '_blank';
// 	} else {
// 		$ajax_add_to_cart_class = $product->supports( 'ajax_add_to_cart' ) ? 'ajax_add_to_cart' : '';
// 	}
// }

if ( ! $is_logged_in && ! $enable_guest_course_cart ) {
	$required_loggedin_class = apply_filters( 'tutor_enroll_required_login_class', 'tutor-open-login-modal' );
} else {
	$ajax_add_to_cart_class = $product->supports( 'ajax_add_to_cart' ) ? 'ajax_add_to_cart' : '';
}

$args     = array();
$defaults = array(
	'quantity'   => 1,
	'class'      => implode(
		' ',
		array_filter(
			array(
				"tutor-btn tutor-btn-outline-primary tutor-btn-md tutor-btn-block",
				'product_type_' . $product->get_type(),
				$product->is_purchasable() && $product->is_in_stock() ? 'add_to_cart_button' : '',
				$required_loggedin_class,
				$ajax_add_to_cart_class
			)
		)
	),
	'attributes' => array(
		'data-product_id'  => $product->get_id(),
		'data-product_sku' => $product->get_sku(),
		'aria-label'       => $product->add_to_cart_description(),
		'rel'              => 'nofollow',
	),
);

$args = apply_filters( 'woocommerce_loop_add_to_cart_args', wp_parse_args( $args, $defaults ), $product );

if ( isset( $args['attributes']['aria-label'] ) ) {
	$args['attributes']['aria-label'] = strip_tags( $args['attributes']['aria-label'] );
}
?>
<?php
if ( ! $isLoggedIn && ! $enable_guest_course_cart ) { ?>
	<a href="#"   class="tutor-btn tutor-btn-outline-primary tutor-btn-md tutor-btn-block tutor-open-login-modal rbt-btn btn-gradient icon-hover w-100 d-block text-center" %s><span class="btn-text"><?php echo esc_html__("Add to cart","histudy"); ?></span><span class="btn-icon"><i class="feather-arrow-right"></i></span></a>
<?php 
} else {

	//phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --contain safe content
	echo apply_filters(
		'tutor_course_restrict_new_entry',
		apply_filters(
			'woocommerce_loop_add_to_cart_link', // WPCS: XSS ok.
			sprintf(
				'<a href="%s" data-quantity="%s" class="%s" %s><span class="tutor-icon-cart-line tutor-mr-8"></span><span class="cart-text">%s</span></a>',
				esc_url( $product->add_to_cart_url() ),
				esc_attr( isset( $args['quantity'] ) ? $args['quantity'] : 1 ),
				esc_attr( isset( $args['class'] ) ? $args['class'] : 'button' ),
				isset( $args['attributes'] ) ? wc_implode_html_attributes( $args['attributes'] ) : '',
				esc_html( $product->add_to_cart_text() )
			),
			$product,
			$args
		),
		$course_id
	);
}
?>

