<?php
/**
 * Course Loop End
 *
 * @package Tutor\Templates
 * @subpackage CourseLoopPart
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$rainbow_tutor_card_layout = $rainbow_options['rainbow_tutor_card_layout'];
$parent_class = '';
if( ( 'layout-2' == $rainbow_tutor_card_layout ) ||
	( 'layout-3' == $rainbow_tutor_card_layout ) ||
	( 'layout-4' == $rainbow_tutor_card_layout ) ||
	( 'layout-5' == $rainbow_tutor_card_layout ) ||
	( 'layout-6' == $rainbow_tutor_card_layout )
) {
	$parent_class = 'p-0';
}
?>
<div class="rbt-card variation-01 rbt-hover <?php echo esc_attr( $parent_class ); ?>">
