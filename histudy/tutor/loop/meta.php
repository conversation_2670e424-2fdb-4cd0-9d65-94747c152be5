<?php
    $course_id          = get_the_ID();
    global $authordata;
    $rainbow_caourse_cart_settings           = Rainbow_Helper::rainbow_caourse_cart_settings();
    $rainbow_course_card_meta_switch = $rainbow_caourse_cart_settings['rainbow_course_card_meta_switch'] ;
    $course_lessons         = tutor_utils()->get_lesson_count_by_course( $course_id );
    $student                = tutor_utils()->count_enrolled_users_by_course( $course_id );
    $rainbow_course_card_author_switch      = $rainbow_caourse_cart_settings['rainbow_course_card_author_switch'];
    $student_count          = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );
    $author_id              = get_post_field('post_author', $course_id);
    $author_image           = get_avatar_url($author_id);
    $author_image_alt       = get_the_author_meta('description', $author_id);
    $categories             = get_the_terms($course_id, 'course-category');
    $author_name            = get_the_author_meta('display_name', $author_id);
    $profile_url       = tutor_utils()->profile_url( $authordata->ID, true );
    $first_category         = '';
    $category_name          = '';
    $category_id            = '';
    $category_link          = '';
    if ($categories && !is_wp_error($categories)) {
        $first_category     = array_shift($categories);
        $category_name      = $first_category->name;
        $category_id        = $first_category->term_id;
        $category_link      = get_term_link($category_id, 'course-category');
    }

    if( function_exists( 'tutor' ) ) {
        $total_lessons = tutor_utils()->get_lesson_count_by_course( $course_id );  
        $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );
    }
?>
<?php if(1 == $rainbow_course_card_meta_switch) : ?>
    <ul class="rbt-meta">
        <li><i class="feather-book"></i><?php echo esc_html( $total_lessons ); ?></li>
        <li><i class="feather-users"></i><?php echo esc_html($student_count); ?></li>
    </ul>
<?php endif; ?>
<p class="rbt-card-text"><?php echo wp_trim_words( get_the_excerpt(), 12 ); ?></p>
<?php if(1 == $rainbow_course_card_author_switch) : ?>
    <div class="rbt-author-meta mb--10">
        <div class="rbt-avater">
            <?php if(!empty($author_image)) : ?>
            <a href="<?php echo esc_url($profile_url); ?>">
                <img src="<?php echo esc_url($author_image); ?>" alt="<?php echo esc_attr($author_image_alt) ? esc_attr($author_image_alt): ''; ?>">
            </a>
            <?php endif; ?>
        </div>
        <?php if(!empty($author_name)) : ?>
        <div class="rbt-author-info">
            <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url($profile_url) ? esc_html($profile_url): ''; ?>"> <?php echo esc_html($author_name); ?></a> <?php echo esc_html__('In', 'histudy'); ?> <a href="<?php echo esc_url($category_link) ? esc_url($category_link): '' ?>"><?php echo esc_html($category_name) ? esc_html($category_name): ''; ?></a>
        </div>
        <?php endif; ?>
    </div>
<?php endif;