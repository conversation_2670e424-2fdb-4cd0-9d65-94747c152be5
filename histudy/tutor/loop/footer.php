<?php
/**
 * @package Tutor\Templates
 * @subpackage CourseLoopPart
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */
$course_id          = get_the_ID();
$rainbow_caourse_cart_settings           = Rainbow_Helper::rainbow_caourse_cart_settings();
$rainbow_course_card_add_to_cart_switch = $rainbow_caourse_cart_settings['rainbow_course_card_add_to_cart_switch'];
$rainbow_course_card_pricing_switch = $rainbow_caourse_cart_settings['rainbow_course_card_pricing_switch'];
?>

<?php if(1 == $rainbow_course_card_add_to_cart_switch) : ?>
	<div class="rbt-card-bottom">
		<?php if(1 == $rainbow_course_card_pricing_switch) : ?>
			<?php get_template_part('template-parts/components/price/layout', 1); ?>
		<?php endif; ?>
		<?php if((!empty($current_product_price) || !empty($regular_price)) && tutor_utils()->is_course_purchasable($course_id)) : ?>
			<?php tutor_course_loop_add_to_cart($course_id); ?>
			<?php else: ?>
				<a class="rbt-btn-link" href="<?php echo get_the_permalink($course_id); ?>"><?php echo esc_html__( 'Learn More', 'histudy' ); ?><i class="feather-arrow-right"></i></a>
		<?php endif; ?>
	</div>
<?php endif; ?>
</div>