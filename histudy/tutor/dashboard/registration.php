<?php
/**
 * <PERSON><PERSON> registration template
 *
 * @package Tutor\Templates
 * @subpackage Dashboard
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */
    /**
     * Redux meta
     */
    $rainbow_become_student_settings = Rainbow_Helper::rainbow_become_student_settings();
    $rainbow_tutor_student_registration_repeater_title = $rainbow_become_student_settings['rainbow_tutor_student_registration_repeater_title'];
    $rainbow_tutor_student_registration_repeater_content = $rainbow_become_student_settings['rainbow_tutor_student_registration_repeater_content'];
    $rainbow_tutor_student_registration_badge_title = $rainbow_become_student_settings['rainbow_tutor_student_registration_badge_title'];
    $rainbow_tutor_student_registration_main_title = $rainbow_become_student_settings['rainbow_tutor_student_registration_main_title'];
    $rainbow_tutor_student_registration_main_desc = $rainbow_become_student_settings['rainbow_tutor_student_registration_main_desc'];
    $student_image_show = $rainbow_become_student_settings['student_image_show'];
    $rainbow_tutor_student_registration_student_image = $rainbow_become_student_settings['rainbow_tutor_student_registration_student_image'];
    $rainbow_tutor_student_registration_student_image_url = $rainbow_tutor_student_registration_student_image['url'];
?>

<?php if ( ! get_option( 'users_can_register', false ) ) : ?>

	<?php
		$args = array(
			'image_path'  => tutor()->url . 'assets/images/construction.png',
			'title'       => __( 'Oooh! Access Denied', 'histudy' ),
			'description' => __( 'You do not have access to this area of the application. Please refer to your system  administrator.', 'histudy' ),
			'button'      => array(
				'text'  => __( 'Go to Home', 'histudy' ),
				'url'   => get_home_url(),
				'class' => 'tutor-btn',
			),
		);
		tutor_load_template( 'feature_disabled', $args );
		?>

<?php else : ?>
    <div class="rbt-become-area bg-color-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title text-center">
                        <?php if(!empty($rainbow_tutor_student_registration_badge_title)) : ?>
                            <span class="subtitle bg-pink-opacity"><?php echo esc_html($rainbow_tutor_student_registration_badge_title); ?></span>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_student_registration_main_title)) : ?>
                            <h2 class="title"><?php echo esc_html($rainbow_tutor_student_registration_main_title); ?></h2>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_student_registration_main_desc)) : ?>
                            <p class="description has-medium-font-size mt--20 mb--40"><?php echo esc_html($rainbow_tutor_student_registration_main_desc); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if(!empty($rainbow_tutor_student_registration_repeater_title) && !empty($rainbow_tutor_student_registration_repeater_content)) : ?>
            <div class="row row row--30">
                <div class="col-lg-12 order-2 order-lg-1">
                    <div class="advance-tab-button">
                        <ul class="nav nav-tabs tab-button-style-2" id="myTab-1" role="tablist">
                            <?php foreach($rainbow_tutor_student_registration_repeater_title as $index => $value) : ?>
                            <li role="presentation">
                                <a href="#" class="tab-button <?php echo esc_attr($index) == 0 ? esc_attr__('active', 'histudy'): ''; ?>" id="home-tab-<?php echo esc_attr($index); ?>" data-bs-toggle="tab" data-bs-target="#home-<?php echo esc_attr($index); ?>" role="tab" aria-controls="home-<?php echo esc_attr($index); ?>" aria-selected="false">
                                    <span class="title"><?php echo esc_html($value); ?></span>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="tab-content advance-tab-content-style-2">
                        <?php foreach($rainbow_tutor_student_registration_repeater_content as $index=>$value) : ?>
                        <div class="tab-pane fade <?php echo esc_attr($index) == 0 ? esc_attr__('active show', 'histudy'): ''; ?>" id="home-<?php echo esc_attr($index); ?>" role="tabpanel" aria-labelledby="home-tab-<?php echo esc_attr($index); ?>">
                            <div class="content">
                                <p><?php echo esc_html($value); ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="row pt--60 g-5 justify-content-center">
                <?php if(!empty($rainbow_tutor_student_registration_student_image_url) && (1 == $student_image_show)) : ?>
                <div class="<?php echo get_option( 'users_can_register' ) ? 'col-lg-4': 'col-lg-10'; ?>">
                    <div class="thumbnail">
                        <img class="radius-10 w-100" src="<?php echo esc_html($rainbow_tutor_student_registration_student_image_url); ?>" alt="<?php echo esc_attr__('Corporate Template', 'histudy'); ?>">
                    </div>
                </div>
                <?php endif; ?>
                <?php if ( get_option( 'users_can_register' ) ) : ?>
                <div class="col-lg-8">
                    <div class="rbt-contact-form contact-form-style-1 max-width-auto">
                        <?php if(!empty($rainbow_tutor_student_registration_badge_title)) : ?>
                        <div class="section-title text-start">
                            <span class="subtitle bg-primary-opacity"><?php echo esc_html($rainbow_tutor_student_registration_badge_title); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_student_registration_main_title)) : ?>
                            <h3 class="title"><?php echo esc_html($rainbow_tutor_student_registration_main_title); ?></h3>
                        <?php endif; ?>
                        <hr class="mb--30">
                        <?php do_action( 'tutor_before_student_reg_form' ); ?>
                        <form method="post" enctype="multipart/form-data" id="tutor-registration-form" class="row row--15">
                        <input type="hidden" name="tutor_course_enroll_attempt" value="<?php echo isset( $_GET['enrol_course_id'] ) ? (int) $_GET['enrol_course_id'] : ''; ?>">
                        <?php do_action( 'tutor_student_reg_form_start' ); ?>
                        <?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
                        <input type="hidden" value="tutor_register_student" name="tutor_action"/>
                        <?php
                            $validation_errors = apply_filters( 'tutor_student_register_validation_errors', array() );
                            if ( is_array( $validation_errors ) && count( $validation_errors ) ) :
                                ?>
                                <div class="tutor-alert tutor-warning tutor-mb-12">
                                    <ul class="tutor-required-fields">
                                        <?php foreach ( $validation_errors as $validation_error ) : ?>
                                            <li>
                                                <?php echo esc_html( $validation_error ); ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <input type="text" name="first_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'first_name' ) ); ?>" required autocomplete="given-name">
                                    <label><?php esc_html_e( 'First Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <input type="text" name="last_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'last_name' ) ); ?>" required autocomplete="family-name">
                                    <label><?php esc_html_e( 'Last Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-group">
                                <input type="text" name="user_login" class="tutor_user_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'user_login' ) ); ?>"  required autocomplete="username">
                                    <label><?php esc_html_e( 'User Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="text" name="email" value="<?php echo esc_attr( tutor_utils()->input_old( 'email' ) ); ?>"  required autocomplete="email">
                                    <label><?php esc_html_e( 'E-Mail', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="password" name="password" value="<?php echo esc_attr( tutor_utils()->input_old( 'password' ) ); ?>" required autocomplete="new-password">
                                    <label><?php esc_html_e( 'Password', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="password" name="password_confirmation" value="<?php echo esc_attr( tutor_utils()->input_old( 'password_confirmation' ) ); ?>" required autocomplete="new-password">
                                    <label><?php esc_html_e( 'Password confirmation', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>
                            <?php do_action( 'tutor_student_reg_form_end' ); ?>
                            <?php
                                $tutor_toc_page_link = tutor_utils()->get_toc_page_link();
                            ?>
                            <?php if ( null !== $tutor_toc_page_link ) : ?>
                                <div class="tutor-mb-24">
                                    <?php esc_html_e( 'By signing up, I agree with the website\'s', 'histudy' ); ?> <a target="_blank" href="<?php echo esc_url( $tutor_toc_page_link ); ?>" title="<?php esc_attr_e( 'Terms and Conditions', 'histudy' ); ?>"><?php esc_html_e( 'Terms and Conditions', 'histudy' ); ?></a>
                                </div>
                            <?php endif; ?>
                            <div class="col-lg-12">
                                <div class="form-submit-group">
                                    <button type="submit" name="tutor_register_student_btn" value="register" class="rbt-btn btn-md btn-gradient hover-icon-reverse w-100 tutor-btn tutor-btn-primary tutor-btn-block">
                                        <span class="icon-reverse-wrapper">
                                            <span class="btn-text"><?php esc_html_e( 'Register as student', 'histudy' ); ?></span>
                                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                            <?php do_action( 'tutor_after_register_button' ); ?>
                            <?php do_action( 'tutor_after_registration_form_wrap' ); ?>
                        </form>
                        <?php do_action( 'tutor_after_student_reg_form' ); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php endif;