<?php
/**
 * Registration template
 *
 * @package Tutor\Templates
 * @subpackage Dashboard\Instructor
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */

    /**
     * Redux meta
     */
    $rainbow_become_instructor_settings = Rainbow_Helper::rainbow_become_instructor_settings();
    $rainbow_tutor_instructor_registration_repeater_title = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_repeater_title'];
    $rainbow_tutor_instructor_registration_repeater_content = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_repeater_content'];
    $rainbow_tutor_instructor_registration_badge_title = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_badge_title'];
    $rainbow_tutor_instructor_registration_main_title = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_main_title'];
    $rainbow_tutor_instructor_registration_main_desc = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_main_desc'];
    $rainbow_tutor_instructor_registration_instructor_image = $rainbow_become_instructor_settings['rainbow_tutor_instructor_registration_instructor_image'];
    $instructor_image_show = $rainbow_become_instructor_settings['instructor_image_show'];
    $rainbow_tutor_instructor_registration_instructor_image_url = $rainbow_tutor_instructor_registration_instructor_image['url'];
?>

<?php if ( ! get_option( 'users_can_register', false ) ) : ?>

	<?php
		$args = array(
			'image_path'  => tutor()->url . get_template_directory_uri() .'/assets/images/construction.png',
			'title'       => __( 'Oooh! Access Denied', 'histudy' ),
			'description' => __( 'You do not have access to this area of the application. Please refer to your system  administrator.', 'histudy' ),
			'button'      => array(
				'text'  => __( 'Go to Home', 'histudy' ),
				'url'   => get_home_url(),
				'class' => 'tutor-btn',
			),
		);
		tutor_load_template( 'feature_disabled', $args );
		?>

<?php else : ?>
    <div class="rbt-become-area bg-color-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title text-center">
                        <?php if(!empty($rainbow_tutor_instructor_registration_badge_title)) : ?>
                        <span class="subtitle bg-pink-opacity"><?php echo esc_html($rainbow_tutor_instructor_registration_badge_title); ?></span>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_instructor_registration_main_title)) : ?>
                        <h2 class="title"><?php echo esc_html($rainbow_tutor_instructor_registration_main_title); ?></h2>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_instructor_registration_main_desc)) : ?>
                            <p class="description has-medium-font-size mt--20 mb--40"><?php echo esc_html($rainbow_tutor_instructor_registration_main_desc); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php 
               
            ?>
            <?php if(isset($rainbow_tutor_instructor_registration_repeater_title[0]) && !empty($rainbow_tutor_instructor_registration_repeater_title[0]) 
            && !empty($rainbow_tutor_instructor_registration_repeater_content)) : 
                ?>
            <div class="row row--30">
                <div class="col-lg-12 order-2 order-lg-1">
                    <div class="advance-tab-button">
                        <ul class="nav nav-tabs tab-button-style-2" id="myTab-1" role="tablist">
                            <?php foreach($rainbow_tutor_instructor_registration_repeater_title as $index=>$value) : 
                                ?>
                            <li role="presentation">
                                <a href="#" class="tab-button <?php echo esc_attr($index) == 0 ? esc_attr__('active', 'histudy'): ''; ?>" id="home-tab-<?php echo esc_attr($index); ?>" data-bs-toggle="tab" data-bs-target="#home-<?php echo esc_attr($index); ?>" role="tab" aria-controls="home-<?php echo esc_attr($index); ?>" aria-selected="false">
                                    <span class="title"><?php echo esc_html($value); ?></span>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="tab-content advance-tab-content-style-2">
                        <?php foreach($rainbow_tutor_instructor_registration_repeater_content as $index=>$value) : ?>
                        <div class="tab-pane fade <?php echo esc_attr($index) == 0 ? esc_attr__('active show', 'histudy'): ''; ?>" id="home-<?php echo esc_attr($index); ?>" role="tabpanel" aria-labelledby="home-tab-<?php echo esc_attr($index); ?>">
                            <div class="content">
                                <p><?php echo esc_html($value); ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="row row--30">
                <div class="rbt-separator-mid">
                    <div class="container">
                        <?php if(empty($rainbow_tutor_instructor_registration_main_desc) ) { ?>
                        <hr class="rbt-separator mt-60">
                        <?php } else { ?>
                            <hr class="rbt-separator  m-0 mt--10">
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php endif;?>
            
            <div class="row pt--60 g-5 justify-content-center">
                <?php if(!empty($rainbow_tutor_instructor_registration_instructor_image_url) && (1 == $instructor_image_show)) : ?>
                <div class="<?php echo get_option( 'users_can_register' ) ? 'col-lg-4': 'col-lg-10'; ?>">
                    <div class="thumbnail">
                        <img class="radius-10 w-100 histudy-reg-img" src="<?php echo esc_html($rainbow_tutor_instructor_registration_instructor_image_url); ?>" alt="<?php echo esc_attr__('Corporate Template', 'histudy'); ?>">
                    </div>
                </div>
                <?php endif; ?>
                <?php if ( get_option( 'users_can_register' ) ) : ?>
                <div class="col-lg-8">
                    <div class="rbt-contact-form contact-form-style-1 max-width-auto">
                        <?php if(!empty($rainbow_tutor_instructor_registration_badge_title)) : ?>
                        <div class="section-title text-start">
                            <span class="subtitle bg-primary-opacity"><?php echo esc_html($rainbow_tutor_instructor_registration_badge_title); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if(!empty($rainbow_tutor_instructor_registration_main_title)) : ?>
                            <h3 class="title"><?php echo esc_html($rainbow_tutor_instructor_registration_main_title); ?></h3>
                        <?php endif; ?>
                        <hr class="mb--30">
                        <?php do_action( 'tutor_before_instructor_reg_form' ); ?>
                        <form method="post" enctype="multipart/form-data" id="tutor-registration-form" class="row row--15">
                            <?php do_action( 'tutor_instructor_reg_form_start' ); ?>
                            <?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
                            <input type="hidden" value="tutor_register_instructor" name="tutor_action"/>
                            <?php
                                $errors = apply_filters( 'tutor_instructor_register_validation_errors', array() );//phpcs:ignore
                                if ( is_array( $errors ) && count( $errors ) ) {
                                    echo '<div class="col-12"><div class="tutor-alert tutor-warning"><ul class="tutor-required-fields">';
                                    foreach ( $errors as $error_key => $error_value ) {
                                        echo wp_kses( "<li>{$error_value}</li>", array( 'li' => array() ) );
                                    }
                                    echo '</ul></div></div>';
                                }
                            ?>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <input type="text" name="first_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'first_name' ) ); ?>" required autocomplete="given-name">
                                    <label><?php esc_html_e( 'First Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <input type="text" name="last_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'last_name' ) ); ?>" required autocomplete="family-name">
                                    <label><?php esc_html_e( 'Last Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-group">
                                <input type="text" name="user_login" class="tutor_user_name" value="<?php echo esc_attr( tutor_utils()->input_old( 'user_login' ) ); ?>"  required autocomplete="username">
                                    <label><?php esc_html_e( 'User Name', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="text" name="email" value="<?php echo esc_attr( tutor_utils()->input_old( 'email' ) ); ?>"  required autocomplete="email">
                                    <label><?php esc_html_e( 'E-Mail', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="password" name="password" value="<?php echo esc_attr( tutor_utils()->input_old( 'password' ) ); ?>" required autocomplete="new-password">
                                    <label><?php esc_html_e( 'Password', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <input type="password" name="password_confirmation" value="<?php echo esc_attr( tutor_utils()->input_old( 'password_confirmation' ) ); ?>" required autocomplete="new-password">
                                    <label><?php esc_html_e( 'Password confirmation', 'histudy' ); ?></label>
                                    <span class="focus-border"></span>
                                </div>
                            </div>
                            <?php do_action( 'tutor_instructor_reg_form_end' ); ?>
                            <?php
                                $tutor_toc_page_link = tutor_utils()->get_toc_page_link();
                            ?>
                            <?php if ( null !== $tutor_toc_page_link ) : ?>
                                <div class="tutor-mb-24">
                                    <?php esc_html_e( 'By signing up, I agree with the website\'s', 'histudy' ); ?> <a target="_blank" href="<?php echo esc_url( $tutor_toc_page_link ); ?>" title="<?php esc_attr_e( 'Terms and Conditions', 'histudy' ); ?>"><?php esc_html_e( 'Terms and Conditions', 'histudy' ); ?></a>
                                </div>
                            <?php endif; ?>
                            <div class="col-lg-12">
                                <div class="form-submit-group">
                                    <button type="submit" name="tutor_register_instructor_btn" value="register" class="rbt-btn btn-md btn-gradient hover-icon-reverse w-100 tutor-btn tutor-btn-primary tutor-btn-block">
                                        <span class="icon-reverse-wrapper">
                                            <span class="btn-text"><?php esc_html_e( 'Register as instructor', 'histudy' ); ?></span>
                                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                            <?php do_action( 'tutor_after_register_button' ); ?>
                        </form>
                        <?php do_action( 'tutor_after_registration_form_wrap' ); ?>
                    </div>
                </div>
                <?php endif; ?>

            </div>
        </div>
    </div>

<?php endif;