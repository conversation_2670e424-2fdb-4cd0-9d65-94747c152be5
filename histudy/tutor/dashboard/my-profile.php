<?php
/**
 * My Profile Page
 *
 * @package Tutor\Templates
 * @subpackage Dashboard
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */

$uid  = get_current_user_id();
$user = get_userdata( $uid );

$profile_settings_link = tutor_utils()->get_tutor_dashboard_page_permalink( 'settings' );

$rdate = $user->user_registered;
$fname = $user->first_name;
$lname = $user->last_name;
$uname = $user->user_login;
$email = $user->user_email;

$phone = get_user_meta( $uid, 'phone_number', true );
$job   = nl2br( wp_strip_all_tags( get_user_meta( $uid, '_tutor_profile_job_title', true ) ) );
$bio   = get_user_meta( $uid, '_tutor_profile_bio', true );

$profile_data = array(
	array( __( 'Registration Date', 'histudy' ), ( $rdate ? tutor_i18n_get_formated_date( tutor_utils()->get_local_time_from_unix( $rdate ) ) : '' ) ),
	array( __( 'First Name', 'histudy' ), ( $fname ? $fname : esc_html__( '-', 'histudy' ) ) ),
	array( __( 'Last Name', 'histudy' ), ( $lname ? $lname : esc_html__( '-', 'histudy' ) ) ),
	array( __( 'Username', 'histudy' ), $uname ),
	array( __( 'Email', 'histudy' ), $email ),
	array( __( 'Phone Number', 'histudy' ), ( $phone ? $phone : esc_html__('-', 'histudy') ) ),
	array( __( 'Skill/Occupation', 'histudy' ), ( $job ? $job : esc_html__('-', 'histudy' ) ) ),
	array( __( 'Biography', 'histudy' ), $bio ? $bio : esc_html__('-', 'histudy' )),
);
?>
<div class="section-title">
    <h4 class="rbt-title-style-3"><?php esc_html_e( 'My Profile', 'histudy' ); ?></h4>
</div>
<div class="rbt-profile-wrapper-main-layout-1 body-color">
	<?php
		foreach ( $profile_data as $key => $data ) :
			?>
		<div class="rbt-profile-row row row--15 mt--15">
			<div class="col-lg-4 col-md-4">
				<span class="rbt-profile-content b2"><?php echo esc_html( $data[0] ); ?></span>
			</div>
			<div class="col-lg-8 col-md-8">
				<?php
				echo 'Biography' === $data[0] ?
						'<span class="rbt-profile-content b2">' . wp_kses_post( wpautop( $data[1] ) ) . '</span>'
						: '<span class="rbt-profile-content b2">' . esc_html( $data[1] ) . '</span>';
				?>
			</div>
		</div>
	<?php endforeach; ?>
</div>
