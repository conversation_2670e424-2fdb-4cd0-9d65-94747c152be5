<?php
/**
 * Instructor filter
 *
 * @package Tutor\Templates
 * @subpackage Shortcode
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 2.0.0
 */

$category_id      = '';
$total_categories = isset( $all_cats ) ? $all_cats : 0;
$categories       = isset( $categories ) ? $categories : array();
$limit            = 8;
$show_more        = false;
$short_by         = array(
	'relevant' => __( 'Relevant', 'histudy' ),
	'new'      => __( 'New', 'histudy' ),
	'popular'  => __( 'Popular', 'histudy' ),
);

if ( $total_categories && $total_categories > $limit ) {
	$show_more = true;
}

$columns = tutor_utils()->get_option( 'courses_col_per_row', 3 );
?>

<div class=" pt--120 pb--120 tutor-wrap tutor-wrap-parent container tutor-instructors" tutor-instructors 
<?php
foreach ( $attributes as $key => $value ) {
	if ( is_array( $value ) ) {
		continue;
	}
	echo esc_attr( 'data-' . $key . '="' . $value . '" ' );
}
?>
>
	<div class="row teacher-row-gutter">
		<aside class="col-lg-3 col-lg-3 col-md-4 col-12" tutor-instructors-filters>
			<div class="tutor-d-flex tutor-align-center justify-content-between">
				<div>
					<span class="tutor-icon-slider-vertical tutor-color-primary tutor-mr-8" ></span>
					<span class="tutor-fs-5 tutor-fw-medium tutor-color-black"><?php esc_html_e( 'Filters', 'histudy' ); ?></span>
				</div>

				<div class="tutor-ml-32">
					<a href="#" class="tutor-btn tutor-btn-ghost tutor-btn-ghost-custom" tutor-instructors-filter-clear>
						<span class="tutor-icon-times tutor-mr-8" ></span>
						<span class="tutor-fw-medium"><?php esc_html_e( 'Clear', 'histudy' ); ?></span>
					</a>
				</div>
			</div>

			<div class="tutor-widget tutor-widget-course-categories tutor-mt-48">
				<h3 class="tutor-widget-title">
					<?php esc_html_e( 'Category', 'histudy' ); ?>
				</h3>

				<div class="tutor-widget-content">
					<div class="<?php echo esc_attr($show_more) ? esc_attr__('tutor-toggle-more-content tutor-toggle-more-collapsed', 'histudy') : ''; ?>"<?php echo esc_attr($show_more) ? ' data-tutor-toggle-more-content data-toggle-height="200" style="height: 200px;"' : ''; ?>>
						<div class="tutor-list" tutor-instructors-filter-category>
							<?php foreach ( $categories as $category ) : ?>
								<div class="tutor-list-item">
									<label>
										<input id="tutor-instructor-checkbox-<?php echo esc_attr( $category->term_id ); ?>" type="checkbox" class="tutor-form-check-input" name="category" value="<?php echo esc_attr( $category->term_id ); ?>" />
										<?php echo esc_html( $category->name ); ?>
									</label>
								</div>
							<?php endforeach; ?>
						</div>
					</div>

					<?php if ( $show_more ) : ?>
						<a href="#" class="tutor-btn-show-more tutor-btn tutor-btn-ghost tutor-mt-32" data-tutor-toggle-more=".tutor-toggle-more-content">
							<span class="tutor-toggle-btn-icon tutor-icon tutor-icon-plus tutor-mr-8" ></span>
							<span class="tutor-toggle-btn-text"><?php esc_html_e( 'Show More', 'histudy' ); ?></span>
						</a>
					<?php endif; ?>
				</div>
			</div>

			<div class="tutor-widget tutor-widget-course-ratings tutor-mt-48">
				<h3 class="tutor-widget-title">
					<?php esc_html_e( 'Ratings', 'histudy' ); ?>
				</h3>

				<div class="tutor-widget-content">
				<div class="tutor-ratings tutor-ratings-lg tutor-ratings-selectable">
						<div class="tutor-ratings-stars">
							<?php for ( $i = 1; $i < 6; $i++ ) : ?>
								<i class="tutor-icon-star-line" tutor-instructors-filter-rating data-value="<?php echo esc_attr( $i ); ?>" ></i>
							<?php endfor; ?> 
						</div>
						<span class="tutor-ratings-count tutor-instructor-rating-filter" tutor-instructors-filter-rating-count></span>  
					</div>
				</div>
			</div>
		</aside>
		<main class="col-lg-9 col-lg-9 col-md-8 col-12">
			<div class="tutor-form-wrap tutor-mb-24">
                <div class="form-group w-100">
                    <input name="contact-name tutor-form-control" name="keyword" id="contact-name" type="text" tutor-instructors-filter-search>
                    <label><?php echo esc_html__('Search instructor...', 'histudy'); ?></label>
                    <span class="focus-border"></span>
                </div>
			</div>
			<div class="tutor-d-flex tutor-align-center tutor-mb-24">
				<div class="tutor-mr-16">
					<label for="tutor-instructor-relevant-sort" class="tutor-fs-6 tutor-color-muted">
						<?php esc_html_e( 'Sort by', 'histudy' ); ?>
					</label>
				</div>
				<div>
					<select class="tutor-form-control" id="tutor-instructor-relevant-sort" tutor-instructors-filter-sort>
						<?php foreach ( $short_by as $k => $v ) : ?>
							<option value="<?php echo esc_attr( $k ); ?>">
								<?php echo esc_html( $v ); ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>
			</div>

			<div tutor-instructors-content>
				<?php echo wp_kses_post( $content );//phpcs:ignore ?>
			</div>
		</main>
	</div>
</div>
