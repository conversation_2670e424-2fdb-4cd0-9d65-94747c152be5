<?php 
// course loop

remove_action( 'academy/templates/course_loop_header', 'academy_course_loop_header', 10 );
remove_action( 'academy/templates/course_loop_content', 'academy_course_loop_content', 11 );
remove_action( 'academy/templates/course_loop_footer', 'academy_course_loop_footer', 12 );
remove_action( 'academy/templates/archive_course_description', 'academy_archive_course_header_filter', 10 );

add_action("academy/templates/course_loop_header", "histudy_course_loop_header", 10 );
add_action("academy/templates/course_loop_content", "histudy_course_loop_content", 11 );
add_action("academy/templates/course_loop_footer", "histudy_course_loop_footer", 12 );
add_action( 'academy/templates/archive_course_description', 'histudy_archive_course_header_filter', 10 );

// course loop header functions

function histudy_course_loop_header() { 
    $card_style = Academy\Helper::get_settings( 'course_card_style' );
    $image_class = 'layout_two' === $card_style || 'layout_four' === $card_style ? 'academy-course-thumbnail-width' : '';

    $course_id = get_the_ID();
    if($course_id) {
        $course_percentage = Rainbow_Helper::rb_get_product_offer_percentage($course_id);
    }
    ?>
    <div class="rbt-card-img">
        <a href="<?php echo esc_url( get_the_permalink() ); ?>">
            <img class="academy-course__thumbnail-image <?php echo esc_html( $image_class ); ?>" src="<?php echo esc_url( Academy\Helper::get_the_course_thumbnail_url( 'academy_thumbnail' ) ); ?>" alt="<?php esc_html_e( 'thumbnail', 'academy' ); ?>">
            <?php if(!empty($course_percentage)) : ?>
            <div class="rbt-badge-3 bg-white">
                <span><?php echo esc_html( $course_percentage );?>%</span>
                <span><?php echo esc_html__("Off","histudy"); ?></span>
            </div>
            <?php endif; ?>
        </a>
    </div>

    <?php
}

// course loop content 

function histudy_course_loop_content() {

    global $wpdb;
    $content = get_the_excerpt();
    $content = apply_filters( 'the_content', $content );
    $content = wp_trim_words( get_the_excerpt(),  '12', '.' );
    $course_id = get_the_ID();
    $user_id   = get_current_user_id();

    $total_enrolled = \Academy\Helper::count_course_enrolled( $course_id );
    $student_count  = sprintf( _n( '%s Student', '%s Students', $total_enrolled, 'histudy' ), $total_enrolled );


    $total_lessons  = \Academy\Helper::get_total_number_of_course_lesson( $course_id );

    if( $total_lessons == 1 ) {
        $lesson = esc_html__('Lesson','histudy');
    } else {
        $lesson = esc_html__('Lessons','histudy');
    }

    $is_already_in_wishlist = $wpdb->get_row( $wpdb->prepare( "SELECT * from {$wpdb->usermeta} WHERE user_id = %d AND meta_key = 'academy_course_wishlist' AND meta_value = %d;", $user_id, $course_id ) );
    $is_show_wishlist = (bool) \Academy\Helper::get_settings( 'is_enabled_course_wishlist', true );
    $card_style = Academy\Helper::get_settings( 'course_card_style' );

    $wishlist_class = 'layout_two' === $card_style || 'layout_four' === $card_style ? 'academy-wishlist-dynamic' : '';

    $rating = \Academy\Helper::get_course_rating( $course_id );
    $reviews_status = Academy\Helper::get_settings( 'is_enabled_course_review', true );
    if( $rating->rating_count == 1) {
        $review_text = __('Review','histudy');
    } else {
        $review_text = __('Reviews','histudy');
    }


    ?>
    <div class="rbt-card-body">
        <div class="rbt-card-top">
            <?php
			if ( $reviews_status ) : ?>
            <div class="rbt-review">
                <div class="rating">
                <?php 
                    $rating_avg = $rating->rating_avg;
                    $fullStars = floor($rating_avg);  
                    $halfStar = ($rating_avg - $fullStars) >= 0.5 ? 1 : 0; 
                    $emptyStars = 5 - ($fullStars + $halfStar); 
                ?>
                    <?php for ($i = 0; $i < $fullStars; $i++): ?>
                        <i class="fas fa-star"></i> 
                    <?php endfor; ?>

                    <?php if ($halfStar): ?>
                        <i class="fas fa-star-half-alt"></i> 
                    <?php endif; ?>

                    <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                        <i class="far fa-star"></i> 
                    <?php endfor; ?>
                    
                    <?php
                
                    ?>
                </div>
                <span class="rating-count"><?php echo esc_html( '(' . $rating->rating_count ." " . $review_text .')' ); ?></span>
            </div>
            <?php 
            endif;
            if ( $is_show_wishlist ) :
                ?>
            
            <div class="rbt-bookmark-btn academy-course-header-meta">
                <?php 
                if ( $is_already_in_wishlist ) : 
                    ?>
                    <a  class="academy-course__wishlist academy-add-wishlist-btn rbt-round-btn" data-course-id="<?php echo esc_attr( get_the_ID() ); ?>"><i class="academy-icon academy-icon--heart-o" aria-hidden="true"></i></a>
                <?php else : ?>
                    <a  class="academy-course__wishlist academy-add-wishlist-btn rbt-round-btn" data-course-id="<?php echo esc_attr( get_the_ID() ); ?>"><i class="academy-icon academy-icon--heart-o" aria-hidden="true"></i></a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

        <h4 class="rbt-card-title"> 
            <a href="<?php echo esc_url( get_permalink( $course_id ) ); ?>">
                <?php the_title(); ?>
            </a>
        </h4>
        <ul class="rbt-meta">
            <li><i class="feather-book"></i>
            <?php echo esc_html( $total_lessons ); ?> <?php echo esc_html($lesson); ?></li>
            <li><i class="feather-users"></i> <?php echo esc_attr($student_count);?></li>
        </ul>
        <?php if ( $content ) { ?>
        <p class="rbt-card-text"><?php echo esc_attr( $content ); ?></p>  
        <?php } ?> 
    <?php
}

// Fix translation issues

function histudy_course_loop_footer() { 
    $course_id = get_the_ID();
    $categories = \Academy\Helper::get_the_course_category( get_the_ID() );
    global $authordata;
    ?>
        <div class="rbt-author-meta mb--10">
            <?php if(!empty( get_the_author())) : ?>
            <div class="rbt-avater">
                <img src="<?php echo esc_url( get_avatar_url( $authordata->ID, [ 'size' => '40' ] ) ); ?>" alt="">
            </div>
            <?php endif; ?>
            <?php if(!empty( get_the_author())) : ?>
            <div class="rbt-author-info">
                <?php esc_html_e('By', 'histudy'); ?> <a href="<?php echo esc_url( home_url( '/author/' . $authordata->user_nicename ) ); ?>"> <?php echo get_the_author(); ?></a> 
                <?php 
                if ( ! empty( $categories ) ) {
                        esc_html_e('In', 'histudy'); ?> 
                <a href="<?php echo esc_url( get_term_link( $categories[0]->term_id ) ); ?>"><?php echo esc_html( $categories[0]->name ); ?></a>
                <?php } ?>
            </div>
            <?php endif; ?>
        </div>
        <div class="rbt-card-bottom">
            <?php get_template_part('template-parts/components/price/layout', 1); ?>
            <a href="<?php echo esc_url( get_permalink( $course_id ) ); ?>"
                class="course_details"><?php esc_html_e( 'Enroll Course', 'histudy' ); ?>
                <i class="feather-arrow-right"></i>
            </a>
        </div>
    </div>
    <?php 
}

if ( ! function_exists( 'histudy_archive_course_header_filter' ) ) {
    function histudy_archive_course_header_filter() {
        global $wp_query;
        $orderby = ( get_query_var( 'orderby' ) ) ? get_query_var( 'orderby' ) : ''; ?>
        
        <div class="rbt-short-item">
            <div class="filter-select academy-courses__header-filter">
                <span class="select-label d-block"><?php esc_html_e('Sort By', 'histudy'); ?></span>
                <div class="filter-select rbt-modern-select search-by-category">
                    <form class="academy-courses__header-ordering" method="get">
                        <select name="orderby" class="academy-courses__header-orderby" aria-label="Course order"
                            onchange="this.form.submit()" data-size="7">
                            <option value="DESC" <?php selected( $orderby, 'DESC' ); ?>>
                                <?php esc_html_e( 'Default Sorting', 'histudy' ); ?>
                            </option>
                            <option value="menu_order" <?php selected( $orderby, 'menu_order' ); ?>>
                                <?php esc_html_e( 'Menu Order', 'histudy' ); ?>
                            </option>
                            <option value="name" <?php selected( $orderby, 'name' ); ?>>
                                <?php esc_html_e( 'Order by course name', 'histudy' ); ?>
                            </option>
                            <option value="date" <?php selected( $orderby, 'date' ); ?>>
                                <?php esc_html_e( 'Order by Publish Date', 'histudy' ); ?>
                            </option>
                            <option value="modified" <?php selected( $orderby, 'modified' ); ?>>
                                <?php esc_html_e( 'Order by Modified Date', 'histudy' ); ?>
                            </option>
                            <option value="ratings" <?php selected( $orderby, 'ratings' ); ?>>
                                <?php esc_html_e( 'Order by Most Reviews', 'histudy' ); ?>
                            </option>
                            <option value="ID" <?php selected( $orderby, 'ID' ); ?>>
                                <?php esc_html_e( 'Order by ID', 'histudy' ); ?>
                            </option>
                        </select>
                        <input type="hidden" name="paged" value="1">
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
}
