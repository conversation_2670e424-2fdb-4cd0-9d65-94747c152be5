<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

	academy_get_header( 'course' );

?>

<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

	academy_get_header( 'course' );
	$course_filter = get_query_var('course_filter_layout');
	$sidebar_position = get_query_var('sidebar_position');

	if(!empty($sidebar_position)) {
		$sidebar_position = get_query_var('sidebar_position');
		
	} else {
		$sidebar_position = \Academy\Helper::get_settings( 'course_archive_sidebar_position', 'right' );
	}

?>

<?php
	/**
	 * @hook - academy/templates/before_main_content
	 */
	do_action( 'academy/templates/before_main_content', 'archive-course.php' );
?>

<div class="academy-courses academy-courses--archive rbt-section-overlayping-top has-filter-layout rbt-section-gapBottom course-layout tutor-course-archive-page">
	<div class="inner">
		<div class="academy-container container">
			<div class="academy-row  <?php echo esc_attr( 'none' === $sidebar_position ? 'rbt-course-grid-column' : 'row--30 gy-5' ); ?>">
				
				<?php
				if ( 'left' === $sidebar_position ) :
					?>
					<div class="academy-col-md-3"><?php do_action( 'academy/templates/archive_course_sidebar' ); ?></div> 
					<?php
					endif;
				?>
				<div class="<?php echo esc_attr( 'none' === $sidebar_position ? 'academy-col-12 academy-course-large-col' : 'academy-col-md-9 academy-course-large-col' ); ?>"><?php do_action( 'academy/templates/archive_course_content' ); ?></div>
				<?php
				if ( 'right' === $sidebar_position ) :
					?>
					<div class="academy-col-md-3"><?php do_action( 'academy/templates/archive_course_sidebar' ); ?></div> 
					<?php
					endif;
				?>
				<div class="academy-col-12"><?php do_action( 'academy/templates/archive_course_footer' ); ?>
				</div>
			</div>
		</div>
	</div>
</div>

<?php
	/**
	 * @hook - academy/templates/after_main_content
	 */
	do_action( 'academy/templates/after_main_content', 'archive-course.php' );
?>

<?php
academy_get_footer( 'course' );
?>