<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package histudy
 */
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

get_header();
$rainbow_options = Rainbow_Helper::rainbow_get_options(); ?>
<div class="rbt-error-area bg-gradient-11 rbt-section-gap">
    <div class="error-area">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-10">
                    <?php if (!empty($rainbow_options['rainbow_404_title'])) { ?> <h1
                            class="title"><?php echo esc_html($rainbow_options['rainbow_404_title']); ?></h1> <?php } ?>
                    <?php if (!empty($rainbow_options['rainbow_404_subtitle'])) { ?>
                        <h3 class="sub-title"><?php echo esc_html($rainbow_options['rainbow_404_subtitle']); ?></h3> <?php } ?>
                    <?php if (!empty($rainbow_options['rainbow_404_content'])) { ?>
                        <p><?php echo esc_html($rainbow_options['rainbow_404_content']); ?></p> <?php } ?>

                    <?php if ($rainbow_options['rainbow_enable_go_back_btn'] !== "no") { ?>
                        <a class="rbt-btn btn-gradient icon-hover" href="<?php echo esc_url(home_url('/')); ?>">
                            <span class="btn-text"><?php echo esc_html($rainbow_options['rainbow_button_text']); ?></span>
                            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
                        </a>
                    <?php } ?>

                </div>
            </div>
        </div>
    </div>
</div><!-- End Error Area  -->
<?php 
if( function_exists( 'tutor' ) ) {
    tutor_load_template_from_custom_path( tutor()->path . '/views/modal/login.php' );
}
get_footer();