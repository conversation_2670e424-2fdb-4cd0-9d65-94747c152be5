#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Histudy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-14 06:01+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.11; wp-6.7\n"
"X-Domain: histudy"

#: template-parts/components/tutor/sidebar/layout-1.php:157
msgid " 30-Day Money-Back Guarantee"
msgstr ""

#: inc/comment-form.php:63 inc/comment-form.php:137
msgid " <i class=\"rbt feather-corner-down-right\"></i> Reply"
msgstr ""

#: inc/helper/meta-trait.php:85
msgid " in"
msgstr ""

#: tutor/dashboard/enrolled-courses.php:56
msgid " is-active"
msgstr ""

#: inc/global-functions.php:42
msgid " min read"
msgstr ""

#: tutor/loop/add-to-cart-2-woocommerce.php:87
msgid " rbt-btn btn-gradient icon-hover w-100 d-block text-center"
msgstr ""

#: template-parts/components/card/layout-5.php:96
msgid " Review"
msgid_plural " Reviews"
msgstr[0] ""
msgstr[1] ""

#: inc/helper/meta-trait.php:38 inc/helper/meta-trait.php:120
msgid "% Comments"
msgstr ""

#: inc/comment-form.php:43 inc/comment-form.php:112
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#. 1: reviews count 2: product name
#: woocommerce/single-product-reviews.php:33
#, php-format
msgid "%1$s review for %2$s"
msgid_plural "%1$s reviews for %2$s"
msgstr[0] ""
msgstr[1] ""

#: template-parts/components/tutor/sidebar/layout-1.php:28
#, php-format
msgid "%s"
msgid_plural "%s"
msgstr[0] ""
msgstr[1] ""

#: template-parts/v3/banner/layout/layout-deprecated.php:33
#, php-format
msgid "%s %s"
msgid_plural "%s %s"
msgstr[0] ""
msgstr[1] ""

#: ajax_handler.php:106
#, php-format
msgid "%s ago"
msgstr ""

#: ajax_handler.php:189 learnpress/custom/content-box-masonary.php:96
#: learnpress/custom/content-box.php:96 tutor/loop/meta.php:29
#: template-parts/components/card/layout-1.php:74
#: template-parts/components/card/layout-1.php:181
#: template-parts/components/card/layout-3.php:65
#: template-parts/components/card/layout-4.php:39
#: template-parts/components/card/layout-5.php:34
#: template-parts/components/card/layout-6.php:63
#: template-parts/components/card/layout-7.php:55
#: template-parts/components/card/layout-8.php:67
#: template-parts/components/card/layout-9.php:55
#: template-parts/components/tutor/dashbord/card.php:10
#: template-parts/components/tutor/sidebar/layout-1.php:83
#, php-format
msgid "%s Lesson"
msgid_plural "%s Lessons"
msgstr[0] ""
msgstr[1] ""

#: template-parts/content-banner.php:133
#, php-format
msgid "%s Product"
msgid_plural "%s Products"
msgstr[0] ""
msgstr[1] ""

#. %s: Quantity.
#: woocommerce/global/quantity-input.php:23
#, php-format
msgid "%s quantity"
msgstr ""

#: learnpress/custom/content-box-masonary.php:46
#: learnpress/custom/content-box.php:46 learnpress/custom/lp-functions.php:269
#: tutor/loop/meta.php:9 template-parts/components/card/layout-1.php:42
#: template-parts/components/card/layout-1.php:155
#: template-parts/components/card/layout-3.php:33
#: template-parts/components/card/layout-4.php:43
#: template-parts/components/card/layout-5.php:39
#: template-parts/components/card/layout-6.php:32
#: template-parts/components/card/layout-7.php:31
#: template-parts/components/card/layout-8.php:33
#: template-parts/components/card/layout-9.php:31
#: template-parts/components/tutor/dashbord/card.php:7
#: template-parts/components/tutor/sidebar/layout-1.php:35
#, php-format
msgid "%s Student"
msgid_plural "%s Students"
msgstr[0] ""
msgstr[1] ""

#: template-parts/single-post/content-single-related-post.php:46
#, php-format
msgid "%s View"
msgid_plural "%s Views"
msgstr[0] ""
msgstr[1] ""

#. used between list items, there is a space after the comma
#: inc/underscore/template-tags.php:100
msgid ", "
msgstr ""

#: tutor/dashboard/my-profile.php:29 tutor/dashboard/my-profile.php:30
#: tutor/dashboard/my-profile.php:33 tutor/dashboard/my-profile.php:34
#: tutor/dashboard/my-profile.php:35
msgid "-"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course.php:154
msgid "-- Select Category --"
msgstr ""

#: inc/global-functions.php:78
msgid "--- Select ---"
msgstr ""

#: template-parts/components/card/layout-5.php:98
msgid "0 Review"
msgstr ""

#: inc/helper/meta-trait.php:38 inc/helper/meta-trait.php:120
msgid "1 Comment"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:110
#: template-parts/components/tutor/filter/filter-archive-course-3.php:112
#: template-parts/components/tutor/filter/filter-archive-course.php:97
msgid "1 STAR"
msgstr ""

#: inc/options/menu-options.php:102
msgid "2 Column"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:96
#: template-parts/components/tutor/filter/filter-archive-course-3.php:98
#: template-parts/components/tutor/filter/filter-archive-course.php:83
msgid "2 STAR"
msgstr ""

#: inc/options/page-options.php:546
#: template-parts/header/header-top/header-top-address.php:19
msgid "20th New York, ND 8545, USA"
msgstr ""

#: inc/options/menu-options.php:103
msgid "3 Column"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:83
#: template-parts/components/tutor/filter/filter-archive-course-3.php:85
#: template-parts/components/tutor/filter/filter-archive-course.php:70
msgid "3 STAR"
msgstr ""

#: inc/options/user-extra-meta.php:1477
msgid "30-Day Money-Back Guarantee"
msgstr ""

#: inc/options/menu-options.php:104
msgid "4 Column"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:70
#: template-parts/components/tutor/filter/filter-archive-course-3.php:72
#: template-parts/components/tutor/filter/filter-archive-course.php:57
msgid "4 STAR"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:57
#: template-parts/components/tutor/filter/filter-archive-course-3.php:59
#: template-parts/components/tutor/filter/filter-archive-course.php:44
msgid "5 STAR"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:74
msgid "9 Reviews"
msgstr ""

#: inc/global-functions.php:122 inc/global-functions.php:171
msgid "<i class=\"feather-chevron-left\"></i>"
msgstr ""

#: inc/global-functions.php:123 inc/global-functions.php:172
msgid "<i class=\"feather-chevron-right\"></i>"
msgstr ""

#: inc/options/portfolio-options.php:181
msgid "A custom URL"
msgstr ""

#: woocommerce/myaccount/form-login.php:117
msgid "A link to set a new password will be sent to your email address."
msgstr ""

#: inc/options/post-and-category-options.php:176
msgid "Above the post content"
msgstr ""

#: tutor/dashboard/registration.php:67
#: tutor/dashboard/instructor/registration.php:73
msgid "active"
msgstr ""

#: tutor/dashboard/dashboard.php:180
msgid "ACTIVE COURSES"
msgstr ""

#: tutor/dashboard/enrolled-courses.php:21
msgid "Active Courses"
msgstr ""

#: inc/options/theme/generic-theme-options.php:141
msgid "Active Plugins:"
msgstr ""

#: tutor/dashboard/registration.php:76
#: tutor/dashboard/instructor/registration.php:82
msgid "active show"
msgstr ""

#. %s is product title
#: woocommerce/single-product-reviews.php:74
msgid "Add a review"
msgstr ""

#: inc/options/post-and-category-options.php:431
msgid "Add Cons"
msgstr ""

#: inc/options/portfolio-options.php:52
msgid "Add More"
msgstr ""

#: inc/options/post-and-category-options.php:427
msgid "Add New Cons"
msgstr ""

#: inc/options/team-extra-meta.php:44 inc/options/user-extra-meta.php:1284
msgid "Add New Network"
msgstr ""

#: inc/options/post-and-category-options.php:379
msgid "Add new Pors"
msgstr ""

#: inc/options/post-and-category-options.php:383
msgid "Add Pors"
msgstr ""

#: inc/options/user-extra-meta.php:1269
msgid "Add Social Icons"
msgstr ""

#: inc/options/team-extra-meta.php:29
msgid "Add Social Network"
msgstr ""

#: tutor/loop/add-to-cart-2-woocommerce.php:75
#: template-parts/components/card/layout-1.php:270
#: template-parts/components/card/layout-3.php:148
#: template-parts/components/card/layout-4.php:169
#: template-parts/components/card/layout-5.php:155
#: template-parts/components/card/layout-6.php:115
#: template-parts/components/card/layout-7.php:118
#: template-parts/components/card/layout-8.php:114
#: template-parts/components/card/layout-9.php:115
msgid "Add to cart"
msgstr ""

#: learnpress/addons/wishlist/button.php:25
msgid "Add to Wishlist"
msgstr ""

#: inc/widget-area-register.php:14 inc/widget-area-register.php:24
#: inc/widget-area-register.php:33
msgid "Add widgets here."
msgstr ""

#: comments.php:46
msgid "Add Your Comment"
msgstr ""

#: woocommerce/checkout/form-shipping.php:57
msgid "Additional information"
msgstr ""

#: inc/options/page-options.php:563
msgid "Address URL"
msgstr ""

#: inc/options/user-extra-meta.php:498
msgid "Advance"
msgstr ""

#: inc/tgm-config.php:46
msgid "Advanced Custom Fields Pro"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:164
#: template-parts/components/tutor/filter/filter-archive-course-3.php:162
#: template-parts/components/tutor/filter/filter-archive-course.php:142
msgid "All"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:175
#: template-parts/components/page-banners/course/layout-3.php:172
msgid "All Course"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:189
#: template-parts/components/tutor/filter/filter-archive-course-3.php:185
msgid "All Levels"
msgstr ""

#: inc/helper/page-title-trait.php:28
msgid "All Posts"
msgstr ""

#: inc/customizer/color.php:60
msgid "Allows you to customize some example settings for histudy."
msgstr ""

#: woocommerce/cart/cart.php:169 woocommerce/cart/cart.php:170
msgid "Apply coupon"
msgstr ""

#. Name of the template
#: templates/template-archive-1.php:2
msgid "Archive style 1"
msgstr ""

#. Name of the template
#: templates/template-archive-2.php:2
msgid "Archive style 2"
msgstr ""

#. Name of the template
#: templates/template-archive-3.php:2
msgid "Archive style 3"
msgstr ""

#. Name of the template
#: templates/template-archive-4.php:2
msgid "Archive style 4"
msgstr ""

#: template-parts/breadcrumb.php:118 template-parts/breadcrumb.php:122
#: template-parts/breadcrumb.php:126 template-parts/breadcrumb.php:133
#: template-parts/breadcrumb.php:137 template-parts/breadcrumb.php:142
msgid "Archives"
msgstr ""

#: tutor/dashboard/my-courses.php:294
msgid ""
"Are you sure you want to delete this course permanently from the site? "
"Please confirm your choice."
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:153
msgid ""
"Are you sure you want to delete this review permanently from the site? "
"Please confirm your choice."
msgstr ""

#: inc/helper/helper.php:190
msgid "Articles"
msgstr ""

#: inc/options/post-format-options.php:6
msgid "Audio Post Options"
msgstr ""

#: inc/options/woo-options.php:6 inc/options/woo-options.php:10
msgid "Author By"
msgstr ""

#: template-parts/breadcrumb.php:153
msgid "Author: "
msgstr ""

#: woocommerce/cart/cart.php:114
msgid "Available on backorder"
msgstr ""

#: woocommerce/single-product-reviews.php:127
#: woocommerce/custom/wooc-functions.php:264
msgid "Average"
msgstr ""

#: learnpress/custom/lp-functions.php:176
msgid "Average Ratings"
msgstr ""

#: inc/options/menu-options.php:11
msgid "Badge"
msgstr ""

#: inc/options/page-options.php:649
msgid "Banner Layout 1"
msgstr ""

#: inc/options/page-options.php:650
msgid "Banner Layout 2"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:120
#: template-parts/components/page-banners/course/layout-3.php:117
msgid "basic-addon2"
msgstr ""

#: woocommerce/single-product-reviews.php:74
#, php-format
msgid "Be the first to review &ldquo;%s&rdquo;"
msgstr ""

#: inc/options/user-extra-meta.php:496
#: template-parts/components/tutor/filter/filter-archive-course-2.php:193
#: template-parts/components/tutor/filter/filter-archive-course-3.php:189
msgid "Beginner"
msgstr ""

#: inc/helper/social-trait.php:105
msgid "Behance"
msgstr ""

#: tutor/public-profile.php:225
msgid "Best Seller Icon"
msgstr ""

#: tutor/public-profile.php:225 learnpress/custom/lp-functions.php:314
msgid "Bestseller"
msgstr ""

#: tutor/public-profile.php:184 tutor/dashboard/my-profile.php:35
msgid "Biography"
msgstr ""

#: inc/options/page-options.php:412
msgid "Blank"
msgstr ""

#. Name of the template
#: templates/blank.php:2
msgid "Blank Template"
msgstr ""

#: template-parts/breadcrumb.php:169
msgid "Blog "
msgstr ""

#: single.php:33 single.php:37
msgid "blog banner background shape images"
msgstr ""

#: template-parts/components/tutor/dashbord/card-top.php:37
msgid "bold"
msgstr ""

#: inc/options/page-options.php:454
msgid "Border Button"
msgstr ""

#: inc/options/post-and-category-options.php:73
msgid "Boxed Banner"
msgstr ""

#: inc/options/page-options.php:728
msgid "Breadcrumbs Enable"
msgstr ""

#: tutor/dashboard/dashboard.php:411
msgid "Browse All Course"
msgstr ""

#: inc/customizer/color.php:204
msgid "Button Border Style Gradient -1"
msgstr ""

#: inc/customizer/color.php:227
msgid "Button Border Style Gradient -2"
msgstr ""

#: inc/options/page-options.php:401
msgid "Button Target"
msgstr ""

#: inc/options/page-options.php:508
msgid "Button Text"
msgstr ""

#: inc/options/page-options.php:441
msgid "Button Type"
msgstr ""

#: inc/options/page-options.php:479
msgid "Button Url"
msgstr ""

#: inc/options/portfolio-options.php:205
msgid "Button URL ( View Project )"
msgstr ""

#: learnpress/custom/purchase.php:21
#: learnpress/single-course/buttons/purchase.php:21
#: template-parts/components/popups/course-details-list-card.php:37
#: template-parts/components/tutor/sidebar/layout-1.php:145
msgid "Buy Now"
msgstr ""

#: learnpress/custom/content-box-masonary.php:152
#: learnpress/custom/content-box.php:142 learnpress/custom/lp-functions.php:351
#: tutor/loop/meta.php:50 template-parts/components/card/layout-1.php:255
#: template-parts/components/card/layout-1.php:328
#: template-parts/components/card/layout-3.php:133
#: template-parts/components/card/layout-4.php:154
#: template-parts/components/card/layout-5.php:140
#: woocommerce/custom/template-parts/content-shop-thumb.php:30
msgid "By"
msgstr ""

#: inc/options/page-options.php:170
msgid "By default works primary location menu."
msgstr ""

#: tutor/dashboard/registration.php:179
#: tutor/dashboard/instructor/registration.php:192
msgid "By signing up, I agree with the website's"
msgstr ""

#: woocommerce/content-single-product.php:69
msgid "By:"
msgstr ""

#: tutor/dashboard/my-courses.php:298
#: tutor/dashboard/reviews/given-reviews.php:134
msgid "Cancel"
msgstr ""

#: tutor/dashboard/my-courses.php:259
msgid "Cancel Submission"
msgstr ""

#: functions.php:700
msgid "card removed"
msgstr ""

#: archive.php:48 author.php:42 index.php:52 search.php:39
msgid "card-boxed"
msgstr ""

#: archive.php:54 author.php:46 index.php:58 search.php:43
msgid "card-minimal"
msgstr ""

#: inc/custom-mobile-footer.php:54
#: template-parts/header/elements/cart-text-icon.php:18
msgid "Cart"
msgstr ""

#: woocommerce/content-single-product.php:86
msgid "Categories: "
msgstr ""

#: inc/custom-mobile-footer.php:65 tutor/shortcode/instructor-filter.php:58
#: template-parts/header/elements/category-menu.php:22
msgid "Category"
msgstr ""

#: inc/options/post-and-category-options.php:10
msgid "Category Background Image"
msgstr ""

#: functions.php:103
msgid "Category Menu"
msgstr ""

#: template-parts/header/elements/category-menu.php:57
msgid "Category Submenu Items"
msgstr ""

#: woocommerce/custom/template-parts/content-product-meta.php:12
msgid "Category:"
msgid_plural "Categories:"
msgstr[0] ""
msgstr[1] ""

#: inc/options/portfolio-options.php:24
msgid "Center Media"
msgstr ""

#: woocommerce/cart/proceed-to-checkout-button.php:25
#: woocommerce/custom/wooc-hooks.php:155
msgid "Checkout"
msgstr ""

#: inc/options/team-extra-meta.php:32 inc/options/user-extra-meta.php:1272
msgid "Choose your icon markup here: https://fontawesome.com/icons"
msgstr ""

#: tutor/shortcode/instructor-filter.php:51
msgid "Clear"
msgstr ""

#: tutor/dashboard/dashboard.php:123
msgid "Click Here"
msgstr ""

#: tutor/dashboard/my-courses.php:103
msgid "Co-author"
msgstr ""

#: index.php:44 tutor/public-profile.php:186
msgid "col-12"
msgstr ""

#: archive.php:37 archive.php:40 author.php:28 author.php:31 index.php:36
#: search.php:28 search.php:31
msgid "col-12 histudy-post-wrapper"
msgstr ""

#: index.php:42
msgid "col-lg-10 histudy-post-wrapper"
msgstr ""

#: single-rainbow_projects.php:16
msgid "col-lg-12 rainbow-post-wrapper"
msgstr ""

#: tutor/public-profile.php:186
msgid "col-lg-8"
msgstr ""

#: archive.php:37 author.php:28 index.php:42 search.php:28
msgid "col-lg-8 histudy-post-wrapper"
msgstr ""

#: single-rainbow_projects.php:16
msgid "col-lg-8 rainbow-post-wrapper"
msgstr ""

#: inc/customizer/color.php:158
msgid "Colar Violet"
msgstr ""

#: inc/customizer/color.php:181
msgid "Color Pink"
msgstr ""

#: inc/helper/helper.php:90
msgid "Comment"
msgstr ""

#: inc/helper/helper.php:91
msgid "Comments"
msgstr ""

#: comments.php:129
msgid "Comments are closed."
msgstr ""

#: inc/helper/meta-trait.php:38 inc/helper/meta-trait.php:120
msgid "Comments off"
msgstr ""

#. 1: number of comments, 2: post title
#: comments.php:98
#, php-format
msgctxt "comments title"
msgid "%1$s reply on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s replies on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#. %s: post title
#: comments.php:94
#, php-format
msgctxt "comments title"
msgid "One reply on &ldquo;%s&rdquo;"
msgstr ""

#: tutor/dashboard/dashboard.php:317
msgid "Complete"
msgstr ""

#: tutor/dashboard/dashboard.php:36
msgid "Complete Your Profile"
msgstr ""

#: tutor/dashboard/dashboard.php:194
msgid "COMPLETED COURSES"
msgstr ""

#: tutor/dashboard/enrolled-courses.php:22
msgid "Completed Courses"
msgstr ""

#: tutor/dashboard/dashboard.php:295
msgid "Completed Lessons:"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:64
msgid "Congratulations! You are now registered as an instructor."
msgstr ""

#: inc/options/post-and-category-options.php:404
msgid "Cons"
msgstr ""

#: inc/tgm-config.php:25
msgid "Contact Form 7"
msgstr ""

#: learnpress/single-course/buttons/continue.php:19
msgid "Continue"
msgstr ""

#: inc/options/page-options.php:1100
msgid "Copyright Text"
msgstr ""

#: inc/customizer/color.php:135
msgid "Coral Color"
msgstr ""

#: tutor/dashboard/registration.php:91
#: tutor/dashboard/instructor/registration.php:109
msgid "Corporate Template"
msgstr ""

#: woocommerce/cart/cart.php:169
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:169
msgid "Coupon:"
msgstr ""

#: tutor/public-profile.php:137
#: learnpress/custom/instructor-tab-contents.php:25
msgid "Course"
msgstr ""

#: template-parts/breadcrumb.php:58
msgid "Course Bundle"
msgstr ""

#: template-parts/components/card/layout-1.php:206
#: template-parts/components/card/layout-2.php:58
#: template-parts/components/card/layout-3.php:91
#: template-parts/components/card/layout-4.php:102
#: template-parts/components/card/layout-5.php:82
#: template-parts/components/card/layout-6.php:81
#: template-parts/components/card/layout-7.php:76
#: template-parts/components/card/layout-8.php:82
#: template-parts/components/card/layout-9.php:76
msgid "course bundle"
msgstr ""

#: tutor/public-profile.php:161
msgid "Course Completed"
msgstr ""

#: learnpress/custom/lp-functions.php:455
msgid "Course Duration:"
msgstr ""

#: template-parts/components/tutor/sidebar/layout-1.php:200
msgid "Course Duration"
msgstr ""

#: tutor/public-profile.php:156
msgid "Course Enrolled"
msgstr ""

#: tutor/dashboard/dashboard.php:365
msgid "Course Name"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:52
msgid "Course Title"
msgstr ""

#: tutor/dashboard/reviews.php:91
msgid "Course:"
msgstr ""

#: ajax_handler.php:98 tutor/dashboard/reviews/given-reviews.php:66
msgid "Course: "
msgstr ""

#: inc/custom-mobile-footer.php:46 template-parts/breadcrumb.php:52
#: template-parts/breadcrumb.php:55 tutor/public-profile.php:137
#: tutor/public-profile.php:240
#: learnpress/custom/instructor-tab-contents.php:25
msgid "Courses"
msgstr ""

#: tutor/public-profile.php:161
msgid "Courses Completed"
msgstr ""

#: tutor/public-profile.php:156
msgid "Courses Enrolled"
msgstr ""

#: tutor/dashboard.php:129 tutor/dashboard.php:154
msgid "Create a New Course"
msgstr ""

#: inc/options/portfolio-options.php:111
#: inc/options/post-format-options.php:104
msgid "Custom Link"
msgstr ""

#: inc/options/page-options.php:702
msgid "Custom Sub Title"
msgstr ""

#: inc/options/page-options.php:672
msgid "Custom Title"
msgstr ""

#: inc/options/menu-options.php:106
msgid "Custom Width (Work form elementor)"
msgstr ""

#: functions.php:161 header.php:59
msgid "Dark"
msgstr ""

#: tutor/dashboard.php:68 tutor/dashboard/dashboard.php:136
msgid "Dashboard"
msgstr ""

#: tutor/dashboard/reviews.php:60
msgid "Date"
msgstr ""

#: learnpress/custom/lp-functions.php:431
#: learnpress/custom/lp-functions.php:583
msgid "Day"
msgstr ""

#: learnpress/custom/lp-functions.php:431
#: learnpress/custom/lp-functions.php:583
msgid "Days"
msgstr ""

#: inc/options/page-options.php:129 inc/options/page-options.php:648
#: inc/options/page-options.php:873 inc/options/single-product.php:22
#: inc/underscore/template-functions.php:121
#: template-parts/components/page-banners/course/layout-2.php:131
#: template-parts/components/page-banners/course/layout-3.php:129
#: template-parts/components/tutor/filter/filter-archive-course-2.php:218
#: template-parts/components/tutor/filter/filter-archive-course-3.php:212
#: template-parts/components/tutor/filter/filter-archive-course.php:111
msgid "Default"
msgstr ""

#: archive.php:38 author.php:29 search.php:29
msgid "default"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:130
#: template-parts/components/page-banners/course/layout-3.php:128
#: template-parts/components/tutor/filter/filter-archive-course.php:110
#: template-parts/components/tutor/filter/filter-archive-course.php:141
msgid "Default select example"
msgstr ""

#: tutor/dashboard/my-courses.php:268
#: tutor/dashboard/reviews/given-reviews.php:88
msgid "Delete"
msgstr ""

#: tutor/dashboard/my-courses.php:293
msgid "Delete This Course?"
msgstr ""

#: inc/scripts.php:153
msgid "Deselect All"
msgstr ""

#: inc/options/team-extra-meta.php:10 inc/options/user-extra-meta.php:1250
msgid "Designation"
msgstr ""

#: template-parts/header/elements/header-top-switcher-language.php:33
#: template-parts/header/header-top/header-top-switcher-language.php:29
msgid "Deutsch"
msgstr ""

#: inc/options/team-extra-meta.php:22 inc/options/user-extra-meta.php:1262
msgid "Developer"
msgstr ""

#: inc/options/theme/generic-theme-options.php:97
msgid "Disabled"
msgstr ""

#: inc/helper/social-trait.php:90
msgid "Discord"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:152
msgid "Do You Want to Delete This Review?"
msgstr ""

#: learnpress/single-course/buttons/retry.php:25
msgid "Do you want to retake the course"
msgstr ""

#: tutor/dashboard/my-courses.php:65
msgid "Draft"
msgstr ""

#: inc/helper/social-trait.php:100
msgid "Dribbble"
msgstr ""

#: tutor/dashboard/my-courses.php:221
msgid "Duplicate"
msgstr ""

#: learnpress/single-course/sidebar/user-time.php:41
msgid "Duration:"
msgstr ""

#: tutor/dashboard/registration.php:153
#: tutor/dashboard/instructor/registration.php:166
msgid "E-Mail"
msgstr ""

#: learnpress/content-lesson/content.php:27
#: tutor/dashboard/reviews/given-reviews.php:83
msgid "Edit"
msgstr ""

#. %s: Name of current post. Only visible to screen readers
#: inc/underscore/template-tags.php:137
#, php-format
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr ""

#: template-parts/content-breadcrumb.php:17
#: template-parts/content-breadcrumb.php:21
#: learnpress/custom/lp-functions.php:294
#: learnpress/custom/lp-functions.php:298
msgid "Education Images"
msgstr ""

#: inc/tgm-config.php:59
msgid "Elementor Page Builder"
msgstr ""

#: woocommerce/single-product-reviews.php:94
#: template-parts/popup/popup-content-lost-password.php:12
#: tutor/dashboard/my-profile.php:32
msgid "Email"
msgstr ""

#: comments.php:64
msgid "Email "
msgstr ""

#: woocommerce/custom/wooc-functions.php:253
msgid "Email *"
msgstr ""

#: woocommerce/myaccount/form-login.php:105
msgid "Email address"
msgstr ""

#: inc/options/menu-options.php:32
msgid "Enable Mega Menu"
msgstr ""

#: inc/options/post-and-category-options.php:348
msgid "Enable Pors and Cons on this post"
msgstr ""

#: inc/options/post-and-category-options.php:147
msgid "Enable review on this post"
msgstr ""

#: inc/options/theme/generic-theme-options.php:97
msgid "Enabled"
msgstr ""

#: learnpress/custom/lp-functions.php:412
#: template-parts/header/elements/header-top-switcher-language.php:20
#: template-parts/header/header-top/header-top-switcher-language.php:16
#: template-parts/components/tutor/sidebar/layout-1.php:46
msgid "English"
msgstr ""

#: template-parts/components/card/layout-1.php:342
msgid "Enroll Course"
msgstr ""

#: learnpress/custom/lp-functions.php:444 tutor/dashboard/dashboard.php:368
#: template-parts/components/tutor/sidebar/layout-1.php:175
msgid "Enrolled"
msgstr ""

#: tutor/dashboard/dashboard.php:166 tutor/dashboard/enrolled-courses.php:20
msgid "Enrolled Courses"
msgstr ""

#: learnpress/single-course/sidebar/user-time.php:36
msgid "Enrollment valid until:"
msgstr ""

#: inc/options/team-extra-meta.php:67 inc/options/user-extra-meta.php:1307
msgid "Enter Social Icon Link"
msgstr ""

#: inc/options/team-extra-meta.php:48 inc/options/user-extra-meta.php:1288
msgid "Enter Social Icon Markup"
msgstr ""

#: template-parts/breadcrumb.php:167
msgid "Error 404"
msgstr ""

#: template-parts/header/elements/header-top-switcher-currency.php:26
msgid "EUR"
msgstr ""

#: template-parts/content-event-banner.php:44
msgid "Events"
msgstr ""

#: template-parts/breadcrumb.php:172
msgid "Events "
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:201
#: template-parts/components/tutor/filter/filter-archive-course-3.php:197
msgid "Expert"
msgstr ""

#: inc/helper/social-trait.php:20
msgid "Facebook"
msgstr ""

#: tutor/dashboard/reviews.php:63
msgid "Feedback"
msgstr ""

#: learnpress/custom/lp-functions.php:155
#: template-parts/components/page-banners/course/layout-3.php:140
#: template-parts/v3/banner/layout/layout-2.php:55
msgid "Filter"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:142
#: template-parts/components/page-banners/course/layout-2.php:148
#: template-parts/components/page-banners/course/layout-3.php:146
msgid "FILTERS"
msgstr ""

#: tutor/shortcode/instructor-filter.php:45
msgid "Filters"
msgstr ""

#: learnpress/single-course/buttons/finish.php:15
#: learnpress/single-course/buttons/finish.php:17
msgid "Finish course"
msgstr ""

#: tutor/dashboard/my-profile.php:29 tutor/dashboard/registration.php:130
#: tutor/dashboard/instructor/registration.php:143
msgid "First Name"
msgstr ""

#: woocommerce/myaccount/form-login.php:97
#: woocommerce/myaccount/form-login.php:104
msgid "focused"
msgstr ""

#: inc/options/page-options.php:754
msgid "Footer"
msgstr ""

#: inc/widget-area-register.php:42
msgid "Footer 1"
msgstr ""

#: inc/widget-area-register.php:43
msgid "Footer 2"
msgstr ""

#: inc/widget-area-register.php:44
msgid "Footer 3"
msgstr ""

#: inc/widget-area-register.php:45
msgid "Footer 4"
msgstr ""

#: inc/options/page-options.php:903
msgid "Footer About Social ON/Off"
msgstr ""

#: inc/widget-area-register.php:84
msgid "Footer Bottom Additional Data"
msgstr ""

#: functions.php:101
msgid "Footer Bottom Menu (No depth supported)"
msgstr ""

#: inc/options/page-options.php:1000
msgid "Footer Get Contact Address Label Text"
msgstr ""

#: inc/options/page-options.php:968
msgid "Footer Get Contact Address Label Title"
msgstr ""

#: inc/options/page-options.php:935
msgid "Footer Get Contact Social ON/Off"
msgstr ""

#: inc/options/page-options.php:874
msgid "Footer Layout 1"
msgstr ""

#: inc/options/page-options.php:875
msgid "Footer Layout 2"
msgstr ""

#: inc/options/page-options.php:876
msgid "Footer Layout 3"
msgstr ""

#: inc/options/page-options.php:877
msgid "Footer Layout 4"
msgstr ""

#: inc/widget-area-register.php:76
msgid "Footer widget 4"
msgstr ""

#: inc/widget-area-register.php:59
msgid "Footer Widget Area 2:1"
msgstr ""

#: inc/widget-area-register.php:60
msgid "Footer Widget Area 2:2"
msgstr ""

#: inc/widget-area-register.php:61
msgid "Footer Widget Area 2:3"
msgstr ""

#: inc/widget-area-register.php:62
msgid "Footer Widget Area 2:4"
msgstr ""

#: inc/options/page-options.php:1051
msgid "Footer Widget Full Info"
msgstr ""

#: template-parts/header/elements/header-top-switcher-language.php:27
#: template-parts/header/header-top/header-top-switcher-language.php:23
msgid "Français"
msgstr ""

#: learnpress/custom/content-box-masonary.php:68
#: learnpress/custom/content-box.php:68 tutor/dashboard/my-courses.php:164
#: template-parts/components/card/layout-1.php:162
#: template-parts/components/price/layout-1.php:43
#: template-parts/components/price/layout-1.php:64
#: template-parts/components/tutor/filter/filter-archive-course-2.php:168
#: template-parts/components/tutor/filter/filter-archive-course-3.php:166
#: template-parts/components/tutor/filter/filter-archive-course.php:143
#: template-parts/components/tutor/filter/filter-archive-course.php:143
msgid "Free"
msgstr ""

#: inc/global-functions.php:528
msgid "Full"
msgstr ""

#: inc/options/post-and-category-options.php:72
msgid "Full Banner"
msgstr ""

#: comments.php:61
msgid "Full Name"
msgstr ""

#: inc/options/menu-options.php:105
msgid "Full Width"
msgstr ""

#: inc/options/portfolio-options.php:110
msgid "Gallery"
msgstr ""

#: inc/options/post-format-options.php:53
msgid "Gallery Image"
msgstr ""

#: template-parts/header/elements/header-top-switcher-currency.php:31
msgid "GBP"
msgstr ""

#: tutor/dashboard/reviews.php:43 tutor/dashboard/reviews/given-reviews.php:37
msgid "Given"
msgstr ""

#: inc/customizer/color.php:66
msgid "Global Colors"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:86
msgid "Go to Dashboard"
msgstr ""

#: tutor/dashboard/registration.php:33
#: tutor/dashboard/instructor/registration.php:34
msgid "Go to Home"
msgstr ""

#: woocommerce/single-product-reviews.php:126
#: woocommerce/custom/wooc-functions.php:263
msgid "Good"
msgstr ""

#: inc/options/page-options.php:455
msgid "Gradient Border Marquee"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:104
#: template-parts/components/page-banners/course/layout-3.php:101
msgid "Grid"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:104
#: template-parts/components/page-banners/course/layout-3.php:101
msgid "Grid Layout"
msgstr ""

#: learnpress/custom/lp-functions.php:588
#: learnpress/custom/lp-functions.php:595
msgid "h"
msgstr ""

#: template-parts/header/header-10.php:65 template-parts/header/header-9.php:41
#: template-parts/header/header-top/header-top-2.php:29
msgid "Hand Emojji Images"
msgstr ""

#: template-parts/header/header-top/header-top-questions.php:10
msgid "Have any Question?"
msgstr ""

#: inc/options/page-options.php:9
msgid "Header"
msgstr ""

#: inc/options/page-options.php:369
msgid "Header Button"
msgstr ""

#: inc/options/page-options.php:130
msgid "Header Layout 1"
msgstr ""

#: inc/options/page-options.php:139
msgid "Header Layout 10"
msgstr ""

#: inc/options/page-options.php:140
msgid "Header Layout 11"
msgstr ""

#: inc/options/page-options.php:141
msgid "Header Layout 12"
msgstr ""

#: inc/options/page-options.php:131
msgid "Header Layout 2"
msgstr ""

#: inc/options/page-options.php:132
msgid "Header Layout 3"
msgstr ""

#: inc/options/page-options.php:133
msgid "Header Layout 4"
msgstr ""

#: inc/options/page-options.php:134
msgid "Header Layout 5"
msgstr ""

#: inc/options/page-options.php:135
msgid "Header Layout 6"
msgstr ""

#: inc/options/page-options.php:136
msgid "Header Layout 7"
msgstr ""

#: inc/options/page-options.php:137
msgid "Header Layout 8"
msgstr ""

#: inc/options/page-options.php:138
msgid "Header Layout 9"
msgstr ""

#: inc/options/page-options.php:338
msgid "Header One Page Menu offset"
msgstr ""

#: inc/options/page-options.php:239
msgid "Header Sticky"
msgstr ""

#: functions.php:102
msgid "Header Top Menu (No depth supported)"
msgstr ""

#: inc/options/page-options.php:272
msgid "Header Transparent"
msgstr ""

#: tutor/login.php:34
msgid "Hi, Welcome back!"
msgstr ""

#. Name of the theme
msgid "Histudy"
msgstr ""

#: inc/customizer/color.php:57
msgid "Histudy Colors Options"
msgstr ""

#. Description of the theme
msgid ""
"Histudy is created for Learning Management System. Online OR Offline The "
"template is perfect for e-Learning, Course School, Online School, "
"Kindergarten, Classic LMS, University Status, Instructor Portfolio, Language "
"Academy, Gym Coaching, Online Course, Single Course, marketplace, University "
"Classic, Home Elegant, Home technology, and other needed dashboard, inner "
"and details pages availability. The template has included everything you "
"need for a complete online education center and LMS."
msgstr ""

#: inc/custom-mobile-footer.php:41 template-parts/breadcrumb.php:25
#: woocommerce/custom/wooc-functions.php:306
#: template-parts/components/page-banners/course/layout-2.php:71
#: template-parts/components/page-banners/course/layout-3.php:69
msgid "Home"
msgstr ""

#: template-parts/header/elements/logo-white.php:30
msgid "home"
msgstr ""

#: learnpress/custom/lp-functions.php:428
msgid "Hour"
msgstr ""

#: learnpress/custom/lp-functions.php:428
msgid "Hours"
msgstr ""

#: inc/options/theme/generic-theme-options.php:69
msgid "How Update Server Condiguration"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:117
msgid "How would you rate this course?"
msgstr ""

#. URI of the theme
msgid "https://rainbowthemes.net/themes/histudy"
msgstr ""

#. Author URI of the theme
msgid "https://themeforest.net/user/rainbow-themes/portfolio"
msgstr ""

#: functions.php:186
msgid "Huge"
msgstr ""

#: inc/options/page-options.php:566
msgid "If this field is empty, then default Header address URL will be showed"
msgstr ""

#: inc/options/page-options.php:539
msgid "If this field is empty, then default Header address will be showed"
msgstr ""

#: inc/options/page-options.php:511
msgid "If this field is empty, then default Header button Text will be showed"
msgstr ""

#: inc/options/page-options.php:482
msgid "If this field is empty, then default Header button url will be showed"
msgstr ""

#: inc/options/page-options.php:675
msgid "If this field is empty, then default page/post title will be showed"
msgstr ""

#: inc/options/portfolio-options.php:108
#: template-parts/components/woocommerce/minicart.php:42
msgid "Image"
msgstr ""

#: template-parts/components/card/layout-3.php:74
#: template-parts/components/card/layout-4.php:85
#: template-parts/header/offcanvas/minicart.php:49
msgid "image"
msgstr ""

#: inc/options/portfolio-options.php:146
msgid "Images (gallery)"
msgstr ""

#: inc/demo-import-config.php:25
msgid "Importing may take 5-10 minutes."
msgstr ""

#: learnpress/custom/content-box-masonary.php:152
#: learnpress/custom/content-box.php:142 learnpress/custom/lp-functions.php:353
#: tutor/loop/meta.php:50 template-parts/components/card/layout-1.php:255
#: template-parts/components/card/layout-1.php:330
#: template-parts/components/card/layout-3.php:133
#: template-parts/components/card/layout-4.php:154
msgid "In"
msgstr ""

#: tutor/dashboard/dashboard.php:261
msgid "In Progress Courses"
msgstr ""

#: inc/options/portfolio-options.php:37
msgid "Information List"
msgstr ""

#: inc/helper/social-trait.php:40
msgid "Instagram"
msgstr ""

#: learnpress/custom/lp-functions.php:536
msgid "Instructor"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:53
msgid "Instructor Application Received"
msgstr ""

#: inc/options/user-extra-meta.php:497
#: template-parts/components/tutor/filter/filter-archive-course-2.php:197
#: template-parts/components/tutor/filter/filter-archive-course-3.php:193
msgid "Intermediate"
msgstr ""

#: inc/options/page-options.php:1080
msgid "is social enabled"
msgstr ""

#: template-parts/content-none.php:24
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: inc/options/portfolio-options.php:56
msgid "Label"
msgstr ""

#: learnpress/custom/lp-functions.php:450
#: template-parts/components/tutor/sidebar/layout-1.php:190
msgid "Language"
msgstr ""

#: template-parts/header/elements/header-top-switcher-language.php:19
#: template-parts/header/elements/header-top-switcher-language.php:26
#: template-parts/header/elements/header-top-switcher-language.php:32
#: template-parts/header/header-top/header-top-switcher-language.php:15
#: template-parts/header/header-top/header-top-switcher-language.php:22
#: template-parts/header/header-top/header-top-switcher-language.php:28
msgid "Language Images"
msgstr ""

#: functions.php:181
msgid "Large"
msgstr ""

#: tutor/dashboard/my-profile.php:30 tutor/dashboard/registration.php:138
#: tutor/dashboard/instructor/registration.php:151
msgid "Last Name"
msgstr ""

#: learnpress/custom/lp-functions.php:362
msgid "Last updated"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:132
#: template-parts/components/page-banners/course/layout-3.php:130
#: template-parts/components/tutor/filter/filter-archive-course-2.php:222
#: template-parts/components/tutor/filter/filter-archive-course-3.php:216
#: template-parts/components/tutor/filter/filter-archive-course.php:112
msgid "Latest"
msgstr ""

#: inc/options/single-product.php:23
msgid "Layout 1"
msgstr ""

#: inc/options/single-product.php:24
msgid "Layout 2"
msgstr ""

#: inc/options/single-product.php:25
msgid "Layout 3"
msgstr ""

#: inc/options/single-product.php:26
msgid "Layout 4"
msgstr ""

#: inc/options/single-product.php:27
msgid "Layout 5"
msgstr ""

#: learnpress/custom/content-box-masonary.php:158
#: learnpress/custom/content-box.php:148 tutor/loop/footer.php:23
#: template-parts/components/card/layout-1.php:275
#: template-parts/components/card/layout-3.php:153
#: template-parts/components/card/layout-4.php:174
#: template-parts/components/card/layout-5.php:160
#: template-parts/components/card/layout-6.php:120
#: template-parts/components/card/layout-7.php:123
#: template-parts/components/card/layout-8.php:119
#: template-parts/components/card/layout-9.php:120
msgid "Learn More"
msgstr ""

#. %s: post title
#: inc/underscore/template-tags.php:120
#, php-format
msgid "Leave a Comment <span class=\"screen-reader-text\"> on %s</span>"
msgstr ""

#: comments.php:77
msgid "Leave a Reply"
msgstr ""

#. %s is product title
#: woocommerce/single-product-reviews.php:76
#, php-format
msgid "Leave a Reply to %s"
msgstr ""

#: learnpress/custom/lp-functions.php:446
#: template-parts/components/tutor/sidebar/layout-1.php:180
msgid "Lectures"
msgstr ""

#: inc/options/menu-options.php:140
msgid "Left"
msgstr ""

#: inc/options/portfolio-options.php:23
msgid "Left Media"
msgstr ""

#: inc/options/post-and-category-options.php:98
msgid "Left Sidebar"
msgstr ""

#: tutor/dashboard/dashboard.php:304
msgid "Lesson"
msgid_plural "Lessons"
msgstr[0] ""
msgstr[1] ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:185
#: template-parts/components/tutor/filter/filter-archive-course-3.php:181
msgid "Levels"
msgstr ""

#: learnpress/single-course/sidebar/user-time.php:42
msgid "Lifetime"
msgstr ""

#: header.php:54
msgid "Light"
msgstr ""

#: template-parts/components/tutor/dashbord/card-top.php:37
msgid "line"
msgstr ""

#: inc/options/post-format-options.php:100
msgid "Link Post Options"
msgstr ""

#: inc/helper/social-trait.php:30
msgid "Linkedin"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:105
#: template-parts/components/page-banners/course/layout-3.php:102
msgid "List"
msgstr ""

#. used between list items, there is a space after the comma
#: inc/underscore/template-tags.php:107
msgctxt "list item separator"
msgid ", "
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:105
#: template-parts/components/page-banners/course/layout-3.php:102
msgid "List Layout"
msgstr ""

#: inc/options/user-extra-meta.php:1396
msgid "Location"
msgstr ""

#: woocommerce/myaccount/form-login.php:67
#: woocommerce/myaccount/form-login.php:69
msgid "Log in"
msgstr ""

#: inc/comment-form.php:64 inc/comment-form.php:138
msgid "Log in to Reply"
msgstr ""

#: woocommerce/myaccount/form-login.php:36
msgid "Login"
msgstr ""

#: inc/options/page-options.php:1055
msgid "Logo"
msgstr ""

#: template-parts/popup/popup-content-lost-password.php:3
msgid "Lost your password"
msgstr ""

#: woocommerce/myaccount/form-login.php:62
msgid "Lost your password?"
msgstr ""

#: learnpress/custom/lp-functions.php:598
msgid "m"
msgstr ""

#: inc/tgm-config.php:64
msgid "MailChimp for WordPress"
msgstr ""

#: tutor/dashboard.php:80
msgid "Menu"
msgstr ""

#: inc/options/page-options.php:305
msgid "Menu Offset Enabled"
msgstr ""

#: inc/options/menu-options.php:6
msgid "Menu Option"
msgstr ""

#: inc/options/menu-options.php:118
msgid "Menu Position"
msgstr ""

#: inc/options/page-options.php:202
msgid "Menu Type"
msgstr ""

#: learnpress/custom/lp-functions.php:425
msgid "Minute"
msgstr ""

#: learnpress/custom/lp-functions.php:425
msgid "Minutes"
msgstr ""

#: functions.php:104
msgid "Mobile Menu"
msgstr ""

#: inc/options/page-options.php:457
msgid "Modern Button"
msgstr ""

#. If there are characters in your language that are not supported by Yantramanav, translate this to 'off'. Do not translate into your own language.
#: inc/scripts.php:515
msgctxt "Montserrat font: on or off"
msgid "on"
msgstr ""

#: tutor/dashboard/enrolled-courses.php:69
msgid "More"
msgstr ""

#: learnpress/custom/lp-functions.php:171
msgid "Most Popular"
msgstr ""

#: tutor/dashboard/my-courses.php:240
msgid "Move to Draft"
msgstr ""

#: inc/options/page-options.php:213
msgid "Multi Page"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course.php:153
msgid "multiple select example"
msgstr ""

#: tutor/dashboard.php:68 tutor/dashboard/dashboard.php:346
#: tutor/dashboard/my-courses.php:47
msgid "My Courses"
msgstr ""

#: tutor/dashboard/my-profile.php:39
msgid "My Profile"
msgstr ""

#: tutor/dashboard/my-quiz-attempts.php:41
msgid "My Quiz Attempts"
msgstr ""

#: woocommerce/custom/template-parts/content-product-sku.php:11
msgid "N/A"
msgstr ""

#: woocommerce/single-product-reviews.php:88
msgid "Name"
msgstr ""

#: woocommerce/custom/wooc-functions.php:252
msgid "Name *"
msgstr ""

#: tutor/shortcode/instructor-filter.php:19
msgid "New"
msgstr ""

#: inc/comment-nav.php:18
msgid "Newer Comments"
msgstr ""

#: learnpress/custom/lp-functions.php:170
msgid "Newly published"
msgstr ""

#: template-parts/portfolio-grid-1.php:231
#: template-parts/project/content.php:225
msgid "Next"
msgstr ""

#: inc/options/theme/generic-theme-options.php:92
msgid "No"
msgstr ""

#: inc/helper/meta-trait.php:38 inc/helper/meta-trait.php:120
msgid "No Comments"
msgstr ""

#: ajax_handler.php:309
msgid "No Course found"
msgstr ""

#: ajax_handler.php:65
msgid "No Course Found."
msgstr ""

#: tutor/profile/courses_taken.php:49
msgid "No course yet."
msgstr ""

#: learnpress/global/no-courses-found.php:17
msgid "No courses were found to match your selection."
msgstr ""

#: ajax_handler.php:308
msgid "No data found"
msgstr ""

#: learnpress/single-course/loop-section.php:67
msgid "No items in this section"
msgstr ""

#: functions.php:646 template-parts/header/offcanvas/minicart.php:46
msgid "No products in the cart."
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:123
#: template-parts/components/tutor/filter/filter-archive-course-3.php:125
msgid "No Rated"
msgstr ""

#: functions.php:728
msgid "No results found."
msgstr ""

#: inc/options/post-and-category-options.php:100
msgid "No Sidebar"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course.php:132
msgid "No User Found"
msgstr ""

#: functions.php:507
msgid "Nonce does not exist"
msgstr ""

#: functions.php:176
msgid "Normal"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course.php:31
msgid "NOT RATED"
msgstr ""

#: woocommerce/single-product-reviews.php:128
#: woocommerce/custom/wooc-functions.php:265
msgid "Not that bad"
msgstr ""

#: search.php:98 template-parts/content-none.php:13
msgid "Nothing Found"
msgstr ""

#: inc/scripts.php:151
msgid "Nothing selected"
msgstr ""

#: tutor/dashboard/dashboard.php:300
#: template-parts/components/page-banners/course/layout-2.php:110
#: template-parts/components/page-banners/course/layout-3.php:107
msgid "of"
msgstr ""

#: learnpress/custom/content-box-masonary.php:122
#: learnpress/custom/content-box.php:113
#: inc/options/theme/generic-theme-options.php:137
#: template-parts/components/card/layout-1.php:199
#: template-parts/components/card/layout-1.php:293
#: template-parts/components/card/layout-3.php:84
#: template-parts/components/card/layout-4.php:95
#: template-parts/components/card/layout-6.php:74
#: template-parts/components/card/layout-7.php:69
#: template-parts/components/card/layout-9.php:69
msgid "Off"
msgstr ""

#: functions.php:100
msgid "Offcanvas Menu"
msgstr ""

#: inc/comment-nav.php:13
msgid "Older Comments"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:133
#: template-parts/components/page-banners/course/layout-3.php:131
#: template-parts/components/tutor/filter/filter-archive-course-2.php:226
#: template-parts/components/tutor/filter/filter-archive-course-3.php:220
#: template-parts/components/tutor/filter/filter-archive-course.php:113
msgid "Oldest"
msgstr ""

#: inc/options/theme/generic-theme-options.php:137
msgid "On"
msgstr ""

#: inc/tgm-config.php:41
msgid "One Click Demo Import"
msgstr ""

#: inc/options/page-options.php:214
msgid "One Page"
msgstr ""

#: ajax_handler.php:228
msgid "Online Class"
msgstr ""

#: woocommerce/single-product-reviews.php:140
msgid ""
"Only logged in customers who have purchased this product may leave a review."
msgstr ""

#: tutor/dashboard/registration.php:30
#: tutor/dashboard/instructor/registration.php:31
msgid "Oooh! Access Denied"
msgstr ""

#: learnpress/custom/lp-functions.php:549
msgid "Overview"
msgstr ""

#: template-parts/breadcrumb.php:157
msgid "Page"
msgstr ""

#: inc/options/page-options.php:595 inc/options/page-options.php:612
msgid "Page Banner Area"
msgstr ""

#: inc/options/page-options.php:5
msgid "Page Options"
msgstr ""

#: template-parts/content-page.php:17
#: template-parts/content-single-project.php:163
#: template-parts/single-post/content-single-audio.php:44
#: template-parts/single-post/content-single-gallery.php:57
#: template-parts/single-post/content-single-link.php:39
#: template-parts/single-post/content-single-quote.php:32
#: template-parts/single-post/content-single-standard.php:33
#: template-parts/single-post/content-single-video.php:34
#: template-parts/single-post/content-single.php:32
msgid "Pages:"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:172
#: template-parts/components/tutor/filter/filter-archive-course-3.php:170
#: template-parts/components/tutor/filter/filter-archive-course.php:144
#: template-parts/components/tutor/filter/filter-archive-course.php:144
msgid "Paid"
msgstr ""

#: inc/options/page-options.php:414
msgid "Parent"
msgstr ""

#: tutor/dashboard/registration.php:161 woocommerce/myaccount/form-login.php:47
#: woocommerce/myaccount/form-login.php:111
#: tutor/dashboard/instructor/registration.php:174
msgid "Password"
msgstr ""

#: functions.php:543
msgid "Password are not matched"
msgstr ""

#: tutor/dashboard/registration.php:169
#: tutor/dashboard/instructor/registration.php:182
msgid "Password confirmation"
msgstr ""

#: tutor/dashboard/dashboard.php:153 tutor/dashboard/my-courses.php:60
msgid "Pending"
msgstr ""

#: woocommerce/single-product-reviews.php:125
#: woocommerce/custom/wooc-functions.php:262
msgid "Perfect"
msgstr ""

#: inc/options/user-extra-meta.php:1376
msgid "Phone"
msgstr ""

#: tutor/dashboard/my-profile.php:33
msgid "Phone Number"
msgstr ""

#: inc/options/theme/generic-theme-options.php:136
msgid "PHP Function \"file_get_content\":"
msgstr ""

#: inc/options/theme/generic-theme-options.php:119
msgid "PHP Max Input Vars:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:126
msgid "PHP Memory Limit:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:109
msgid "PHP Post Max Size:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:114
msgid "PHP Time Limit:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:131
msgid "PHP Upload Max Size:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:104
msgid "PHP Version:"
msgstr ""

#: inc/helper/social-trait.php:60
msgid "Pinterest"
msgstr ""

#: searchform.php:20
msgctxt "placeholder"
msgid "Search ..."
msgstr ""

#: inc/options/theme/inactive-theme-options.php:26
msgid "Please activate your theme and utilize the theme options."
msgstr ""

#: tutor/dashboard/dashboard.php:59
msgid "Please complete profile"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:79
msgid "Please contact the site administrator for further information."
msgstr ""

#: inc/options/page-options.php:547
msgid "Please enter your address"
msgstr ""

#: inc/options/page-options.php:574
msgid "Please enter your address URL"
msgstr ""

#: template-parts/popup/popup-content-lost-password.php:5
msgid ""
"Please enter your username or email address. You will receive a link to "
"create a new password via email."
msgstr ""

#: inc/options/theme/generic-theme-options.php:39
#: inc/options/theme/generic-theme-options.php:40
msgid "Plugins"
msgstr ""

#. If there are characters in your language that are not supported by Poppins, translate this to 'off'. Do not translate into your own language.
#: inc/scripts.php:507
msgctxt "Poppins font: on or off"
msgid "on"
msgstr ""

#: tutor/shortcode/instructor-filter.php:20
msgid "Popular"
msgstr ""

#: inc/options/portfolio-options.php:10
msgid "Popup layout"
msgstr ""

#: inc/options/post-and-category-options.php:356
msgid "Pors"
msgstr ""

#: inc/options/post-and-category-options.php:329
msgid "Pors and Cons"
msgstr ""

#: woocommerce/content-single-product.php:63
#: woocommerce/custom/template-parts/content-shop-thumb.php:45
msgid "Positive Reviews"
msgstr ""

#. %s: post author.
#: inc/underscore/template-tags.php:82
#, php-format
msgctxt "post author"
msgid "by %s"
msgstr ""

#: comments.php:73
msgid "Post Comment"
msgstr ""

#. %s: post date.
#: inc/underscore/template-tags.php:65
#, php-format
msgctxt "post date"
msgid "Posted on %s"
msgstr ""

#: inc/options/post-and-category-options.php:55
msgid "Post Layout Options"
msgstr ""

#: template-parts/header/elements/search-dropdown.php:35
#: template-parts/header/elements/search-dropdown.php:36
msgid "Post Not Found"
msgstr ""

#: inc/options/post-and-category-options.php:132
msgid "Post Review Options"
msgstr ""

#: functions.php:788
msgid "Post updated successfully"
msgstr ""

#. 1: list of categories.
#: inc/underscore/template-tags.php:103
#, php-format
msgid "Posted in %1$s"
msgstr ""

#: learnpress/single-course/loop-section.php:121
#: learnpress/loop/single-course/loop-section-item.php:37
msgid "Preview"
msgstr ""

#: learnpress/single-course/sidebar.php:61
msgid ""
"Preview\n"
"\t\t\t\t\tthis course"
msgstr ""

#: inc/options/portfolio-options.php:96
msgid "Preview Type"
msgstr ""

#: template-parts/portfolio-grid-1.php:227
#: template-parts/project/content.php:221
msgid "Previous"
msgstr ""

#: woocommerce/cart/cart.php:30 woocommerce/cart/cart.php:119
msgid "Price"
msgstr ""

#: learnpress/custom/lp-functions.php:174
msgid "Price high to low"
msgstr ""

#: learnpress/custom/lp-functions.php:175
msgid "Price low to high"
msgstr ""

#: tutor/dashboard/my-courses.php:158
msgid "Price:"
msgstr ""

#: functions.php:99 functions.php:141
msgid "Primary"
msgstr ""

#: inc/options/page-options.php:452
msgid "Primary Button"
msgstr ""

#: inc/customizer/color.php:89
msgid "Primary Color"
msgstr ""

#: inc/options/page-options.php:456
msgid "Primary Gradient Button"
msgstr ""

#: inc/options/page-options.php:453
msgid "Primary Opacity Button"
msgstr ""

#: learnpress/addons/wishlist/button.php:24
msgid "Processing..."
msgstr ""

#: woocommerce/cart/cart.php:29 woocommerce/cart/cart.php:94
msgid "Product"
msgstr ""

#: inc/helper/page-title-trait.php:18
msgid "Product Details"
msgstr ""

#: inc/options/single-product.php:6
msgid "Product Layout Options"
msgstr ""

#: woocommerce/global/quantity-input.php:44
msgid "Product quantity"
msgstr ""

#: inc/custom-mobile-footer.php:60
msgid "Profile"
msgstr ""

#: tutor/dashboard/my-courses.php:55 tutor/dashboard/my-courses.php:199
msgid "Publish"
msgstr ""

#: tutor/dashboard/dashboard.php:152
msgid "Published"
msgstr ""

#: template-parts/header/topbar/header-top-btn.php:11
msgid "Purchase Now"
msgstr ""

#: tutor/dashboard.php:74
msgid "Q&A"
msgstr ""

#: inc/helper/social-trait.php:75
msgid "QQ"
msgstr ""

#: woocommerce/cart/cart.php:31 woocommerce/cart/cart.php:125
#: woocommerce/global/quantity-input.php:23
msgid "Quantity"
msgstr ""

#: tutor/dashboard.php:74
msgid "Quiz Attempts"
msgstr ""

#: learnpress/custom/lp-functions.php:452
#: template-parts/components/tutor/sidebar/layout-1.php:195
msgid "Quizzes"
msgstr ""

#: inc/options/post-format-options.php:144
msgid "Quote Author Name"
msgstr ""

#: inc/options/post-format-options.php:182
msgid "Quote Author Name Designation"
msgstr ""

#: inc/options/post-format-options.php:140
msgid "Quote Post Options"
msgstr ""

#: inc/options/post-format-options.php:163
msgid "Quote Text"
msgstr ""

#: inc/tgm-config.php:69
msgid "Rainbow Elements"
msgstr ""

#. Author of the theme
msgid "Rainbow-Themes"
msgstr ""

#: archive.php:43 author.php:34 index.php:47 search.php:34
#: single-rainbow_projects.php:15
msgid "rainbow-thumbnail-archive"
msgstr ""

#: archive.php:43 author.php:34 index.php:47 search.php:34
#: single-rainbow_projects.php:15
msgid "rainbow-thumbnail-single"
msgstr ""

#: woocommerce/single-product-reviews.php:124
#: woocommerce/custom/wooc-functions.php:261
msgid "Rate&hellip;"
msgstr ""

#: learnpress/custom/instructor-tab-contents.php:96
#: tutor/dashboard/dashboard.php:371
#: template-parts/components/tutor/filter/filter-archive-course-2.php:45
#: template-parts/components/tutor/filter/filter-archive-course-3.php:47
#: template-parts/components/tutor/filter/filter-archive-course.php:17
msgid "Rating"
msgstr ""

#: learnpress/custom/lp-functions.php:331
msgid "rating"
msgstr ""

#: tutor/shortcode/instructor-filter.php:86
msgid "Ratings"
msgstr ""

#: header.php:16
msgid "rbt-archive-tutor-course-event"
msgstr ""

#: woocommerce/custom/template-parts/content-shop-header.php:9
#: woocommerce/custom/template-parts/content-shop-header.php:14
msgid "rbt-section-gapTop"
msgstr ""

#: woocommerce/custom/template-parts/content-shop-header.php:11
msgid "rbt-section-overlayping-top"
msgstr ""

#: inc/underscore/template-functions.php:104
msgid "Read Article"
msgstr ""

#: template-parts/content-none.php:18
msgid "Ready to publish your first post? Please create a post."
msgstr ""

#: tutor/dashboard/reviews.php:37 tutor/dashboard/reviews/given-reviews.php:34
msgid "Received"
msgstr ""

#. If there are characters in your language that are not supported by Nunito+Sans Sans, translate this to 'off'. Do not translate into your own language.
#: inc/register-custom-fonts.php:13
msgctxt "Red Hat Display font: on or off"
msgid "on"
msgstr ""

#: inc/helper/social-trait.php:65
msgid "Reddit"
msgstr ""

#: inc/tgm-config.php:54
msgid "Redux Framework"
msgstr ""

#: woocommerce/myaccount/form-login.php:92
#: woocommerce/myaccount/form-login.php:125
#: woocommerce/myaccount/form-login.php:128
msgid "Register"
msgstr ""

#: tutor/dashboard/instructor/registration.php:199
msgid "Register as instructor"
msgstr ""

#: tutor/dashboard/registration.php:186
msgid "Register as student"
msgstr ""

#: tutor/dashboard/my-profile.php:28
msgid "Registration Date"
msgstr ""

#: template-parts/single-post/content-single-related-post.php:31
msgid "Related Posts"
msgstr ""

#: tutor/shortcode/instructor-filter.php:18
msgid "Relevant"
msgstr ""

#: woocommerce/myaccount/form-login.php:56
msgid "Remember me"
msgstr ""

#: template-parts/popup/popup-content-lost-password.php:6
#, php-format
msgid "Remember now? %1$sBack to login%2$s"
msgstr ""

#. %s is the product name
#: woocommerce/cart/cart.php:73
#: template-parts/components/woocommerce/minicart.php:56
#, php-format
msgid "Remove %s from cart"
msgstr ""

#: learnpress/addons/wishlist/button.php:25
msgid "Remove from Wishlist"
msgstr ""

#: woocommerce/cart/cart.php:27
msgid "Remove item"
msgstr ""

#: template-parts/popup/popup-content-lost-password.php:23
msgid "Reset Password"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:110
#: template-parts/components/page-banners/course/layout-3.php:107
msgid "results"
msgstr ""

#: learnpress/single-course/buttons/retry.php:40
msgid "Retake course"
msgstr ""

#: template-parts/header/offcanvas/minicart.php:54
msgid "Return To Shop"
msgstr ""

#: learnpress/custom/content-box2.php:14 learnpress/custom/content-box3.php:14
#: learnpress/custom/instructor-tab-contents.php:62
#: template-parts/components/card/layout-2.php:94
#: tutor/dashboard/reviews/given-reviews.php:53
#: template-parts/components/learnpress/card/card-top.php:13
#: template-parts/components/tutor/dashbord/card-top.php:20
msgid "Review"
msgstr ""

#: inc/options/post-and-category-options.php:136
msgid "Review Box"
msgstr ""

#: inc/options/post-and-category-options.php:155
msgid "Review Box Position"
msgstr ""

#: inc/options/post-and-category-options.php:188
msgid "Review Image"
msgstr ""

#: inc/options/post-and-category-options.php:220
msgid "Review Name"
msgstr ""

#: inc/options/post-and-category-options.php:274
msgid "Review Score"
msgstr ""

#: woocommerce/single-product-reviews.php:36
#: learnpress/custom/content-box2.php:16 learnpress/custom/content-box3.php:16
#: learnpress/custom/instructor-tab-contents.php:62
#: tutor/dashboard/reviews.php:30 tutor/loop/header.php:26
#: template-parts/components/card/layout-1.php:223
#: template-parts/components/card/layout-2.php:70
#: template-parts/components/card/layout-2.php:96
#: template-parts/components/card/layout-3.php:104
#: template-parts/components/card/layout-4.php:115
#: template-parts/components/card/layout-6.php:102
#: template-parts/components/card/layout-7.php:91
#: template-parts/components/card/layout-9.php:102
#: tutor/dashboard/reviews/given-reviews.php:28
#: template-parts/components/learnpress/card/card-top.php:15
#: template-parts/components/tutor/dashbord/card-top.php:22
msgid "Reviews"
msgstr ""

#: inc/options/menu-options.php:141
msgid "Right"
msgstr ""

#: inc/options/portfolio-options.php:25
msgid "Right Media"
msgstr ""

#: inc/options/post-and-category-options.php:99
msgid "Right Sidebar"
msgstr ""

#: template-parts/header/elements/search-dropdown.php:13
msgid "Search"
msgstr ""

#: tutor/shortcode/instructor-filter.php:105
msgid "Search instructor..."
msgstr ""

#: template-parts/header/header-mid/header-mid-search-1.php:14
msgid "Search Post"
msgstr ""

#: inc/helper/page-title-trait.php:23
msgid "Search Results for : "
msgstr ""

#: template-parts/breadcrumb.php:162
msgid "Search results for: "
msgstr ""

#: learnpress/custom/lp-functions.php:147
#: template-parts/components/page-banners/course/layout-2.php:120
#: template-parts/components/page-banners/course/layout-2.php:120
#: template-parts/components/page-banners/course/layout-3.php:117
#: template-parts/components/page-banners/course/layout-3.php:117
msgid "Search Your Course.."
msgstr ""

#: functions.php:146
msgid "Secondary"
msgstr ""

#: inc/customizer/color.php:112
msgid "Secondary Color"
msgstr ""

#: ajax_handler.php:295
msgid "See Details"
msgstr ""

#: inc/options/menu-options.php:139
#: inc/options/post-and-category-options.php:71
msgid "Select"
msgstr ""

#: inc/scripts.php:152
msgid "Select All"
msgstr ""

#: inc/options/post-and-category-options.php:59
msgid "Select Banner Style"
msgstr ""

#: inc/options/page-options.php:637
msgid "Select Banner Template"
msgstr ""

#: inc/options/page-options.php:862
msgid "Select Footer Template"
msgstr ""

#: inc/options/page-options.php:118
msgid "Select Header Template"
msgstr ""

#: inc/options/menu-options.php:51
msgid "Select Mega Menu"
msgstr ""

#: inc/options/page-options.php:167
msgid "Select Menu"
msgstr ""

#: inc/options/single-product.php:11
msgid "Select Product Template"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:118
msgid "Select Rating"
msgstr ""

#: inc/options/post-and-category-options.php:85
msgid "Select Sidebar"
msgstr ""

#: inc/options/menu-options.php:81
msgid "Select Width"
msgstr ""

#: inc/options/page-options.php:413
msgid "Self"
msgstr ""

#: inc/options/theme/generic-theme-options.php:101
msgid "Server"
msgstr ""

#: woocommerce/checkout/form-shipping.php:26
msgid "Ship to a different address?"
msgstr ""

#: template-parts/breadcrumb.php:176
msgid "Shop"
msgstr ""

#: inc/helper/layout-trait.php:522
msgid "shop"
msgstr ""

#: woocommerce/loop/orderby.php:26
msgid "Shop order"
msgstr ""

#: template-parts/header/offcanvas/minicart.php:27
msgid "Shopping cart"
msgstr ""

#: inc/options/page-options.php:771
msgid "Show Footer"
msgstr ""

#: inc/options/page-options.php:26
msgid "Show Header"
msgstr ""

#: inc/scripts.php:83
msgid "Show Less"
msgstr ""

#: inc/scripts.php:82 learnpress/custom/lp-functions.php:459
#: tutor/shortcode/instructor-filter.php:78
#: learnpress/single-course/tabs/overview.php:76
msgid "Show More"
msgstr ""

#: learnpress/loop/single-course/loop-section.php:56
msgid "Show more items"
msgstr ""

#: learnpress/single-course/tabs/curriculum-v2.php:60
msgid "Show more Sections"
msgstr ""

#: template-parts/components/page-banners/course/layout-2.php:110
#: template-parts/components/page-banners/course/layout-3.php:107
msgid "Showing"
msgstr ""

#. 1: from, 2: to, 3: total
#: learnpress/custom/lp-functions.php:20
#, php-format
msgid "Showing %1$s-%2$s of %3$s results"
msgstr ""

#. %d: total results
#: woocommerce/loop/result-count.php:30
msgid "Showing all %d result"
msgid_plural "Showing all %d results"
msgstr[0] ""
msgstr[1] ""

#: learnpress/custom/lp-functions.php:16
#, php-format
msgid "Showing last course of %s results"
msgstr ""

#: learnpress/custom/lp-functions.php:7
msgid "Showing only one result"
msgstr ""

#: woocommerce/loop/result-count.php:27
msgid "Showing the single result"
msgstr ""

#: inc/widget-area-register.php:12 inc/helper/layout-trait.php:521
msgid "Sidebar"
msgstr ""

#: inc/widget-area-register.php:31
msgid "Sidebar Event"
msgstr ""

#: inc/widget-area-register.php:22
msgid "Sidebar Shop"
msgstr ""

#: learnpress/custom/lp-functions.php:448
#: template-parts/components/tutor/sidebar/layout-1.php:185
msgid "Skill Level"
msgstr ""

#: tutor/dashboard/my-profile.php:34
msgid "Skill/Occupation"
msgstr ""

#: woocommerce/content-single-product.php:83
#: woocommerce/custom/template-parts/content-product-sku.php:14
msgid "SKU:"
msgstr ""

#: inc/helper/social-trait.php:80
msgid "Skype"
msgstr ""

#: functions.php:171
msgid "Small"
msgstr ""

#: inc/helper/social-trait.php:50
msgid "Snapchat"
msgstr ""

#: functions.php:584
msgid "some error"
msgstr ""

#: ajax_handler.php:160
msgid "Sorry !!!  No Enrolled Course Found."
msgstr ""

#: functions.php:774
msgid "Sorry! nonce validation failed"
msgstr ""

#: functions.php:515
msgid "Sorry! User Ragistration Disabled."
msgstr ""

#: search.php:101 template-parts/content-none.php:20
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: ajax_handler.php:119
msgid "Sorry, but you are looking for something that isn't here."
msgstr ""

#: woocommerce/loop/orderby.php:23
msgid "SORT By"
msgstr ""

#: tutor/shortcode/instructor-filter.php:112
msgid "Sort by"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:137
#: template-parts/components/tutor/filter/filter-archive-course.php:121
msgid "SORT By Author"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-3.php:137
msgid "Sort By Author"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:20
#: template-parts/components/tutor/filter/filter-archive-course-3.php:24
#: template-parts/components/tutor/filter/filter-archive-course.php:152
msgid "SORT By Category"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:214
#: template-parts/components/tutor/filter/filter-archive-course-3.php:208
#: template-parts/components/tutor/filter/filter-archive-course.php:109
msgid "SORT By Order"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course-2.php:160
#: template-parts/components/tutor/filter/filter-archive-course-3.php:158
#: template-parts/components/tutor/filter/filter-archive-course.php:140
msgid "SORT By Price"
msgstr ""

#: template-parts/components/tutor/filter/filter-archive-course.php:16
msgid "SORT By Rating"
msgstr ""

#: learnpress/custom/lp-functions.php:165
msgid "Sort-by:"
msgstr ""

#. If there are characters in your language that are not supported by Poppins, translate this to 'off'. Do not translate into your own language.
#: inc/scripts.php:536
msgctxt "Source font: on or off"
msgid "on"
msgstr ""

#: inc/helper/social-trait.php:95
msgid "Stack Overflow"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:77
msgid ""
"Start building your first course today and let your eLearning journey begin."
msgstr ""

#: template-parts/components/tutor/add-to-cart/layout-1.php:33
msgid "Start Learning"
msgstr ""

#: learnpress/single-course/buttons/enroll.php:28
msgid "Start Now"
msgstr ""

#: inc/options/theme/generic-theme-options.php:68
msgid "Status"
msgstr ""

#: tutor/public-profile.php:144
#: learnpress/custom/instructor-tab-contents.php:32
#: tutor/dashboard/reviews.php:57
msgid "Student"
msgstr ""

#: tutor/public-profile.php:144
#: learnpress/custom/instructor-tab-contents.php:32
msgid "Students"
msgstr ""

#: woocommerce/single-product-reviews.php:80 tutor/dashboard/my-courses.php:201
msgid "Submit"
msgstr ""

#: woocommerce/cart/cart.php:32 woocommerce/cart/cart.php:151
msgid "Subtotal"
msgstr ""

#: functions.php:657 template-parts/header/offcanvas/minicart.php:68
msgid "Subtotal:"
msgstr ""

#: inc/options/post-and-category-options.php:302
msgid "Summary"
msgstr ""

#: woocommerce/custom/template-parts/content-product-meta.php:13
msgid "Tag:"
msgid_plural "Tags:"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/content-single-product.php:89
msgid "Tag: "
msgstr ""

#. 1: list of tags.
#: inc/underscore/template-tags.php:110
#, php-format
msgid "Tagged %1$s"
msgstr ""

#: inc/options/team-extra-meta.php:6
msgid "Team Options"
msgstr ""

#: learnpress/single-course/loop-section.php:47
#: learnpress/loop/single-course/loop-section.php:28
msgctxt "template title empty"
msgid "Untitled"
msgstr ""

#: tutor/dashboard/registration.php:179
#: tutor/dashboard/instructor/registration.php:192
#: tutor/dashboard/instructor/registration.php:192
msgid "Terms and Conditions"
msgstr ""

#: functions.php:151
msgid "Tertiary"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:62
msgid "Thank you for registering as an instructor! "
msgstr ""

#: tutor/dashboard/dashboard.php:63
msgid "Thanks for completing your profile"
msgstr ""

#: learnpress/single-course/tabs/curriculum-v2.php:50
#: learnpress/single-course/tabs/curriculum.php:64
msgid "The curriculum is empty"
msgstr ""

#: learnpress/content-lesson/content.php:24
msgid "The lesson content is empty."
msgstr ""

#: inc/options/portfolio-options.php:13
msgid "The theme option will work if you select default"
msgstr ""

#: inc/options/theme/generic-theme-options.php:76
msgid "Theme Name:"
msgstr ""

#: inc/options/theme/inactive-theme-options.php:11
#: inc/options/theme/inactive-theme-options.php:12
#: inc/options/theme/inactive-theme-options.php:25
msgid "Theme Options"
msgstr ""

#: inc/options/theme/generic-theme-options.php:81
msgid "Theme Version:"
msgstr ""

#: learnpress/custom/lp-functions.php:5
msgid "There are no available courses!"
msgstr ""

#: learnpress/addons/wishlist/user-wishlist.php:13
msgid "There are no courses in the wishlist"
msgstr ""

#: woocommerce/single-product-reviews.php:63
msgid "There are no reviews yet."
msgstr ""

#: learnpress/single-course/tabs/tabs.php:36
msgid "This course has been blocked for expiration"
msgstr ""

#: inc/global-functions.php:707
#, php-format
msgid "This input field has support for the following HTML tags: %1$s"
msgstr ""

#: template-parts/single-post/social-share.php:21
#, php-format
msgctxt "This will check like count"
msgid "%s Like"
msgid_plural "%s Likes"
msgstr[0] ""
msgstr[1] ""

#: functions.php:219
msgid "Thumbnail Archive - (800x450)"
msgstr ""

#: woocommerce/cart/cart.php:28
msgid "Thumbnail image"
msgstr ""

#: functions.php:218
msgid "Thumbnail large - (800x600)"
msgstr ""

#: functions.php:217
msgid "Thumbnail Medium - (400x400)"
msgstr ""

#: functions.php:220
msgid "Thumbnail Single - (1220x686)"
msgstr ""

#: functions.php:216
msgid "Thumbnail Small - (335x250)"
msgstr ""

#: inc/helper/social-trait.php:45
msgid "Tiktok"
msgstr ""

#: learnpress/custom/lp-functions.php:172
msgid "Title a-z"
msgstr ""

#: learnpress/custom/lp-functions.php:173
msgid "Title z-a"
msgstr ""

#: inc/options/page-options.php:415
msgid "Top"
msgstr ""

#: tutor/dashboard/dashboard.php:225
msgid "TOTAL COURSES"
msgstr ""

#: tutor/dashboard/dashboard.php:239
msgid "Total Earnings"
msgstr ""

#: template-parts/content-blog-banner.php:40
msgctxt "Total number of article"
msgid "Article"
msgid_plural "Articles"
msgstr[0] ""
msgstr[1] ""

#: tutor/dashboard/dashboard.php:211
msgid "TOTAL STUDENTS"
msgstr ""

#: tutor/dashboard/dashboard.php:154
msgid "Trash"
msgstr ""

#: inc/tgm-config.php:36
msgid "Tutor LMS"
msgstr ""

#: tutor/shortcode/instructor-filter.php:62
msgid "tutor-toggle-more-content tutor-toggle-more-collapsed"
msgstr ""

#: inc/helper/social-trait.php:25
msgid "Twitter"
msgstr ""

#: inc/options/post-and-category-options.php:175
msgid "Under the post content"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:66
msgid "Unfortunately, your instructor status has been removed."
msgstr ""

#: learnpress/custom/lp-functions.php:442
msgid "Update"
msgstr ""

#: woocommerce/cart/cart.php:176
msgid "Update cart"
msgstr ""

#: inc/class-hi-study-base.php:85
msgid "Update Checking.."
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:137
msgid "Update Review"
msgstr ""

#: template-parts/components/tutor/sidebar/layout-1.php:169
msgid "Update:"
msgstr ""

#: inc/options/post-format-options.php:10
msgid "Upload Audio"
msgstr ""

#: template-parts/header/elements/header-top-switcher-currency.php:20
msgid "USD"
msgstr ""

#: template-parts/header/elements/user.php:71
#: template-parts/header/elements/user.php:189
msgid "User image"
msgstr ""

#: tutor/dashboard/registration.php:145
#: tutor/dashboard/instructor/registration.php:158
msgid "User Name"
msgstr ""

#: inc/options/user-extra-meta.php:1246
msgid "User Social Media"
msgstr ""

#: tutor/dashboard/my-profile.php:31 woocommerce/myaccount/form-login.php:98
msgid "Username"
msgstr ""

#: woocommerce/myaccount/form-login.php:42
msgid "Username or email address"
msgstr ""

#: inc/options/portfolio-options.php:75
msgid "Value"
msgstr ""

#: woocommerce/single-product/review-meta.php:38
msgid "verified owner"
msgstr ""

#: woocommerce/custom/wooc-functions.php:266
msgid "Very Poor"
msgstr ""

#: woocommerce/single-product-reviews.php:129
msgid "Very poor"
msgstr ""

#: inc/options/portfolio-options.php:109
msgid "Video"
msgstr ""

#: inc/options/post-format-options.php:225
msgid "Video Link"
msgstr ""

#: inc/options/portfolio-options.php:122
msgid "Video URL (Vimeo or Youtube or Locally Hosted MP4)"
msgstr ""

#: tutor/dashboard/dashboard.php:352
msgid "View All"
msgstr ""

#: tutor/loop/add-to-cart-woocommerce.php:76
#: template-parts/components/tutor/add-to-cart/layout-1.php:17
msgid "View Cart"
msgstr ""

#: woocommerce/custom/wooc-hooks.php:137
msgid "View cart"
msgstr ""

#: template-parts/header/elements/user.php:75
#: template-parts/header/elements/user.php:193
msgid "View Profile"
msgstr ""

#: inc/helper/meta-trait.php:34 inc/helper/meta-trait.php:117
#: inc/lab/post-views.php:66
msgid "Views"
msgstr ""

#: inc/helper/social-trait.php:70
msgid "Vimeo"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:75
msgid ""
"We've received your application, and we will review it soon. Please hang "
"tight!"
msgstr ""

#: comments.php:66
msgid "Website"
msgstr ""

#: learnpress/custom/lp-functions.php:434
#: learnpress/custom/lp-functions.php:578
msgid "Week"
msgstr ""

#: learnpress/custom/lp-functions.php:434
#: learnpress/custom/lp-functions.php:578
msgid "Weeks"
msgstr ""

#: tutor/dashboard.php:184
msgid "Welcome"
msgstr ""

#: template-parts/header/elements/header-search.php:13
#: template-parts/header/elements/search-dropdown.php:12
msgid "What are you looking for?"
msgstr ""

#: inc/helper/social-trait.php:55
msgid "WhatsApp"
msgstr ""

#: functions.php:156
msgid "White"
msgstr ""

#: tutor/dashboard/wishlist.php:23
msgid "Wishlist"
msgstr ""

#. 1: first result 2: last result 3: total results
#: woocommerce/loop/result-count.php:35
#, php-format
msgctxt "with first and last result"
msgid "Showing %1$d&ndash;%2$d of %3$d result"
msgid_plural "Showing %1$d&ndash;%2$d of %3$d results"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm-config.php:30
msgid "WooCommerce"
msgstr ""

#: inc/helper/helper.php:207
msgid "WooCommerce is not installed or activated."
msgstr ""

#: inc/helper/social-trait.php:85
#: inc/options/theme/generic-theme-options.php:73
msgid "WordPress"
msgstr ""

#: inc/options/theme/generic-theme-options.php:96
msgid "WP Debug Mode:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:91
msgid "WP Multisite:"
msgstr ""

#: inc/options/theme/generic-theme-options.php:86
msgid "WP Version:"
msgstr ""

#: tutor/dashboard/reviews/given-reviews.php:130
msgid "write a review"
msgstr ""

#: comments.php:75
msgid "Write your comment here… "
msgstr ""

#: inc/options/theme/generic-theme-options.php:92
msgid "Yes"
msgstr ""

#: tutor/dashboard/my-courses.php:301
#: tutor/dashboard/reviews/given-reviews.php:155
msgid "Yes, Delete This"
msgstr ""

#: tutor/dashboard/dashboard.php:61
msgid "You are almost done"
msgstr ""

#: tutor/dashboard/registration.php:31
#: tutor/dashboard/instructor/registration.php:32
msgid ""
"You do not have access to this area of the application. Please refer to your "
"system  administrator."
msgstr ""

#: learnpress/single-course/sidebar/user-time.php:30
msgid "You enrolled in this course on:"
msgstr ""

#: learnpress/single-course/sidebar/user-time.php:47
msgid "You finished on:"
msgstr ""

#: learnpress/single-course/tabs/tabs.php:31
msgid "You finished this course. This course has been blocked"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:42
msgid "You have been blocked from being an instructor."
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:20
msgid "You have been rejected from being an instructor."
msgstr ""

#. %s opening and closing link tags respectively
#: woocommerce/single-product-reviews.php:119
#, php-format
msgid "You must be %1$slogged in%2$s to post a review."
msgstr ""

#: woocommerce/checkout/form-checkout.php:25
msgid "You must be logged in to checkout."
msgstr ""

#: template-parts/components/card/layout-4.php:3
msgid "You'll need activate tutor LMS"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:40
msgid ""
"Your application has been accepted. Further necessary details have been sent "
"to your registered email account."
msgstr ""

#: tutor/dashboard.php:166
msgid "Your Application is pending as of"
msgstr ""

#: tutor/dashboard/instructor/logged-in.php:38
msgid ""
"Your application will be reviewed and the results will be sent to you by "
"email."
msgstr ""

#: template-parts/single-post/content-single-audio.php:27
#: template-parts/single-post/content-single-link.php:25
msgid "Your browser does not support the audio tag."
msgstr ""

#: inc/comment-form.php:34 inc/comment-form.php:119
#: inc/views/comments-callback.php:39
msgid "Your comment is awaiting moderation."
msgstr ""

#: woocommerce/checkout/form-checkout.php:48
msgid "Your order"
msgstr ""

#: woocommerce/custom/wooc-functions.php:259
msgid "Your Rating"
msgstr ""

#: woocommerce/single-product-reviews.php:123
msgid "Your rating"
msgstr ""

#: woocommerce/single-product-reviews.php:133
msgid "Your review"
msgstr ""

#: woocommerce/custom/wooc-functions.php:270
msgid "Your Review *"
msgstr ""

#: woocommerce/single-product/review-meta.php:28
msgid "Your review is awaiting approval"
msgstr ""

#: inc/helper/social-trait.php:35
msgid "Youtube"
msgstr ""
