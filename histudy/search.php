<?php
    /**
     * The main template file
     *
     * This is the most generic template file in a WordPress theme
     * and one of the two required files for a theme (the other being style.css).
     * It is used to display a page when nothing more specific matches a query.
     * E.g., it puts together the home page when no home.php file exists.
     *
     * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
     *
     * @package histudy
     */

    if (!defined('ABSPATH')) {
        exit; // Exit if accessed directly.
    }

    get_header();
    /**
     * Required Attributes
     * 
     * @since 1.0.0
     */
    // common attributes
    $rb_is_post_archive = is_home() || ( is_archive() && get_post_type() == 'post' ) ? true : false;
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $rainbow_blog_sidebar_class = (sanitize_text_field( $rainbow_options['rainbow_blog_sidebar'] ) === 'no') || !is_active_sidebar('sidebar-1') ? __( 'col-12 histudy-post-wrapper', 'histudy' ) : __( 'col-lg-8 histudy-post-wrapper', 'histudy' );
    $rainbow_blog_layout = isset($rainbow_options['rainbow_blog_layout']) ? sanitize_text_field( $rainbow_options['rainbow_blog_layout'] ): __('default', 'histudy');
    if( 'blog-grid' == $rainbow_blog_layout ) {
        $rainbow_blog_sidebar_class = __('col-12 histudy-post-wrapper', 'histudy');
    }
    // blog attributes
    $thumb_size = ($rainbow_options['rainbow_blog_sidebar'] === 'no') ? __( 'rainbow-thumbnail-single', 'histudy' ) : __('rainbow-thumbnail-archive', 'histudy');
    $allowed_tags = wp_kses_allowed_html( 'post' );
    /**
     * Attributes for blog grid
     */
    $blog_grid_card_layout = isset($rainbow_options['blog_grid_card_layout']) ? sanitize_text_field( $rainbow_options['blog_grid_card_layout'] ): __('card-boxed', 'histudy');
    /**
     * Attribute for blog grid minimal
     */
    $blog_grid_minimal_card_layout = isset($rainbow_options['blog_grid_minimal_card_layout']) ? sanitize_text_field( $rainbow_options['blog_grid_minimal_card_layout'] ): __( 'card-minimal', 'histudy' );
    global $wp_query;
    $published_post_count = $wp_query->found_posts;
?>
<!-- blog area start -->
<div class="rbt-blog-area rbt-section-gap">
    <div class="container">
        <div class="row row--30 gy-5 justify-content-center">
            <?php if( 'blog-grid' !== $rainbow_blog_layout ) : ?>
            <!-- left sidebar control -->
            <?php if (is_active_sidebar('sidebar-1') && $rainbow_options['rainbow_blog_sidebar'] == 'left') { ?>
                <div class="col-lg-4 col-xl-4">
                    <aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">
                        <?php dynamic_sidebar(); ?>
                    </aside>
                </div>
            <?php } ?>
            <?php endif; ?>
            <!-- /. left sidebar control -->
            <!-- main blog content -->
            <?php if( $published_post_count > 0 ): ?>
                <div class="<?php echo esc_attr($rainbow_blog_sidebar_class); ?>">
                    <?php
                        /**
                         * Show blog card based on card layout
                         * 
                         * @since 1.0.0
                         * 
                         * @see https://developer.wordpress.org/reference/functions/get_template_part/
                         */
                        switch( $rainbow_blog_layout ) {
                            case 'blog-list':
                                get_template_part('template-parts/blog-layout/blog', 'list');
                                break;
                            case 'blog-grid':
                                get_template_part('template-parts/blog-layout/blog', 'grid');
                                break;
                            case 'blog-grid-minimal':
                                get_template_part('template-parts/blog-layout/blog', 'grid-minimal');
                                break;
                            default:
                                get_template_part('template-parts/blog-layout/blog', 'default');
                                break;
                        }
                        if( !empty(rainbow_blog_pagination_simple()) ) {
                            echo '<div class="mt--60">';
                                rainbow_blog_pagination_simple();
                            echo '</div>';
                        }
                    ?>
                </div>
            <?php else: ?>
                <div class="col-lg-8 col-md-12 col-12">
                    <section class="no-results not-found rainbow-search-no-result-found">
                        <header class="page-header">
                            <h3 class="page-title"><?php echo esc_html__('Nothing Found', 'histudy'); ?></h3>
                        </header>
                        <div class="page-content">
                            <p><?php echo esc_html__('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'histudy'); ?></p>
                            <div class="inner">
                                <?php get_search_form(); ?>
                            </div>
                        </div>
                    </section>
                </div>
            <?php endif; ?>
            <!-- /.main blog content -->
            <?php if( 'blog-grid' !== $rainbow_blog_layout ) : ?>
            <!-- right sidebar control -->
            <?php if (is_active_sidebar('sidebar-1') && $rainbow_options['rainbow_blog_sidebar'] == 'right') { ?>
                <div class="col-lg-4 col-xl-4">
                    <aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">
                        <?php dynamic_sidebar(); ?>
                    </aside>
                </div>
            <?php } ?>
            <!-- /. right sidebar control -->
            <?php endif; ?>
        </div>
    </div>
</div>
<!-- blog area end -->
<?php
get_footer();