<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package histudy
 */
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}
$archive_class = '';
if( is_archive() && 'course_event' == get_post_type() ) {
    $archive_class = __( 'rbt-archive-tutor-course-event', 'histudy' );
}

$active_dark_mode = isset( $rainbow_options['active_dark_mode'] ) ? $rainbow_options['active_dark_mode'] : '';
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php $rainbow_options = Rainbow_Helper::rainbow_get_options(); ?>
    <meta name="theme-style-mode" content="<?php echo (int) ($active_dark_mode); ?>">
    <?php wp_head(); ?>
</head>

<body <?php body_class($archive_class); ?>>
<?php

if (function_exists('wp_body_open')) {
     wp_body_open();
}


$preloader = isset( $rainbow_options['histudy_preloader_on_off'] ) ? $rainbow_options['histudy_preloader_on_off'] : '';

if( $preloader == '1' ) {
    get_template_part('template-parts/header/preloader');
}

/**
 * Style Switcher
 */
if (isset($rainbow_options['show_ld_switcher_form_user_end'])) {
    if ($rainbow_options['show_ld_switcher_form_user_end'] === 'on' || $rainbow_options['show_ld_switcher_form_user_end'] == 1) {
        ?>
<div id="main-wrapper" class="main-wrapper">
    <div id="my_switcher" class="my_switcher">
        <ul>
            <li>
                <button data-theme="light" class="setColor light">
                    <img src="<?php echo RAINBOW_IMG_URL;?>/about/sun-01.svg" alt="Sun images"><span title="Light Mode"> <?php echo esc_html__("Light","histudy");?></span>
                </button>
            </li>
            <li>
                <button  data-theme="dark" class="setColor dark">
                    <img src="<?php echo RAINBOW_IMG_URL;?>/about/vector.svg" alt="Vector Images"><span title="Dark Mode"> <?php echo esc_html__("Dark","histudy");?></span>
                </button>
            </li>
        </ul>
    </div>
<?php } } 

    get_template_part('template-parts/header/header', 'main');  ?>

<?php 

if( class_exists('LearnPress') && is_page( 'lp-checkout'  ) || class_exists('LearnPress') && is_page( 'instructors'  ) )  { 
    get_template_part('template-parts/v3/banner/layout/layout', '1'); 
}

if( class_exists('LearnPress') && is_page( 'lp-profile'  ) || class_exists('LearnPress') && is_page( 'instructor'  ) )  { 
    get_template_part('template-parts/v3/banner/layout/layout', '3'); 
}


?>