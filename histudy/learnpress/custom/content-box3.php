<?php 


$lp_rainbow_course_list_archive_img_size = isset( $rainbow_options['lp_rainbow_course_list_archive_img_size'] ) ? $rainbow_options['lp_rainbow_course_list_archive_img_size'] : 'full';
if( class_exists( 'LearnPress' ) ) :
    $course_id = get_the_ID();   
    
    $course = LP_Course::get_course($course_id);
    $course_rate_res = learn_press_get_course_rate( $course_id, false );
    $rated = $course_rate_res['rated'] ?? 0;
    $total = $course_rate_res['total'] ?? 0;

    if( $rated == 1 ) {
        $review = __('Review','histudy');
    } else {
        $review = __('Reviews','histudy');
    }
    ?>
    <!-- Start Single Card  -->
    <div class="rbt-card variation-01 rbt-hover">
            <div class="rbt-card-img">
                <a href="<?php echo get_the_permalink($course_id); ?>">
                    <?php echo get_the_post_thumbnail($course_id,$lp_rainbow_course_list_archive_img_size); ?>
                </a>
            </div>
            <div class="rbt-card-body">
                <h5 class="rbt-card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h5>
                <div class="rbt-review">
                    <div class="rating">
                    <?php
                    if(class_exists('LP_Addon_Course_Review_Preload') ) {
                        LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                    }
                    ?>
                    </div>
                    <span class="rating-count"> (<?php echo esc_attr($rated); ?> <?php echo esc_html($review ); ?>)</span>
                </div>
                <div class="rbt-card-bottom">
                    <?php get_template_part('template-parts/components/price/layout', 1); ?>
                </div>
            </div>
        </div>
    <!-- End Single Card  -->
<?php endif; ?>