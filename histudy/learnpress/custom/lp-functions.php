<?php
// Display indexing text on top of course archive
function histudy_lp_the_course_indexing_text( $total ) {
	if ( $total == 0 ) {
		$result = esc_html__( 'There are no available courses!', 'histudy' );
	} elseif ( $total == 1 ) {
		$result = esc_html__( 'Showing only one result', 'histudy' );
	} else {
		$courses_per_page = absint( LP()->settings->get( 'archive_course_limit' ) );
		$paged            = get_query_var( 'paged' ) ? intval( get_query_var( 'paged' ) ) : 1;

		$from = 1 + ( $paged - 1 ) * $courses_per_page;
		$to   = ( $paged * $courses_per_page > $total ) ? $total : $paged * $courses_per_page;

		if ( $from == $to ) {
			$result = sprintf( esc_html__( 'Showing last course of %s results', 'histudy' ), $total );
		} else {
			$result = sprintf(
                /* translators: 1: from, 2: to, 3: total */
                __( 'Showing %1$s-%2$s of %3$s results', 'histudy' ), 
                '<span class="rbt-course-archive-count-from">' . esc_html( $from ) . '</span>',
                '<span class="rbt-course-archive-count-to">' . esc_html( $to ) . '</span>',
                '<span class="rbt-course-archive-count">' . esc_html( $total ) . '</span>'
            );
		}
	}
	echo wp_kses_post( $result );
}


// Course archive top search
function histudy_lp_archive_top_search()
{
    if ( ! (is_post_type_archive('lp_course') || is_tax('course_category') ) ) {
        return;
    }

    global $wp_query;

    global $rainbow_options;
    $enable_breadcrumb    = $rainbow_options['generic_banner_breadcrumb_enable'];
    $enable_title         = $rainbow_options['generic_banner_title_enable'];
    $enable_badge         = $rainbow_options['generic_banner_badge_enable'];
    $badge_label_singular = $rainbow_options['generic_banner_badge_text_singular'];
    $badge_label_plural   = $rainbow_options['generic_banner_badge_text_plural'];
    $banner_subtitle      = $rainbow_options['generic_banner_subtitle'];
    $enable_subtitle = $rainbow_options['generic_banner_subtitle_enable'];
    $enable_grid_list_filter = $rainbow_options['generic_banner_layout_filter_enable'];
    $banner_layout = $rainbow_options['generice_course_archive_banner_layout'];
    $generic_banner_grid_btn_label = isset( $rainbow_options['generic_banner_grid_btn_label'] ) ? $rainbow_options['generic_banner_grid_btn_label'] : 'grid';
    $generic_banner_list_btn_label = isset( $rainbow_options['generic_banner_list_btn_label'] ) ? $rainbow_options['generic_banner_list_btn_label'] : 'list';
    $generic_banner_breadcrumb_enable = isset( $rainbow_options['generic_banner_breadcrumb_enable'] ) ? $rainbow_options['generic_banner_breadcrumb_enable'] : ' ';
    $generic_banner_layout_filter_enable = isset( $rainbow_options['generic_banner_layout_filter_enable'] ) ? $rainbow_options['generic_banner_layout_filter_enable'] : ' ';

    $count = histudy_get_total_course_count();

    // Determine the singular or plural label based on the count
    $badge_label = ($count == 1) ? $badge_label_singular : $badge_label_plural;

    $layouts = learn_press_courses_layouts();
    $active  = learn_press_get_courses_layout();
    $s       = LP_Request::get_param( 'c_search' );

    $course_filter_layout = get_query_var('course_filter_layout');
    $histudy_learnpress_course_sidebar = isset($rainbow_options['histudy_learnpress_course_breadcrumb_filter_layout']) ? $rainbow_options['histudy_learnpress_course_breadcrumb_filter_layout'] : '';

    if (isset($course_filter_layout) && !empty($course_filter_layout)) {
        $course_sidebar__pos = $course_filter_layout;
    } else {
        $course_sidebar__pos = $histudy_learnpress_course_sidebar;
    }
    
    $seleccted = $_GET['course-filter'] ?? 'regular';
    $show_archive_filter = '';
    if (!empty(get_query_var('show_archive_filter'))) {
        $show_archive_filter = get_query_var('show_archive_filter');
    }

    $card_layout = get_query_var('card_layout');


    if ( $banner_layout  == 'layout-2' && $generic_banner_breadcrumb_enable == '1' ) {
?>
        <div class="rbt-page-banner-wrapper page-banner-layout-style-two-learnpress">
            <!-- Start Banner BG Image  -->
            <div class="rbt-banner-image"></div>
            <!-- End Banner BG Image  -->
            <div class="rbt-banner-content">
                <!-- Start Banner Content Top  -->
                <div class="rbt-banner-content-top">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <?php if (!empty($enable_breadcrumb)) : ?>
                                    <!-- Start Breadcrumb Area  -->
                                    <?php rainbow_lms_breadcrumb(); ?>
                                    <!-- End Breadcrumb Area  -->
                                <?php endif; ?>
                                <div class=" title-wrapper">
                                    <?php if (!empty($enable_title)) : ?>
                                        <h1 class="title mb--0"><?php echo Rainbow_Helper::rainbow_get_page_title(); ?></h1>
                                    <?php endif; ?>
                                    <?php if (!empty($enable_badge)) : ?>
                                        <a href="#" class="rbt-badge-2">
                                            <div class="image">🎉</div> <?php echo sprintf('%s %s', number_format_i18n($count), esc_html($badge_label));  ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($banner_subtitle) && !empty($enable_subtitle)) : ?>
                                    <p class="description"><?php echo esc_html($banner_subtitle); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Banner Content Top  -->
                <!-- Start Course Top  -->
                <div class="rbt-course-top-wrapper mt--40 mt_sm--20 rbt-generic-banner-course-filter-banner">
                    <div class="container">

                        <div class="row g-5 align-items-center">
                            <div class="col-lg-5 col-md-6">
                                <div class="rbt-sorting-list d-flex flex-wrap align-items-center">
                                    <?php if( $generic_banner_layout_filter_enable == '1' ) { ?>
                                    <?php if( $card_layout !='layout-3' ) { ?>
                                    <div class="rbt-short-item switch-layout-container">
                                        <ul class="course-switch-layout">
                                            <li class="course-switch-item"><button class="rbt-grid-view active" title="Grid Layout"><i class="feather-grid"></i> <span class="text"><?php echo esc_html( $generic_banner_grid_btn_label );?></span></button></li>
                                            <li class="course-switch-item"><button class="rbt-list-view" title="List Layout"><i class="feather-list"></i> <span class="text"><?php echo esc_html( $generic_banner_list_btn_label );?></span></button></li>
                                        </ul>
                                    </div>
                                    <?php } } ?>
                                    <div class="rbt-short-item">
                                        <span class="course-index"><?php histudy_lp_the_course_indexing_text($wp_query->found_posts);?></span>
                                    </div>
                                </div>
                            </div>
                            <?php if( $course_sidebar__pos != 'layout-2' && $course_sidebar__pos != 'layout-3' ) { ?>
                            <div class="col-lg-7 col-md-6">
                                <div class="rbt-sorting-list d-flex flex-wrap align-items-center justify-content-start justify-content-lg-end rbt-mobile-course-archive-filter">
                                    <div class="rbt-short-item rbt-short-item-searchbar">
                                        <form action="#" class="rbt-search-style me-0 rbt-banner-course-filter-search search-courses" method="get" action="<?php echo esc_url_raw( learn_press_get_page_link( 'courses' ) ); ?>">
                                        <input type="hidden" name="post_type" value="<?php echo esc_attr( LP_COURSE_CPT ); ?>">
                                                <input type="hidden" name="taxonomy" value="<?php echo esc_attr( get_queried_object()->taxonomy ?? $_GET['taxonomy'] ?? '' ); ?>">
                                                <input type="hidden" name="term_id" value="<?php echo esc_attr( get_queried_object()->term_id ?? $_GET['term_id'] ?? '' ); ?>">
                                                <input type="hidden" name="term" value="<?php echo esc_attr( get_queried_object()->slug ?? $_GET['term'] ?? '' ); ?>">
                                            <input type="text" class="form-control course_search_input" placeholder="<?php esc_attr_e( 'Search Your Course..', 'histudy' ); ?>" name="c_search" value="<?php echo esc_attr( $s ); ?>">
                                            <button type="submit" name="lp-btn-search-courses" class="course_search_button rbt-search-btn rbt-round-btn">
                                                <i class="feather-search"></i>
                                            </button>
                                        </form>
                                    </div>
                                    <div class="rbt-short-item rbt-short-item-filter">
                                        <div class="view-more-btn text-start text-sm-end">
                                            <button class="rbt-filter-toggle-btn rbt-btn btn-white btn-md radius-round"><span><?php echo esc_html__("Filter","histudy"); ?></span><i class="feather-filter"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php } else { ?>
                            <div class="col-lg-7 col-md-12">
                                <div class="rbt-sorting-list d-flex flex-wrap align-items-center justify-content-start justify-content-lg-end">
                                    <div class="rbt-short-item">
                                        <div class="filter-select">
                                            <span class="select-label d-block"><?php esc_html_e( 'Sort-by:', 'histudy' );?></span>
                                            <form id="histudy-course-sort-form" class="course-status" method="get" action="<?php echo esc_url( get_post_type_archive_link( 'lp_course' ) ); ?>">
                                                <div class="filter-select rbt-modern-select search-by-category">
                                                <input type="hidden" name="ref" value="course">
                                                    <select name='orderby' id="histudy-learnpress-course-filter-select" data-size="7">
                                                        <option <?php echo esc_attr($seleccted=='post_date' ? 'selected' : '') ?> value="post_date"><?php esc_html_e( 'Newly published', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='popular' ? 'selected' : '') ?> value="popular"><?php esc_html_e( 'Most Popular', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='post_title' ? 'selected' : '') ?> value="post_title"><?php esc_html_e( 'Title a-z', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='post_title_desc' ? 'selected' : '') ?> value="post_title_desc"><?php esc_html_e( 'Title z-a', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='price' ? 'selected' : '') ?> value="price"><?php esc_html_e( 'Price high to low', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='price_low' ? 'selected' : '') ?> value="price_low"><?php esc_html_e( 'Price low to high', 'histudy' ); ?></option>
                                                        <option <?php echo esc_attr($seleccted=='rating' ? 'selected' : '') ?> value="rating"><?php esc_html_e( 'Average Ratings', 'histudy' ); ?></option>
                                                    </select>
                                                </div>
                                            </form>


                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php } ?>
                        </div>

                        <?php if (class_exists('LearnPress')) : 

                            if( $course_sidebar__pos != 'layout-2' && $course_sidebar__pos != 'layout-3') {
                               
                                $filter_open = 'none';
                                if( $show_archive_filter == 'yes') {
                                    $filter_open = 'block';
                                }
                            ?>
                            <div class="rbt-lp-course-sidebar-filter" style="display: <?php echo esc_attr($filter_open); ?>;">
                                <?php

                                if ( is_active_sidebar('course-sidebar') ) {
                                    dynamic_sidebar('course-sidebar');
                                }

                                ?>
                            </div>
                            <?php } ?>
                    </div>
                <?php endif; ?>
                </div>
                <!-- End Course Top  -->

            </div>
        </div>

<?php
    }
}


function histudy_course_single_breadcrumb () { 
    global $rainbow_options;
    $rainbow_course_details_brd_image = isset( $rainbow_options['rainbow_learnpress_course_details_brd_image'] ) ? $rainbow_options['rainbow_learnpress_course_details_brd_image'] : '';
    $rainbow_course_details_gradient_image = isset( $rainbow_options['rainbow_learnpress_course_details_gradient_image'] ) ? $rainbow_options['rainbow_learnpress_course_details_gradient_image'] : '';
    $rainbow_course_details_title_switch = isset( $rainbow_options['rainbow_course_details_title_switch'] ) ? $rainbow_options['rainbow_course_details_title_switch'] : '';
    $rainbow_course_details_bestsellar_student_count_switch = isset( $rainbow_options['rainbow_course_details_bestsellar_student_count_switch'] ) ? $rainbow_options['rainbow_course_details_bestsellar_student_count_switch'] : '';
    $rainbow_course_details_bestsellar_rating_switch = isset( $rainbow_options['rainbow_course_details_bestsellar_rating_switch'] ) ? $rainbow_options['rainbow_course_details_bestsellar_rating_switch'] : '';
    $lp_rainbow_single_course_student_enroll_image = isset( $rainbow_options['lp_rainbow_single_course_student_enroll_image']['url'] ) ? $rainbow_options['lp_rainbow_single_course_student_enroll_image']['url'] : '';

    $rainbow_course_details_layout = isset( $rainbow_options['lp_rainbow_course_details_layout'] ) ? $rainbow_options['lp_rainbow_course_details_layout'] : '';

    if( !empty(get_query_var( 'course_single_layout' )) ) {
		$rainbow_course_details_layout = get_query_var( 'course_single_layout', 'layout-1' );
	}


    if( $rainbow_course_details_layout == 'layout-4') {
        $rainbow_course_details_brd_image = isset( $rainbow_options['lp_rainbow_course_details_brd_image_layout4'] ) ? $rainbow_options['lp_rainbow_course_details_brd_image_layout4'] : '';
    } else {
        $rainbow_course_details_brd_image = isset( $rainbow_options['rainbow_learnpress_course_details_gradient_image'] ) ? $rainbow_options['rainbow_learnpress_course_details_gradient_image'] : '';
    }
    
    $image_url = '';
    $image_color = '';
    $gradient_color = '';

    if ( class_exists('ACF') ) {
        
       // $image_url = get_field('rb_each_course_banner_image', get_the_ID());
        $image_url = get_field('learnpress_course_baner_image', get_the_ID());

        if(empty( $image_url ) ) {
            $image_url = isset($rainbow_course_details_brd_image['background-image']) ? $rainbow_course_details_brd_image['background-image'] : '';
            $image_color = isset($rainbow_course_details_brd_image['background-color']) ? $rainbow_course_details_brd_image['background-color'] : '';
            $gradient_color = isset($rainbow_course_details_gradient_image) ? $rainbow_course_details_gradient_image: '';
        }
    }

    $author_id         = get_post_field('post_author', get_the_ID() );
	$author_image      = get_avatar_url($author_id);
	$author_image_alt  = get_the_author_meta('description', $author_id);
	$author_name      = get_the_author_meta('display_name', $author_id);

    // blank variables
	$first_category = '';
	// used value variables
	$categories = get_the_terms( get_the_ID(), 'course-category');

    $course_id = get_the_ID();

	if(!empty($categories) && is_array( $categories ) ) {
		$first_category = array_shift($categories);
		$category_name  = $first_category->name;
		$category_id        = $first_category->term_id;
		$category_link      = get_term_link($category_id, 'course-category');
	}

    $language = Elementor\rainbow_get_acf_data('rbt_course_language');

    $is_course_featured = function_exists( 'get_field' ) ? get_field('featured_course', $course_id): '';

    $course = LP_Course::get_course($course_id);

    $student = $course->get_total_user_enrolled_or_purchased();
    $student_count  = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );

    $course_rate_res = learn_press_get_course_rate( $course_id, false );

    $rated = $course_rate_res['rated'] ?? 0;
    $total = $course_rate_res['total'] ?? 0;

    if ($rated > 0) {
        $average_rating = round( $total / $rated,2 );
    } else {
        $average_rating = 0;
    }

    $instructor = $course->get_instructor();

    if( $instructor ){
        $instructorId =  $instructor->get_id();
    } 

    

    $course_id = get_the_ID();
    $course_single_video = function_exists('get_field') ? get_field('course_single_video', $course_id) : '#';

    ?>
    <?php if( $rainbow_course_details_layout == 'layout-2' ) { ?>
    <!-- ********************* Banner ******************** -->
    <div class="rbt-page-banner-wrapper">
        <!-- Start Banner BG Image  -->
        <div class="rbt-banner-image"></div>
        <!-- End Banner BG Image  -->
        <div class="rbt-banner-content">
            <!-- Start Banner Content Top  -->
            <div class="rbt-banner-content-top rbt-breadcrumb-style-3">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="content text-center">
                                <div class="rbt-course-single-layout-2">
                                    <div class="d-flex align-items-center flex-wrap justify-content-center mb--15 rbt-course-details-feature">
                                        <?php if(1 == $is_course_featured) : ?>
                                        <div class="feature-sin best-seller-badge">
                                            <span class="rbt-badge-2">
                                                <span class="image"><img src="<?php echo esc_url( get_template_directory_uri() .'/assets/images/rbt-common/card-icon-1.png');?>"
                                                        alt="Best Seller Icon"></span> <?php echo esc_html__('Bestseller', 'histudy'); ?>
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                        <?php if(1 == $rainbow_course_details_bestsellar_rating_switch) : ?>
                                            <div class="feature-sin rating">
                                                <?php
                                                if(class_exists('LP_Addon_Course_Review_Preload') ) {
                                                    LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                                                }
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if(1 == $rainbow_course_details_bestsellar_student_count_switch) : ?>
                                        <div class="feature-sin total-student">
                                            <span><?php echo esc_html($student_count);?> </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if(1 == $rainbow_course_details_title_switch) : ?>
                                    <h2 class="title theme-gradient"><?php echo get_the_title(); ?></h2>
                                <?php endif; ?>

                                <div class="rbt-author-meta mb--20 justify-content-center">
                                    <?php if(!empty($author_image)) : ?>
                                        <div class="rbt-avater">
                                            <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>">
                                                <img src="<?php echo esc_url($author_image); ?>" alt="<?php echo esc_attr( $author_image_alt) ? esc_attr($author_image_alt): ''; ?>">
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                        <?php if(!empty($author_name)) : ?>
                                        <div class="rbt-author-info">
                                            <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>"><?php echo esc_html( ucwords( $author_name )); ?></a>
                                            <?php if(!empty($first_category)) : ?>
                                            <?php echo esc_html__('In', 'histudy'); ?> <a href="<?php echo esc_url( $category_link ) ? esc_url($category_link): ''; ?>"><?php echo esc_html($category_name); ?></a>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                </div>
                                <ul class="rbt-meta rbt-course-single-meta-2">
                                    <li><i class="feather-calendar"></i><?php echo esc_html__("Last updated","histudy"); ?> <?php echo wp_kses_post( get_the_modified_date( get_option( 'date_format' ) ) ); ?></li>
                                    <?php if(!empty($language)) : ?>
                                    <li><i class="feather-globe"></i><?php echo esc_html($language); ?></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Banner Content Top  -->
        </div>
    </div>
    <?php } elseif( $rainbow_course_details_layout == 'layout-3' ) { ?>
        <!-- Start breadcrumb Area -->
		<div class="rbt-breadcrumb-default rbt-breadcrumb-style-3 histdylp-course-single-three">
			<div class="breadcrumb-inner">
				<?php if(!empty($image_url)) { ?>
					<img src="<?php echo esc_url($image_url);?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php } else { 
					if(empty($image_color) && empty( $gradient_color ) ) {
					?>
				<img src="<?php echo esc_url( get_template_directory_uri() .'/assets/images/rbt-common/bg-image-10.jpg');?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php }} ?>
			</div>
			<div class="container">
				<div class="row rbt-breadcrumb-three-row-part">
					<div class="col-lg-7 rbt-course-single-video-mobile rbt-course-single-layout3-content-part">
						<div class="content text-start rbt-new-layout3-left-part">
							
							<?php rainbow_breadcrumbs(); ?>
							
							<div class="rbt-single-student-img-enroll">
								<?php
									if(!empty($lp_rainbow_single_course_student_enroll_image) ) { 
								?>
								<img src="<?php echo esc_url( $lp_rainbow_single_course_student_enroll_image ); ?>" alt="<?php echo esc_html__('Best Seller Icon', 'histudy'); ?>">
								<?php } ?>
								<div class="enroll-total-student">
									<span class="student-count"><?php echo wp_kses_post( $student );?></span>
									<span class="enroll-text"><?php echo esc_html__("Students Enrolled","histudy"); ?></span>
								</div>
							</div>
							<?php if(1 == $rainbow_course_details_title_switch) : ?>
								<h2 class="title"><?php echo get_the_title(); ?></h2>
							<?php endif; 
								$content = get_the_content(); 
								$trimmed_content = wp_trim_words($content, 23);
								?>
								<p class="description"><?php echo wp_kses_post( $trimmed_content ); ?></p>
							
							<div class="course-meta-bottom-part">
                                <div class="rbt-author-meta">
                                    <?php if(!empty($author_name)) : ?>
                                    <i class="feather-user"></i>
                                    <div class="rbt-author-info">
                                    <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>"><?php echo esc_html($author_name); ?></a>
                                    </div>
                                    <?php endif; ?>
                                </div>
								<ul class="rbt-meta">
									<li><i class="feather-calendar"></i><?php echo isset( $last_updated ) ? esc_html($last_updated): get_the_date(); ?></li>
								</ul>
								<div class="d-flex align-items-center  flex-wrap rbt-course-details-feature course-details-layout3-brd">
									<?php if(1 == $rainbow_course_details_bestsellar_rating_switch) : ?>
										<i class="feather-thumbs-up"></i><div class="feature-sin rating">
                                        <?php
                                                if(class_exists('LP_Addon_Course_Review_Preload') ) {
                                                    LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                                                }
                                                ?>
										</div>
										<?php 
											$rating_text = esc_html__('Reviews','histudy');
										?>
										<div class="feature-sin total-rating total-rating-remove-bg">
											<a  href="#tutor-course-details-tab-reviews">(<?php echo esc_html( $total ); ?> <?php echo wp_kses_post( $rating_text ); ?>)</a>
										</div>
										
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-5">
                        <?php if(!empty($course_single_video)) { ?>
                            <a class="video-popup-with-text video-popup-wrapper text-center popup-video sidebar-video-hidden mb--15" href="<?php echo esc_url($course_single_video); ?>">
                                <?php } ?>
                                <div class="rbt-course-feature-has-video-thumbnail rbt-course-feature-box rbt-shadow-box thuumbnail rbt-layout3-brd-video-part">
                                    <div class="video-content">
                                        <?php single_course_sidebar_thumbnail(); ?>
                                        <?php if(!empty($course_single_video)) { ?>
                                        <div class="position-to-top">
                                            <span class="rbt-btn rounded-player-2 with-animation">
                                                <span class="play-icon"></span>
                                            </span>
                                        </div>
                                <?php } ?>
                                    </div>
                                </div>
                                <?php if(!empty($course_single_video)) { ?>
                            </a>
                        <?php }  ?>
                        <!-- End Viedo Wrapper  -->
					</div>
				</div>
			</div>
		</div>
		<!-- End Breadcrumb Area -->
   <?php } elseif( $rainbow_course_details_layout == 'layout-4' ) {  ?>

    <div class="rbt-breadcrumb-default rbt-breadcrumb-style-3 rbt-course-single-brd-layout-four">
			<div class="breadcrumb-inner">
				<?php if(!empty($image_url)) { ?>
					<img src="<?php echo esc_url($image_url);?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php } else { 
					if(empty($image_color) && empty( $gradient_color ) ) {
					?>
				<img src="<?php echo esc_url( get_template_directory_uri() .'/assets/images/rbt-common/bg-image-10.jpg');?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php }} ?>
			</div>
			<div class="container">
				<div class="row rbt-breadcrumb-three-row-part">
					<div class="col-lg-8 rbt-course-single-video-mobile rbt-course-single-layout3-content-part">
						<div class="content text-start rbt-new-layout3-left-part">
							<?php rainbow_breadcrumbs(); ?>
							<div class="rbt-single-student-img-enroll">
								<?php
									if(!empty($lp_rainbow_single_course_student_enroll_image) ) { 
								?>
								<img src="<?php echo esc_url( $lp_rainbow_single_course_student_enroll_image ); ?>" alt="Best Seller Icon">
								<?php } ?>
								<div class="enroll-total-student">
									<span class="student-count"><?php echo wp_kses_post( $student );?></span>
									<span class="enroll-text"><?php echo esc_html__("Students Enrolled","histudy"); ?></span>
								</div>
							</div>
							<?php if(1 == $rainbow_course_details_title_switch) : ?>
								<h2 class="title"><?php echo get_the_title(); ?></h2>
							<?php endif; 
								$content = get_the_content(); 
								$trimmed_content = wp_trim_words($content, 23);
								?>
								<p class="description"><?php echo wp_kses_post( $trimmed_content ); ?></p>
							
                                <div class="course-meta-bottom-part">
                                <div class="rbt-author-meta">
                                    <?php if(!empty($author_name)) : ?>
                                    <i class="feather-user"></i>
                                    <div class="rbt-author-info">
                                    <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>"><?php echo esc_html($author_name); ?></a>
                                    </div>
                                    <?php endif; ?>
                                </div>
								<ul class="rbt-meta">
									<li><i class="feather-calendar"></i><?php echo isset( $last_updated ) ? esc_html($last_updated): get_the_date(); ?></li>
								</ul>
								<div class="d-flex align-items-center  flex-wrap rbt-course-details-feature course-details-layout3-brd">
									<?php if(1 == $rainbow_course_details_bestsellar_rating_switch) : ?>
										<i class="feather-thumbs-up"></i><div class="feature-sin rating">
                                        <?php
                                                if(class_exists('LP_Addon_Course_Review_Preload') ) {
                                                    LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                                                }
                                                ?>
										</div>
										<?php 
											$rating_text = esc_html__('Reviews','histudy');
										?>
										<div class="feature-sin total-rating total-rating-remove-bg">
											<a  href="#tutor-course-details-tab-reviews">(<?php echo esc_html( $total ); ?> <?php echo wp_kses_post( $rating_text); ?>)</a>
										</div>
										
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
    <?php } elseif( $rainbow_course_details_layout == 'layout-5' ) { ?>
        <!-- Start breadcrumb Area -->
		<div class="rbt-breadcrumb-default rbt-breadcrumb-style-3 rbt-course-single-brd-layout-five">
			<div class="breadcrumb-inner">
				<?php if(!empty($image_url)) { ?>
					<img src="<?php echo esc_url($image_url);?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php } else { 
					if(empty($image_color) && empty( $gradient_color ) ) {
					?>
				<img src="<?php echo esc_url( get_template_directory_uri() .'/assets/images/rbt-common/bg-image-10.jpg');?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php }} ?>
			</div>
			<div class="container">
				<div class="row rbt-breadcrumb-three-row-part">
					<div class="col-lg-8 rbt-course-single-video-mobile rbt-course-single-layout3-content-part">
						<div class="content text-start rbt-new-layout3-left-part">
							<?php rainbow_breadcrumbs(); ?>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- End Breadcrumb Area -->
    <?php } else { ?>
    <!-- Start breadcrumb Area -->
    <div class="rbt-breadcrumb-default rbt-breadcrumb-style-3 course-details-breadcrumb-learnpress">
        <div class="breadcrumb-inner breadcrumb-dark">
        <?php if(!empty($image_url)) { ?>
					<img src="<?php echo esc_url($image_url);?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php } else { 
					if(empty($image_color) && empty( $gradient_color ) ) {
					?>
				<img src="<?php echo esc_url( get_template_directory_uri() .'/assets/images/rbt-common/bg-image-10.jpg');?>" alt="<?php echo esc_html__('Education Images', 'histudy'); ?>">
				<?php }} ?>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="content text-start">
                        <?php rainbow_breadcrumbs(); ?>
                        <h2 class="title"><?php echo get_the_title(); ?></h2>
                        <p class="description"><?php echo wp_trim_words(get_the_excerpt(), 23); ?></p>

                        <div class="d-flex align-items-center mb--20 flex-wrap rbt-course-details-feature">
                            <?php if(1 == $is_course_featured) : ?>
                            <div class="feature-sin best-seller-badge">
                                <span class="rbt-badge-2">
                                    <span class="image"><img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/rbt-common/card-icon-1.png' ); ?>" 
                                    alt="Best Seller Icon"></span> <?php echo esc_html__('Bestseller', 'histudy'); ?>
                                </span>
                            </div>
                            <?php endif; ?>

                            <div class="feature-sin rating learnpress-single-page-rating">
                                <?php if( $average_rating > 0 ) { ?>
                                <a href="#"><?php echo esc_html($average_rating);?></a>
                                <?php } ?>
                                <?php
                                if(class_exists('LP_Addon_Course_Review_Preload') ) {
                                    LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                                }
                                ?>
                            </div>

                            <div class="feature-sin total-rating">
                                <a class="rbt-badge-4" href="#"><?php echo esc_html( $total ); ?> <?php echo esc_html__("rating","histudy"); ?> </a>
                            </div>

                            <div class="feature-sin total-student">
                                <span><?php echo esc_html($student_count);?></span>
                            </div>

                        </div>

                        <?php if($instructor){ ?>
                        <div class="rbt-author-meta mb--20">
                            <?php if(!empty($author_image)) : ?>
                            <div class="rbt-avater">
                                <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>">
                                    <img src="<?php echo esc_url($author_image); ?>" alt="<?php echo esc_attr( $author_image_alt) ? esc_attr($author_image_alt): ''; ?>">
                                </a>
                            </div>
                            <?php endif; ?>
                            <?php if(!empty($author_name)) : ?>
                            <div class="rbt-author-info">
                                <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>"><?php echo esc_html($author_name); ?></a>
                                <?php if(!empty($first_category)) : ?>
                                <?php echo esc_html__('In', 'histudy'); ?> <a href="<?php echo esc_url( $category_link ) ? esc_url($category_link): ''; ?>"><?php echo esc_html($category_name); ?></a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php } ?>
						<?php get_template_part('template-parts/components/price/layout', '1'); ?>
							
                        <ul class="rbt-meta mt-2">
                            <li><i class="feather-calendar"></i><?php echo esc_html__("Last updated","histudy"); ?> <?php echo wp_kses_post( get_the_modified_date( get_option( 'date_format' ) ) ); ?></li>
                            <?php if(!empty($language)) : ?>
                            <li><i class="feather-globe"></i><?php echo esc_html($language); ?></li>
                            <?php endif; ?>
                        </ul>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php } ?>
    <!-- End Breadcrumb Area -->
<?php } 


/**
 * single course thumbnail image
 * @since version 1.7
 * return void
 */

function single_course_sidebar_thumbnail() {
	if ( has_post_thumbnail() ): ?>
        <?php the_post_thumbnail( 'full' ); ?>
	<?php endif;
}


/*course single sidebar info*/
function histudy_lp_course_sidebar_features() {
    
	$course        = LP_Global::course();
	$course_id     = get_the_ID();
	$lecture       = $course->get_items( 'lp_lesson' );
	$lecture       = $lecture ? count( $lecture ) : false;
	$quiz          = $course->get_items( 'lp_quiz' );
	$quiz          = $quiz ? count( $quiz ) : false;
	$duration      = get_post_meta( $course_id, '_lp_duration', true );
	$duration_time = absint( $duration );
	$duration_time = ! empty( $duration_time ) ? $duration_time : false;
	$level = learn_press_get_post_level( $course_id );

	$histudy_lecture       = $course->get_items( 'lp_lesson' );
	$histudy_lecture       = ! empty ( $histudy_lecture ) ? count( $histudy_lecture ) : 0;

	$histudy_quiz = $course->get_items( 'lp_quiz' );
	$histudy_quiz       = ! empty ( $histudy_quiz ) ? count( $histudy_quiz ) : 0;


    $student = $course->get_total_user_enrolled_or_purchased();
    $language = function_exists('get_field') ? get_field('rbt_course_language'): __('English', 'histudy');

    $enrolled = !empty($student) && is_array($student) ? count($student) : 0;
    $modified_date = '';
    if ( is_singular('lp_course') ) {
       $modified_date = get_the_modified_date();
    }

	if ( $duration_time  ) {
		$duration_text = str_replace( $duration_time, '', $duration );
		$duration_text = trim( $duration_text );
		switch ( $duration_text ) {
			case 'minute':
				$duration_text = $duration_time > 1 ? esc_html__( 'Minutes', 'histudy' ) : esc_html__( 'Minute', 'histudy' );
				break;
			case 'hour':
				$duration_text = $duration_time > 1 ? esc_html__( 'Hours', 'histudy' ) : esc_html__( 'Hour', 'histudy' );
				break;
			case 'day':
				$duration_text = $duration_time > 1 ? esc_html__( 'Days', 'histudy' ) : esc_html__( 'Day', 'histudy' );
				break;
			case 'week':
				$duration_text = $duration_time > 1 ? esc_html__( 'Weeks', 'histudy' ) : esc_html__( 'Week', 'histudy' );
				break;
		}
		$duration_html = "$duration_time $duration_text";
	}

	?>
    <ul class="has-show-more-inner-content rbt-course-details-list-wrapper">
        <li><span><?php echo esc_html__("Update","histudy");?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($modified_date);?></span>
        </li>
        <li><span><?php echo esc_html__("Enrolled","histudy");?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html( $enrolled );?></span>
        </li>
        <li><span><?php echo esc_html__("Lectures","histudy");?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html($histudy_lecture); ?></span>
        </li>
        <li><span><?php echo esc_html__("Skill Level","histudy");?></span><span
                class="rbt-feature-value rbt-badge-5"><?php echo esc_html( $level ); ?></span></li>
        <li><span><?php echo esc_html__("Language","histudy");?></span><span
                class="rbt-feature-value rbt-badge-5"><?php echo esc_html($language);?></span></li>
        <li><span><?php echo esc_html__("Quizzes","histudy");?></span><span class="rbt-feature-value rbt-badge-5"><?php echo esc_html( $histudy_quiz );?></span>
        </li>
        <li>
            <span><?php echo esc_html__("Course Duration:","histudy");?> </span>
            <span class="rbt-feature-value rbt-badge-5"><?php echo esc_html( $duration_html );?></span>
        </li>
    </ul>
    <div class="rbt-show-more-btn"><?php echo esc_html__("Show More","histudy");?></div>
	<?php
}


// enrolled course user time
function histudy_user_time() {
    $user = learn_press_get_current_user();
    $course = LP_Global::course();

    if ( ! $user ) {
        return;
    }

    if ( ! $user->has_enrolled_or_finished( $course->get_id() ) ) {
        return;
    }

    /**
     * @var LP_User_Item_Course
     */
    $user_course = $user->get_course_data( $course->get_id() );

    if ( ! $user_course ) {
        return;
    }

    $status          = $user_course->get_status();
    $start_time      = $user_course->get_start_time();
    $end_time        = $user_course->get_end_time();
    $expiration_time = $user_course->get_expiration_time();
    $data            = [
        'status'          => $status,
        'start_time'      => $start_time,
        'end_time'        => $end_time,
        'expiration_time' => $expiration_time,
    ];

    learn_press_get_template(
        'single-course/sidebar/user-time',
        compact( 'data' )
    );
}


function histudy_tab_upper_video() {

    global $rainbow_options;
    $rainbow_course_details_layout = isset( $rainbow_options['lp_rainbow_course_details_layout'] ) ? $rainbow_options['lp_rainbow_course_details_layout'] : '';

    if( !empty(get_query_var( 'course_single_layout' )) ) {
		$rainbow_course_details_layout = get_query_var( 'course_single_layout', 'layout-1' );
	}

    $author_id         = get_post_field('post_author', get_the_ID() );
	$author_image      = get_avatar_url($author_id);
	$author_image_alt  = get_the_author_meta('description', $author_id);
	$author_name      = get_the_author_meta('display_name', $author_id);

    if( $rainbow_course_details_layout == 'layout-5' ) { 

        $course_id = get_the_ID();
        $is_course_featured = function_exists( 'get_field' ) ? get_field('featured_course', $course_id): '';
        $course = LP_Course::get_course($course_id);

        $student = $course->get_total_user_enrolled_or_purchased();
        $student_count  = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );

        $course_rate_res = learn_press_get_course_rate( $course_id, false );
        $rated = $course_rate_res['rated'] ?? 0;
        $total = $course_rate_res['total'] ?? 0;

        if ($rated > 0) {
            $average_rating = round( $total / $rated,2 );
        } else {
            $average_rating = 0;
        }

        $instructor = $course->get_instructor();

        if( $instructor ){
            $instructorId =  $instructor->get_id();
        } 

        $rainbow_course_details_title_switch = isset( $rainbow_options['rainbow_course_details_title_switch'] ) ? $rainbow_options['rainbow_course_details_title_switch'] : '';

        $rainbow_course_details_bestsellar_rating_switch = isset( $rainbow_options['rainbow_course_details_bestsellar_rating_switch'] ) ? $rainbow_options['rainbow_course_details_bestsellar_rating_switch'] : '';

        $lp_rainbow_single_course_student_enroll_image = isset( $rainbow_options['lp_rainbow_single_course_student_enroll_image']['url'] ) ? $rainbow_options['lp_rainbow_single_course_student_enroll_image']['url'] : '';
        
        ?>
        <div class="rbt-course-single-layout3-content-part">
            <div class="content text-start rbt-new-layout3-left-part">
                <div class="rbt-single-student-img-enroll">
                    <?php
                        if(!empty($lp_rainbow_single_course_student_enroll_image) ) { 
                    ?>
                    <img src="<?php echo esc_url( $lp_rainbow_single_course_student_enroll_image ); ?>" alt="Best Seller Icon">
                    <?php } ?>
                    <div class="enroll-total-student">
                        <span class="student-count"><?php echo wp_kses_post( $student );?></span>
                        <span class="enroll-text"><?php echo esc_html__("Students Enrolled","histudy"); ?></span>
                    </div>
                </div>
                <?php if(1 == $rainbow_course_details_title_switch) : ?>
					<h2 class="title"><?php echo get_the_title(); ?></h2>
                <?php endif; 
                $content = get_the_content(); 
                $trimmed_content = wp_trim_words($content, 23);
                ?>
                <p class="description"><?php echo wp_kses_post( $trimmed_content ); ?></p>
                
                <div class="course-meta-bottom-part">
                    <div class="rbt-author-meta">
                        <?php if(!empty($author_name)) : ?>
                        <i class="feather-user"></i>
                        <div class="rbt-author-info">
                        <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( learn_press_user_profile_link( $instructorId ) ); ?>"><?php echo esc_html($author_name); ?></a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <ul class="rbt-meta">
                        <li><i class="feather-calendar"></i><?php echo isset( $last_updated ) ? esc_html($last_updated): get_the_date(); ?></li>
                    </ul>
                    <div class="d-flex align-items-center  flex-wrap rbt-course-details-feature course-details-layout3-brd">
                        <?php if(1 == $rainbow_course_details_bestsellar_rating_switch) : ?>
                            <i class="feather-thumbs-up"></i><div class="feature-sin rating">
                            <?php
                                    if(class_exists('LP_Addon_Course_Review_Preload') ) {
                                        LP_Addon_Course_Review_Preload::$addon->get_template( 'rating-stars.php', [ 'rated' => $rated ] );
                                    }
                                    ?>
                            </div>
                            <?php 
                                $rating_text = esc_html__('Reviews','histudy');
                            ?>
                            <div class="feature-sin total-rating total-rating-remove-bg">
                                <a  href="#tutor-course-details-tab-reviews">(<?php echo esc_html( $total ); ?> <?php echo wp_kses_post( $rating_text ); ?>)</a>
                            </div>
                            
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div> 
   <?php }

    if( $rainbow_course_details_layout == 'layout-1' || $rainbow_course_details_layout == 'layout-4' || $rainbow_course_details_layout == 'layout-5') {

        $course_id = get_the_ID();
        $course_single_video = function_exists('get_field') ? get_field('course_single_video', $course_id) : '#';
        if(has_post_thumbnail()) : ?>
            <div class="rbt-course-feature-has-video-thumbnail rbt-course-feature-box rbt-shadow-box thuumbnail tab-upper-video-content-part mb--30">
                <!-- Start Viedo Wrapper  -->
                <?php if(!empty($course_single_video)) { ?>
                <a class="video-popup-with-text video-popup-wrapper text-center popup-video mb--15" href="<?php echo esc_url($course_single_video); ?>">
                    <?php } ?>
                    <div class="video-content">
                        <?php single_course_sidebar_thumbnail(); ?>
                        <?php if(!empty($course_single_video)) { ?>
                        <div class="position-to-top">
                            <span class="rbt-btn rounded-player-2 with-animation">
                                <span class="play-icon"></span>
                            </span>
                        </div>
                        <?php } ?>
                    </div>
                    <?php if(!empty($course_single_video)) { ?>
                </a>
                <?php } ?>
            </div>
    <?php endif; 
    }
}

function histudy_lp_instructor_tab( $tabs ) {
	$tabs['instructor'] = [
		'title'    => esc_html__( 'Instructor', 'histudy' ),
		'priority' => 42,
		'callback' => 'histudy_lp_instructor_tab_contents',
	];

	return $tabs;
}

// // default show overview tabs

function histudy_lp_show_overview_tab_always( $tabs ) {
	if ( empty( $tabs['overview'] ) ) {
		$overview = [
			'title'    => esc_html__( 'Overview', 'histudy' ),
			'priority' => 10,
			'callback' => 'learn_press_course_overview_tab',
		];
		$tabs     = [ 'overview' => $overview ] + $tabs;
	}
	return $tabs;
}

function histudy_lp_instructor_tab_contents() {
	learn_press_get_template( 'custom/instructor-tab-contents.php' );
}

function histudy_lp_modify_reviews_tab( $tabs ) {
	$tabs['reviews']['priority'] = 50;

	return $tabs;
}

//Calculate lesson duration
function histudy_lp_lesson_duration( $lesson_id ) {
	$duration     = get_post_meta( $lesson_id, '_lp_duration', true );
	$duration_val = absint( $duration );
	// when disabled
	if ( empty( $duration_val ) ) {
		return false;
	}
	// when week
	if ( strrpos( $duration, 'week' ) ) {
		$weektext = ( $duration > 1 ) ? esc_html__( 'Weeks', 'histudy' ) : esc_html__( 'Week', 'histudy' );
		return "$duration_val $weektext";
	}
	// when week
	if ( strrpos( $duration, 'day' ) ) {
		$daytext = ( $duration > 1 ) ? esc_html__( 'Days', 'histudy' ) : esc_html__( 'Day', 'histudy' );
		return "$duration_val $daytext";
	}
	// when hour
	if ( strrpos( $duration, 'hour' ) ) {
		return $duration_val . esc_html__( 'h', 'histudy' );
	}
	// when min
	$hour = floor( $duration_val / 60 );
	if ( $hour == 0 ) {
		$hour = '';
	} else {
		$hour = $hour . esc_html__( 'h', 'histudy' );
	}
	$minute = $duration_val % 60;
	$minute = $minute . esc_html__( 'm', 'histudy' );

	return $hour . $minute;
}


// instructor related course 

function instructor_related_course() {
    $author_id = get_post_field('post_author', get_the_ID() );
    Elementor_Helper::instructor_related_course($author_id); 
}

function histudy_related_course_lp() {
    $course_id = get_the_ID();
    Elementor_Helper::related_course($course_id);
}

 


?>