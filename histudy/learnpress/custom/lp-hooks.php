<?php 
/**
 * <AUTHOR>
 * @since   1.0
 * @version 2.7
 */
add_filter( 'learn-press/override-templates', function(){ return true; } );

/*-------------------------------------
#. Course Archive
---------------------------------------*/
remove_action( 'learn-press/before-main-content', 'learn_press_breadcrumb', 10 );
remove_action( 'learn-press/before-main-content', 'learn_press_search_form', 15 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_courses_loop_item_begin_meta', 10 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_courses_loop_item_price', 20 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_courses_loop_item_instructor', 25 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_courses_loop_item_end_meta', 30 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_course_loop_item_buttons', 35 );
remove_action( 'learn-press/after-courses-loop-item', 'learn_press_course_loop_item_user_progress', 40 );

if (version_compare(LEARNPRESS_VERSION, '4', '>=')) {
    remove_action( 'learn-press/before-main-content', LP()->template( 'general' )->func( 'breadcrumb' ) );
   remove_action( 'learn-press/before-courses-loop', LP()->template( 'course' )->func( 'courses_top_bar' ), 10 );
}

add_action( 'learn-press/before-main-content', 'histudy_lp_archive_top_search' );



// course single page remove hooks

// When user not enrolled
remove_action( 'learn-press/content-landing-summary', 'learn_press_course_meta_start_wrapper', 5 );
remove_action( 'learn-press/content-landing-summary', 'learn_press_course_students', 10 );
remove_action( 'learn-press/content-landing-summary', 'learn_press_course_meta_end_wrapper', 15 );
remove_action( 'learn-press/content-landing-summary', 'learn_press_course_price', 25 );
remove_action( 'learn-press/content-landing-summary', 'learn_press_course_buttons', 30 );

// When user enrolled
remove_action( 'learn-press/content-learning-summary', 'learn_press_course_meta_start_wrapper', 10 );
remove_action( 'learn-press/content-learning-summary', 'learn_press_course_students', 15 );
remove_action( 'learn-press/content-learning-summary', 'learn_press_course_meta_end_wrapper', 20 );
remove_action( 'learn-press/content-learning-summary', 'learn_press_course_progress', 25 );
remove_action( 'learn-press/content-learning-summary', 'learn_press_course_buttons', 40 );


//top content
if (version_compare(LEARNPRESS_VERSION, '4', '>=')) {
    LP()->template('course')->remove('learn-press/course-content-summary', array('<div class="course-detail-info"> <div class="lp-content-area"> <div class="course-info-left">', 'course-info-left-open'), 10);
    LP()->template('course')->remove_callback('learn-press/course-content-summary', 'single-course/meta-primary', 10);
    LP()->template('course')->remove_callback('learn-press/course-content-summary', 'single-course/title', 10);
    LP()->template('course')->remove_callback('learn-press/course-content-summary', 'single-course/meta-secondary', 10);
    LP()->template('course')->remove('learn-press/course-content-summary', array('</div> </div> </div>', 'course-info-left-close'), 15);
    LP()->template('course')->remove('learn-press/course-content-summary',array( '<div class="lp-entry-content lp-content-area">', 'lp-entry-content-open' ),30);
    LP()->template('course')->remove('learn-press/course-content-summary',array( '</div">', 'lp-entry-content-close' ),30);

    add_action(
        'learn-press/course-content-summary',
        LP()->template( 'course' )->text( '<div class="course-main-content">', 'course-main-content-open' ),
        36
    );
    add_action(
        'learn-press/course-content-summary',
        LP()->template( 'course' )->text( '<!-- end course-main-content --> </div>', 'course-main-content-close' ),
        71
    );
}


function custom_course_content_summary() {
    $rainbow_course_details_layout = isset( $rainbow_options['lp_rainbow_course_details_layout'] ) ? $rainbow_options['lp_rainbow_course_details_layout'] : '';

    if( !empty(get_query_var( 'course_single_layout' )) ) {
		$rainbow_course_details_layout = get_query_var( 'course_single_layout', 'layout-1' );
	}

    $course_details_class = 'rbt-course-details-area ptb--60';

    if( $rainbow_course_details_layout == 'layout-2') {
        $course_details_class = 'rbt-section-overlayping-top rbt-section-gapBottom rbt-course-details-content-2 rbt-course-details-area';
    }
    elseif( $rainbow_course_details_layout == 'layout-3') {
        $course_details_class = 'rbt-course-details-area ptb--60 rbt-lp-course-details-style-threee';
    }
    elseif( $rainbow_course_details_layout == 'layout-4') {
        $course_details_class = 'rbt-course-details-area ptb--60 rbt-lp-course-details-four';
    }
    elseif( $rainbow_course_details_layout == 'layout-5') {
        $course_details_class = 'rbt-course-details-area ptb--60 rbt-course-details-layout-style-five';
    }

    

    $course_id = get_the_ID();
    $course_single_video = function_exists('get_field') ? get_field('course_single_video', $course_id) : '#';

    if( $rainbow_course_details_layout == 'layout-2') {

        echo '<div class="' . esc_attr( $course_details_class ) . '"><div class="container"><div class="row">';
        echo '<div class="col-12">';
        if ( has_post_thumbnail() ) {
            echo '<div class="rbt-course-feature-has-video-thumbnail rbt-course-feature-box rbt-shadow-box thumbnail mb--40">';
           ?>
            <!-- Start Viedo Wrapper  -->
            <?php if(!empty($course_single_video)) { ?>
            <a class="video-popup-with-text video-popup-wrapper text-center popup-video mb--15" href="<?php echo esc_url($course_single_video); ?>">
                <?php } ?>
                <div class="video-content">
                    <?php single_course_sidebar_thumbnail(); ?>
                    <?php if(!empty($course_single_video)) { ?>
                    <div class="position-to-top">
                        <span class="rbt-btn rounded-player-2 with-animation">
                            <span class="play-icon"></span>
                        </span>
                    </div>
                    <?php } ?>
                </div>
                <?php if(!empty($course_single_video)) { ?>
            </a>
            <?php } 
            echo '</div>';
        }
        echo '</div></div></div>';
        echo '<div class="lp-entry-content lp-content-area">';
    } else {
        echo '<div class="' . esc_attr( $course_details_class ) . '">';
        echo '<div class="lp-entry-content lp-content-area">';

    }
}

// Hook the function into the 'learn-press/course-content-summary' action hook
add_action( 'learn-press/course-content-summary', 'custom_course_content_summary', 30 );


add_action(
	'learn-press/course-content-summary',
	LearnPress::instance()->template( 'course' )->text( ' </div></div>', 'lp-entry-content-close' ),
	100
);



// Overview - Include features information
remove_action('learn-press/course-content-summary', 'single_course_thumbnail', 50 );
add_action('learn-press/course-content-summary', 'instructor_related_course', 100);
add_action('learn-press/course-content-summary', 'histudy_related_course_lp', 101);


// Content
add_action('learn-press/course-content-summary',"histudy_course_single_breadcrumb",10 );



// Tabs property change
add_filter( 'learn-press/course-tabs', 'histudy_lp_instructor_tab' , 5 ); // Add instructor tab
add_filter( 'learn-press/course-tabs', 'histudy_lp_show_overview_tab_always' , 5 ); // Show overview tab even if no contents

if ( class_exists( 'LP_Addon_Course_Review' ) ) {
	add_filter( 'learn-press/course-tabs', 'histudy_lp_modify_reviews_tab' , 6 ); // Modify Reviews Tab
}


add_action('learn-press/course-content-summary','histudy_tab_upper_video',35);