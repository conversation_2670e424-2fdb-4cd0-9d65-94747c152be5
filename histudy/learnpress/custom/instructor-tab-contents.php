<?php
/**
 * <AUTHOR>
 * @since   2.0
 * @version 2.3
 */

$course_id   = get_the_ID();
$author_id   = get_post_field( 'post_author', $course_id );
$author_name = get_the_author_meta( 'display_name', $author_id );
$author_bio  = get_user_meta( $author_id, 'description', true );
$author_link = learn_press_user_profile_link( get_post_field( 'post_author', $course_id ) );

$args2 = array(
	'post_type'           => 'lp_course',
	'post_status'         => 'publish',
	'suppress_filters'    => false,
	'ignore_sticky_posts' => 1,
	'numberposts'      => -1,
	'author'              => $author_id
);

$courses = get_posts( $args2 );
$course_count = sizeof( $courses );                
$course_count_text  = $course_count == 1 ? esc_html__( 'Course', 'histudy' ) : esc_html__( 'Courses', 'histudy' );

$enroll_count = 0;
foreach ( $courses as $each_course ) {
	$course = learn_press_get_course( $each_course->ID );
	$enroll_count += $course->get_users_enrolled();
}
$enroll_count_text  = $enroll_count == 1 ? esc_html__( 'Student', 'histudy' ) : esc_html__( 'Students', 'histudy' );


global $rainbow_options;

$author_id              = get_post_field('post_author', $course_id);
$author_image           = get_avatar_url($author_id);
$author_image_alt       = get_the_author_meta('description', $author_id);
$author_name      = get_the_author_meta('display_name', $author_id);
$author_firstname = get_the_author_meta('first_name', $author_id) ? get_the_author_meta('first_name', $author_id): '';
$author_lastname  = get_the_author_meta('last_name', $author_id) ? get_the_author_meta('last_name', $author_id)  : '';
$custom_field_value = function_exists('get_field') ? get_field('user_designation_key', 'user_' . $author_id): '';
$author_fullname  = $author_firstname . ' '.$author_lastname;

$instructor_title = isset( $rainbow_options['lp_rainbow_course_details_instructor_title'] ) ? $rainbow_options['lp_rainbow_course_details_instructor_title'] : '';

$total_reviews = 0;
$total_rating_sum = 0;
foreach ( $courses as $each_course ) {
	
	$course_rate_res = learn_press_get_course_rate( $each_course->ID, false );
	$rated = $course_rate_res['rated'] ?? 0; // Number of reviews
    $total = $course_rate_res['total'] ?? 0; // Total rating score (sum of all reviews)

        // Step 3: Add the course's total reviews and total rating to the cumulative sums
	$total_reviews += $rated;
	$total_rating_sum += $total;
	
}

$review_count_text  = $total_reviews == 1 || $total_reviews == '0' ? esc_html__( 'Review', 'histudy' ) : esc_html__( 'Reviews', 'histudy' );
if ($rated > 0) {
	$average_rating = round( $total / $rated,2 );
} else {
	$average_rating = 0;
}

$author_courses_link = add_query_arg('post_type', 'lp_course', get_author_posts_url($author_id));

?>

<?php if(!empty($rainbow_options['lp_rainbow_course_details_show_instructor_profile'])) : ?>
	<!-- Start Intructor Area  -->
	<div class="rbt-instructor rbt-shadow-box intructor-wrapper mt--30" id="intructor">
		<div class="about-author border-0 pb--0 pt--0">
			<div class="section-title mb--30">
				<h4 class="rbt-title-style-3"><?php echo esc_html($instructor_title); ?></h4>
			</div>
			<div class="media align-items-center">
				<div class="thumbnail">
					<a href="<?php echo esc_url($author_link); ?>">
						<img src="<?php echo esc_url($author_image); ?>" alt="<?php echo esc_attr($author_image_alt)  ? esc_attr($author_image_alt): ''; ?>">
					</a>
				</div>
				<div class="media-body">
					<div class="author-info">
						<h5 class="title">
							<a class="hover-flip-item-wrapper" href="<?php echo esc_url($author_link); ?>"><?php echo esc_html($author_fullname); ?></a>
						</h5>
						<?php if(!empty($custom_field_value)) : ?>
							<span class="b3 subtitle"><?php echo esc_html($custom_field_value); ?></span>
						<?php endif; ?>

						<ul class="rbt-meta mb--20 mt--10">
							<li><i class="fa fa-star color-warning"></i><?php echo sprintf( "%d $review_count_text", $total_reviews );?> <span class="rbt-badge-5 ml--5"><?php echo esc_html($average_rating);?> <?php echo esc_html__("Rating","histudy"); ?></span></li>
							<?php if( $enroll_count_text) { ?>
							<li><i class="feather-users"></i><?php echo sprintf( "%d $enroll_count_text", $enroll_count );?></li>
							<?php } ?>
							<li><a href="<?php echo esc_url($author_courses_link); ?>"><i class="feather-video"></i><?php echo sprintf( "%d $course_count_text", $course_count );?></a></li>
						</ul>
					</div>
					<div class="content">
						<?php
							if ( $author_bio ) {
								?>
								<p  class="description"><?php echo wp_kses_post(  $author_bio ); ?></p>
								<?php
							} 
						?>
						<?php if (class_exists('ACF')) { ?>
							<?php if (have_rows('rainbow_add_social_icons', 'user_' . $author_id)): ?>
								<ul class="social-icon social-default icon-naked justify-content-start">
									<?php
									while (have_rows('rainbow_add_social_icons', 'user_' . $author_id)): the_row();
										$social_icon = get_sub_field('rainbow_enter_social_icon_markup');
										$social_link = get_sub_field('rainbow_enter_social_icon_link'); ?>
										<li>
											<a href="<?php echo esc_url($social_link); ?>"><?php echo rainbow_awescapeing($social_icon); ?></a>
										</li> <?php
									endwhile;
									?>
								</ul>
							<?php endif; ?>
						<?php } ?>
					</div>

				</div>
			</div>
		</div>
	</div>
	<!-- End Intructor Area  -->
<?php endif; ?>





