<?php 
    // generic
    $course_id          = get_the_ID();
    $content = get_the_excerpt();
    $content = apply_filters( 'the_content', $content );
    $content = wp_trim_words( get_the_excerpt(),  '12', '.' );
    $allowed_tags = wp_kses_allowed_html( 'post' );
    $course_attributes      = get_post_meta($course_id);
    /**
     * Author attributes
     */
    $author_id              = get_post_field('post_author', $course_id);
    $author_image           = get_avatar_url($author_id);
    $author_image_alt       = get_the_author_meta('description', $author_id);
    $author_name            = get_the_author_meta('display_name', $author_id);
    $categories             = get_the_terms($course_id, 'course-category');
    $first_category         = '';
    $category_name          = '';
    $category_id            = '';
    $category_link          = '';


    // review
    $course = LP_Course::get_course($course_id);
    $course_rate_res = learn_press_get_course_rate( $course_id, false );
    $rated = $course_rate_res['rated'] ?? 0;
    $total = $course_rate_res['total'] ?? 0;
    $user = get_userdata($author_id);
    // Get the roles
    $teacher_metadata = isset( $author_id ) ? get_user_meta( $author_id ): array(); 
    $single_instructor_page_id = learn_press_get_page_id( 'single_instructor' );
    $instructor_profile_url = trailingslashit( get_permalink( $single_instructor_page_id ) );
    $profile_url = sprintf('%s%s', $instructor_profile_url, $author_name);
    $user_info = get_userdata($author_id);
    $author_email = $user_info->user_email;
    $thumb_image = get_avatar($author_email);
    $thumb = sprintf(
        '<a href="%s">%s</a>',
        $profile_url,
        $thumb_image
    );
    
    $name             = isset( $teacher_metadata['nickname'] ) ? '<h3 class="title"><a href=' . $profile_url . '>' . $teacher_metadata['nickname'][0] . '</a></h3>' : '';
    $designation = get_field('user_designation', 'user_' . $author_id);
    $student = $course->get_total_user_enrolled_or_purchased();
    $student_count  = sprintf( _n( '%s Student', '%s Students', $student, 'histudy' ), $student );
    $categories = wp_get_post_terms( $course_id, 'course_category' );

    if ($categories && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            $category_name = $category->name;
            $category_id = $category->term_id;
            $category_link = get_term_link($category_id, 'course_category');
        }
    }

    $total_lessons = 0;
    if ($course) {
        $curriculum = $course->get_curriculum();
        foreach ($curriculum as $item) {
            $total_lessons++;
        }
    }

    $sale_price = $course->get_sale_price();
    $current_product_price = isset($sale_price) ? $sale_price : 0;
                               
    $regular_price_value = $course->get_regular_price();
    $regular_price = isset($regular_price_value) ? $regular_price_value : 0;


    if( !empty($regular_price) && $current_product_price > 0 && $regular_price > 0 ) {
        $course_product_id = $course_id;
    } else {
        $course_product_id = null;
    }

    $product_percentage     = 0;
    if($course_product_id) {
        $product_percentage = Rainbow_Helper::rb_get_product_offer_percentage($course_product_id);
    }

    global $rainbow_options;

    $rainbow_course_grid_archive_img_size = isset( $rainbow_options['rainbow_learnpress_course_grid_archive_img_size']) ? $rainbow_options['rainbow_learnpress_course_grid_archive_img_size'] : '';

    $total_lessons  = sprintf( _n( '%s Lesson', '%s Lessons', $total_lessons, 'histudy' ), $total_lessons );

?>

<div class="course-grid-3">
    <?php do_action( 'learn_press_before_courses_loop_item' ); ?>
    <div class="rbt-card variation-01 rbt-hover">
        <?php if (has_post_thumbnail()) { ?>
        <div class="rbt-card-img">
            <a href="<?php echo esc_url( get_permalink( $course_id ) ); ?>">
                <?php
                    $attachment_id = get_post_thumbnail_id( $course_id );
                    echo wp_get_attachment_image( $attachment_id, $rainbow_course_grid_archive_img_size  );
                ?>
                 <?php if(!empty($product_percentage)) : ?>
                    <div class="rbt-badge-3 bg-white">
                        <span>-<?php echo esc_html($product_percentage); ?>%</span>
                        <span><?php echo esc_html__('Off', 'histudy'); ?></span>
                    </div>
                <?php endif; ?>
            </a>
        </div>
        <?php } ?> 
        <div class="rbt-card-body">
            <?php get_template_part('template-parts/components/learnpress/card/card-top'); ?>
            <h4 class="rbt-card-title"><a href="<?php echo esc_url( get_permalink( $course_id ) ); ?>"><?php the_title(); ?></a>
            </h4>
            <ul class="rbt-meta">
                <li><i class="feather-book"></i>
                    <?php echo esc_html( $total_lessons); ?></li>
                <li><i class="feather-users"></i> <?php echo esc_attr($student_count);?></li>
            </ul>

            <?php if ( $content ) { ?>
                <p class="rbt-card-text"><?php echo esc_attr( $content ); ?></p>  
            <?php } ?> 
            <?php if( isset($author_image) && !empty($author_image)) : ?>
            <div class="rbt-author-meta mb--10">
                <div class="rbt-avater">
                    <a href="<?php echo !empty($profile_url) ? esc_url($profile_url): ''; ?>">
                        <img src="<?php echo esc_url($author_image); ?>" alt="<?php echo esc_attr( $author_image_alt) ? esc_attr($author_image_alt): ''; ?>">
                    </a>
                </div>
                <?php endif; ?>
                <?php if(!empty($author_name)) : ?>
                    <div class="rbt-author-info">
                        <?php echo esc_html__('By', 'histudy'); ?> <a href="<?php echo esc_url( $profile_url) ? esc_html($profile_url): ''; ?>"> <?php echo esc_html($author_name); ?></a> <?php echo esc_html__('In', 'histudy'); ?> <a href="<?php echo esc_url( $category_link ) ? esc_url($category_link): '' ?>"><?php echo esc_html( $category_name ) ? esc_html($category_name): ''; ?></a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="rbt-card-bottom">
                <?php get_template_part('template-parts/components/price/layout', 1); ?>
                <a class="rbt-btn-link left-icon" href="<?php echo esc_url( get_permalink( $course_id ) ); ?>"><?php echo esc_html__( 'Learn More', 'histudy' ); ?><i class="feather-arrow-right"></i></a>

            </div>
        </div>
    </div>
    <?php do_action( 'learn_press_after_courses_loop_item' ); ?>
</div>