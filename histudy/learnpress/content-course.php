<?php
/**
 * Template for displaying course content within the loop.
 *
 * This template can be overridden by copying it to yourtheme/learnpress/content-course.php
 *
 * <AUTHOR>
 * @package LearnPress/Templates
 * @version 4.0.0
 */

/**
 * Prevent loading this file directly
 */
defined( 'ABSPATH' ) || exit();
$course_layout  = learn_press_get_courses_layout();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$card_layout = 'layout-1';
$rainbow_learnpress_card_layout_course = isset( $rainbow_options['rainbow_learnpress_card_layout_course'] ) ? $rainbow_options['rainbow_learnpress_card_layout_course'] : 'layout-1';

$card_layout = isset( $_GET['card_layout'] ) ? $_GET['card_layout'] : '';

$course_column_num = isset( $_GET['course_column_num'] ) ? $_GET['course_column_num'] : '';

if( isset( $card_layout ) && !empty( $card_layout ) ) {
	$card_layout = $card_layout;
} else {
	$card_layout = $rainbow_learnpress_card_layout_course;
} 

$course_col_extra_class = '';
if( $course_column_num == '2') {
	$course_col_extra_class = 'histudy-learnpress-course2';
}
elseif( $course_column_num == '3') {
	$course_col_extra_class = 'histudy-learnpress-course3';
}

?>

<li id="post-<?php the_ID(); ?>" <?php post_class($course_col_extra_class); ?>>
	<div class="rt-course-default">
		<input type="hidden" name="histudy_course_layout" value="<?php echo esc_attr( $course_layout ); ?>" id="histudy_course_layout"/>

		<?php 

		if( $card_layout == 'layout-2') {
			learn_press_get_template( 'custom/content-box2.php' ); 
		}
		elseif( $card_layout == 'layout-3') {
			learn_press_get_template( 'custom/content-box-masonary.php' ); 
		} else {
			learn_press_get_template( 'custom/content-box.php' ); 
		}

		?>
		
	</div>
</li>
