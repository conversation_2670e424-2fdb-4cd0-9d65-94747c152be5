<?php

/**
 * Template default for displaying content of archive courses page.
 * If you want to override layout default, please override via hook 'learn-press/list-courses/layout', or another hook inside.
 * Override file is will be soon not support on the feature. Because it is many risks.
 *
 * <AUTHOR>
 * @package LearnPress/Templates
 * @version 4.0.2
 */

// use LearnPress\TemplateHooks\Course\ListCoursesTemplate;
defined('ABSPATH') || exit;

/**
 * @since 4.0.0
 * @see LP_Template_General::template_header()
 */
if (!wp_is_block_theme()) {
    do_action('learn-press/template-header');
}

$page_title = learn_press_page_title(false);
$classes    = [];

if (is_active_sidebar('archive-courses-sidebar')) {
    $classes[] = 'has-sidebar';
}

$card_layout = get_query_var('card_layout');


/**
 * @since 4.2.3.4
 * Filter: lp/show-archive-course/title
 */

/**
 * Redux options
 */

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$course_filter_layout = get_query_var('course_filter_layout');
$banner_layout = isset($rainbow_options['generice_course_archive_banner_layout']) ? $rainbow_options['generice_course_archive_banner_layout'] : '';
$banner_layout_class = $banner_layout == 'layout-1' ? 'banner-layout-1' : 'banner-layout-2';
$generic_banner_breadcrumb_enable = isset($rainbow_options['generic_banner_breadcrumb_enable']) ? $rainbow_options['generic_banner_breadcrumb_enable'] : ' ';
$histudy_learnpress_course_sidebar = isset($rainbow_options['histudy_learnpress_course_breadcrumb_filter_layout']) ? $rainbow_options['histudy_learnpress_course_breadcrumb_filter_layout'] : '';
$breadcrumb_on_off_class = $generic_banner_breadcrumb_enable != '1' ? 'breadcrumb-off-class' : '';

if (isset($course_filter_layout) && !empty($course_filter_layout)) {
    $course_sidebar__pos = $course_filter_layout;
} else {
    $course_sidebar__pos = $histudy_learnpress_course_sidebar;
}

$layout_class = 'col-lg-12'; 
if ( is_active_sidebar('course-sidebar') && ( $course_sidebar__pos == 'layout-2' || $course_sidebar__pos == 'layout-3' ) ) {
	$layout_class = 'col-lg-9';
}

    // Banner
    get_template_part('template-parts/v3/banner/archive', 'course-banner');
    do_action('learn-press/before-main-content');

    if ( ( $banner_layout == 'layout-2' || $banner_layout == 'layout-1')  && $generic_banner_breadcrumb_enable == '1') {
        if ($banner_layout == 'layout-1') { ?>
            <div class="tutor-wrap tutor-wrap-parent tutor-courses-wrap tutor-container course-archive-page">
                <div class="has-filter-layout rbt-section-gapBottom course-layout tutor-course-archive-page ptt-120">
        <?php } else { ?>
            <div class="rbt-section-overlayping-top has-filter-layout rbt-section-gapBottom course-layout tutor-course-archive-page">
        <?php } ?>
            <div class="inner">
                <div class="container">
                    <div class="row row--30">
						<?php 
							if( $course_sidebar__pos == 'layout-3' ) { ?>
							<div class="col-lg-3">
								<div class="rbt-course-details-left-sidebar histudy-learnpress-sidebar-section">
								<?php
									if( is_active_sidebar( 'course-sidebar' ) ) {
										dynamic_sidebar( 'course-sidebar' );
									}
								?>
								</div>
							</div>
						<?php } ?>
                        <div class="<?php echo esc_attr( $layout_class);?> mobile-course-archive-page">
        <?php } ?>

        <div class="lp-content-area <?php echo esc_attr(implode($classes)); ?> <?php echo esc_attr($banner_layout_class . ' ' . $breadcrumb_on_off_class); ?>">
            <div class="lp-main-content">
                <?php if ($page_title && apply_filters('lp/show-archive-course/title', true)) : ?>
                    <header class="learn-press-courses-header"></header>
                <?php endif; ?>

                <?php
                /**
                 * LP Hook
                 */
                do_action('learn-press/before-courses-loop');
                LearnPress::instance()->template('course')->begin_courses_loop();

                if (LP_Settings_Courses::is_ajax_load_courses() && !LP_Settings_Courses::is_no_load_ajax_first_courses()) {
                    echo '<div class="lp-archive-course-skeleton" style="width:100%">';
                    echo lp_skeleton_animation_html(10, 'random', 'height:20px', 'width:100%');
                    echo '</div>';
                } else {
                    if (have_posts()) {
                        while (have_posts()) :
                            the_post();

                            learn_press_get_template_part('content', 'course');
                            
                        endwhile;
                    } else {
                        LearnPress::instance()->template('course')->no_courses_found();
                    }

                    if (LP_Settings_Courses::is_ajax_load_courses()) {
                        echo '<div class="lp-archive-course-skeleton no-first-load-ajax" style="width:100%; display: none">';
                        echo lp_skeleton_animation_html(10, 'random', 'height:20px', 'width:100%');
                        echo '</div>';
                    }
                }

                LearnPress::instance()->template('course')->end_courses_loop();
                do_action('learn-press/after-courses-loop');
                ?>

            </div>
			</div>
            <?php
            if ($banner_layout == 'layout-2' || $banner_layout == 'layout-1') { ?>
        </div>
		<?php if ($banner_layout == 'layout-1') { ?>
    </div>
	<?php } ?>

	<?php
		if ($course_sidebar__pos == 'layout-2') { ?>
			<div class="col-lg-3">
				<div class="rbt-course-details-right-sidebar histudy-learnpress-sidebar-section">
					<?php
						if (is_active_sidebar('course-sidebar')) {
							dynamic_sidebar('course-sidebar');
						}
					?>
				</div>
			</div>
		<?php } ?>
	</div>
</div>
								
</div>

<?php 
		}
		/**
		 * LP Hook
		 * @since 4.0.0
		 */
		do_action('learn-press/archive-course/sidebar');


/**
 * @since 4.0.0
 * @see LP_Template_General::template_footer()
 */
if (!wp_is_block_theme()) {
    do_action('learn-press/template-footer');
}