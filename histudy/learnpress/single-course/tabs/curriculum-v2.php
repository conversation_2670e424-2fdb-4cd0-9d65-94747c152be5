<?php
/**
 * Template for displaying curriculum tab of single course.
 *
 * This template can be overridden by copying it to yourtheme/learnpress/single-course/tabs/curriculum.php.
 *
 * <AUTHOR>
 * @package  Learnpress/Templates
 * @version  4.1.6
 */

defined( 'ABSPATH' ) || exit();

if ( empty( $args ) ) {
	return;
}

if ( isset( $args['sections'] ) && isset( $args['filters'] ) ) {
	$sections = $args['sections'];
	$filters  = $args['filters'];
} else {
	return;
}

$rainbow_options = Rainbow_Helper::rainbow_get_options();
$curriculum_heading = isset( $rainbow_options['lp_rainbow_course_details_course_curriculam_heading'] ) ? $rainbow_options['lp_rainbow_course_details_course_curriculam_heading'] : '';

?>

<div class="course-curriculum learnpress-course-curriculum course-content rbt-shadow-box coursecontent-wrapper mt--30" id="learn-press-course-curriculum">
	<div class="curriculum-scrollable rbt-course-feature-inner">
		<div class="section-title">
			<h4 class="rbt-title-style-3"><?php echo esc_html( $curriculum_heading ); ?></h4>
		</div>
		
					<?php if ( $sections['total'] > 0 ) : ?>
						<ul class="curriculum-sections">
							<?php
							foreach ( $sections['results'] as $section ) :
								
								$args['section'] = $section;
								learn_press_get_template( 'loop/single-course/loop-section', $args );
								
							endforeach;
							?>
						</ul>
					<?php else : ?>
						<p class="histudy-learnpress-curriculum-empty">
						<?php
						echo wp_kses_post( apply_filters( 'learnpress/course/curriculum/empty', esc_html__( 'The curriculum is empty', 'histudy' ) ) );
						?>
						</p>
					<?php endif; ?>
				
	</div>

	<?php if ( $sections['pages'] > 1 && $sections['pages'] > $filters->page ) : ?>
		<div class="curriculum-more">
			<button class="curriculum-more__button" data-page="<?php echo esc_attr( $filters->page ); ?>">
				<?php esc_html_e( 'Show more Sections', 'histudy' ); ?>
			</button>
		</div>
	<?php endif; ?>
</div>