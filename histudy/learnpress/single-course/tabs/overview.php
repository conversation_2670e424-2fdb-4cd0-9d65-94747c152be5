<?php

/**
 * Template for displaying overview tab of single course.
 *
 * This template can be overridden by copying it to yourtheme/learnpress/single-course/tabs/overview.php.
 *
 * <AUTHOR>
 * @package  Learnpress/Templates
 * @version  3.0.0
 */

/**
 * Prevent loading this file directly
 */
defined('ABSPATH') || exit();

/**
 * @var LP_Course $course
 */
$course = learn_press_get_course();
if (! $course) {
	return;
}



$string = apply_filters( 'the_content', $course->get_content() );

$post_size_in_words = sizeof( explode( ' ', $string ) );
	$word_limit     = 100;
	$has_show_more  = false;

if (  $post_size_in_words > $word_limit  ) {
	$has_show_more = true;
}
$rainbow_options = Rainbow_Helper::rainbow_get_options();

$overview_heading = isset( $rainbow_options['lp_rainbow_course_details_overview_heading'] ) ? $rainbow_options['lp_rainbow_course_details_overview_heading'] : '';
	?>


<div class="course-description rbt-course-feature-box overview-wrapper mt--30 has-show-more rbt-shadow-box" id="learn-press-course-description">
	<div class="rbt-course-feature-inner has-show-more-inner-content">
		<div class="section-title">
			<h4 class="rbt-title-style-3"><?php echo esc_html($rainbow_options['lp_rainbow_course_details_overview_heading']); ?></h4>
		</div>

	<?php
	/**
	 * @deprecated
	 */
	do_action('learn_press_begin_single_course_description');

	/**
	 * @since 3.0.0
	 */
	do_action('learn-press/before-single-course-description');

	/*$content = apply_filters( 'the_content', $course->get_content() );
	echo str_replace( ']]>', ']]&gt;', $content );*/
	//echo apply_filters( 'the_content', $string ); //phpcs:ignore 
	learn_press_echo_vuejs_write_on_php( apply_filters( 'the_content', $string ) );

	/**
	 * @since 3.0.0
	 */
	do_action('learn-press/after-single-course-description');

	/**
	 * @deprecated
	 */
	do_action('learn_press_end_single_course_description');
	?>
	</div>
	<div class="rbt-show-more-btn"><?php echo esc_html__("Show More","histudy");?></div>
</div>
