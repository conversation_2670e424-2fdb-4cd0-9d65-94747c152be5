<?php
/**
 * Template for displaying tab nav of single course.
 *
 * This template can be overridden by copying it to yourtheme/learnpress/single-course/tabs/tabs.php.
 *
 * <AUTHOR>
 * @package  Learnpress/Templates
 * @version  4.0.1
 */

defined( 'ABSPATH' ) || exit();

$tabs = learn_press_get_course_tabs();

if ( empty( $tabs ) ) {
	return;
}

$active_tab = 'overview';

// Show status course
$lp_user = learn_press_get_current_user();

if ( $lp_user && ! $lp_user instanceof LP_User_Guest ) {
	$can_view_course = $lp_user->can_view_content_course( get_the_ID() );

	if ( ! $can_view_course->flag ) {
		if ( LP_BLOCK_COURSE_FINISHED === $can_view_course->key ) {
			learn_press_display_message(
				esc_html__( 'You finished this course. This course has been blocked', 'histudy' ),
				'warning'
			);
		} elseif ( LP_BLOCK_COURSE_DURATION_EXPIRE === $can_view_course->key ) {
			learn_press_display_message(
				esc_html__( 'This course has been blocked for expiration', 'histudy' ),
				'warning'
			);
		}
	}
}
?>

<div class="rbt-inner-onepage-navigation sticky-top">
	<nav class="mainmenu-nav onepagenav">
		<ul class="mainmenu">
		<?php foreach ( $tabs as $key => $tab ) : 
			$classes = array( 'course-nav course-nav-tab-' . esc_attr( $key ) );

			if ( $active_tab === $key ) {
				$classes[] = 'active current';
			}

			$replace_title = str_replace( " ","-",$tab['title'] );
			
		?>
			<li class="<?php echo esc_attr( implode( ' ', $classes ) ); ?>">
				<a href="#tab-<?php echo esc_attr( strtolower( $replace_title ) ); ?>"><?php echo esc_html( $tab['title'] ); ?></a>
			</li>
			
		<?php endforeach; ?>
		</ul>
	</nav>
</div>


<?php 
	foreach ( $tabs as $key => $tab ) : ?>
		<div class="course-tab-panel-<?php echo esc_attr( $key ); ?> course-tab-panel"
			id="<?php echo esc_attr( $tab['id'] ); ?>">
			<?php
			if ( isset( $tab['callback'] ) && is_callable( $tab['callback'] ) ) {
				call_user_func( $tab['callback'], $key, $tab );
			} else {
				do_action( 'learn-press/course-tab-content', $key, $tab );
			}
			?>
		</div>
<?php endforeach; ?>


