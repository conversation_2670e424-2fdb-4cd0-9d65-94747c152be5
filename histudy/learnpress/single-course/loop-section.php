<?php
/**
 * Template for displaying loop course of section.
 *
 * This template can be overridden by copying it to yourtheme/learnpress/single-course/loop-section.php.
 *
 * <AUTHOR>
 * @package  Learnpress/Templates
 * @version  4.0.2
 */

defined( 'ABSPATH' ) || exit();

/**
 * @var LP_Course_Section $section
 */
$course = learn_press_get_course($course_id);
$user = learn_press_get_current_user();
$user_course       = $user->get_course_attend( $course->get_id() );
if ( ! isset( $section ) || ! isset( $can_view_content_course )
	|| ! isset( $user_course ) || ! isset( $user ) ) {
	return;
}
if ( ! apply_filters( 'learn-press/section-visible', true, $section, $course ) ) {
	return;
}

/**
 * List items of section
 *
 * @var LP_Course_Item[]
 */
$items = $section->get_items();
?>

<li <?php $section->main_class(); ?>
	id="section-<?php echo esc_attr( $section->get_slug() ); ?>"
	data-id="<?php echo esc_attr( $section->get_slug() ); ?>"
	data-section-id="<?php echo esc_attr( $section->get_id() ); ?>">
	<?php do_action( 'learn-press/before-section-summary', $section, $course->get_id() ); ?>

	<div class="section-header">
		<div class="section-left">
			<h5 class="section-title">
				<?php
				$title = $section->get_title();
				echo wp_kses_post( ! $title ? _x( 'Untitled', 'template title empty', 'histudy' ) : $title );
				?>

				<?php $description = $section->get_description(); ?>

				<?php if ( $description ) : ?>
					<p class="section-desc"><?php echo wp_kses_post( $description ); ?></p>
				<?php endif; ?>
			</h5>

			<span class="section-toggle">
				<i class="lp-icon-caret-down"></i>
				<i class="lp-icon-caret-up"></i>
			</span>
		</div>
	</div>

	<?php do_action( 'learn-press/before-section-content', $section, $course->get_id() ); ?>

	<?php if ( ! $items ) : ?>
		<?php learn_press_display_message( __( 'No items in this section', 'histudy' ) ); ?>
	<?php else : ?>
		<ul class="section-content">

			<?php

			foreach ( $items as $item ) :

				
				$can_view_item = $user->can_view_item( $item->get_id(), $can_view_content_course );
				$class_item    = implode( ' ', $item->get_class_v2( $course->get_id(), $item->get_id(), $can_view_item ) );
				?>
				<li class="<?php echo esc_attr( $class_item ); ?>"
					data-id="<?php echo esc_attr( $item->get_id() ); ?>">

					<?php
					do_action( 'learn-press/before-section-loop-item', $item, $section, $course );

					$item_link = $can_view_item->flag ? $item->get_permalink() : false;
					$item_link = apply_filters( 'learn-press/section-item-permalink', $item_link, $item, $section, $course );

					$duration = histudy_lp_lesson_duration( $item->get_id() );
				
					
					if ( $item->is_visible() ):
					?>

					<a class="section-item-link" href="<?php echo esc_url_raw( $item_link ? $item_link : 'javascript:void(0);' ); ?>">
						<div class="course-item-info">
						<?php
						do_action( 'learn-press/before-section-loop-item-title', $item, $section, $course );

						learn_press_get_template(
							'single-course/section/' . $item->get_template(),
							array(
								'item'    => $item,
								'section' => $section,
							)
						);
						?>

						
							<?php do_action( 'learn-press/after-section-loop-item-title', $item, $section, $course ); ?>
						</div>

						<div class="course-item-meta">
							<div class="course-item-info-pre">
								<span class="item-meta duration"><?php echo wp_kses_post( $duration ); ?></span>
							</div>
							<?php 
								if ( $item->is_preview() ): 
									$course_id = $section->get_course_id(); 
									 ?>
                                       
										<span class="rbt-badge bg-success-opacity"><i class="feather-eye"></i> <bdi class="rtb-no-xs"><?php esc_html_e( 'Preview', 'histudy' ); ?></bdi></span>
									<?php 
								
								else: ?>
                                    <span disabled="" class="course-lock"><i class="feather-lock"></i></span>
								<?php endif; ?>
						</div>
					</a>

					<?php do_action( 'learn-press/after-section-loop-item', $item, $section, $course ); 

					endif;
					?>
				</li>
			<?php endforeach; ?>
		</ul>
	<?php endif; ?>

	<?php do_action( 'learn-press/after-section-summary', $section, $course->get_id() ); ?>
</li>
