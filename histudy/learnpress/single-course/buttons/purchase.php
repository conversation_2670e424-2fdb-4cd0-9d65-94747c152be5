<?php 
if ( ! isset( $course ) ) {
	$course = learn_press_get_course();
}

$classes_purchase  = 'purchase-course';
$classes_purchase .= ( LearnPress::instance()->checkout()->is_enable_guest_checkout() ) ? ' guest_checkout' : '';

$classes_purchase = apply_filters( 'lp/btn/purchase/classes', $classes_purchase );
?>

<?php do_action( 'learn-press/before-purchase-form' ); ?>

	<form  name="purchase-course" class="<?php echo esc_attr( $classes_purchase ); ?>" method="post" enctype="multipart/form-data">

		<?php do_action( 'learn-press/before-purchase-button' ); ?>

		<input type="hidden" name="purchase-course" value="<?php echo esc_attr( $course->get_id() ); ?>"/>

		<button class="lp-button button button-purchase-course rbt-btn btn-gradient icon-hover w-100 d-block text-center">
            <span class="btn-text"><?php echo esc_html( apply_filters( 'learn-press/purchase-course-button-text', esc_html__( 'Buy Now', 'histudy' ), $course->get_id() ) ); ?></span>
            <span class="btn-icon"><i class="feather-arrow-right"></i></span>
		</button>

		<?php do_action( 'learn-press/after-purchase-button' ); ?>

	</form>

<?php do_action( 'learn-press/after-purchase-form' ); ?>