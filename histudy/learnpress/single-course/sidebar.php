<?php

/**
 * Template for displaying course sidebar.
 *
 * <AUTHOR>
 * @package LearnPress/Templates
 * @version 4.0.0
 */

defined('ABSPATH') || exit;

$course_id = get_the_ID();

/**
 * Hide sidebar if there is no content
 */
if (! is_active_sidebar('course-sidebar') && ! LearnPress::instance()->template('course')->has_sidebar()) {
    return;
}

$course_single_video = function_exists('get_field') ? get_field('course_single_video', $course_id) : '#';
$money_back_text = function_exists('get_field') ? get_field('money_back_text', $course_id) : '';

$rainbow_options    = Rainbow_Helper::rainbow_get_options();

$rainbow_course_details_card_bottom_show                = $rainbow_options['lp_rainbow_course_details_card_bottom_show'];
$rainbow_course_details_card_social_show                = $rainbow_options['lp_rainbow_course_details_card_social_show'];
$rainbow_course_details_card_fb_switch                  = $rainbow_options['lp_rainbow_course_details_card_fb_switch'];
$rainbow_course_details_card_twitter_switch             = $rainbow_options['lp_rainbow_course_details_card_twitter_switch'];
$rainbow_course_details_card_linkedin_switch            = $rainbow_options['lp_rainbow_course_details_card_linkedin_switch'];
$rainbow_course_details_card_contact_label              = $rainbow_options['lp_rainbow_course_details_card_contact_label'];
$rainbow_course_details_card_contact_number_before_text = $rainbow_options['lp_rainbow_course_details_card_contact_number_before_text'];
$rainbow_course_details_card_contact_number_link        = $rainbow_options['lp_rainbow_course_details_card_contact_number_link'];

$course_single_url = get_the_permalink($course_id);
$page_title = get_the_title();
$facebookShareUrl = "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($course_single_url);
$twitterShareUrl = "https://twitter.com/intent/tweet?url=" . urlencode($course_single_url) . "&text=" . urlencode($page_title);
$linkedInShareUrl = "https://www.linkedin.com/sharing/share-offsite/?url=" . urlencode($course_single_url);

$rainbow_course_details_layout = isset( $rainbow_options['lp_rainbow_course_details_layout'] ) ? $rainbow_options['lp_rainbow_course_details_layout'] : '';

if( !empty(get_query_var( 'course_single_layout' )) ) {
    $rainbow_course_details_layout = get_query_var( 'course_single_layout', 'layout-1' );
}

$course_details_sidebar = '';
if( $rainbow_course_details_layout == 'layout-3' ) {
    $course_details_sidebar = 'course--single-layout-three';
}
else if( $rainbow_course_details_layout == 'layout-4' ) {
    $course_details_sidebar = 'course--single-layout-four';
}
else if( $rainbow_course_details_layout == 'layout-5' ) {
    $course_details_sidebar = 'course--single-layout-five-sidebar rbt-course-single-layout-five-sidebar';
}

if( $rainbow_course_details_layout == 'layout-5' ) {
?>
<aside class="course-sidebar sticky-top  course-sidebar-top  course--single-layout-five-sidebar rbt-course-single-layout-five-sidebar">
    <div class="rbt-course-sidebar-main-wrapper rbt-shadow-box rbt-gradient-border">
<?php } else { ?>
<aside class="course-sidebar sticky-top rbt-shadow-box course-sidebar-top rbt-gradient-border <?php echo esc_attr( $course_details_sidebar ); ?>">
<?php } ?>
    <div class="inner">
        <?php if( $rainbow_course_details_layout == 'layout-1') { ?>
            <!-- Start Viedo Wrapper  -->
            <?php if(!empty($course_single_video)) { ?>
            <a class="video-popup-with-text video-popup-wrapper text-center popup-video sidebar-video-hidden mb--15" href="<?php echo esc_url($course_single_video); ?>">
                <?php } ?>
                <div class="video-content">
                    <?php single_course_sidebar_thumbnail(); ?>
                    <?php if(!empty($course_single_video)) { ?>
                    <div class="position-to-top">
                        <span class="rbt-btn rounded-player-2 with-animation">
                            <span class="play-icon"></span>
                        </span>
                    </div>
                    
                    <span class="play-view-text d-block color-white"><i class="feather-eye"></i> <?php echo esc_html__("Preview
                        this course", "histudy"); ?></span>
                        <?php } ?>
                </div>
                <?php if(!empty($course_single_video)) { ?>
            </a>
        <?php } } ?>
        <!-- End Viedo Wrapper  -->

        <div class="content-item-content">
            <div class="rbt-price-wrapper d-flex flex-wrap align-items-center justify-content-between">

                <?php get_template_part('template-parts/components/price/layout', '1'); ?>
                <?php if (class_exists('Elementor_Helper') && (!empty(Elementor_Helper::rbt_get_tutor_course_offer_left($course_id)))) : ?>
                    <div class="discount-time">
                        <span class="rbt-badge color-danger bg-color-danger-opacity"><i class="feather-clock"></i>
                            <?php echo class_exists('Elementor_Helper') ? Elementor_Helper::rbt_get_tutor_course_offer_left($course_id) : ''; ?></span>
                    </div>
                <?php endif; ?>
            </div>
           
            <div class="widget course-meta-wid course_progress_wid single-sidebar">
                <?php 
                LearnPress::instance()->template( 'course' )->user_progress();
                ?>
            </div>
            <?php  
             LP()->template( 'course' )->course_buttons(); ?>

            <?php histudy_user_time(); ?>
           
            <?php if (!empty($money_back_text)) { ?>
                <span class="subtitle"><i class="feather-rotate-ccw"></i> <?php echo esc_html($money_back_text); ?></span>
            <?php } ?>

            <div class="rbt-widget-details has-show-more">
                <?php histudy_lp_course_sidebar_features(); ?>
            </div>

            <?php if (1 == $rainbow_course_details_card_bottom_show) : ?>
                <div class="social-share-wrapper text-center mt-4">
                    <?php if (1 == $rainbow_course_details_card_social_show) : ?>
                        <div class="rbt-post-share d-flex align-items-center justify-content-center">
                            <?php if ((1 == $rainbow_course_details_card_fb_switch && !empty($facebookShareUrl)) || (1 == $rainbow_course_details_card_twitter_switch && !empty($twitterShareUrl)) || (1 == $rainbow_course_details_card_linkedin_switch && !empty($linkedInShareUrl))) : ?>
                                <ul class="social-icon social-default transparent-with-border justify-content-center mb--20">
                                    <?php if (1 == $rainbow_course_details_card_fb_switch) : ?>
                                        <?php if (!empty($facebookShareUrl)) : ?>
                                            <li><a target="_blank" href="<?php echo esc_url($facebookShareUrl); ?>">
                                                    <i class="feather-facebook"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if (1 == $rainbow_course_details_card_twitter_switch) : ?>
                                        <?php if (!empty($twitterShareUrl)) : ?>
                                            <li><a target="_blank" href="<?php echo esc_url($twitterShareUrl); ?>">
                                                    <i class="feather-twitter"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if (1 == $rainbow_course_details_card_linkedin_switch) : ?>
                                        <?php if (!empty($linkedInShareUrl)) : ?>
                                            <li><a target="_blank" href="<?php echo esc_url($linkedInShareUrl); ?>">
                                                    <i class="feather-linkedin"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <hr class="">
                    <div class="contact-with-us text-center">
                        <?php if (!empty($rainbow_course_details_card_contact_label)) : ?>
                            <p><?php echo esc_html($rainbow_course_details_card_contact_label); ?></p>
                        <?php endif; ?>
                        <p class="rbt-badge-2 mt--10 justify-content-center w-100"><i class="feather-phone mr--5"></i>
                            <?php if (!empty($rainbow_course_details_card_contact_number_before_text)) : ?>
                                <?php echo esc_html($rainbow_course_details_card_contact_number_before_text); ?>
                            <?php endif; ?>
                            <a
                                href="tel: <?php echo esc_attr($rainbow_course_details_card_contact_number_link) ? esc_attr($rainbow_course_details_card_contact_number_link) : '#0'; ?>"><strong><?php echo esc_html($rainbow_course_details_card_contact_number_link); ?></strong></a>
                        </p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php if( $rainbow_course_details_layout == 'layout-5' ) { ?>
    </div>
    <?php if (is_active_sidebar('sidebar-tutor-course')) { 
        dynamic_sidebar('sidebar-tutor-course');
    } ?>
    <?php } ?>

</aside>