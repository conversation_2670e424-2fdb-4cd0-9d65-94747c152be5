<?php
/**
 * Template for displaying the list of course is in wishlist
 *
 * <AUTHOR>
 */

defined( 'ABSPATH' ) || exit();

global $post;

if ( !$wishlist ) {
	learn_press_display_message( esc_html__( 'There are no courses in the wishlist', 'histudy' ) );
	return;
}

?>
<div class="row g-5">
<?php foreach ( $wishlist as $post ): ?>
	<?php setup_postdata( $post ); ?>
	<div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
		<?php learn_press_get_template( 'custom/content-box.php' ); ?>	
	</div>
	<?php wp_reset_postdata(); ?>
<?php endforeach; ?>
</div>
