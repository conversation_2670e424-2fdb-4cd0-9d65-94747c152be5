<?php
/**
 * Template Name: Archive style 4
 * 
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
 
$args = array(
	'post_type' => "post",
);

if ( get_query_var('paged') ) {
	$args['paged'] = get_query_var('paged');
}
elseif ( get_query_var('page') ) {
	$args['paged'] = get_query_var('page');
}
else {
	$args['paged'] = 1;
}
$query = new WP_Query( $args );
global $wp_query;
$wp_query = NULL;
$wp_query = $query;
$rb_is_post_archive = is_home() || ( is_archive() && get_post_type() == 'post' ) ? true : false;
$rainbow_options = Rainbow_Helper::rainbow_get_options();
get_header();
?>
<div class="rbt-blog-area rbt-section-overlayping-top rbt-section-gapBottom">
    <div class="container">   
            <?php 
			ob_start();
				get_template_part( 'template-parts/post/content-4', get_post_format() );
			echo ob_get_clean();
			rainbow_blog_pagination2(); ?> 
	</div>
</div> 
<?php
get_footer();