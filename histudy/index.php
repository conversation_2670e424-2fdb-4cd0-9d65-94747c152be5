<?php
    /**
     * The main template file
     *
     * This is the most generic template file in a WordPress theme
     * and one of the two required files for a theme (the other being style.css).
     * It is used to display a page when nothing more specific matches a query.
     * E.g., it puts together the home page when no home.php file exists.
     *
     * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
     *
     * @package histudy
     */

    if (!defined('ABSPATH')) {
        exit; // Exit if accessed directly.
    }

    get_header();
    /**
     * Required Attributes
     * 
     * @since 1.0.0
     */
    // common attributes

    $rb_is_post_archive = is_home() || ( is_archive() && get_post_type() == 'post' ) ? true : false;
    $rainbow_options = Rainbow_Helper::rainbow_get_options();
    $rainbow_blog_layout = isset($rainbow_options['rainbow_blog_layout']) ? sanitize_text_field( $rainbow_options['rainbow_blog_layout'] ): 'default';

    if( get_query_var('blog_layout') ) {
        $rainbow_blog_layout = sanitize_text_field( get_query_var('blog_layout') );
    }
    
    if( 'blog-grid' == $rainbow_blog_layout ) {
        $rainbow_blog_sidebar_class = __( 'col-12 histudy-post-wrapper', 'histudy' );
    }
    $rainbow_enable_sidebar = isset( $rainbow_options['rainbow_blog_sidebar'] ) ? $rainbow_options['rainbow_blog_sidebar']: '';
    if( get_query_var('blog_sidebar') ) {
        $rainbow_enable_sidebar = sanitize_text_field( get_query_var('blog_sidebar') );
    }
    $rainbow_blog_sidebar_class = ($rainbow_enable_sidebar == 'no') || !is_active_sidebar('sidebar-1') ? __( 'col-lg-10 histudy-post-wrapper', 'histudy' ) : __( 'col-lg-8 histudy-post-wrapper', 'histudy' );
    if( 'blog-grid' ==  $rainbow_blog_layout ) {
        $rainbow_blog_sidebar_class = __( 'col-12', 'histudy' );
    }
    // blog attributes
    $thumb_size = ($rainbow_enable_sidebar === 'no') ? __('rainbow-thumbnail-single', 'histudy') : __( 'rainbow-thumbnail-archive', 'histudy' );
    $allowed_tags = wp_kses_allowed_html( 'post' );
    /**
     * Attributes for blog grid
     */
    $blog_grid_card_layout = isset($rainbow_options['blog_grid_card_layout']) ? sanitize_text_field( $rainbow_options['blog_grid_card_layout'] ): __( 'card-boxed', 'histudy' );
    /**
     * Attribute for blog grid minimal
     */
    $blog_overlape = 'rbt-blog-area rbt-section-gap';
    $row_class = 'row justify-content-center row--30 gy-5';
    $blog_grid_minimal_card_layout = isset($rainbow_options['blog_grid_minimal_card_layout']) ? sanitize_text_field( $rainbow_options['blog_grid_minimal_card_layout'] ): __( 'card-minimal', 'histudy' );
    if( ('blog-grid' == get_query_var( 'blog_layout' )) ||('blog-grid-minimal' == get_query_var( 'blog_layout' )) && ( 'no' == get_query_var( 'blog_sidebar' ) ) ) {
        $rainbow_blog_sidebar_class = 'col-12 histudy-post-wrapper';
        $row_class = 'row g-5';
    }
    if( 'blog-list' == get_query_var( 'blog_layout' ) ) {
        $rainbow_blog_sidebar_class = 'col-lg-10 offset-lg-1 histudy-post-wrapper';
        
    }
    if(('blog-grid-minimal' == get_query_var( 'blog_layout' ) || 'blog-grid' == get_query_var( 'blog_layout' )|| 'blog-list' == get_query_var( 'blog_layout' ))) {
        $blog_overlape = 'rbt-blog-area rbt-section-overlayping-top rbt-section-gapBottom style-2';
    }
    $rainbow_enable_blog_breadcrumb_overlap = $rainbow_options['rainbow_enable_blog_breadcrumb_overlap'] ? $rainbow_options['rainbow_enable_blog_breadcrumb_overlap']: '';
    if( ( 'yes' == $rainbow_enable_blog_breadcrumb_overlap ) || !empty( $rainbow_enable_blog_breadcrumb_overlap ) ) {
        $blog_overlape = 'rbt-blog-area rbt-section-overlayping-top rbt-section-gapBottom style-2';
    }
    $banner_layout    =  isset($rainbow_options['rainbow_blog_title_layout']) ? $rainbow_options['rainbow_blog_title_layout']: '';
    if( 1 == $banner_layout ) {
        $blog_overlape = 'rbt-blog-area rbt-section-gap';
    }
?>
<!-- blog area start -->
<div class="<?php echo esc_attr( $blog_overlape ); ?>">
    <div class="container">
        <div class="<?php echo esc_attr( $row_class ); ?>">
            <?php if( 'blog-grid' !== $rainbow_blog_layout ) : ?>
            <!-- left sidebar control -->
            <?php if (is_active_sidebar('sidebar-1') && $rainbow_enable_sidebar == 'left') { ?>
                <div class="col-lg-4 col-xl-4">
                    <aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">
                        <?php dynamic_sidebar(); ?>
                    </aside>
                </div>
            <?php } ?>
            <?php endif; ?>
            <!-- /. left sidebar control -->
            <!-- main blog content -->
            <div class="<?php echo esc_attr( $rainbow_blog_sidebar_class ); ?>">
                <?php
                    /**
                     * Show blog card based on card layout
                     * 
                     * @since 1.0.0
                     * 
                     * @see https://developer.wordpress.org/reference/functions/get_template_part/
                     */
                    switch( $rainbow_blog_layout ) {
                        case 'blog-list':
                            get_template_part('template-parts/blog-layout/blog', 'list');
                            break;
                        case 'blog-grid':
                            get_template_part('template-parts/blog-layout/blog', 'grid');
                            break;
                        case 'blog-grid-minimal':
                            get_template_part('template-parts/blog-layout/blog', 'grid-minimal');
                            break;
                        default:
                            get_template_part('template-parts/blog-layout/blog', 'default');
                            break;
                    }
                    if( !empty(rainbow_blog_pagination_simple()) ) {
                        echo '<div class="mt--60">';
                            rainbow_blog_pagination_simple();
                        echo '</div>';
                    }
                ?>
            </div>
            <!-- /.main blog content -->
            <?php if( 'blog-grid' !== $rainbow_blog_layout ) :  ?>
            <!-- right sidebar control -->
            <?php if (is_active_sidebar('sidebar-1') && $rainbow_enable_sidebar == 'right') { ?>
                <div class="col-lg-4 col-xl-4">
                    <aside class="rbt-sidebar-widget-wrapper rbt-gradient-border">
                        <?php dynamic_sidebar(); ?>
                    </aside>
                </div>
            <?php } ?>
            <!-- /. right sidebar control -->
            <?php endif; ?>
        </div>
    </div>
</div>
<!-- blog area end -->
<?php
get_footer();