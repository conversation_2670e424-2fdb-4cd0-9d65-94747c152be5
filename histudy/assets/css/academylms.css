
.academy-icon {
    font-family: 'academy-icon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.rbt-course-card-academy-price {
    gap: 8px;
}

.rbt-course-card-academy-price ins {
    font-size: 24px;
    font-weight: 700;
    color: var(--color-body);
}

.rbt-course-card-academy-price del {
    font-size: 20px;
    font-weight: 500;
    opacity: 0.4;
}

.rbt-bookmark-btn.academy-course-header-meta .academy-course__wishlist {
    cursor: pointer;
}

.tutor-course-archive-page.academy-courses .academy-course {
    margin-bottom: 0;
}  

.tutor-course-archive-page.academy-courses .academy-course {
    border: 0;
    border-radius: 6px;
}

.rbt-course-card-academy-price del {
    font-size: 18px;
}

.rbt-course-card-academy-price ins {
    font-size: 19px;
}

.rbt-author-info {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 200px;
}

.academy-archive-course-widget--search input.academy-archive-course-search {
    color: var(--color-body);
    box-shadow: var(--shadow-10);
    border: var(--border-width) solid var(--color-border);
    border-radius: var(--radius);
}

.academy-archive-course-widget__title {
    font-size: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-border);
    margin-bottom: 15px;
}

.academy-archive-course-widget__body label {
    position: relative;
    font-size: 15px;
    line-height: 25px;
    color: var(--color-body);
    font-weight: 400;
    cursor: pointer;
    margin-bottom: 10px;
}

.academy-courses__header-filter {
    display: inline-block;
}

.dropdown.bootstrap-select.academy-courses__header-orderby {
    border: none!important;
}

.academy-courses__header-ordering .dropdown-item.active {
    color: var(--color-primary)!important;
    background-color: var(--black-opacity)!important;
}

.academy-courses--archive .rbt-card.card-list-2,
.academy-courses--archive .academy-courses__body.active-list-view .academy-course.rbt-card {
    display: flex;
    border-radius: var(--radius);
    align-items: center;
    height: 100%;
}

.academy-courses--archive .rbt-card.card-list-2 .rbt-card-img,
.academy-courses--archive .academy-courses__body.active-list-view .academy-course.rbt-card .rbt-card-img {
    flex-basis: 40%;
    height: 100%;
}

.academy-courses--archive .rbt-card.card-list-2 .rbt-card-body,
.academy-courses--archive .academy-courses__body.active-list-view .academy-course.rbt-card .rbt-card-body {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-basis: 60%;
    padding-left: 30px;
    margin-top: 0;
}

.academy-courses--archive .rbt-course-grid-column.active-list-view .course-grid-3, 
.academy-courses--archive .rbt-course-grid-column.active-list-view .course-grid-2 {
    max-width: 100%;
}

.academy-course-large-col {
    margin-top: 3rem;
}

.academy-courses__body.rbt-course-grid-column .academy-row {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
}

.rbt-section-overlayping-top .rainbow-course-not-found-error {
    margin-top: 0!important;
}

.academy-courses.academy-courses--archive {
    padding-bottom: 70px!important;
}

.academy-courses.academy-courses--archive .academy-courses__pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
}

.academy-courses__pagination .page-numbers.prev, 
.academy-courses__pagination .page-numbers.next,
.academy-courses__pagination .page-numbers {
    background: var(--color-white);
    color: var(--color-body);
    box-shadow: var(--shadow-1);
    transition: all 0.3s ease-in-out;
}

.academy-courses__pagination .page-numbers.current, 
.academy-courses__pagination .page-numbers:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.academy-courses__pagination .page-numbers.next:hover, 
.academy-courses__pagination .page-numbers.prev:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.academy-courses__pagination .page-numbers.next i, 
.academy-courses__pagination .page-numbers.prev i {
    color: var(--color-body);
}

.academy-courses__pagination .page-numbers.next:hover i, 
.academy-courses__pagination .page-numbers.prev:hover i {
    color: var(--color-white);
}