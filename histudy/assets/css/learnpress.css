:root {
    --lp-primary-color: #2f57ef
}
@media (min-width: 1400px) {
    .lp-content-area {
        max-width: 1320px !important;
    }
}
.lp-archive-courses .course-summary-sidebar {
    margin-top: 0;
    position: sticky;
    top: 0;
}
.lp-archive-courses .course-sidebar-top {
    margin-top: 0;
}
.single-lp_course:not(.active-dark-mode) .lp-archive-courses .course-summary-content .lp-content-area * {color: var(--color-heading) !important;}

.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-level::before {
    color: var(--color-heading);
}

.main-page-wrapper .lp-archive-courses .course-meta__pull-left .meta-item::before {
    color: var(--color-heading);
}
.main-page-wrapper .lp-archive-courses .course-detail-info {
    background: linear-gradient(#b966e70a 0%, #2f57ef8c 100%);
    padding: 90px 0;
}
.main-page-wrapper ul.learn-press-nav-tabs .course-nav.active::before {
    background-color: var(--color-primary);
}

.main-page-wrapper .course-tabs .course-nav label {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-heading);
}
.single-lp_course:not(.active-dark-mode) .lp-archive-courses .course-summary-content .lp-content-area *:not(h1, h2, h3, h4, h5, h6, label,.lp-button,.subtitle) {
    color: var(--color-body) !important;
}
.main-page-wrapper .lp-entry-content.lp-content-area h1 {
    font-size: var(--h1);
    font-weight: var(--f-bold);
}
.main-page-wrapper .lp-entry-content.lp-content-area h2 {
    font-size: var(--h2);
    font-weight: var(--f-bold);
}
.main-page-wrapper .lp-entry-content.lp-content-area h3 {
    font-size: var(--h3);
    font-weight: var(--f-bold);
}
.main-page-wrapper .lp-entry-content.lp-content-area h4 {
    font-size: var(--h4);
    font-weight: var(--f-bold);
}
.main-page-wrapper .lp-entry-content.lp-content-area h5 {
    font-size: var(--h5);
    font-weight: var(--f-bold);
}
.main-page-wrapper .lp-entry-content.lp-content-area h6 {
    font-size: var(--h6);
    font-weight: 500;
}
.single-lp_course:not(.active-dark-mode) .main-page-wrapper .lp-entry-content.lp-content-area p {
    color: var(--color-body);
}

.main-page-wrapper .course-summary-sidebar .lp-course-buttons button {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white) !important;
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}
.lp-entry-content.lp-content-area a.rbt-btn.btn-gradient.icon-hover.radius-round span, .lp-entry-content.lp-content-area a.rbt-btn.btn-gradient.icon-hover.radius-round span i {
    color: #fff !important;
}
.lp-entry-content.lp-content-area div#comments.comment-respond, .learn-press-comments div#comments.comment-respond {
    padding: 0;
    box-shadow: none;
}
.learnpress-page input[type=text], .learnpress-page input[type=email], .learnpress-page input[type=number], .learnpress-page input[type=password], .learnpress-page textarea {
    border: 0;
    border-bottom: 2px solid var(--color-border);
    background-color: transparent;
    padding-bottom: 8px;
    padding-top: 20px;
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
    box-shadow: none;
}
.lp-archive-courses .course-summary-content .lp-content-area label[for="comment"] {color: var(--color-body) !important;font-size: 14px;}
.lp-archive-courses .course-summary-content .lp-content-area .comment-respond input[type=submit], div#respond.comment-respond .comment-form input[type=submit] {
    color: #fff !important;
    padding: 0 30px;
}

.single-lp_course #learn-press-course .rbt-course-details-area .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li.current a,
.single-lp_course #learn-press-course .rbt-course-details-area .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li:hover a {
    color: var(--color-white)!important;
}

.single-lp_course:not(.active-dark-mode) #learn-press-course .rbt-course-details-area .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li a {
    color: var(--color-heading)!important;
}


.lp-entry-content.lp-content-area .course-curriculum .section-left .section-title {
    font-size: 18px;
    line-height: 27px;
    outline: none;
    text-decoration: none;
    box-shadow: none;
    width: 100%;
    text-align: left;
    padding: 0;
    background-color: transparent;
    position: relative;
    font-weight: 600;
    color: var(--color-primary)!important;
}



.main-page-wrapper .lp-entry-content.lp-content-area p.section-desc {
    font-size: 18px;
}
.lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link .course-item-info span, .lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link::before {color: var(--color-heading) !important;font-weight: 500;}
.course-curriculum .course-item .section-item-link::before {
    color: var(--color-heading);
}

.lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link:hover .course-item-info span, .lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link:hover::before {
    color: var(--color-primary) !important;
}
.lp-entry-content.lp-content-area .course-curriculum .section-content .course-item-preview::before {
    background: var(--color-primary);
}
.single-lp_course .rbt-breadcrumb-default {
    display: none;
}
.single-lp_course .course-extra-box__content li::before {
    color: var(--body-color);
}
.course-tab-panel .lp-course-author .author-title a span {
    color: var(--color-heading) !important;
    transition: all .3s;
}
.course-tab-panel .lp-course-author .author-title a:hover span {
    color: var(--color-primary) !important;
}
div#learn-press-course-tabs .course-tab-panel {
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
}
div#learn-press-course-tabs .course-tab-panel-faqs .course-faqs-box__title {
    font-weight: 500;
}
.course-featured-review.margin-bottom {
    margin-bottom: 0;
}

.lp-archive-courses .course-summary-content .lp-content-area .course-featured-review .featured-review__title {
    font-size: 20px;
    color: var(--color-heading) !important;
}

.lp-archive-courses .course-summary-content .lp-content-area .course-featured-review .featured-review__content p {
    font-style: normal;
}
.lp-entry-content.lp-content-area .course-summary-sidebar__inner {
    top: 140px !important;
    z-index: 99;
}
div#tab-curriculum ul.curriculum-sections li:last-child .section-header {
    border: 0;
}
.lp-main-content ul.learn-press-courses[data-layout="grid"] li.course .course-item {
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    position: relative;
    height: 100%;
    border: 0;
}
.lp-main-content ul.learn-press-courses[data-layout="grid"] li {
    padding: 0;
}
.lp-main-content ul.learn-press-courses[data-layout=grid] {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}
.lp-main-content .learn-press-courses[data-layout=grid] .course {
    padding: 0;
    flex: 0 0 auto;
    width: 33.33%;
    padding-right: 15px;
    padding-left: 15px;
}
.lp-main-content .learn-press-courses .course {
    width: auto;
    margin: 0;
}




/**
 *Course Card
*/
.lp-archive-courses .course-item .course-instructor a {
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-body);
}

.learn-press-courses[data-layout=grid] .course-content .course-permalink span {
    font-size: 26px;
    font-weight: 700;
    color: var(--color-heading);
    transition: all .3s;
}

.learn-press-courses[data-layout=grid] .course-content .course-permalink:hover span {
    color: var(--color-primary);
}

ul.learn-press-courses.lp-list-courses-no-css .lp-archive-courses .course-item .course-instructor a:hover {
    color: var(--color-primary);
}

.learn-press-courses[data-layout=grid] .course-content .course-readmore {
    display: block;
}

.learn-press-courses[data-layout=grid] .course-content {
    padding: 30px 0;
}

.learn-press-courses[data-layout=grid] .course-content .separator {
    margin: 0;
}

.learn-press-courses[data-layout=grid] .course-content {
    padding-bottom: 0;
}

.lp-archive-courses .course-content .course-categories a:first-child {
    background: var(--color-primary);
}

.lp-archive-courses .course-content .course-categories a:first-child:hover {
    color: var(--color-white);
}

.learn-press-courses[data-layout=grid] .course-content .course-wrap-meta {
    display: block;
}
.learn-press-courses[data-layout=grid] .course-content .course-wrap-meta > * {display: inline-block;margin-right: 13px;margin-bottom: 5px;}

.learn-press-courses[data-layout=grid] .course-content .course-wrap-meta {
    padding-bottom: 25px;
}
.lp-archive-courses ul.learn-press-breadcrumb {
    padding-left: 0;
}
.lp-archive-courses ul.learn-press-breadcrumb {
    padding-left: 0;
}

.learn-press-courses[data-layout=list] .course-item {
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    position: relative;
}

.lp-archive-courses .course-content .course-permalink span {
    font-size: 26px;
    font-weight: 700;
    color: var(--color-heading);
    transition: all .3s;
    margin-bottom: 18px;
    display: block;
    margin-top: 10px;
}

.lp-archive-courses .course-content .course-permalink:hover span {
    color: var(--color-primary);
}
.learn-press-courses[data-layout=list] .course-wrap-meta .meta-item::before {
    color: var(--color-body);
}
.learn-press-courses[data-layout=list] .course-content .course-readmore a {
    padding: 0 26px;
    background: var(--color-primary);
    height: 45px;
    line-height: 43px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.learn-press-courses[data-layout=list] .course-content .course-readmore a:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}
.learn-press-courses[data-layout=list] .course {
    width: 100%;
    border: 0;
}
.learn-press-courses[data-layout=list] .course-content .course-footer {
    align-items: center;
}

.lp-archive-courses .course-content .course-info .course-price span {
    font-size: 24px;
    font-weight: 700;
    color: var(--color-heading);
}

.learn-press-courses[data-layout=list] .course-item {
    align-items: center;
}
.lp-archive-courses .course-item .course-instructor a:hover {
    color: var(--color-primary);
}
.lp-courses-bar input[type=radio]:nth-child(3):checked ~ .switch-btn:nth-child(4)::before {
    color: var(--color-primary);
    margin: 0;
}
.lp-courses-bar .switch-layout .switch-btn.list::before {
    margin: 0;
}
.lp-courses-bar input[type=radio]:nth-child(1):checked ~ .switch-btn:nth-child(2)::before {
    color: var(--color-primary);
    margin: 0;
}
.lp-courses-bar input[type=radio] ~ label::after {
    display: none;
}
.lp-courses-bar .switch-layout .switch-btn.grid::before {
    margin: 0;
}

.lp-courses-bar  input[type=radio]:checked ~ label::before {
    background: transparent;
    border: 0;
    border-radius: 0;
}
.lp-courses-bar .switch-layout .switch-btn {
    height: auto;
}
.lp-courses-bar input[type=radio] ~ label::before {
    position: relative;
}
.lp-courses-bar .switch-layout .switch-btn.grid::before {
    margin: 0;
    border: 0;
    height: 0;
}
.lp-courses-bar .switch-layout .switch-btn.list {
    width: auto;
}

.lp-courses-bar .search-courses label {
    width: 100%;
}

.lp-courses-bar .search-courses input[type=text]:focus {
    border-color: var(--color-primary);
}

.lp-courses-bar .search-courses label {
    margin-bottom: 0;
}
.lp-archive-courses {
    padding-top: 100px;
    padding-bottom: 120px;
}
.lp-main-content ul.learn-press-courses[data-layout=grid] {
    padding: 0 15px !important;
}

.lp-main-content ul.learn-press-courses[data-layout=grid] {
    padding: 0 !important;
}
.lp-archive-courses ul.learn-press-breadcrumb {
    padding: 0 15px;
}
.single.single-lp_course .lp-archive-courses {
    padding-top: 0;
}
.single.single-lp_course .lp-archive-courses ul.learn-press-breadcrumb {
    padding-top: 10px;
    padding-bottom: 10px;
}

.post-type-archive-lp_course .learn-press-courses .rainbow-course-not-found-error {
    margin-top: 0;
    margin-left: 10px;
}

@media (min-width: 400px) {
    .lp-archive-courses ul.learn-press-breadcrumb {
        max-width: 1320px !important
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .lp-main-content ul.learn-press-courses[data-layout=grid] {
        grid-template-columns: repeat(2, 1fr);
    }
}
div#learn-press-course {
    position: relative;
    z-index: 9;
}
@media (max-width: 767px) {
    .lp-main-content ul.learn-press-courses[data-layout=grid] {
        grid-template-columns: repeat(1, 1fr);
    }
}
.lp-archive-courses .course-sidebar-top {
    margin-top: -140px;
}
@media (max-width: 767px) {
    .lp-archive-courses .course-sidebar-top {
        margin-top: 0;
    }
}
aside.course-summary-sidebar.slide-down .course-sidebar-top {
    margin-top: 0;
}
.learn-press-comments textarea#comment {
    box-shadow: none;
}
.learn-press-comments textarea#comment {
    box-shadow: none;
    color: #222;
}
#popup-header {
    background: var(--tutor-color-primary);
}
div#learn-press-quiz-app ul.quiz-intro {
    padding: 0;
}

.quiz-intro-item::before {
    color: var(--color-primary);
}

#popup-content .lp-button {color: var(--color-white);background-size: 300% 100%;background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));border: 0;}

#popup-content .lp-button:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}
.lp-modal-dialog .lp-modal-header {
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.lp-modal-dialog .lp-modal-footer button:not(:last-child) {
    margin-bottom: 20px;
}

.lp-modal-dialog .lp-modal-footer button {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
}

.lp-modal-dialog .lp-modal-footer button:hover {
    background: var(--color-secondary);
}
body.lp_quiz-template-default.single.single-lp_quiz .rbt-breadcrumb-default {
    display: none;
}
/**
* Quiz
**/
.quiz-status>div {
    background: var(--color-primary);
}

.lp-sidebar-toggle__open #content-item-quiz .quiz-status>div .questions-index {
    color: #fff;
}

.quiz-status .questions-index span {
    color: #fff;
}

.quiz-status>div>div .countdown {
    color: #fff;
}

.question .btn-show-hint {
    width: auto;
}

.question .answer-option .option-title {
    padding: 14px 20px;
}

.content-item-wrap .questions-pagination .nav-links .page-numbers {
    width: 55px;
    height: 55px;
    background: var(--color-white);
    border-radius: 6px;
    text-align: center;
    color: var(--color-body);
    transition: 0.4s;
    font-weight: 500;
    box-shadow: var(--shadow-1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0;
    border: 0;
}
#popup-content .lp-button.back-quiz {
    margin-top: 40px;
}
.content-item-wrap .questions-pagination .nav-links .page-numbers.current, .content-item-wrap .questions-pagination .nav-links .page-numbers:hover {background: var(--color-primary);color: var(--color-white);}

.content-item-wrap .questions-pagination .nav-links .page-numbers.next, .content-item-wrap .questions-pagination .nav-links .page-numbers.prev {
    border: 0;
}
#popup-footer .course-item-nav .prev:hover a, #popup-footer .course-item-nav .prev:hover::before, #popup-footer .course-item-nav .next:hover a, #popup-footer .course-item-nav .next:hover::before {
    color: var(--color-primary);
}
.content-item-wrap .quiz-buttons.align-center .button-left.fixed {
    position: relative;
    margin: 0 !important;
}
.course-curriculum .course-item .section-item-link:hover .item-name {
    color: var(--color-primary);
}
#checkout-payment #checkout-order-action button {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white) !important;
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}
#checkout-payment #checkout-order-action button:hover {
    background-color: var(--color-secondary);
    color: var(--color-white);
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-7)
}
.lp-terms-and-conditions a:hover, .lp-checkout-form .lp-checkout-remember a:hover, #checkout-account-register .lp-checkout-sign-in-link a:hover, #checkout-account-register .lp-checkout-sign-up-link a:hover, #checkout-account-login .lp-checkout-sign-in-link a:hover, #checkout-account-login .lp-checkout-sign-up-link a:hover {
    color: var(--color-primary);
    text-decoration-color: var(--color-primary);
}
.lp-entry-content.lp-content-area .form-group textarea {
    box-shadow: none;
}
#popup-sidebar .search-course button {
    width: auto;
}

/**
 * Instructor Profile
*/
.lp-user-profile .lp-profile-content-area {background: #fff;border-radius: var(--radius);background: var(--color-white);overflow: hidden;box-shadow: var(--shadow-1);padding: 30px;}

.lp-user-profile .wrapper-profile-header {
    background: transparent;
    color: var(--color-heading);
}

.lp-user-profile .lp-user-profile-avatar img {
    border-radius: 50%;
}
.lp-user-profile .lp-profile-content-area {background: #fff;border-radius: var(--radius);background: var(--color-white);overflow: hidden;box-shadow: var(--shadow-1);padding: 30px;}

.lp-user-profile .wrapper-profile-header {
    background: transparent;
    color: var(--color-heading);
}

.lp-user-profile .lp-user-profile-avatar img {
    border-radius: 50%;
}
.lp-user-profile #profile-sidebar {
    border-radius: var(--radius);
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    padding: 30px;
    border: 0;
    background: linear-gradient(-145deg, #CFA2E8, #637FEA) !important;
    z-index: 10;
    position: relative;
}
.lp-user-profile #profile-sidebar::before {
    content: "";
    z-index: -1;
    top: 3px;
    left: 3px;
    position: absolute;
    background: #fff;
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    border-radius: var(--radius);
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs > li.has-child > a:after {
    margin-top: 0!important;
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs>li>a {
    padding: 12px 0;
    font-weight: 400;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs>li>a>i {
    position: static;
    transform: none;
    vertical-align: middle;
    margin-right: 5px;
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs>li.active, .lp-user-profile #profile-nav .lp-profile-nav-tabs>li:hover {
    background: transparent;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs li.active>a, .lp-user-profile #profile-nav .lp-profile-nav-tabs li:hover>a {
    color: var(--color-primary);
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs>li ul li a:hover, .lp-user-profile #profile-nav .lp-profile-nav-tabs>li ul li:hover>a i, .lp-user-profile #profile-nav .lp-profile-nav-tabs li.active>a i, .lp-user-profile #profile-nav .lp-profile-nav-tabs li:hover>a i {
    color: var(--color-primary);
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs>li.active a {
    padding: 12px 0;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs>li>a>i {
    color: var(--color-primary);
}
ul.profile-tab-sections .avatar::before {
    display: none;
}

ul.profile-tab-sections .avatar::after {
    display: none;
}
.dashboard-statistic__row .statistic-box {
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    border-radius: 5px;
    text-align: center;
    padding: 30px 30px 50px;
    z-index: 1;
    overflow: hidden;
    position: relative;
    border: 0;
    border: 2px dashed var(--color-border);
    background: var(--primary-opacity);
    color: var(--color-primary);
}

.lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box {
    width: calc((100% - 60px) / 3);
}

.dashboard-statistic__row .statistic-box:hover {
    background: none;
}

.statistic-box .statistic-box__number {
    font-weight: 700;
    font-size: 50px;
    line-height: 50px;
    margin-bottom: 9px;
    margin-top: 30px;
    color: var(--color-primary);
}

.learnpress-profile .statistic-box .statistic-box__text,
.learnpress-profile .statistic-box .statistic-box__text label,
.learnpress-profile .statistic-box .statistic-box__text span {
    color: var(--color-heading);
    font-size: 18px !important;
    font-weight: 500;
}

.dashboard-statistic__row {
    grid-gap: 30px;
}
.learn-press-course-tab-filters .learn-press-filters a.active {
    color: var(--color-primary);
}
div#profile-content-courses .learn-press-filters>li a {
    text-decoration: none;
}
.learn-press-profile-course__progress .lp-archive-courses {
    padding-top: 0;
}
div#profile-content-courses .learn-press-courses[data-layout=grid] .course-item {
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    position: relative;
    height: 100%;
    border: 0;
}

div#profile-content-courses .learn-press-courses[data-layout=grid] .course-content .course-readmore {
    text-decoration: none;
}

div#profile-content-courses .course-readmore a {
    text-decoration: none;
}

div#profile-content-courses .learn-press-courses[data-layout=grid] .course-content .course-footer {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 0 20px;
    justify-content: space-between;
}
div#profile-content-courses .lp-user-profile ul.learn-press-courses .course {
    margin-bottom: 20px;
}
div#profile-content-courses .statistic-box .statistic-box__number {
    margin-bottom: 20px;
    position: relative;
    background: var(--color-white);
    width: 100px;
    margin-right: auto;
    margin-left: auto;
    height: 100px;
    line-height: 100px;
    padding: 25px;
    border-radius: 100%;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-10);
    justify-content: center;
    background: var(--primary-opacity);
}
.lp-user-profile ul.learn-press-courses .course {
    margin: 0 0 20px 0;
}
div#profile-content-courses .dashboard-statistic__row .statistic-box {
    transition: all .3s;
}
div#profile-content-courses .course .course-item  .span.course-item-price {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

div#profile-content-courses ul.profile-courses-list.learn-press-courses .course-item .course-item-price {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.learn-press-profile-course__progress .lp-archive-courses {
    padding-bottom: 40px;
}
@media (min-width: 992px) and (max-width: 1199px) {
    .learn-press-courses[data-size="3"] .course {
        width: 50%;
    }
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box {
        width: calc((100% - 60px) / 2);
    }
    .learn-press-courses[data-size="3"] .course {
        width: 50%;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .lp-user-profile #profile-nav .lp-profile-nav-tabs>li {
        border: 0;
        padding-right: 20px;
    }
    .lp-user-profile #profile-nav {
        border-top: 0;
    }
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box {
        width: calc((100% - 60px) / 2);
    }
    .lp-user-profile .learn-press-courses .course {
        width: 50%;
    }
    .lp-user-profile ul.learn-press-courses .course {
        margin: 0 0 20px 0;
    }
}
@media  (max-width: 767px) {
    .lp-user-profile .lp-user-profile-avatar img {
        width: auto;
        margin: 0 auto;
    }
   
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box {
        width: 100%;
    }
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 30px;
    }
    .lp-user-profile .lp-user-profile-avatar img {
        width: auto;
        margin: 0 auto;
    }
    .lp-user-profile #profile-nav .lp-profile-nav-tabs>li a {
        font-size: 15px;
        padding: 10px 15px;
    }
    
    .lp-user-profile #profile-nav .lp-profile-nav-tabs>li {
        max-width: max-content;
        border: 0;
    }
    .dashboard-statistic__row .statistic-box {
        flex-direction: column;
    }
    .lp-user-profile #profile-nav {
        border: 0;
    }
    .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 20px;
    }
    .lp-user-profile .wrapper-profile-header .lp-profile-content-area {
        flex-direction: column;
        text-align: center;
    }
    
    .lp-user-profile .wrapper-profile-header .lp-profile-right {
        width: 100%;
        padding: 0 10px;
        padding-top: 30px;
    }
    .lp-user-profile .lp-profile-username {
        margin-bottom: 0;
    }
    .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 30px;
    }
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 0;
    }
    .learn-press-profile-course__progress .lp-archive-courses {
        padding-top: 0;
    }
    .learn-press-profile-course__tab ul.learn-press-filters {
        padding: 30px 0 !important;
    }
}
@media (min-width: 576px) and (max-width: 767px) {
    .lp-user-profile #profile-content-courses .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 0;
    }
}
/**
* My course
*/
.statistic-box {
    transition: all .3s;
}
.lp-user-profile #profile-content-my-courses .dashboard-statistic__row .statistic-box {
    width: calc((100% - 60px) / 3);
}
.learn-press-course-tab-filters .learn-press-filters a {
    text-decoration: none;
}
.learn-press-profile-course__progress .lp_profile_course_progress__header th {
    color: var(--color-white);
    font-weight: 400;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td a:hover {
    color: var(--color-primary);
}
.learn-press-profile-course__progress .lp_profile_course_progress__header {
    background-color: var(--color-primary);
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    background-size: 300% 100%;
}
.learn-press-course-tab-filters .learn-press-filters {
    margin: 0;
    padding: 30px 0 !important;
}
.lp-profile-content table.lp-list-table tr td a:hover, .learn-press-filters>li span {
    color: var(--color-primary);
}
#profile-content-quizzes .learn-press-subtab-content {
    overflow-x: auto;
}
div#profile-content-quizzes .learn-press-filters>li a {
    text-decoration: none;
}
div.order-recover input[type=text] {
    height: 50px;
}

.lp-user-profile .lp-profile-content .lp-button {
    padding: 0 26px;
    background: var(--color-primary);
    height: 48px;
    line-height: 48px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none!important;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs li.active>ul {
    box-shadow: none;
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs li.active>ul .active>a {
    color: var(--color-primary);
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs li.active>ul .active>a i {
    color: var(--color-primary);
}
@media (max-width: 767px) {
    .learn-press-course-tab-filters .learn-press-filters {
        overflow-x: auto;
        padding-bottom: 20px !important;
    }
    .lp-user-profile #profile-content-my-courses .dashboard-statistic__row .statistic-box {
        width: 100%;
    }
    .lp-user-profile #profile-content-my-courses .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 30px;
    }
}
@media (min-width: 576px) and (max-width: 767px) {
    .lp-user-profile #profile-content-my-courses .dashboard-statistic__row .statistic-box {
        width: calc((100% - 30px) / 2);
    }
    .lp-user-profile #profile-content-my-courses .dashboard-statistic__row .statistic-box:not(:last-child) {
        margin-bottom: 0;
    }
}
.lp-user-profile div#profile-content-settings button[type="submit"] {
    padding: 0 26px;
    background: var(--color-primary);
    height: 50px;
    line-height: 50px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}
.lp-user-profile div#profile-content-settings button[type="submit"]:hover {
    background-color: var(--color-secondary);
    color: var(--color-white);
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-7);
}
div#profile-content-settings input[type="checkbox"] {
    width: 15px;
    height: 15px;
}

div#profile-content-settings p.description {
    display: inline-block;
}


/**
* instructors page
*/
.lp-list-instructors .ul-list-instructors li.item-instructor {
    background: var(--color-white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-1);
    border: 0 !important;
}

.lp-list-instructors .ul-list-instructors li.item-instructor .instructor-display-name {
    font-size: 29px;
    line-height: 1.4;
    font-weight: 700;
    margin-bottom: 6px;
    color: var(--color-heading);
}
.lp-list-instructors .ul-list-instructors li.item-instructor .instructor-btn-view {
    padding: 0 26px !important;
    background: var(--color-primary) !important;
    height: 60px;
    line-height: 60px;
    color: var(--color-white) !important;
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    font-weight: 400;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
}

.lp-list-instructors .ul-list-instructors li.item-instructor .instructor-info>div span {
    font-size: 15px;
}
.lp-list-instructors .ul-list-instructors li.item-instructor .instructor-display-name {
    margin: 3px 0;
    margin-top: 20px;
}
.lp-single-instructor__info {
    background: #fff;
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px !important;
    border: 0 !important;
}

.lp-single-instructor .wrapper-instructor-total-courses {display: inline-block;margin-right: 10px;}

.lp-single-instructor .wrapper-instructor-total-students {
    display: inline-block;
}
.lp-single-instructor__info__right .instructor-description {
    padding-bottom: 15px;
}

.lp-single-instructor__info .instructor-avatar img {
    border-radius: 50%;
}
.lp-single-instructor .ul-instructor-courses li {
    background: #fff;
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
}

.lp-single-instructor .ul-instructor-courses .price-categories .course-item-price .free {
    font-size: 16px;
}

.lp-single-instructor .ul-instructor-courses h2 {
    font-family: var(--font-secondary);
    line-height: 1.4074;
    color: var(--color-heading);
    font-size: 23px !important;
}

.lp-single-instructor .ul-instructor-courses .course-count div {
    font-size: 12px;
}
.lp-single-instructor .ul-instructor-courses li {
    margin-top: 0;
}
.learn-press-courses[data-layout=list] .course-content .course-footer {
    flex-wrap: wrap;
    grid-gap: 10px 0;
}
.become-teacher-form button {
    padding: 0 26px;
    background: var(--color-primary);
    height: 50px;
    line-height: 50px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    border-radius: 50px;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.become-teacher-form button:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}
.learn-press-form .form-fields .form-field textarea {
    height: 150px;
}
.lp-checkout-form .lp-checkout-form__after div#checkout-account-logged-in {
    margin-bottom: 30px;
}

.lp-checkout-form a:hover {
    color: var(--color-primary);
}
/* wishlist */
.rbt-bookmark-btn button.lp-btn-wishlist {
    font-size: 0!important;
    padding: 7px;
    border: 0;
    background: #eff1fe;
    border-radius: 50%;
}

.rbt-bookmark-btn button.lp-btn-wishlist::before {
    font-size: 15px;
    margin: 0;
    color: var(--color-body);
}

.rbt-bookmark-btn button.lp-btn-wishlist.on::before {
    color: var(--color-primary)!important;
}
.list-instructors .item-instructor {
    background: var(--color-white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-1);
}

.list-instructors .item-instructor .instructor-avatar img {
    width: 100%;
    border-radius: 5px;
}

span.instructor-display-name {}

.list-instructors .item-instructor span.instructor-display-name {
    font-size: 25px;
    line-height: 1.4;
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    padding-top: 20px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
}

.list-instructors .item-instructor  p {
    font-size: 18px;
    line-height: 1.4;
    margin-bottom: 10px;
    display: inline-block;
    color: var(--color-body);
    font-weight: 400;
    margin-right: 10px;
}

.list-instructors .item-instructor p .instructor-total-courses::after {
    content: "|";
    padding-left: 10px;
}

.learn-press-courses[data-layout=grid] .course-item {
    border: 0;
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    position: relative;
    height: 100%;
}
.instructor-btn-view {
    padding: 0 25px;
    font-size: var(--font-size-b3);
    height: 50px;
    line-height: 48px;
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    width: fit-content;
    color: var(--color-white);
    border-radius: 30px;
}

.instructor-btn-view:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
    color: var(--color-white);
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-7);
}

.rainbow-learnpress-featured-course-widget-single .section-left {
    justify-content: space-between;
}
.rainbow-learnpress-featured-course-widget-single {
    background: #fff;
    padding: 10px 20px;
    border-radius: 5px;
}

.rainbow-learnpress-featured-course-widget-single .lp-archive-courses {
    padding-top: 0;
}
.rainbow-learnpress-featured-course-widget-single {
    position: relative;
}
.rainbow-learnpress-featured-course-widget-single .alert.alert-warning {
    position: sticky;
    top: 10px;
    z-index: 999;
}
/**
 * archive course
*/
.archive.post-type-archive-lp_course .lp-archive-courses {
    padding-top: 0;
    padding-bottom: 0;
}
nav.learn-press-breadcrumb li {
    display: inline-block;
    list-style: none;
    font-size: 14px;
    color: var(--color-body);
}
.learnpress-course-archive-page-overlap {
    margin: -175px auto 0;
}


.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .lp-form-course-filter__item {
    padding: 0 15px;
    border: 0;
    margin: 0;
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .lp-form-course-filter__item input[type="checkbox"] {
    display: none;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter__content .lp-course-filter__field label {
    padding-left: 24px;
}
.rbt-generic-banner-course-filter-banner .lp-form-course-filter .lp-form-course-filter__item input {
    display: block;
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-submit, 
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-reset {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white) ;
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    width: 100%;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-submit {
    background-size: 300% 100%!important;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary))!important;
}
.rbt-generic-banner-course-filter-banner .lp-form-course-filter__title {
    color: var(--color-heading);
    margin-bottom: 10px;
}
.histudy-custom-lp-course-archive .lp-courses-bar {
    float: none;
    padding-top: 100px;
}
.widget.widget_lpr_course_review {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__title {
    cursor: pointer;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__title::after {
    content: "\f078";
    float: right;
    font-family: 'Font awesome 6 pro';
}
.rbt-generic-banner-course-filter-banner .lp-form-course-filter .lp-form-course-filter__content {
    background: #fff;
    padding: 15px;
    border-radius: 5px;
}
.rbt-generic-banner-course-filter-banner .lp-form-course-filter {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-submit, .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-reset {
    width: calc(100% - 15px);
    margin-left: 15px;
    margin-top: 30px;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .lp-form-course-filter__item {
    margin-top: 30px;
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter__content .lp-course-filter-search-result {
    border: 0;
}
.rbt-generic-banner-course-filter-banner form.lp-form-course-filter {
    background: #ffffff54;
    padding: 0 20px;
    border-radius: 10px;
    padding-bottom: 30px;
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter {
    position: relative;
}
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter button.rbt-filter-toggle-btn {
    position: absolute;
    right: 0;
    top: 0;
}
.rbt-generic-banner-course-filter-banner form.lp-form-course-filter {
    margin-top: 80px;
}
.post-type-archive-lp_course .rbt-page-banner-wrapper {
    padding: 0;
    padding-bottom: 100px;
}

.post-type-archive.post-type-archive-lp_course .rbt-section-gapBottom {
    padding-bottom: 110px;
}
.archive.post-type-archive-lp_course .lp-archive-courses .rbt-breadcrumb-default {
    padding-top: 0 !important;
}


.lp-form-course-filter__content .lp-course-filter__field {
    margin-top: 10px;
    margin-bottom: 10px;
}


.rbt-bookmark-btn button.lp-btn-wishlist {
    background: transparent!important;
    width: 40px !important;
    height: 40px !important;
    line-height: 41px !important;
    text-align: center !important;
    border-radius: 100%!important;
    position: relative!important;
    z-index: 1;
    padding: 0!important;
    border: 0 none;
    display: block;
    margin-top: 0 !important;
    box-shadow: unset;
}

.rbt-bookmark-btn button.lp-btn-wishlist:hover {
    background: #eff1fe!important;
}

.profile-courses-list .course-grid-3 .rbt-card-title {
    font-size: 22px;
}

.profile-courses-list .course-grid-3 .rbt-card-title a,
.profile-courses-list .rbt-author-meta .rbt-author-info a {
    text-decoration: none!important;
}

.rbt-bookmark-btn button.lp-btn-wishlist::after {
    background: var(--color-gray-light);
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: 0.4s;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    z-index: -1;
}

.profile-courses-list .rbt-card .rbt-card-body .rbt-card-bottom .rbt-btn-link {
    text-decoration: none!important;
}

.rbt-bookmark-btn button.lp-btn-wishlist::before { 
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}


.profile-courses-list .rbt-bookmark-btn button.lp-btn-wishlist:hover:after {
    opacity: 1;
    transform: scale(1);
}

.profile-courses-list .rbt-bookmark-btn button.lp-btn-wishlist::before {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.learn-press-profile-course__progress .lp_profile_course_progress__item img {
    aspect-ratio: unset;
}

.lp-profile-content .lp_profile_course_progress__nav {
    margin-top: 30px;
}

.lp-ajax-message {
    border: 2px solid #637FEA;
}

.course-curriculum .section-content .course-item-meta .count-questions {
    background: transparent!important;
}

.course-curriculum .section-content .course-item-meta .item-meta {
    color: var(--color-heading)!important;
    font-size: 16px;
    margin-right: 5px;
}

.single-lp_course .course-curriculum .course-item.course-item-lp_quiz {
    padding-left: 0;
    padding-right: 0;
}

.course-curriculum .course-item .item-name, 
.course-curriculum .course-item .section-item-link .course-item-info .course-item-info-pre .item-meta.duration {
    color: var(--color-heading)!important;
    font-size: 16px;
}

#learn-press-quiz-app .quiz-buttons.is-first.is-last {
    justify-content: left!important;
}

.question .answer-option .option-title {
    padding-left: 52px!important;
}

.question .answer-option .option-title {
    border: 1px solid var(--color-border)!important;
}

.question .answer-option input[type=radio]:checked:not(:disabled)::after, 
.question .answer-option input[type=checkbox]:checked:not(:disabled)::after {
    border-color: var(--color-primary)!important;
}

.answer-options .answer-option input[type=checkbox]::after, 
.answer-options .answer-option input[type=radio]::after {
    color: var(--color-primary)!important;
}

.quiz-result.passed .result-message {
    font-size: 16px;
}

.answer-options .answer-option.answer-correct {
    background: var(--color-grey)!important;
}

.question .answer-option.answer-correct .option-title {
    border-color: var(--color-primary)!important;
}

.quiz-result .result-message {
    font-size: 16px!important;
}

.lp-profile-content table.lp-list-table tr th, 
.lp-profile-content table.lp-list-table tr td {
    border-right: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
}

.lp-user-profile #profile-content-wishlist .learn-press-wishlist-courses li .course-title img {
    aspect-ratio: unset!important;
} 

#profile-content-wishlist .course-grid-3 .rbt-card-title a {
    text-decoration: none;
}

#profile-content-wishlist .rbt-author-meta .rbt-author-info a,
#profile-content-wishlist .rbt-card .rbt-card-body .rbt-card-bottom .rbt-btn-link {
    text-decoration: none;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a {
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
}

.page-lp-profile .rbt-footer {
    padding-top: 60px!important;
}

.learn-press-profile-course__progress .lp_profile_course_progress__item td {
    border: 0.5px solid var(--color-border);
}

@media (min-width: 992px) and (max-width: 1199px) {
    .learn-press-courses .course {
        width: 33.33%;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .lp-form-course-filter {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .lp-form-course-filter {
        grid-template-columns: repeat(1, 1fr);
    }
}

.lp-archive-courses .courses-btn-load-more {
    margin: 0 auto;
    margin-top: 50px;
}
.learn-press-courses[data-layout=grid] .course-content .course-footer {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 0 15px;
    align-items: center;
}
.single-lp_course aside.course-summary-sidebar .course-sidebar-secondary {
    display: none;
}

.learnpress-popular-course-section .rbt-review .rating .review-stars-rated {
    margin-bottom: 0px;
}

.review-stars-rated {
    margin-bottom: 5px!important;
}


.review-stars-rated {
    display: flex;
    margin-bottom: 10px;
    position: relative;
}

.review-stars-rated .review-star .fas {
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    color: #ffb60a;
}

.review-stars-rated .review-star {
    position: relative;
    line-height: 1;
    margin: 0 2px;
}

.lp-archive-courses ul, 
.lp-archive-courses ol {
    padding: 0;
}

.learn-press-courses[data-layout=list] .course-grid-3,
.learn-press-courses[data-layout=list] .course-grid-2,
.learn-press-courses[data-layout=list] .rt-course-default
{
    width: 100%;
}

.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card,
.learn-press-courses[data-layout=list] .rt-course-default .rbt-card
 {
    display: flex;
    border-radius: var(--radius);
    align-items: center;
    height: 100%;
}

.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card .rbt-card-img,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card .rbt-card-img,
.learn-press-courses[data-layout=list] .rt-course-default .rbt-card .rbt-card-img {
    flex-basis: 40%;
    height: 100%;
}

.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card .rbt-card-body,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card .rbt-card-body,
.learn-press-courses[data-layout=list] .rt-course-default .rbt-card .rbt-card-body
{
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-basis: 60%;
    padding-left: 30px;
}

.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card .rbt-card-img a,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card .rbt-card-img a {
    display: block;
    height: 100%;
    width: 100%;
}

.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card .rbt-card-img a img ,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card .rbt-card-img a img 
{
    object-position: center;
    border-radius: var(--radius);
    max-height: 100%;
    max-width: 100%;
}


.learn-press-courses[data-layout=list] .course-grid-3 .rbt-card .rbt-card-body .rbt-card-title,
.learn-press-courses[data-layout=list] .course-grid-2 .rbt-card .rbt-card-body .rbt-card-title
 {
    font-size: 26px;
}

.rbt-generic-banner-course-filter-banner .widget_course_filter .widget-title {
    display: none;
}

.rbt-generic-banner-course-filter-banner form.lp-form-course-filter {
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    border: none;
    outline: none;
    position: relative;
    color: var(--color-heading);
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .rbt-search-style input {
    background: transparent;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    padding-right: 60px;
    border-radius: 500px;
    min-width: 300px;
    border: 2px solid var(--color-white);
    color: var(--color-white);
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .rbt-search-style input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: none;
    color: #212529;
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .course_search_input:focus + .rbt-round-btn::after {
    background: var(--color-gray-light);
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .course_search_input:focus + .rbt-round-btn::after, 
.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .course_search_input:focus + .rbt-round-btn.open::after {
    opacity: 1;
    transform: scale(1);
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .course_search_input:focus + .rbt-search-btn i {
    color: var(--color-primary);
}

.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter {
    border-top: 1px solid var(--color-border-2);
    margin-top: 30px;
}

.rbt-generic-banner-course-filter-banner form.lp-form-course-filter {
    margin-top: 40px;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .lp-form-course-filter__item {
    margin-top: 0;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter .lp-form-course-filter__content {
    padding: 0;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__title {
    font-size: 12px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-border);
    margin-bottom: 15px;
    color: var(--color-heading);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
    font-weight: var(--f-bold);
}


.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 20px;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .lp-form-course-filter__item {
    padding: 0 7px;
    border: 0;
    margin: 0;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter__field label {
    position: relative;
    font-size: 15px;
    line-height: 25px;
    color: var(--color-body);
    font-weight: 400;
    padding-left: 20px;
    cursor: pointer;
    margin-bottom: 0;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter__field .count {
    display: none;
}

.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-submit, 
.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-reset {
    margin-top: 0;
    margin-left: 8px;
    height: 40px;
    line-height: 40px;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter-search-field input {
    border-radius: 4px;
}

.rbt-generic-banner-course-filter-banner .learnpress.widget {
    margin-bottom: 0;
}

.post-type-archive-lp_course .page-banner-layout-style-two-learnpress {
    padding-bottom: 235px;
    padding-top: 60px;
}

.learn-press-pagination .page-numbers, .learn-press-pagination .page-numbers.current {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.learn-press-pagination .page-numbers>li {
    margin: 8px;
}

.learn-press-pagination .page-numbers, 
.learn-press-pagination .page-numbers.current {
    width: 45px;
    height: 45px;
    background: var(--color-white);
    border-radius: 6px;
    text-align: center;
    color: var(--color-body);
    transition: 0.4s;
    font-weight: 500;
    box-shadow: var(--shadow-1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.learn-press-pagination .page-numbers>li .page-numbers.current,
.learn-press-pagination .page-numbers>li .page-numbers:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

nav.learn-press-pagination.navigation.pagination {
    align-items: center;
    justify-content: center;
    margin-top: 60px;
}

.courses-btn-load-more.learn-press-pagination {
    padding: 0 26px;
    background: var(--color-primary);
    height: 50px; 
    line-height: 50px;
    color: var(--color-white); 
    font-size: 16px;
    letter-spacing: 0.5px; 
    font-weight: 500; 
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%!important;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary))!important;
}


.courses-btn-load-more.learn-press-pagination:hover,
.rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-submit:hover, 
.rbt-lp-course-sidebar-filter .lp-form-course-filter .course-filter-reset:hover  {
    background-position: 102% 0!important;
    border: none!important;
    transition: all 0.4s ease-in-out;
}
/* Remove conflict from .lp-button hover */
.learnpress-page .courses-btn-load-more.learn-press-pagination:hover {
    background: none /* Override the solid background */
}

.mc4wp-form  .mc4wp-form-fields input[type="email"] {
    padding-top: 10px;
    padding-left: 20px;
}

.lp-content-area.breadcrumb-off-class {
    padding-top: 60px;
    padding-bottom: 120px;
}

.histudy-learnpress-sidebar-section .widget_course_filter {
    box-shadow: var(--shadow-1);
    padding: 30px;
    background: var(--color-white);
    border-radius: 10px;
    margin-left: 0;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item input[type="checkbox"] {
    opacity: 0;
    position: absolute;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item input[type="checkbox"] ~ label,
.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item input[type="radio"] ~ label {
    padding-left: 23px;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item input[type="checkbox"] ~ label,
.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item input[type="radio"] ~ label {
    position: relative;
    font-size: 15px;
    line-height: 25px;
    color: var(--body-color);
    font-weight: 400;
    padding-left: 20px;
    cursor: pointer;
    margin-bottom: 0;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter__content .lp-course-filter-search-result {
    border: 0px solid transparent!important;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item ,
.histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__item {
    background: var(--color-white);
    padding-top: 30px;
}



.histudy-learnpress-sidebar-section .lp-form-course-filter .lp-form-course-filter__item {
    margin-bottom: 0; 
    border-bottom: 0px solid #eee;
    padding-bottom: 0;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item:first-child {
    padding-top: 0;
}

.histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__item {
    padding-top: 30px!important;
}

.histudy-learnpress-sidebar-section .widget-title {
    font-size: 20px;
}

.histudy-learnpress-sidebar-section .learnpress-widget-wrapper {
    margin-top: 0;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter .lp-form-course-filter__item:first-child .lp-form-course-filter__title {
    padding-bottom: 0px;
    border-bottom: 0px solid var(--color-border);
}

.histudy-learnpress-sidebar-section .lp-form-course-filter__content .lp-course-filter__field:last-child {
    margin-bottom: 0;
}

.histudy-learnpress-sidebar-section .widget_course_filter .lp-course-filter-search {
    font-size: 16px;
    font-weight: 400;
    height: 50px;
    line-height: 28px;
    background: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 15px;
    outline: none;
    border: var(--border-width) solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-body);
    box-shadow: var(--shadow-10);
}

.histudy-learnpress-sidebar-section .lp-form-course-filter__content .lp-course-filter__field .count {
    background: var(--color-gray-light);
    padding: 5px 7px;
    font-size: 12px;
    display: flex;
    height: 22px;
    align-items: center;
    border-radius: var(--radius-small);
}
  
.histudy-learnpress-sidebar-section .lp-form-course-filter__item .lp-form-course-filter__title,
.histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__title {
    font-size: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-border);
    margin-bottom: 15px;
    font-weight: var(--f-bold);
    color: var(--color-heading);
}

.histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__item:first-child .lp-form-course-filter__title {
    border-bottom: 2px solid var(--color-border);
    padding-bottom: 15px;
}

.rbt-course-details-right-sidebar.histudy-learnpress-sidebar-section {
    box-shadow: unset;  
    padding: 0;
    border-radius: 10px; 
    margin-left: 0;

}

.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-submit,
.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-reset {

    margin-top: 30px;
    padding: 6px 10px;
    font-size: 12px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border-radius: 6px;
    line-height: 16px;
    height: auto;
    color: var(--color-white);
    background-size: 300% 100%!important;
    
}

.lp-content-area.banner-layout-2,
.lp-content-area.banner-layout-1 {
    padding-left: 0;
    padding-right: 0;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-submit {
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary))!important;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-reset {
    background-color: var(--color-primary) !important;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-submit:hover,
.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-reset:hover {
    background-color: inherit!important;
    background-position: 102% 0!important;
    transition: all 0.4s ease-in-out;
}

.learn-press-courses[data-layout=list] .course {
    padding-bottom: 0!important;
}

.learn-press-courses[data-layout=list] .course {
    padding-top: 32px!important;
}

.learn-press-courses[data-layout=list] .course:first-child {
    padding-top: 0!important;
}

.learn-press-message {
    position: relative;
    margin: 0 !important;
    padding: 0;
    border-top: 0px solid #00adff;
    border-radius: none;
    background: transparent;
    width: 100%;
}

.learn-press-courses.learnrpess-course-not-found {
    display: block!important;
}

.lp-archive-courses .course-content .separator {
    display: inline-block;
} 

.course-details-breadcrumb-learnpress .rbt-price .current-price {
    color: #212327;
}

.learnpress-single-page-rating {
    display: flex;
    align-items: center;
}

.lp-archive-courses .lp-entry-content .entry-content-left {
    padding-top: 0;
}

.lp-archive-courses .lp-entry-content .entry-content-left {
    width: calc(100% - 415px);
}

.lp-archive-courses .course-sidebar-top.rbt-gradient-border {
    width: 415px;
}

.course-sidebar-top {
    margin-top: -500px!important;
}

.single-lp_course .continue-course.form-button.lp-form {
    width: 100%;
}

button.lp-button.rbt-btn.btn-gradient.button-purchase-course,
button.lp-button.rbt-btn.btn-gradient.button-enroll-course,
.continue-course.form-button.lp-form .button.rbt-btn.btn-gradient{
    line-height: 60px!important;
    padding: 0 26px!important;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    text-decoration: none;
    
}

button.lp-button.rbt-btn.btn-gradient.button-purchase-course,
button.lp-button.rbt-btn.btn-gradient.button-enroll-course,
.continue-course.form-button.lp-form .button.rbt-btn.btn-gradient {
    color: var(--color-white)!important;
    background-size: 300% 100%!important;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary))!important;
}

button.lp-button.rbt-btn.btn-gradient.button-purchase-course:hover,
button.lp-button.rbt-btn.btn-gradient.button-enroll-course:hover,
.continue-course.form-button.lp-form .button.rbt-btn.btn-gradient:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}

button.lp-button.rbt-btn.btn-gradient.button-purchase-course .btn-text,
button.lp-button.rbt-btn.btn-gradient.button-enroll-course .btn-text,
button.lp-button.rbt-btn.btn-gradient.button-purchase-course .btn-icon i,
button.lp-button.rbt-btn.btn-gradient.button-enroll-course .btn-icon i {
    color: var(--color-white)!important;
}

.lp-entry-content .course-sidebar .social-icon  {
    list-style: none;
}

.course-sidebar-top .lp-course-buttons .lp-btn-wishlist {
    background: transparent;
    border: 2px solid var(--color-border);
    color: var(--color-heading);
    padding: 0 26px;
    height: 60px;
    line-height: 60px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    outline: none;
}

.course-sidebar-top .lp-course-buttons .enroll-course {
    width: 100%;
}

.course-sidebar-top .lp-course-buttons .lp-btn-wishlist:hover {
    border-color: var(--color-primary);
    color: var(--color-white)!important;
}

.course-sidebar-top .social-share-wrapper .social-icon li a:hover i {
    color: var(--color-white)!important;
}

.single-sidebar .items-progress__heading {
    margin-bottom: 16px!important;
    font-size: 14px!important;
    color: var(--tutor-body-color)!important;
    font-weight: 700!important;
    margin-top: 10px;
}

.single-lp_course:not(.active-dark-mode) .course-summary-content .lp-content-area *:not(h1, h2, h3, h4, h5, h6, label) {
    color: var(--color-heading) !important;
}

.course_progress_wid .course-results-progress {
    margin-bottom: 15px;
    margin-top: 15px;
}
.course_progress_wid .course-results-progress .lp-course-status { 
    color: #41454F!important;
    font-size: 14px;
}
.course_progress_wid .learn-press-progress {
    width: 100%;
}

.learn-press-progress.lp-course-progress {
    position: relative;
    width: 100%;
    height: 4px;
    background: #e3e5eb;
    border-radius: 10px;
    overflow: hidden;
}

.course-time.histudy-course-time-single-page {
    margin-top: 15px;
}

.lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row {
    color: #757c8e!important;
    font-size: 14px;
    font-weight: 400!important;

}

.lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row strong,
.lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row .entry-date.expire { 
    color: #757c8e!important;
    font-weight: 400!important;
}
.lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row .entry-date.enrolled {
    color: #24A148!important;
    font-weight: 700!important;
}

.lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row:first-child {
    margin-bottom: 8px;
}

span.play-view-text.d-block.color-white {
    color: var(--color-white)!important;
    font-size: 16px;
}

.course-tab-panels .course-tab-panel {
    margin-top: 30px!important;
}

ul.learn-press-nav-tabs.course-nav-tabs {
    margin-top: 30px;
}

.course-tab-panel-instructor.course-tab-panel .social-icon.social-default {
    list-style: none;
}
.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a,
.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a i  {
    color: var(--color-body)!important;
}

.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a:hover,
.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a:hover i {
    color: var(--color-primary) !important;
}

.single-lp_course .course-tab-panel-instructor.course-tab-panel {
    padding: 0!important;
}



.single-lp_course .course-tab-panel-reviews.course-tab-panel {
    background: transparent!important;
    padding: 0!important;
    box-shadow: unset!important;
    overflow: visible!important;

}

.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate {
    display: flex;
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
}

.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate .course-rate__summary {
    border-radius: 5px;
    background: var(--warning-opacity);
    text-align: center;
    padding: 22px 10px;
    padding-top: 10px;
    flex: 0 0 25%;
}

.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate .course-rate__details {
    flex: 0 0 75%;
    margin-left: 0;
    padding-left: 24px;
}

.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate .review-star .far {
    color: var(--color-warning)!important;
}
.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate .review-star .far svg {
    fill: var(--color-warning)!important;
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value .rating-count {
    right: -40px;
    color: var(--color-body)!important;
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-star {
    color: var(--color-body)!important;
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-star i { 
    color: var(--color-warning)!important;
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-star i svg {
    fill: var(--color-warning)!important;
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value {
    margin: 0 40px 0 10px;
}

/*learnpress custom css*/

.single-lp_course .course-tab-panel {
    display: block!important;
}

.single-lp_course .lp-entry-content .mainmenu-nav ul {
    list-style: none;
}

.single-lp_course .lp-entry-content .mainmenu-nav ul li a:hover,
.single-lp_course .lp-entry-content .mainmenu-nav ul li.current a {
    color: var(--color-white)!important;
}

.course-tab-panel-overview.course-tab-panel {

}

.single-lp_course .course-curriculum .course-item {
    background: transparent;
}

.single-lp_course .course-curriculum .section-header {
    border-bottom: 1px solid var(--color-border);
}

.single-lp_course .course-curriculum .section-header:last-child,
div#tab-curriculum ul.curriculum-sections li:last-child .section-header {
    border-bottom: 1px solid var(--color-border)!important; 
}

.single-lp_course .course-curriculum .section .section-title,
.single-lp_course .course-curriculum .section .section-toggle i {
    color: var(--color-primary)!important;
}

.single-lp_course .course-curriculum .section.closed .section-title,
.single-lp_course .course-curriculum .section.closed .section-toggle i {
    color: var(--color-heading)!important;
}

.course-item.course-item-lp_lesson {
    padding-left: 0;
    padding-right: 0;
    font-size: 16px;
    color: var(--color-heading)!important;
    font-weight: 400;
}

.course-item.course-item-lp_lesson:first-child {
    margin-top: 15px;
}

.single-lp_course .main-page-wrapper .lp-archive-courses #learn-press-course .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li.current a,
.single-lp_course .main-page-wrapper .lp-archive-courses #learn-press-course .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li:hover a {
 color: var(--color-white)!important;
}

.course-item.course-item-lp_lesson .section-item-link {
    padding-top: 0px;
}

.course-curriculum .course-item .item-name,
.course-curriculum .course-item .section-item-link .course-item-info .course-item-info-pre .item-meta.duration {
    font-weight: 400!important;
}

.course-curriculum .section-content .course-item-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.course-curriculum ul.curriculum-sections .item-meta.duration {
    background: transparent;
}

.lp-entry-content.lp-content-area .course-curriculum .section-content .course-item-preview::before {
    background: var(--primary-opacity) !important;
    color: var(--color-primary) !important;
}

.course-curriculum .course-item.item-locked .course-item-status::before {
    color: var(--color-heading)!important;
}

.curriculum-scrollable.rbt-course-feature-inner .section-title .rbt-title-style-3 {
    margin-bottom: 0;
}

.course-curriculum .section-header .section-left .section-toggle {
    flex: 0;
}

.course-curriculum .section-content .course-item-meta .course-item-status::before {
    content: "\e987";
    font-family: 'feather' !important;
}

.single-lp_course .lp-icon-caret-down:before {
    content: "\e9b1";
    font-family: "feather" !important;
}

.single-lp_course .lp-icon-caret-up:before {
    content: "\e996";
    font-family: "feather" !important;
}

.single-lp_course .course-tab-panel-reviews.course-tab-panel {
    margin-top: 40px;
}

aside.course-sidebar.sticky-top.rbt-shadow-box.course-sidebar-top.rbt-gradient-border img ,
.rbt-course-feature-has-video-thumbnail.rbt-course-feature-box.rbt-shadow-box.thuumbnail.tab-upper-video-content-part img {
    border-radius: 6px;
}

.single-lp_course .rbt-price-wrapper.d-flex.flex-wrap.align-items-center.justify-content-between {
    margin-top: 10px;
    margin-bottom: 8px;
}

.lp-archive-courses .course-summary-content .comment-list {
    list-style: none;
}

.single-lp_course .comment-list .comment .reply-edit a.comment-reply-link {
    padding-left: 0;
    margin-left: 0;
}

.single-lp_course .learnpress-course-review .write-a-review.lp-button {
    font-size: var(--font-size-b3);
    height: 50px;
    line-height: 48px;
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    color: #fff !important;
    padding: 0 30px;
    font-weight: 500;
    transition: 0.3s;
    border-radius: 500px !important;
    border: none;
    margin-top: 30px;
}

.single-lp_course .learnpress-course-review .write-a-review.lp-button:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
    border: none;
}

.rbt-course-details-area .sticky-top.course-sidebar-top {
    align-self: baseline;
}

body.learnpress-page.single-lp_course {
    overflow-x: unset!important;
}

.single-lp_course .rbt-inner-onepage-navigation.sticky-top {
    z-index: 99999;
}

.review-stars-rated .review-star .far,
.course-tab-panel-reviews.course-tab-panel .review-stars-rated .review-star .far {
    position: relative;
}

.histudy-learnpress-curriculum-empty {
    margin-top: 20px;
}

.learnpress-course-review .review-form {
    background-color: #fff;
    position: relative;
    width: 100%;
    pointer-events: auto;
    background-clip: padding-box;
    border: 1px solid var(--tutor-border-color);
    border-radius: 16px;
    outline: 0;
    padding: 24px 32px;
}

.lp-archive-courses .course-summary-content button.lp-button.submit-review,
.lp-archive-courses .course-summary-content .review-actions button.lp-button.close {
   
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    padding: 0 25px;
    font-size: var(--font-size-b3);
    height: 50px;
    line-height: 48px;
   
}

.lp-archive-courses .course-summary-content button.lp-button.submit-review {
    background: var(--primary-opacity) !important;
    color: var(--color-primary) !important;
}

.lp-archive-courses .course-summary-content .review-actions button.lp-button.close { 
    background: var(--pink-opacity) !important;
    color: var(--color-pink) !important;
}

.lp-archive-courses .course-summary-content .review-actions button.lp-button.close:hover {
    background: var(--color-pink) !important;
    color: var(--color-white) !important;
}

.lp-archive-courses .course-summary-content button.lp-button.submit-review:hover
{
    background: var(--color-primary) !important;
    color: var(--color-white) !important;
}

.course-tab-panel-reviews.course-tab-panel #course-reviews {
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
    margin-top: 30px;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list {
    margin: 0;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li {
    border: none;
    background-color: transparent;
    padding: 0;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li .user-name {
    text-transform: capitalize;
    font-weight: var(--f-bold);
    font-size: var(--h5);
    line-height: 1.24;
    margin: 0px 0 8px 2px;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li .user-name:hover {
    color: var(--color-primary)!important;
}

.course-tab-panel-reviews.course-tab-panel .course-review-head {
    margin-bottom: 24px;
    font-size: 20px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--color-border-2);
}

.course-tab-panel-reviews.course-tab-panel .review-stars-rated .review-star .fas,
.course-tab-panel-reviews.course-tab-panel .review-stars-rated .review-star .fas svg path {
    color: var(--color-warning)!important;
}

.course-tab-panel-reviews.course-tab-panel .review-stars-rated .review-star .fas svg {
    fill:  var(--color-warning)!important;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list .review-text .review-content {
    font-weight: 400;
    color: var(--color-body)!important;
}

.learnpress-course-review .course-reviews-list li .review-title {
    margin: 9px 0 10px;
}

.lp-archive-courses .about-author .media-body .author-info .title {
    font-weight: var(--f-bold);
}

.main-page-wrapper .lp-entry-content.lp-content-area h4,
.main-page-wrapper .lp-entry-content.lp-content-area h3 {
    font-weight: var(--f-bold);
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li {
    border-bottom: 1px solid var(--color-border);
    padding: 25px 0;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li {
    padding-top: 0;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list li:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.lp-archive-courses .learnpress-course-review .course-reviews-list .review-author img {
    border-radius: 6px;
}

.single-lp_course .lp-entry-content.lp-content-area .related-course .rbt-switch-btn:hover,
.single-lp_course .lp-entry-content.lp-content-area .related-course .rbt-switch-btn span:hover {
    color: var(--color-white)!important;
}

.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist,
.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist {
    width: 40px!important;
    height: 40px!important;
    line-height: 41px!important;
    text-align: center!important;
    border-radius: 100%;
    position: relative;
    z-index: 1;
    background: transparent;
    padding: 0;
    border: 0 none;
    display: block;
    margin-top: 0!important;
    box-shadow: unset;
}

.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::after,
.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::after {
    background: var(--color-gray-light);
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: 0.4s;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    z-index: -1;
}

.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::before,
.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::before {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist:hover::before,
.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist:hover::before {
    color: var(--color-primary);
}

.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist:hover::after,
.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist:hover::after {
    opacity: 1;
    transform: scale(1);
}

.lp-archive-courses .rbt-related-course-area {
    padding-bottom: 0;
}

#popup-header .course-title a:hover {
    color: var(--color-white);
}

.curriculum-scrollable.rbt-course-feature-inner .wrapper-section-title .section-title {
    font-size: 18px;
    line-height: 27px;
    outline: none;
    text-decoration: none;
    box-shadow: none;
    width: 100%;
    text-align: left;
    padding: 0;
    background-color: transparent;
    position: relative;
    font-weight: 600;
}

.curriculum-scrollable.rbt-course-feature-inner .curriculum-sections .section-header {
    padding-left: 0 !important;
    padding-right: 0!important;
}

#popup-sidebar .course-item.current {
    background: transparent;
}

.content-item-wrap .course-item-title {
    font-weight: var(--f-bold);
    font-size: 30px;
}

#popup-content .lp-button.completed {
    background: var(--color-primary); 
    border-radius: 500px;    
    font-size: var(--font-size-b3);
}

button.lp-button.button.button-complete-item.button-complete-lesson.lp-btn-complete-item {
    border-radius: 500px;    
    font-size: var(--font-size-b3);
    margin-top: 0px!important;
}

.learn-press-message.notice,
.learn-press-message.notice a {
    font-size: 18px;
}

.lp-archive-courses #popup-header {
    background: var(--color-primary)!important;
}

#learn-press-checkout {
    padding-left: 0!important;
    padding-right: 0!important;
}

.learnpress-checkout .learnpress .learn-press-message {
    margin: 0 0 2em;
    position: relative;
    background-color: #f6f5f8;
    color: #515151;
    border-top: 3px solid #7f54b3;
    list-style: none outside;
    width: auto;
    word-wrap: break-word;
    border-top-color: #b81c23;
    padding: 15px 25px;
    border-radius: 6px;
}

#checkout-payment #checkout-order-action button:hover {
    opacity: 0.8;
}


.lp-terms-and-conditions a,
.lp-checkout-form a {
    color: var(--color-primary)!important;
}
.lp-checkout-form .lp-checkout-remember a {
    text-decoration-color: var(--color-primary);
}

#checkout-order .lp-checkout-order__inner,
.order-comments {
    border: 1px solid var(--color-border)!important;
}

#learn-press-checkout #learn-press-checkout .learn-press-message.error {
    margin-top: 30px!important;
}

#learn-press-checkout .learn-press-message {
    margin: 0 auto!important;
}

.lp-entry-content.lp-content-area  .lp-course-buttons form {
    width: 100%!important;
}
 
.learn-press-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
}

.learn-press-pagination .page-numbers {
    display: flex!important;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.learn-press-message.notice, .learn-press-message.notice a {
    margin-bottom: 30px!important;
}

.learn-press-message.notice {
    width: 792px;
    max-width: 100%;
    margin: 20px auto !important;
}

form[name="learn-press-form-complete-lesson"] {
    width: 792px;
    max-width: 100%;
    margin: 0 auto !important;
    display: block;
}

.learn-press-pagination .page-numbers > li .page-numbers:hover,
.learn-press-pagination .page-numbers > li .page-numbers.current {
    color: var(--color-white) !important;
}

.page-lp-profile .rbt-section-gap {
    margin: -175px auto 0;
    padding-top: 0;
    position: relative;
    z-index: 2;
}

.page-lp-profile .lp-user-profile.current-user {
    background: transparent!important;
}

.learn-press-profile-course__tab {
    margin-top: 20px;
}

.review-stars-rated .review-star .far {
    color: var(--color-warning)!important;
}

.learn-press-message.learn-press-content-protected-message.error {
    padding-top: 30px;
}

.single-lp_course .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li.current a,
.single-lp_course .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li:hover a,
button.mt--15.rbt-btn.btn-border.icon-hover.w-100.d-block.text-center.lp-button.lp-btn-wishlist.course-wishlist.on:hover,
.single-lp_course .rbt-course-details-area  .learnpress-course-review .write-a-review.lp-button {
    color: var(--color-white) !important;
}

.course-curriculum .section-content .course-item-preview::before {
    background: var(--primary-opacity);
    color: var(--color-primary);
}

.logged-in .course-curriculum .course-item.has-status.status-completed .course-item-status::before, 
.logged-in .course-curriculum .course-item.has-status.status-evaluated .course-item-status::before {
    font-family: "lp-icon" !important;
    content: "\f00c" !important;
}
/* single course faq*/

input[name=course-faqs-box-ratio]:checked + .course-faqs-box ,
.course-tab-panel-faqs .course-faqs-box:hover{
    background-color: transparent!important;
}

.course-tab-panel-faqs .course-faqs-box {
    border: 2px solid var(--color-border);
}

.single-lp_course .course-summary-content .course-tab-panel-faqs .course-faqs-box .course-faqs-box__content-inner {
    color: var(--color-body)!important;
    font-weight: 400;
}

.single-lp_course .course-extra-box__content li:first-child {
    border-top: 1px solid var(--color-border);
}

.course-extra-box__content li {
    border-bottom: 1px solid var(--color-border);
}

.single-lp_course .course-tab-panel-faqs {
    padding-top: 30px;
}

.course-curriculum .course-item.course-item-lp_quiz .section-item-link {
    padding-top: 0;
}

.course-tab-panel-faqs .course-faqs-box:last-child {
    margin-bottom: 30px!important;
}       

.single-lp_course .course-extra-box:last-child {
    margin-bottom: 30px;
}

.single-lp_course .course-extra-box:first-child {
    margin-top: 30px;
}

.single-lp_course .course-extra-box__title {
    background: transparent;
}

.single-lp_course .course-tab-panel-reviews.course-tab-panel {
    margin-bottom: 40px;
}

.single-lp_course .course-extra-box__content li::before {
    color: var(--color-success);
}

.lp-checkout-form .lp-checkout-remember label,
.lp-checkout-form .lp-checkout-remember a,
#checkout-account-register .lp-checkout-sign-in-link,
 #checkout-account-register .lp-checkout-sign-up-link, 
 #checkout-account-login .lp-checkout-sign-in-link, 
#checkout-account-login .lp-checkout-sign-up-link,
 .lp-terms-and-conditions {
    font-size: 16px;
}

.single-lp_course .course-extra-box {
    border: 2px solid var(--color-border);
}

.related-course .rbt-card-bottom .course_details,
.rbt-related-course-area .rbt-card-bottom .course_details {
    position: relative;
}

.rbt-related-course-area .rbt-card-bottom .course_details::after,
.related-course .rbt-card-bottom .course_details::after {
    position: absolute;
    content: "";
    left: auto;
    bottom: 0;
    background: currentColor;
    width: 0;
    height: 2px;
    transition: 0.3s;
    right: 0;
}

.rbt-related-course-area .rbt-card-bottom .course_details:hover::after,
.related-course .rbt-card-bottom .course_details:hover::after { 
    width: 100%;
    left: 0;
}

.single-lp_course .course-tab-panel-faqs .course-faqs-box__title::after {
    content: "\e9b1";
    font-family: "feather" !important;
    font-size: 18px;
    line-height: 27px;
}

.single-lp_course input[name=course-faqs-box-ratio]:checked + .course-faqs-box .course-faqs-box__title::after {
    content: "\e996";
    font-family: "feather" !important;
}

.single-lp_course .course-tab-panel-faqs .course-faqs-box .course-faqs-box__content-inner {
    border-top: 2px solid var(--color-border);
}

.single-lp_course .course-tab-panel-faqs .course-faqs-box__content-inner {
    padding: 20px 30px 25px;
}

.course-extra-box__title::after {
    content: "\e9b1";
    font-family: "feather"!important;
}

.course-extra-box.active .course-extra-box__title::after {
    font-family: "feather"!important;
    content: "\e996";
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value .rating {
    background-color: var(--color-warning);
}

.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value .rating, 
.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value .rating-gray {
   
    border-radius: 0;
    height: 6px;
    margin-top: -2px;
}

.single-lp_course #learn-press-course .lp-entry-content .course-sidebar .social-icon a:hover i {
    color: var(--color-white)!important;
}

.course-sidebar .course-results-progress .items-progress, 
.course-sidebar .course-results-progress .course-progress {
    display: flex;
    flex-direction: row;
    margin: 0;
    padding: 0;
    justify-content: space-between;
    flex-wrap: wrap;
} 

.course-sidebar .course-results-progress .items-progress__heading, 
.course-sidebar .course-results-progress .course-progress__heading {
    margin: 0;
    margin-bottom: 7px;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
    flex: 1 1 auto;
    width: auto;
}

.course-sidebar .course-results-progress .number {
    display: block;
    margin: 0;
    color: #666;
    font-size: 14px;
    font-weight: 400;
    line-height: 1em;
    text-align: right;
}

.single-lp_course .course-sidebar .lp-course-buttons .lp-button.button .btn-text,
.single-lp_course .course-sidebar .lp-course-buttons .lp-button.button .btn-icon i,
.single-lp_course .course-sidebar .lp-course-buttons .course-wishlist:hover {
    color: var(--color-white)!important;
}

.lp-user-profile .lp-user-profile-avatar img {
    width: 200px;
    height: 200px;
}

.lp-user-profile .lp-profile-left {
    min-width: 200px !important;
    max-width: 200px !important;
}

.learn-press-tabs .learn-press-tabs__tab::before {
    padding: 0!important;
    border-radius: 0!important;
    transform: unset!important;
}

.lp-user-profile #profile-content-settings .learn-press-tabs__nav .learn-press-tabs__tab {
    cursor: pointer;
}

.lp-password-input .lp-show-password-input {
    top: 12px;
}

.lp-course-students-list-wrapper ul.lp-students-list-wrapper li {
    border: 0px solid #fff!important;
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px!important;
}

.single-lp_course .rbt-instructor.rbt-shadow-box.intructor-wrapper .author-info .hover-flip-item-wrapper,
.single-lp_course #learn-press-course .lp-students-list-wrapper .lp-student-enrolled .student-course-item .student-info a,
.single-lp_course #learn-press-course .rbt-course-details-area .leave-comment-form .comment-list .single-comment .commenter a {
    color:var(--color-heading)!important;
    text-transform: capitalize;
    font-weight: 700!important;
}
.single-lp_course #learn-press-course .lp-students-list-wrapper .lp-student-enrolled .student-course-item .student-info a { 
    font-size: 18px;
}

.single-lp_course #learn-press-course .rbt-course-details-area .leave-comment-form .comment-list .single-comment .reply a,
.single-lp_course #learn-press-course .rbt-course-details-area .leave-comment-form .comment-list .single-comment .reply i {
    font-size: 14px;
    line-height: 18px;
    display: flex;
    color: var(--color-primary)!important;
    margin-left: 8px;
    padding-left: 8px;
    position: relative;
    font-weight: 500;
    overflow: visible;

}

.single-lp_course #learn-press-course .course-sidebar .rbt-badge-2 {
    color: var(--color-heading)!important;
}

.single-lp_course #learn-press-course .course-sidebar .rbt-badge-2 strong {
    color: var(--color-primary)!important;
}

.lp-course-students-list-wrapper .lp-load-ajax-element .lp-target  {
    padding-top: 35px;
}

.lp-course-students-list-wrapper .lp-load-ajax-element .lp-target p {
    margin-bottom: 20px;
}

.lp-course-students-list-wrapper .lp-load-ajax-element .lp-target .student-info p {
    margin-bottom: 0;
}

.learn-press-progress .learn-press-progress__active  {
    background: #A664E8;
}

.lp-checkout-form .lp-form-fields input[type=text], 
.lp-checkout-form .lp-form-fields input[type=password], 
.lp-checkout-form .lp-form-fields input[type=email], 
.lp-checkout-form .lp-form-fields input[type=number], 
.lp-checkout-form .lp-form-fields input[type=tel],
.lp-checkout-form .lp-form-fields textarea,
.lp-checkout-form__after .acf-user-register-fields .acf-input input[type="text"],
.lp-checkout-form__after .acf-user-register-fields .acf-input input[type="url"] {
    border: 2px solid var(--color-border);
    width: 100%;
    background-color: transparent;
    border: 2px solid var(--color-border);
    border-radius: 6px;
    line-height: 23px;
    padding: 10px 20px;
    font-size: 14px;
    color: var(--color-body);
    margin-bottom: 15px;
    
}

.lp-checkout-form__after .acf-user-register-fields .acf-input input[type="url"],
.lp-checkout-form__after .acf-user-register-fields .acf-input input[type="checkbox"] {
    height: 100%!important;
    width: unset!important;
}

#checkout-order .lp-checkout-order__inner,
 .order-comments {
    border: 2px solid var(--color-border)!important;
}

.lp-checkout-form .lp-checkout-remember label input[type=checkbox] {
    border: 2px solid var(--color-border);
}

.lp-checkout-form .lp-checkout-remember label input[type=checkbox]:checked::after {
    color: var(--color-primary);
}

.lp-checkout-form .acf-label label,
.lp-checkout-form .acf-label .description {
    margin-bottom: 10px!important;
}

.lp-checkout-form__after .acf-user-register-fields .acf-field .acf-label {
    display: flex;
    align-items: start;
    flex-direction: column;
    margin-top: 20px;
}

.lp-checkout-form__after .acf-user-register-fields .acf-input .acf-label input[type="checkbox"] { 
    width: unset!important;
}

.lp-checkout-form__after .acf-user-register-fields .acf-input .acf-true-false label {
    display: flex;
    align-items: center;
}

.lp-checkout-form__after .acf-user-register-fields .acf-input .acf-true-false label .acf-switch-off {
    display: none;
}

.lp-checkout-form__after .acf-user-register-fields .acf-input .acf-true-false label .acf-switch-on {
    margin-left: 5px;
    font-size: 16px;
}

.lp-checkout-form .lp-form-fields {
    padding: 0!important;
}

div#checkout-account-logged-in {
    color: var(--color-heading)!important;
}

#checkout-payment h4 {
    font-size: 20px;
}

#learn-press-checkout .payment-methods .lp-payment-method.selected > label {
    background: transparent;
    border: 2px solid var(--color-border);
    border-radius: 6px;
}

.payment-method-form.payment_method_offline-payment {
    margin-top: 20px;
}

#learn-press-checkout .payment-methods .payment-method-form {
    display: none;
    padding: 15px 20px;
    border-top: 2px solid var(--color-border);

}

#checkout-payment .lp-payment-method .gateway-input {
    border: 1px solid var(--color-border);
    background: #fff;
}

#checkout-payment .lp-payment-method .gateway-input::before {
    width: 10px;
    height: 10px;
    background: var(--color-primary);
}

.lp-content-area .order_details th, 
.lp-content-area .order_details td {
    border: 1px solid var(--color-border);
}

.post-type-archive.post-type-archive-lp_course .learn-press-pagination .page-numbers {
    gap: 0;
}

.page-instructor .rbt-section-gap {
    margin: -175px auto 0;
    padding-top: 0;
    position: relative;
    z-index: 2;
}

.lp-single-instructor__info .instructor-avatar img {
    width: 200px;
    height: 200px;
}

.lp-single-instructor__info {
    align-items: center;
}

span.instructor-display-name {
    text-transform: capitalize;
}

/*course instructor*/

.lp-single-instructor .ul-instructor-courses .price-categories .course-item-price .free {
    font-size: 16px;
    font-weight: 500;
    color: var(--color-heading)!important;
}

.lp-single-instructor .ul-instructor-courses .price-categories .course-categories {
    color: var(--color-heading)!important;
    font-weight: 500;
    font-size: 16px;
}

.lp-single-instructor .ul-instructor-courses h2 {
    border-bottom: 1px solid var(--color-border)!important;
}

.lp-single-instructor {
    padding-bottom: 100px;
}

.instructor-total-courses,
.wrapper-instructor-total-students {
    font-size: 16px;
}

.lp-list-instructors .ul-list-instructors li.item-instructor .instructor-btn-view {
    height: 45px!important;
    line-height: 45px!important;
}

.single-lp_course #learn-press-course .rbt-course-details-area  .entry-content-left .rbt-list-style-1 li i {
    color: var(--color-success)!important;
}

.rbt-generic-banner-course-filter-banner .lp-form-course-filter .lp-form-course-filter__content {
	overflow-y: auto;
   height: 120px;
}

.lp-main-content ul.learn-press-courses[data-layout="grid"] > li  {
    margin-top: 0;
    margin-bottom: 30px;
}

.lp-main-content .learn-press-courses[data-layout=grid] .course .rt-course-default,
.lp-main-content .learn-press-courses[data-layout=list] .course .rt-course-default  {
    height: 100%;
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    position: relative;
    height: 100%;
} 



.lp-main-content .learn-press-courses[data-layout=grid] .course .rbt-card,
.lp-main-content .learn-press-courses[data-layout=list] .course .rbt-card {
    height: unset;
    overflow: hidden;
    box-shadow: unset;
    padding: unset;
    border-radius: unset;
    background: unset;
    position: unset;
    height: unset;
}

.lp-main-content .learn-press-courses[data-layout=grid] .course.histudy-learnpress-course2  {
    width: 50%;
}

.course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single {
    background: transparent;
}

.rainbow-learnpress-featured-course-widget-single .alert.alert-warning {
    display: none;
}

.rainbow-learnpress-featured-course-widget-single .course-curriculum .section-left .section-title {
    color: var(--color-heading);
    font-size: 18px;
    line-height: 27px;
    outline: none;
    text-decoration: none;
    box-shadow: none;
    width: 100%;
    text-align: left;
    padding: 0;
    background-color: transparent;
    position: relative;
    font-weight: 600;
}

.rbt-search-dropdown .review-stars-rated .review-star svg {
    width: 10px;
    height: 10px;
} 

.learnpress-page .lp-button:disabled, 
.learnpress-page .lp-button[disabled] {
    color: var(--color-white)!important;
}

.lp-overlay {
    background: rgba(0, 0, 0, 0.8)!important;
    backdrop-filter: blur(15px);
}

.lp-modal-dialog .lp-modal-footer button {
    padding: 0px 35px!important;
    background: var(--color-primary);
    height: 40px!important;
    line-height: 40px!important;
}

.lp-modal-dialog .lp-modal-footer button.lp-button.btn-no {
    background: var(--color-secondary);
}

.course-item-lp_lesson .lp-archive-courses #popup-content {
    padding-left: 0;
    padding-right: 0;
}

.course-item-lp_lesson .content-item-wrap  .lesson-description iframe { 
    width: 100%;
}
.content-item-wrap .content-item-summary {
    padding-top: 25px;
}

.course-item-lp_lesson .content-item-wrap .course-item-title {
    text-align: center;
}

.learn-press-courses .lp-archive-course-skeleton {
    width: 1305px!important;
    left: 15px;
}

.post-type-archive-lp_course .learn-press-courses .rainbow-course-not-found-error {
    width: 1305px!important;
    margin-left: 15px;
}

button.lp-button.button.button-complete-item.button-complete-lesson.lp-btn-complete-item {
    background: var(--color-primary)!important;
}

.course-item-lp_lesson .learn-press-comments {
    padding-top: 20px;
    border-top: 1px solid var(--color-border);
}

.histudy-lesson-main-content {
    width: 792px;
    max-width: 100%;
    margin: 0 auto;
}

.course-item-lp_lesson #popup-footer {
    
    height: 60px!important;
    border-top: 1px solid var(--color-border);

} 

.course-item-lp_lesson #popup-footer .course-item-nav { 
    margin-top: 10px;
} 

#popup-footer .course-item-nav__name {
    top: -55px!important;
}

.course-item-nav .prev, 
.course-item-nav .next  {
    padding: 0 26px;
    background: var(--primary-opacity) !important;
    color: var(--color-primary) !important;
    height: 40px;
    line-height: 40px!important;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    text-decoration: none;
}

#popup-footer .course-item-nav[data-nav=next] {
    border-top: none!important;
}

.course-item-nav .prev:hover, 
.course-item-nav .next:hover  {
    background: var(--color-primary) !important;
    color: var(--color-white) !important;
}

#popup-footer .course-item-nav .prev a:hover, 
#popup-footer .course-item-nav .next a:hover {
    color: var(--color-white) !important; 
}

#popup-footer .course-item-nav .prev:hover a, 
#popup-footer .course-item-nav .prev:hover::before,
 #popup-footer .course-item-nav .next:hover a, 
 #popup-footer .course-item-nav .next:hover::before {
    color: var(--color-white)!important; 
}

#popup-footer .course-item-nav__name {

    background-color: var(--color-gray-light)!important; 
    color: var(--color-body)!important; 
}

#sidebar-toggle {
    width: 60px!important;
}

.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item .section-item-link {
    justify-content: flex-start;
}

.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item {
    background: transparent;
}

.rainbow-learnpress-featured-course-widget-single .curriculum-sections li:first-child .section-header {
    padding-top: 0!important;
}

.course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item.status-completed.passed {
    padding-left: 0!important;
    padding-right: 0!important;
}

.rainbow-learnpress-featured-course-widget-single .curriculum-sections .section.closed .section-toggle .lp-icon-caret-up {
    position: relative;
}

.curriculum-sections .section .section-toggle .lp-icon-caret-up::before, 
.curriculum-sections .section .section-toggle .lp-icon-caret-up::before {
    content: "\e996"!important;
    
    font-family: "feather"!important;
}

.curriculum-sections .section.closed .section-toggle .lp-icon-caret-down::before, 
.curriculum-sections .section.closed .section-toggle .lp-icon-caret-down::before {
   
    content: "\e9b1"!important;
    font-family: "feather"!important;
}

.course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item {
    transition: all 0.3s ease-in-out!important;
}

.course-sidebar-top .lp-course-buttons .lp-btn-wishlist {
    margin-bottom: 10px;
}

.rbt-card .rbt-card-body .lp-review-svg-star svg {
    width: 16px!important;
}

.rbt-card .rbt-card-body .review-stars-rated {
    margin-bottom: 0px !important;
}

form[name="learn-press-form-complete-lesson"] {
    width: 100%;
}

.learn-press-message.notice {
    width: 100%;
}

span.lp-loading-circle.lp-loading-no-css.hide {
	color: var(--color-white)!important;
}

.mc4wp-form .mc4wp-form-fields input[type="email"] {
	padding-top: 0;
}

.rbt-swiper-thumb {
    position: absolute!important;
}

.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-reset,
.histudy-learnpress-sidebar-section .lp-form-course-filter .course-filter-reset:hover {
    background-color: var(--color-primary) !important;
	background-image: unset!important;
}

.learnpress-popular-course-section .rbt-review .rating .review-stars-rated {
    margin-bottom: 0px;
}

.rbt-card .rbt-card-body .lp-review-svg-star svg {
    width: 16px!important;
}

.rbt-search-dropdown .wrapper form input {
    border-radius: var(--radius)!important;
}

.lp-user-profile .lp-profile-content-area {
    display: flex;
    align-items: center;
}

.learn-press-tabs__item > a {
    text-decoration: none!important;
}

.course-extra-box__title {
    font-size: 1em!important;
}

.lp-archive-courses .learn-press-courses-header {
    margin-bottom: 0;
    display: none;
}

.learn-press-message.warning.lp-content-area {
    font-size: 14px;
    display: block;
    margin-top: 10px;
    margin-bottom: 20px;
    text-align: left;
    background: transparent!important;
    padding: 0;
}

.course-sidebar-top.course--single-layout-three {
    margin-top: 0!important;
} 

.rbt-lp-course-details-style-threee .rbt-inner-onepage-navigation {
    margin-top: 0!important;
}

.rbt-course-details-content-2.rbt-course-details-area .entry-content-left {
    margin-top: 25px;
}

.histdylp-course-single-three .video-popup-wrapper.popup-video {
    display: block!important;
}

.rbt-course-details-area.rbt-lp-course-details-four {
    padding-top: 40px !important;
}

.rbt-course-details-layout-style-five .course-sidebar-top {
    margin-top: 0px!important;
}

.rbt-course-details-layout-style-five .course-details-layout3-brd .feature-sin.rating {
    margin-top: 5px;
}

.rbt-course-single-brd-layout-four .course-details-layout3-brd .feature-sin.rating {
    margin-top: 10px !important;
}

.rbt-course-details-layout-style-five .rbt-course-single-layout-five-sidebar .inner,
.widget_learnpress_related_course_widget,
.widget_learnpress_related_course_widget .rbt-single-widget .widget-title  {
    position: relative;
}

.widget_learnpress_related_course_widget .rbt-single-widget .widget-title {
    color: var(--color-heading)!important;
    font-size: 22px!important;
    font-weight: 600!important;
    line-height: 24px;
    position: relative;
}

.widget_learnpress_related_course_widget .recent-post-list li .content .title {
    font-size: 16px !important;
}

.single-lp_course .course-summary-content .lp-content-area .widget_learnpress_related_course_widget .recent-post-list li .content .title a {
    color: var(--color-heading)!important;
}

.single-lp_course .course-summary-content .lp-content-area .widget_learnpress_related_course_widget .recent-post-list li .content .title a:hover {
    color: var(--color-primary)!important;
}

.widget_learnpress_related_course_widget .rbt-price .off-price {
    font-size: 20px;
    font-weight: 500;
    opacity: 0.4;
    margin-right: 7px;
}

.single-lp_course .course-summary-content .lp-content-area .rbt-course-single-layout-five-sidebar .widget_learnpress_related_course_widget .rbt-price .current-price  {
    font-size: 20px;
    font-weight: 700;
    color: var(--color-heading)!important;
}

.single-lp_course .rbt-single-widget .recent-post-list li .thumbnail a img {
    object-fit: inherit;
}

.single-lp_course .rbt-single-widget .lp-review-svg-star svg {
    width: 16px;
}

.single-lp_course .course-summary-content .lp-content-area .rbt-course-single-layout-five-sidebar .description {
    font-size: 16px;
}

.single-lp_course .course-summary-content .lp-content-area .rbt-course-single-layout-five-sidebar .widget_learnpress_related_course_widget .description a {
    text-transform: capitalize;
    color: var(--color-heading)!important;
}

.single-lp_course .course-summary-content .lp-content-area .rbt-course-single-layout-five-sidebar .widget_learnpress_related_course_widget .rbt-sidebar-price {
    display: flex;
    align-items: center;
    gap: 10px;
}

.single-lp_course .course-summary-content .lp-content-area .rbt-course-single-layout-five-sidebar .widget_learnpress_related_course_widget .review-stars-rated {
    margin-top: 3px;
}

.widget_learnpress_related_course_widget {
    margin-top: 30px;
}

.course-extra-box__title {
    font-size: 1em!important;
}
.lp-archive-courses .learn-press-courses-header {
    margin-bottom: 0;
    display: none;
}

.learn-press-message.warning.lp-content-area {
    font-size: 14px;
    display: block;
    margin-top: 10px;
    margin-bottom: 20px;
    text-align: left;
    background: transparent!important;
    padding: 0;
}

.rbt-portfolio-filter .cat-icon-img img {
		border-radius: 50%;
}

/* .lp-single-instructor .ul-instructor-courses {
	display: flex;
}
 */
.lp-single-instructor .ul-instructor-courses {
	display: flex!important;
	flex-wrap: wrap;
}

.lp-single-instructor .learn-press-courses .course {
		width: 31.33%;
}


.lp-single-instructor .ul-instructor-courses .course-item {
	border: none!important;
}


.lp-single-instructor .learn-press-courses .course {
	margin-bottom: 0;
}

.lp-single-instructor .ul-instructor-courses .course-readmore a {
   border: 1px solid #e6e6e6!important;
   font-size: 16px
}


.lp-single-instructor .ul-instructor-courses .course-item:hover {
	box-shadow: none!important;
}
.lp-single-instructor .learn-press-courses {
		display: block!important;
}

.lp-single-instructor .learn-press-pagination {
	display: flex;
	align-items: center;
	justify-content: center;
}

.lp-single-instructor {
	padding-bottom: 0!important;
}

.lp-single-instructor .ul-instructor-courses .course-price .free {
    color: var(--lp-primary-color)!important;
    font-size: 18px !important;
}


@media (min-width: 1200px) {
    .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
        grid-template-columns: repeat(5, 1fr);
    }
}

#popup-footer .course-item-nav[data-nav=next] {
    border-top: 1px solid var(--color-border);
}

/* For responsive view, we can adjust columns accordingly */
@media (max-width: 1199px) {
    .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
        grid-template-columns: repeat(2, 1fr);
    }

    .single-lp_course .lp-archive-courses .lp-entry-content .entry-content-left {
        width: 100%!important;
        
    }

    .single-lp_course .lp-archive-courses .course-sidebar-top {
        width: 100%!important;
    }

    .rbt-schedule-author-box-single {
        flex: 0 0 100%;
    }
}


@media only screen and (max-width: 767px) {
    .rbt-generic-banner-course-filter-banner .rbt-mobile-course-archive-filter .rbt-filter-toggle-btn span {
        width: 0;
        opacity: 0;
        font-size: 0;
        visibility: hidden;
    }

    .rbt-generic-banner-course-filter-banner .rbt-mobile-course-archive-filter .rbt-filter-toggle-btn.rbt-btn i { 
        padding-left: 0;
    }

    .rbt-generic-banner-course-filter-banner .rbt-mobile-course-archive-filter .rbt-filter-toggle-btn.rbt-btn {
        text-align: left;
    }

    .lp-main-content .learn-press-courses[data-layout=grid] .course .rt-course-default, .lp-main-content .learn-press-courses[data-layout=list] .course .rt-course-default {
        padding: 10px;
    }

    .lp-main-content .learn-press-courses[data-layout=grid] .course,
    .lp-main-content .learn-press-courses[data-layout=list] .course {
        padding-right: 5px;
        padding-left: 5px;
    }

    .rbt-card.card-list-2 .rbt-card-body {
        padding-top: 12px!important;
    }
    .lp-main-content ul.learn-press-courses[data-layout="grid"] > li,
    .lp-main-content ul.learn-press-courses[data-layout="list"] > li {
        margin-bottom: 10px;
    }

    .post-type-archive-lp_course .page-banner-layout-style-two-learnpress {
        padding-bottom: 170px;
        padding-top: 65px;
    }
    
}

@media (max-width: 480px) {
    .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
        grid-template-columns: 1fr; 
    }

    .lp-main-content .learn-press-courses[data-layout=grid] .course .rbt-card .rbt-card-top,
    .lp-main-content .learn-press-courses[data-layout=list] .course .rbt-card .rbt-card-top {
        flex-wrap: wrap;
    }
}