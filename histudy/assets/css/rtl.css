body {
  direction: rtl;
  unicode-bidi: embed;
}

.rbt-feature .icon {
  margin-left: 15px;
  margin-right: 0;
}

div#scene {
  right: -41px;
}

.rbt-information-list li a i {
  margin-right: 0;
  margin-left: 10px;
}

.rbt-header-top-news .inner .content .news-text img {
  margin-left: 5px;
}

ul.rbt-dropdown-menu li a .left-image {
  margin-right: 0;
  margin-left: 5px;
}

.rbt-admin-profile .admin-thumbnail {
  margin-right: 0;
  margin-left: 12px;
}

.rbt-user-menu-list-wrapper .user-list-wrapper li:not(.tutor-dashboard-menu-divider) a i {
  margin-right: 0;
  margin-left: 7px;
}

.rbt-header .mainmenu-nav .mainmenu li.with-megamenu .rbt-megamenu .wrapper .mega-menu-item li a .rbt-badge-card {
  margin-left: 0;
  margin-right: 10px;
}

a.rbt-btn-link i,
button.rbt-btn-link i,
span.rbt-btn-link i {
  transform: rotate(180deg);
  margin-left: 0;
}

a.rbt-btn-link::after,
button.rbt-btn-link::after,
span.rbt-btn-link::after {
  left: 0;
  right: auto;
}

a.rbt-btn-link:hover::after,
button.rbt-btn-link:hover::after,
span.rbt-btn-link:hover::after {
  left: auto;
  right: 0;
}

.rbt-review .rating {
  margin-right: 0;
  margin-left: 6px;
}

.rbt-author-meta .rbt-avater {
  margin-right: 0;
  margin-left: 10px;
}

.rbt-feature-wrapper.mt--20.ml_dec_20 {
  margin-left: 0;
  margin-right: -20px;
}

.rbt-testimonial-box.style-2 .clint-info-wrapper .client-info {
  padding-left: 0;
  padding-right: 15px;
}

.rbt-meta li i {
  margin-left: 5px;
  margin-right: 0px;
}

.rbt-team-tab-content .rbt-team-details {
  padding-left: 0;
  padding-right: 30px;
}

.top-circle-shape {
  right: auto;
  left: -96px;
}

a.transparent-button i,
button.transparent-button i,
span.transparent-button i {
  rotate: 180deg;
}

.single-counter + .single-counter::before {
  left: 100%;
}

.footer-widget .ft-link li a:after {
  transform-origin: bottom left;
}

.footer-widget .ft-link li a:hover:after {
  transform-origin: bottom right;
}

.rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg);
}

.rbt-btn:hover.hover-icon-reverse .btn-text {
  transform: translateX(-23px);
}

.rbt-btn:hover.hover-icon-reverse:hover .btn-icon {
  transform: rotate(180deg);
}

.rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg);
}

.rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-10px);
}

.rbt-price .off-price {
  margin-left: 0;
  margin-right: 7px;
}

.rbt-header-1 .header-right .quick-access {
  margin-right: 0;
  margin-left: 18px;
}

.rbt-category-btn {
  gap: 8px;
}

.rbt-banner-1 .content .inner .description {
  padding-right: 0;
  padding-left: 24px;
}

.rbt-bookmark-btn .tutor-course-wishlist-btn {
  margin-left: 0;
}

.about-style-1 .thumbnail-wrapper .thumbnail.image-1 {
  left: 100px;
}

.about-style-1 .thumbnail-wrapper .thumbnail.image-2 {
  right: auto;
  left: 0;
}

.about-style-1 .thumbnail-wrapper .thumbnail.image-3 {
  left: 0;
}

.rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper {
  left: auto;
  right: 100%;
}

.rbt-testimonial-box .inner::before {
  right: auto;
  left: 40px;
}

.rbt-testimonial-box.style-2 .inner::before {
  transform: rotateY(180deg);
}

.rbt-course-area .rbt-card .rbt-meta {
  display: flex;
}
.rbt-course-area .rbt-card .rbt-meta li {
  display: flex;
}

.rbt-card.event-grid-card .rbt-meta {
  display: flex;
}
.rbt-card.event-grid-card .rbt-meta li {
  display: flex;
}

.rbt-team-tab-thumb li a.active .thumb::before {
  transform: rotateY(180deg) translateY(-50%);
}

.rbt-card .rbt-card-body .rbt-meta {
  display: flex;
}
.rbt-card .rbt-card-body .rbt-meta li {
  display: flex;
}

a.course_details {
  display: flex;
  align-items: center;
  gap: 4px;
}
a.course_details i {
  rotate: 180deg;
}

.nav-category-item a i {
  rotate: 180deg;
}

.nav-category-item {
  overflow: hidden;
  border-radius: var(--radius);
}

.rbt-link-hover a::after {
  transform-origin: bottom left;
}

.rbt-link-hover a:hover::after {
  transform-origin: bottom right;
}

ul.quick-access .access-icon.shopping-cart a.rbt-cart-sidenav-activation.rbt-cart-sidenav-activation.rbt-round-btn i {
  margin-right: 0;
}

a.rbt-cart-sidenav-activation.rbt-cart-sidenav-activation:not(.rbt-round-btn) {
  display: flex;
  gap: 10px;
  align-items: center;
}

.rbt-mini-cart .rbt-cart-count {
  left: -6px;
}

.rbt-card .rbt-card-body .rbt-meta {
  flex-wrap: wrap;
  gap: 4px;
}

.rbt-card.elegant-course .rbt-card-body .rbt-card-bottom .rbt-btn-link {
  margin-left: 0;
}

.rbt-testimonial-box .clint-info-wrapper .client-info {
  padding-left: 0;
  padding-right: 25px;
}

.rbt-testimonial-box .inner::before {
  transform: rotateY(180deg);
}

.rbt-btn.icon-hover .btn-text {
  transform: translateX(-7px);
}

.rbt-btn:hover.icon-hover .btn-text {
  transform: translateX(7px);
}

.rbt-btn.icon-hover .btn-icon {
  transform: rotate(180deg);
}

.rbt-user-wrapper .rbt-user-menu-list-wrapper {
  right: auto;
  left: 0;
}

.rbt-header-campaign .icon-close.position-right {
  right: auto;
  left: 30px;
}

.rbt-header-top-news .inner .right-button {
  margin-left: 0;
  margin-right: 30px;
}

.rbt-header-top-news .inner .content .news-text {
  margin-left: 0;
  margin-right: 7px;
}

.meta-list li i {
  margin-right: 0;
  margin-left: 6px;
}

/* .rbt-footer .rbt-link-hover a {
    display: inline-block;
    width: fit-content;
} */
input#course_search {
  padding-right: 20px;
  padding-left: 65px;
}

button.rbt-round-btn.serach-btn {
  right: auto;
  left: 0;
}

section.no-results.not-found.rainbow-search-no-result-found .histudy-search.form-group button.search-button {
  right: auto;
  left: 0;
}

.page-list li .icon-right i {
  transform: rotate(180deg);
}

ul.rbt-dropdown-menu li .sub-menu {
  right: auto;
  left: 0;
}

ul.rbt-dropdown-menu li .sub-menu.hover-reverse {
  left: 0;
  right: auto;
}

.rbt-badge-2 .image {
  margin-right: 0;
  margin-left: 10px;
}

.rbt-badge-2 strong {
  margin-left: 0;
  margin-right: 5px;
}

.rbt-list-style-2 li i {
  margin-right: 0;
  margin-left: 10px;
}

.rbt-list-style-3 li i {
  padding-right: 0px;
  padding-left: 10px;
}

.rbt-flipbox .rbt-flipbox-wrap .rbt-flipbox-back {
  text-align: right;
  align-items: start;
}

.inner.pl--50.pl_sm--0.pl_md--0 {
  padding-left: 0 !important;
  padding-right: 50px;
}

.pricing-billing-duration {
  text-align: left;
}

.team-style-default .inner .content .team-form,
.rbt-default-card .inner .content .team-form {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.rbt-testimonial-content .inner::before {
  left: auto;
  right: -64px;
  transform: rotateY(180deg);
}

.advance-tab-button-1 .tab-button-list .tab-button.active::after {
  transform: scale(1) rotate(180deg);
}

.advance-tab-button-1 .tab-button-list .tab-button::after {
  left: -4%;
  transform: scale(1) rotate(180deg);
}

.advance-tab-button-1.right-align .tab-button-list .tab-button.active::after {
  transform: scale(1) rotate(0);
}

.advance-tab-button-1.right-align .tab-button-list .tab-button::after {
  transform: scale(1) rotate(0);
  left: 100%;
  right: auto;
}

.rbt-card.card-list-2 .rbt-card-body {
  padding-left: 0;
  padding-right: 30px;
}

.rbt-accordion-style .card .card-header button::before {
  right: auto;
  left: 0;
}

.rbt-accordion-style .card .card-header {
  padding-right: 30px;
}

.table-responsive.mobile-table-750 .rbt-table tr td,
.table-responsive .rbt-table tr th {
  text-align: right;
}

@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(1750px);
  }
}
@keyframes scroll {
  0% {
    transform: translateX(1750px);
  }
  100% {
    transform: translateX(1750px);
  }
}
.rbt-header-top-2 .social-icon-wrapper::before {
  right: -20px;
  left: auto;
}

.rbt-header-top-2 .address-content p + p::before {
  left: auto;
  right: -1px;
}

.rbt-mini-cart .rbt-cart-count {
  max-width: 16px;
}

.rbt-header-top-2 .social-icon-wrapper {
  margin-left: 0;
  margin-right: 10px;
}

.rbt-header-top-2 .address-content {
  margin: 0 8px;
}

.rbt-header-top-2 .address-content p a:hover {
  color: var(--color-white);
}

.rbt-header-top-2 .address-content p i {
  margin-right: 0;
  margin-left: 8px;
}

.rbt-card.variation-03 .card-information .card-count {
  padding-left: 0;
  padding-right: 10px;
}

.rbt-cat-box-1.variation-5 .inner .content .read-more-btn {
  transform: rotate(180deg);
}

.pricing-table .pricing-body .list-item li i {
  margin-right: 0;
  margin-left: 10px;
}

.rbt-cta-default.style-4 .content-wrapper .thumbnail {
  left: auto;
  right: 0;
}

.rbt-header-top-2.color-white a {
  position: relative;
}

.rbt-header-top-2 p a i {
  position: absolute;
  left: -15px;
  transform: rotate(180deg);
}

header.rbt-header.rbt-header-10 .rbt-header-wrapper .rbt-btn {
  margin-left: 0;
}

.profile-share .more-author-text {
  margin-left: 0;
  margin-right: 30px;
}

.modal .modal-dialog .modal-content .modal-body .rbt-team-details p {
  padding-right: 0;
  padding-left: 16%;
}

.modal .modal-dialog .modal-content .modal-header .rbt-round-btn {
  right: auto;
  left: 20px;
}

.rbt-cta-5 .title {
  padding-right: 0 !important;
}

a.rbt-moderbt-btn::before {
  left: auto;
  right: 0;
}

a.rbt-moderbt-btn i {
  margin-left: 0;
  transform: translateX(0px) rotate(180deg);
  margin-right: 10px;
}

a.rbt-moderbt-btn:hover i {
  transform: translateX(-5px) rotate(180deg);
}

a.rbt-moderbt-btn:hover::before {
  width: 97%;
}

.rbt-counterup .inner .content .counter::after,
.rbt-counterup .inner .content .counters::after {
  right: auto;
}

.rbt-splite-style .thumbnail.image-left-content img {
  border-radius: 10px 0 0 10px;
}

.service-card-6 .inner .number-text {
  right: auto;
  left: 15px;
}

.rbt-accordion-style.rbt-accordion-01.rbt-accordion-06.accordion {
  padding-left: 15px;
}

img.attachment-large.size-large.wp-image-7541 {
  padding-right: 15px;
}

.modern-course-features-box .inner .content .title {
  padding-right: 0%;
  padding-left: 14%;
}

.modern-course-features-box .inner .content {
  padding-left: 0;
  margin-right: 30px;
}

.course-feature-list li .icon {
  margin-right: 0;
  margin-left: 12px;
}

.rbt-featured-course-single-1 .tutor-course-content-list-item div:first-child {
  padding-right: 0;
}

.rbt-accordion-style.rbt-accordion-02 .card .card-header {
  padding-right: 0;
}

.rbt-course-main-content li .course-content-right > a {
  margin-right: 0;
}

.top-circle-shape.position-bottom-right {
  right: auto;
}

.contact-address {
  display: flex;
  flex-direction: column;
}

.contact-address li {
  display: flex;
  align-items: center;
}

.contact-address li i {
  margin-right: 0;
  margin-left: 10px;
}

.advance-pricing .pricing-right .plan-offer-list li i {
  margin-right: 0;
  margin-left: 10px;
}

.section-title.pl--100.pl_sm--30 {
  padding-left: 0 !important;
  padding-right: 100px;
}

.popup-mobile-menu .mainmenu li.with-megamenu a::after {
  right: auto;
  left: 0;
}

.demo-single .inner .content .title a {
  text-align: start;
}

.popup-mobile-menu .inner-wrapper .description {
  padding-right: 0;
  padding-left: 18%;
}

.popup-mobile-menu .mainmenu li.has-dropdown > a::after {
  right: auto;
  left: 0;
}

.rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 img {
  margin-right: 0;
  margin-left: 7px;
}

.rbt-list-style-1 li i {
  margin-right: 0;
  margin-left: 10px;
  padding-right: 0;
}

.rbt-banner-1.variation-2 .content .inner .title {
  padding-right: 0;
  padding-left: 12%;
}

.rbt-banner-1.variation-2 .content .inner .description {
  padding-right: 0;
  padding-left: 17%;
}

.modern-course-features-box.grid-content-reverse .inner .content {
  margin-right: 0;
}

.rbt-course-block .rbt-review .rating-count {
  margin-left: 0;
  margin-right: 6px;
}

ul.rbt-meta {
  display: flex;
}
ul.rbt-meta li {
  display: flex;
}

@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  ul.rbt-dropdown-menu li .sub-menu {
    left: auto;
    right: 0;
  }
  .rbt-header-1 .header-right .quick-access {
    padding-right: 0;
  }
  .mainbar-row .rbt-main-navigation {
    margin: 0 20px;
  }
  .about-style-1 .thumbnail-wrapper .thumbnail.image-1 {
    left: 0;
  }
  .section-title.pl--100.pl_sm--30 {
    padding-right: 35px;
  }
  .rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
    margin-left: 0;
    margin-right: 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  ul.rbt-dropdown-menu li .sub-menu {
    left: auto;
    right: 0;
  }
  .rbt-banner-1 .content .banner-card {
    right: auto;
    left: 7%;
    bottom: 10%;
  }
  .about-style-1 .thumbnail-wrapper .thumbnail.image-1 {
    left: 0;
  }
  .popup-mobile-menu .inner-wrapper {
    left: auto;
    right: -150px;
  }
  .popup-mobile-menu.active .inner-wrapper {
    left: auto;
    right: 0;
  }
  .rbt-testimonial-box .clint-info-wrapper .client-info {
    padding-right: 10px;
  }
  .rbt-testimonial-content .inner::before {
    right: 0;
  }
  .inner.pl--50.pl_sm--0.pl_md--0 {
    padding-right: 10px;
  }
  .rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
    margin-left: 0;
    margin-right: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  ul.rbt-dropdown-menu li .sub-menu {
    left: auto;
    right: 0;
  }
  .rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
    padding-left: 20px;
  }
  .rbt-header-top .top-bar-expended {
    right: auto;
    left: 25px;
  }
  .popup-mobile-menu .inner-wrapper {
    left: auto;
    right: -150px;
  }
  .popup-mobile-menu.active .inner-wrapper {
    left: auto;
    right: 0;
  }
  .rbt-banner-1 .content .banner-card {
    right: auto;
    left: 60px;
    bottom: 10%;
  }
  div#scene {
    right: -67px;
  }
  .about-style-1 .thumbnail-wrapper .thumbnail.image-1 {
    left: 0;
  }
  .about-style-1 .thumbnail-wrapper .thumbnail.image-3 {
    left: auto;
    right: 75px;
  }
  .inner.pl--50.pl_sm--0.pl_md--0 {
    padding-right: 0;
  }
  .rbt-testimonial-box .clint-info-wrapper .client-info {
    padding-right: 10px;
  }
  .rbt-testimonial-content .inner::before {
    right: auto;
    left: 50%;
    transform: translateX(-50%) rotateY(180deg);
  }
  .rbt-card.card-list-2 .rbt-card-body {
    padding-right: 0;
  }
  .brand-style-2 li {
    display: flex;
  }
  .rbt-accordion-style .card .card-header button {
    text-align: right;
  }
  .modern-course-features-box .inner .content {
    margin-right: 0;
  }
  .rbt-accordion-style.rbt-accordion-02 .card .card-header button::before {
    right: auto;
  }
  .rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
    margin-left: 0;
    margin-right: 20px;
  }
}
@media only screen and (max-width: 767px) {
  ul.rbt-dropdown-menu li .sub-menu {
    left: auto;
    right: 0;
  }
  .profile-share .more-author-text {
    margin-right: 0;
  }
  .section-title.pl--100.pl_sm--30 {
    padding-right: 30px;
  }
  .hamberger {
    margin-left: 0;
  }
  .popup-mobile-menu .inner-wrapper {
    left: auto;
    right: -150px;
  }
  .popup-mobile-menu.active .inner-wrapper {
    left: auto;
    right: 0;
  }
  .rbt-card.card-list-2 .rbt-card-body {
    padding-right: 0;
  }
  .about-style-1 .thumbnail-wrapper .thumbnail.image-1 {
    left: 0;
  }
  .inner.pl--50.pl_sm--0.pl_md--0 {
    padding-right: 0;
  }
  .rbt-team-tab-content .rbt-team-details {
    padding-right: 0;
  }
  .rbt-header-1 .header-right .quick-access {
    margin-left: 0;
  }
  .rbt-header-top-news .inner .right-button {
    margin-right: 0;
  }
  .rbt-header-top-news .inner {
    padding-right: 0;
  }
  .rbt-header-campaign .icon-close.position-right {
    left: 10px;
  }
  .rbt-testimonial-box .clint-info-wrapper .client-info {
    padding-right: 0;
  }
  .rbt-header-top .top-bar-expended {
    right: auto;
    left: 25px;
  }
  .rbt-list-style-2 li {
    text-align: start;
  }
  .rbt-testimonial-content .inner::before {
    right: auto;
    left: 50%;
    transform: translateX(-50%) rotateY(180deg);
  }
  .brand-style-2 li {
    display: flex;
  }
  .rbt-accordion-style .card .card-header button {
    text-align: right;
  }
  .rbt-accordion-style .card .card-header button::before {
    right: auto;
    left: 0;
  }
  .modern-course-features-box .inner .content {
    margin-right: 0;
  }
  .rbt-accordion-style.rbt-accordion-02 .card .card-header button::before {
    right: auto;
  }
}
ul.rbt-dropdown-menu li .sub-menu {
  left: auto;
  right: 0;
}

.col-lg-10.offset-lg-1.mt_dec--50 {
  margin-top: 0 !important;
}

.rbt-minicart-wrapper .product-content {
  padding-left: 0;
  padding-right: 20px;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
  margin-left: 0;
  margin-right: 15px;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item ul.course-switch-layout {
  margin-right: 0;
  margin-left: 15px;
}

.rbt-single-widget input[type=checkbox] ~ label::before,
.rbt-single-widget input[type=radio] ~ label::before {
  left: auto;
  right: 0;
}

.rbt-single-widget input[type=checkbox] ~ label::before,
.rbt-single-widget input[type=radio] ~ label::after {
  left: auto;
  right: 3px;
}

.rbt-single-widget input[type=checkbox] ~ label,
.rbt-single-widget input[type=radio] ~ label {
  padding-left: 0;
  padding-right: 23px;
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  text-align: start;
}

button.rbt-filter-rating-toggle {
  text-align: start;
}

.rbt-modern-select .bootstrap-select button.btn-light {
  padding-right: 20px;
}

.rbt-search-style .rbt-search-btn {
  right: auto;
  left: 5px;
}

.rbt-search-style input {
  padding-right: 20px;
  padding-left: 60px;
}

.rbt-page-banner-wrapper .rbt-banner-content-top .title-wrapper .rbt-badge-2 {
  margin-left: 0;
  margin-right: 20px;
}

.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  right: auto;
  left: 15px;
}

.bootstrap-select.show-tick .dropdown-menu li a span.text {
  margin-right: 0;
  margin-left: 34px;
}

.rbt-btn i {
  padding-left: 0;
  padding-right: 6px;
}

.about-author .thumbnail img {
  margin-right: 0;
  margin-left: 30px;
}

i.feather-phone.mr--5 {
  margin-right: 0 !important;
  margin-left: 5px !important;
}

.rbt-btn .btn-icon {
  padding-right: 0;
  margin-right: 6px;
}

.meta-list li .author-thumbnail {
  margin-right: 8px;
  margin-left: 8px;
}

ul.blog-meta li {
  display: flex;
  align-items: center;
}
ul.blog-meta li i {
  margin-right: 0;
  margin-left: 5px;
}

blockquote.rbt-blockquote.mt--0.alignwide.square.rbt-border-none {
  padding: 40px 100px 40px 40px;
}

.rainbow-post-content-wrapper blockquote {
  padding: 40px 100px 40px 40px;
}

blockquote::before {
  left: auto;
  right: 30px;
}

input[type=checkbox] ~ label::before,
input[type=radio] ~ label::before {
  left: auto;
  right: 0;
}

input[type=checkbox] ~ label,
input[type=radio] ~ label {
  padding-left: 0;
  padding-right: 20px;
}

input[type=checkbox] ~ label::after,
input[type=radio] ~ label::after {
  left: auto;
  right: 2px;
}

.histudy-post-wrapper .rbt-card ul.blog-meta li i {
  margin-right: 0;
  margin-left: 6px;
}

.rbt-single-widget .recent-post-list li .thumbnail {
  margin-right: 0;
  margin-left: 12px;
}

.footer-layout-4 .histudy-search .search-button,
.rbt-sidebar-widget-wrapper .histudy-search .search-button {
  left: 0;
  right: auto;
}

.post-like.pt-like-it.rainbow-blog-details-like .like-button i {
  margin-right: 0;
  margin-left: 12px;
}

.rbt-image-gallery-1 .image-2 {
  right: auto;
  left: 10%;
}

.rbt-image-gallery-1.text-end .image-2 {
  left: auto;
  right: 10%;
}

.progress .progress-number {
  right: auto;
  left: 0;
}

.service-card-5 .inner .icon {
  margin-right: 0;
  margin-left: 20px;
}

.newsletter-form-1 input {
  text-align: end;
  padding-right: 20px;
  padding-left: 180px;
}

.newsletter-form-1 .rbt-btn {
  right: auto;
  left: 10px;
}

.plan-offer-list li i {
  margin-right: 10px;
  margin-left: 10px;
}

.rbt-cta-default.style-2 .shape-text-image img {
  right: 73%;
}

.rbt-btn.rounded-player span i {
  padding-right: 0;
  padding-left: 6px;
}

input[type=checkbox] ~ label::after,
input[type=radio] ~ label::after {
  right: 5px;
}

.plan-offer-list-wrapper .plan-offer-list + .plan-offer-list {
  margin-left: 0;
  margin-right: 50px;
}

.plan-offer-list li i {
  margin-right: 0;
}

.addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
  padding-left: 0 !important;
  padding-right: 50px;
}

.tutor-user-public-profile .photo-area .pp-area {
  padding-right: 40px;
}

.tutor-user-public-profile.tutor-user-public-profile-pp-rectangle .profile-name {
  padding-left: 0;
  padding-right: 23px;
}

.form-group input {
  text-align: right;
}

.form-group label {
  left: auto;
  width: fit-content;
  white-space: nowrap;
  right: 0;
}

.rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg) translateX(124px);
}
.rbt-contact-form.contact-form-style-1 .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-78px);
}
.rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(0);
}
.rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-102px);
}

.rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(0px);
}

.rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn.hover-icon-reverse .btn-text {
  margin-inline-end: -23px;
  margin-inline-start: 0;
}

.rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg) translateX(100px);
}

.rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-98px);
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: none;
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.rbt-single-product .pro-qty {
  margin-right: 0;
  margin-left: 20px;
}

.rbt-single-product .pro-qty .qtybtn.inc {
  padding-right: 0;
  padding-left: 7px;
}

.rbt-single-product .pro-qty .qtybtn.dec {
  padding-left: 0;
  padding-right: 7px;
}

.comment-list .comment .single-comment .comment-img {
  margin-right: 0;
  margin-left: 20px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
    padding-right: 0;
  }
  .rbt-mini-cart .rbt-cart-count {
    left: 73%;
    font-weight: 500;
    right: auto;
  }
}
@media only screen and (max-width: 767px) {
  .blog-content-wrapper blockquote.rbt-blockquote.mt--0.alignwide.square.rbt-border-none {
    padding: 24px;
  }
  blockquote::before {
    right: 84px;
  }
  .rainbow-post-content-wrapper blockquote {
    padding: 20px 40px 20px 20px;
  }
  .rainbow-post-content-wrapper blockquote::before {
    right: 10px;
  }
  .addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
    padding-right: 0;
  }
}
@media only screen and (max-width: 575px) {
  .rbt-page-banner-wrapper .rbt-banner-content-top .title-wrapper .rbt-badge-2 {
    margin-right: 0;
  }
  .rbt-blog-details-area .about-author .thumbnail img.avatar {
    margin-bottom: 24px;
  }
  .rbt-accordion-style .card .card-header {
    padding-right: 20px;
  }
  .rbt-accordion-style .card .card-header span {
    display: block;
    padding-left: 20px;
  }
  .rbt-video-area .inner.pr--50 {
    padding-right: 0px !important;
  }
  .rbt-testimonial-area .section-title.pl--100.pl_sm--30 {
    padding-right: 0px;
  }
  .rbt-btn .btn-icon {
    margin-right: 0;
  }
}
.rbt-accordion-style .card .card-header button {
  padding-left: 30px;
}

.rbt-header-top-1 .rbt-header-top-news .inner .content .news-text img {
  margin-right: 0px;
}

.modern-course-features-box.one-colume-grid.h-100 .inner .content {
  margin-right: 0;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 li {
  display: flex !important;
}

.advance-pricing .pricing-right .plan-offer-list li.no {
  margin-right: 0;
}

.advance-pricing .pricing-right .plan-offer-list li.off {
  margin-right: 0;
}

.side-menu .inner-wrapper .description {
  padding-right: 0;
  padding-left: 18%;
}

.team ul.social-icon {
  left: auto;
  right: 35px;
}

.rbt-header-sec .search-field input {
  padding-right: 20px;
}

.rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
  padding-left: 20px;
  padding-right: 10px;
}

.rbt-contact-form.contact-form-style-1 .form-submit-group {
  display: flex;
  justify-content: end;
}

aside.rbt-sidebar-widget-wrapper .rbt-single-widget .recent-post-list li .content .rbt-meta li i {
  margin-right: 0;
}

.rbt-minicart-wrapper .product-content {
  padding-left: 0;
  padding-right: 20px;
}

.is-large.wc-block-cart .wc-block-cart__totals-title {
  text-align: right !important;
  padding-right: 16px !important;
}

.rbt-breadcrumb-default.rbt-breadcrumb-style-3 .content {
  padding-right: 0;
  padding-left: 10%;
}

/*# sourceMappingURL=rtl.css.map */

.post-type-archive-courses .bootstrap-select>.dropdown-toggle:after {
  margin-right: -5px;
}

.rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list img {
    margin-right: 0 !important;
    margin-left: 10px;
}

.cd-headline.clip .cd-words-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background-color: #aebcb9;
}

.cd-headline.clip .cd-words-wrapper::after {
  display: none;
}

.rbt-pagination li .prev.page-numbers,
.rbt-pagination li .next.page-numbers {
  transform: rotate(180deg);
}

.comment-list .single-comment .comment-author {

}

.comment-list .comment .single-comment .comment-img img,
.comment-list .comment .reply-edit a.comment-edit-link, 
.comment-list .pingback .reply-edit a.comment-edit-link, 
.comment-list .trackback .reply-edit a.comment-edit-link {
  margin-right: 0;
}

.comment-list .comment .comment-img h6, 
.comment-list .pingback .comment-img h6, 
.comment-list .trackback .comment-img h6 {
  padding-right: 15px;
}

.comment-list .comment .reply-edit .reply a.comment-reply-link, 
.comment-list .pingback .reply-edit .reply a.comment-reply-link, 
.comment-list .trackback .reply-edit .reply a.comment-reply-link {
  padding-right: 7px;
}

.tutor-row.tutor-frontend-dashboard-maincontent table.rbt-table.table.table-borderless th,
.tutor-row.tutor-frontend-dashboard-maincontent .content.tutor-dashboard-content table.rbt-table.table.table-borderless td {
  text-align: right;
}

.rbt-tutor-information .rbt-tutor-information-left .thumbnail {
  margin-right: 0;
}

.rbt-tutor-information .rbt-tutor-information-left .tutor-content {
  padding-right: 20px;
}

.rbt-user-menu-list-wrapper .user-list-wrapper li:not(.tutor-dashboard-menu-divider),
.tutor-table tr th:not(.tutor-text-left):not(.tutor-text-center):not(.tutor-text-right),
.tutor-table tr td:not(.tutor-text-left):not(.tutor-text-center):not(.tutor-text-right),
.tutor-dashboard-content-inner .tutor-password-field .field-label,
.tutor-dashboard-setting-profile.tutor-dashboard-content-inner input[type="tel"],
.tutor-dashboard-setting-profile .tutor-form-control.tutor-form-select.tutor-js-form-select .tutor-form-select-label,
.tutor-dashboard-setting-social.tutor-dashboard-content-inner .tutor-social-field .tutor-form-control {
  text-align: right;
}

#tutor-withdraw-account-set-form .tutor-radio-select .tutor-radio-select-content {
  padding-right: 10px;
}

.tutor-form-control span.tutor-form-select-label,
.tutor-form-select.is-active .tutor-form-select-dropdown {
  text-align: right;
}

.tutor-course-price-wrapper input[type=checkbox] ~ label::after, 
.tutor-course-price-wrapper input[type=radio] ~ label::after,
#settings-tab-contentdrip input[type=radio] ~ label::after {
  right: 3px;
}

#settings-tab-contentdrip input[type=checkbox] ~ label::after {
  right: 2px;
}

.tutor-course-price-wrapper .tutor-course-price-toggle .tutor-form-check {
  margin-right: 10px;
}

.tutor-pagination ul.tutor-pagination-numbers .next.page-numbers,
.tutor-pagination ul.tutor-pagination-numbers .prev.page-numbers {
    transform: rotate(180deg);
}

.page-student-registration .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg) translateX(30px);
}
.page-student-registration .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-20px);
}

.page-student-registration .rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(18px);
}

.rbt-header .mainmenu-nav .mainmenu li.has-dropdown .submenu,
.wpcf7 form.invalid .wpcf7-response-output, 
.wpcf7 form.unaccepted .wpcf7-response-output,
.wpcf7 form.sent .wpcf7-response-output,
.rbt-contact-form.contact-form-style-1 textarea {
  text-align: right;
}

.rainbow-dynamic-form .form-submit-group p,
.rbt-contact-form.contact-form-style-1 .form-submit-group p {
   width: 100%;
}

.copyright-style-1 .copyright-link li + li::after {
  right: -1px !important;
}

.rbt-card.variation-01 .rbt-card-bottom .add_to_cart_button .tutor-icon-cart-line {
  margin-left: 0;
  margin-right: 8px;
}

.rbt-banner-area.rbt-banner-3 .rbt-btn.hover-icon-reverse .btn-text {
  margin-inline-start: 22px;
}

.rbt-banner-area.rbt-banner-3 .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg) translateX(124px)!important;
}

.rbt-banner-area.rbt-banner-3 .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-118px)!important;
}

.rbt-banner-area.rbt-banner-3 .rbt-contact-form.contact-form-style-1 .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-118px)!important;
}

.rbt-megamenu .feather-arrow-right:before {
  content: "\e910"!important;
}

.my_switcher {
  position: fixed;
  right: 30px;
  left: unset;
  transform: rotate(90deg);
  z-index: 99;
  bottom: 30px;
}

.rbt-countdown-area .rbt-btn.hover-icon-reverse .btn-text {
  margin-inline-start: 20px;
}

.rbt-countdown-area .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
  transform: rotate(180deg) translateX(124px)!important;
}

.rbt-countdown-area .rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-118px)!important;
}

.rbt-countdown-area .rbt-contact-form.contact-form-style-1 .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon {
  transform: rotate(180deg) translateX(-118px)!important;
}

.rbt-contact-me.bg-color-white.rbt-section-gap .rbt-contact-form.contact-form-style-1 .form-submit-group  {
  display: block;
  text-align: right;
}

.rbt-contact-me.bg-color-white.rbt-section-gap .form-submit-group button.rbt-btn {
  display: inline-block;
  text-align: right;

}

a.transparent-button i, 
a.transparent-button i svg,
button.transparent-button i,
 span.transparent-button i {
  rotate: 180deg;
  padding-left: 4px;
}

button.rbt-filter-rating-toggle {
  padding-right: 20px;
}

.quick-access > li > a i {
  margin-right: 0;
  margin-left: 10px;
}

.quick-access > li > a.search-trigger-active i,
.quick-access > li > a.rbt-cart-sidenav-activation i,
.quick-access > li.rbt-user-wrapper a i {
  margin-left: 0;
}
 

.rbt-information-list .d-xxl-block {
  margin-right: 5px;
}

a.transparent-button i svg {
  padding-left: unset!important;
  margin-top: -3px;
}

.elementor-widget-rainbow-program-block a.transparent-button i, 
.elementor-widget-rainbow-program-block a.transparent-button i svg, 
.elementor-widget-rainbow-program-block button.transparent-button i, 
.elementor-widget-rainbow-program-block span.transparent-button i,

.elementor-widget-rainbow-weekly-online-classes a.transparent-button i, 
.elementor-widget-rainbow-weekly-online-classes a.transparent-button i svg, 
.elementor-widget-rainbow-weekly-online-classes button.transparent-button i, 
.elementor-widget-rainbow-weekly-online-classes span.transparent-button i {
  rotate: 90deg;
}


.rbt-header .mainmenu-nav .mainmenu li.has-dropdown .submenu,
.rbt-user-wrapper .rbt-user-menu-list-wrapper,
.rbt-category-update .update-category-dropdown,
ul.rbt-dropdown-menu li .sub-menu.hover-reverse {
  left: auto;
  right: 0;
}

.rbt-megamenu .elementor-widget-container p {
  text-align: right;
}

.widget_mc4wp_form_widget .newsletter-form-1 .rbt-btn {
  right: auto;
  left: 7px;
}

.rbt-header .mainmenu-nav .mainmenu li.menu-item:last-child a {
  padding-right: 15px;
  padding-left: 0;
}

.rbt-header .mainmenu-nav .mainmenu li.menu-item:last-child  .rbt-megamenu a {
  padding-right: 0;
}

.rbt-header .mainmenu-nav .mainmenu li.menu-item:first-child a {
  padding-right: 0;
  padding-left: 15px;
}

.rbt-header .mainmenu-nav .mainmenu li.menu-item:first-child .rbt-megamenu a  {
  padding-left: 0;
}

.active-dark-mode .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper {
  border-right: 1px solid var(--dark-color-border);
}

.rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper {
  border-left: unset!important;
}

.rbt-rbt-blog-area .rbt-card-body .transparent-button i,
.rbt-rbt-blog-area .rbt-card-body .transparent-button i svg {
  transform: rotate(270deg);
  padding-left: 3px;
  margin-right: 3px;
  transition: all 0.3s ease-in-out;
}

.rbt-rbt-blog-area .rbt-card-body .transparent-button:hover i {
  margin-right: 6px;
}

.rbt-copyright-content-top p a {
  display: inline-block;
}

.rbt-category-update .update-category-dropdown {
  border-radius: 0 0 10px 0;
}

.rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper {
  border-radius: 0 0 0 10px;
}

body.rtl.active-dark-mode .rbt-category-update .update-category-dropdown .inner {
  border-right: 1px solid var(--dark-color-border);
}