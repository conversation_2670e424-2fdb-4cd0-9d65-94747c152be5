(function ($) {
    $(document).on('ready', function () {
        const ajax_url = histudy_ajax_object.ajax_url;
        const nonce = histudy_ajax_object.nonce;
        $('.like-button').on('click', function(e) {
            e.preventDefault();
            const post_id = $(this).data('id');
            var data = {
                method: "POST",
                url: ajax_url,
                data: {
                    action   : 'update_single_post_like_meta',
                    nonce,
                    post_id
                }
            }
            $.ajax(data).success(function(response) {
                const value = response?.data?.value;

                if( value >= 1 ) {
                    var totalLike =  value + " " + 'likes';
                } else {
                    var totalLike =  value + " " + 'likes';
                }

                
                $('.like-count').text(totalLike);
            })
        });
    })
})(jQuery)