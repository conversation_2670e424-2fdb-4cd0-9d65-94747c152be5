(function($) {
    /**
     * Ajax for login
     */
   $('#rbt-user-login-form').on( 'submit', function(e) {
    e.preventDefault();
    const warningBox = $(this).closest('.rbt-contact-form').find('.rbt-form-warning-box');
    const user_login = $(this).find('input[name="user_name_email"]').val();
    const password = $(this).find('input[name="user_pass"]').val();
    var rememberme = $('#rbt_rememberme').is(':checked') ? 'forever' : '';
    $.ajax({
        type: 'post',
        url: ajax_login_object.ajax_url,
        data: {
            action: 'rbt_ajax_user_login',
            nonce: ajax_login_object.nonce,
            user_login,
            password,
            rememberme
        },
        beforeSend: () => {
            $('.form-submit-group .rbt-btn:not(".loading-btn")').addClass('d-none');
            $('.loading-btn').removeClass('d-none');
        },
        success: (response) => {
            if( Bo<PERSON>an(response.success === false) ) {
                warningBox.html('<div class="err">'+response?.data?.messages+'</div>');
            } else {
                window.location.href = response.data.redirect;
            }
        },
        error: (err) => {
            console.log('something err');
            console.warn(err);
        },
        complete: () => {
            $('.form-submit-group .rbt-btn:not(".loading-btn")').removeClass('d-none');
            $('.loading-btn').addClass('d-none');
        }
    });
   } );


   /**
    * Register user
    */
   $('.rbt-user-register form').on( 'submit', function(e) {
        e.preventDefault();
        const warningBox = $(this).closest('.rbt-contact-form').find('.rbt-form-warning-box');
        const firstname = $(this).find('input[name="firstname"]').val();
        const lastname = $(this).find('input[name="lastname"]').val();
        const username = $(this).find('input[name="username"]').val();
        const email = $(this).find('input[name="email"]').val();
        const password = $(this).find('input[name="password"]').val();
        const check_password = $(this).find('input[name="re_password"]').val();
        var accept_account = $(this).find('input[name="rbt_policty"]').is(':checked') ? 'enabled' : '';
        console.log(accept_account);
        if ('enabled' !== accept_account) {
            warningBox.html('<div class="err">Please Accept the Terms and Privacy Policy.</div>');
            return;
        }
        console.log(accept_account);
        $.ajax({
            type: 'post',
            url: ajax_login_object.ajax_url,
            data: {
                action: 'rbt_ajax_user_registration',
                nonce: ajax_login_object.nonce,
                firstname,
                lastname,
                username,
                email,
                password,
                check_password,
                accept_account
            },
            beforeSend: () => {
                $('.form-submit-group .rbt-btn:not(".loading-btn")').addClass('d-none');
                $('.loading-btn').removeClass('d-none');
            },
            success: (response) => {
                if(Boolean(response.success) === false) {
                    console.log(response);
                    warningBox.html('<div class="err">'+response?.data?.messages+'</div>');
                } else {
                    warningBox.html('<div class="success">'+response?.data?.messages+'</div>');
                    setTimeout(() => {
                        window.location.href = response.data.redirect;
                    }, 1000);
                }
            },
            error: (err) => {
                console.warn(err);
            },
            complete: () => {
                $('.form-submit-group .rbt-btn:not(".loading-btn")').removeClass('d-none');
                $('.loading-btn').addClass('d-none');
            }
        });
   } );
   /**
    * Forget password
    */
   $('.user-lost-password').on( 'submit', function(e) {
        e.preventDefault();
        const email = $(this).find('input[name="user_email"]').val();
        console.log(email);
        $.ajax({
            type: 'post',
            url: ajax_login_object.ajax_url,
            data: {
                action: 'rbt_ajax_user_forgot_password',
                nonce: ajax_login_object.nonce,
                email
            },
            beforeSend: () => {
                console.log('sending');
            },
            success: (response) => {
                if(Boolean(response.success) === false) {
                    $('.rbt-form-warning-box').html('<div class="err">'+response?.data?.messages+'</div>');
                } else {
                    $('.rbt-form-warning-box').html('<div class="success">'+response?.data?.messages+'</div>');
                }
            },
            error: (err) => {
                console.log(err);
            }
        });
   } )
})(jQuery);