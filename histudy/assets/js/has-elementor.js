(function() {
	'use strict';

	var $ = jQuery;


	function _typeof(obj) {
		"@babel/helpers - typeof";

		if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
			_typeof = function(obj) {
				return typeof obj;
			};
		} else {
			_typeof = function(obj) {
				return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
			};
		}

		return _typeof(obj);
	}
    var eduJs = {
        i: function (e) {
            eduJs.d();
            eduJs.methods();
        },
        d: function (e) {
            this._window = $(window),
                this._document = $(document),
                this._body = $('body'),
                this._html = $('html')
        },
        methods: function (e) {
            eduJs.pricingPlan();
            eduJs.salActive();
        },
        salActive: function () {
            sal({
                threshold: 0.01,
                once: true,
            });
        },
        pricingPlan: function () {
            var mainPlan = $('.rbt-pricing-area');
            mainPlan.each(function() {
                var yearlySelectBtn = $('.yearly-plan-btn'),
                    monthlySelectBtn = $('.monthly-plan-btn'),
                    monthlyPrice = $('.monthly-pricing'),
                    yearlyPrice = $('.yearly-pricing'),
                    buttonSlide = $('.pricing-checkbox');

                $(monthlySelectBtn).on('click', function() {
                    buttonSlide.prop('checked', true);
                    $(this).addClass('active').parent('.nav-item').siblings().children().removeClass('active');
                    monthlyPrice.css('display', 'block');
                    yearlyPrice.css('display', 'none');
                });
                
                $(yearlySelectBtn).on('click', function() {
                    buttonSlide.prop('checked', false);
                    $(this).addClass('active').parent('.nav-item').siblings().children().removeClass('active');
                    monthlyPrice.css('display', 'none');
                    yearlyPrice.css('display', 'block');
                });
    
                $(buttonSlide).change(function() {
                    if ($('input[class="pricing-checkbox"]:checked').length > 0) {
                        monthlySelectBtn.addClass('active');
                        yearlySelectBtn.removeClass('active');
                        monthlyPrice.css('display', 'block');
                        yearlyPrice.css('display', 'none');
                    }else {
                        yearlySelectBtn.addClass('active');
                        monthlySelectBtn.removeClass('active');
                        monthlyPrice.css('display', 'none');
                        yearlyPrice.css('display', 'block');
                    }
                });
            });
        },

    }
	function _defineProperty(obj, key, value) {
		if (key in obj) {
			Object.defineProperty(obj, key, {
				value: value,
				enumerable: true,
				configurable: true,
				writable: true
			});
		} else {
			obj[key] = value;
		}

		return obj;
	}

	var ThemeHelper = {
		
        
	};
	var Theme = {

        masonryActivation: function name() {
            $('.masonary-wrapper-activation').imagesLoaded(function () {
                // filter items on button click
                $('.messonry-button').on('click', 'button', function () {
                    var filterValue = $(this).attr('data-filter');
                    $(this).siblings('.is-checked').removeClass('is-checked');
                    $(this).addClass('is-checked');
                    $grid.isotope({
                        filter: filterValue
                    });
                });
                // init Isotope
                var $grid = $('.mesonry-list').isotope({
                    percentPosition: true,
                    transitionDuration: '0.7s',
                    layoutMode: 'masonry',
                    masonry: {
                        columnWidth: '.resizer',
                    }
                });
            });
            $('.splash-masonary-wrapper-activation').imagesLoaded(function () {
                // filter items on button click
                $('.messonry-button').on('click', 'button', function () {
                    var filterValue = $(this).attr('data-filter');
                    $(this).siblings('.is-checked').removeClass('is-checked');
                    $(this).addClass('is-checked');
                    $grid.isotope({
                        filter: filterValue
                    });
                });
                // init Isotope
                var $grid = $('.splash-mesonry-list').isotope({
                    percentPosition: true,
                    transitionDuration: '0.7s',
                    layoutMode: 'masonry',
                    masonry: {
                        columnWidth: '.resizer',
                    }
                });
            });
        },
       counterUp: function counterUp() {
            var odo = $('.odometer');
            odo.each(function() {
                $('.odometer').appear(function(e) {
                    var countNumber = $(this).attr('data-count');
                    $(this).html(countNumber);
                });
            });
        },

      eduSwiperActive: function eduSwiperActive() {
        
            var bannerSwiper = new Swiper('.banner-swiper-active', {
                effect: 'cards',
                grabCursor: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
            });

            

            var swiper = new Swiper('.team-slide-activation', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                breakpoints: {
                    575: {
                      slidesPerView: 1,
                    },
                    768: {
                      slidesPerView: 2,
                    },
                    992: {
                      slidesPerView: 3,
                    },
                },
            });

            var swiper = new Swiper('.team-slide-activation-4', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                breakpoints: {
                    575: {
                      slidesPerView: 1,
                    },
                    768: {
                      slidesPerView: 2,
                    },
                    992: {
                      slidesPerView: 3,
                    },
                    
                    1200: {
                      slidesPerView: 4,
                    },
                },
            });

            var swiper = new Swiper('.blog-post-gallery-activation', {
                slidesPerView: 1,
                autoHeight: true,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                }
            });

            var swiper = new Swiper('.team-slide-activation-2', {
                slidesPerView: 3,
                spaceBetween: 0,
                loop: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    320: {
                        slidesPerView: 1,
                    },
                    480: {
                        slidesPerView: 2,
                    },
                    768: {
                        slidesPerView: 2,
                    },
                    992: {
                        slidesPerView: 3,
                    },
                },
            });

            var swiper = new Swiper('.service-item-3-activation', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                breakpoints: {
                    480: {
                      slidesPerView: 1,
                    },
                    481: {
                      slidesPerView: 2,
                    },
                    768: {
                      slidesPerView: 3,
                    },
                    992: {
                      slidesPerView: 3,
                    },
                },
            });

            var swiper = new Swiper('.viral-banner-activation', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
            });

           

            var swiperThumb = new Swiper('.rbtmySwiperThumb', {
                spaceBetween: 10,
                slidesPerView: 2,
                allowTouchMove: false,
            });


            var swiper = new Swiper('.rbt-banner-activation', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                autoHeight: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                thumbs: {
                    swiper: swiperThumb,
                },
            });

            var swiper = new Swiper('.rbt-banner-activation-two', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                autoHeight: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
               
            });

            var swiper = new Swiper('.rbt-gif-banner-area', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
            });

            var swiper = new Swiper('.testimonial-item-3-activation', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    575: {
                      slidesPerView: 1,
                    },

                    768: {
                      slidesPerView: 2,
                    },

                    992: {
                      slidesPerView: 3,
                    },
                },
            });

            var swiper = new Swiper('.testimonial-activation-1', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
            });

            var swiper = new Swiper('.modern-course-carousel-activation', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
            });

            var swiper = new Swiper('.category-activation-one', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                breakpoints: {
                    481: {
                        slidesPerView: 1,
                    },
                    768: {
                        slidesPerView: 2,
                    },
                    992: {
                        slidesPerView: 3,
                    },
                    1200: {
                        slidesPerView: 4,
                    }
                },
            });

            var swiper = new Swiper('.category-activation-stylefive', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                pagination: {
                    el: '.swiper-pagination', // This targets the element where the dots will appear
                    clickable: true,           // Makes the dots clickable
                },
                breakpoints: {
                    481: {
                        slidesPerView: 1,
                    },
                    768: {
                        slidesPerView: 2,
                    },
                    992: {
                        slidesPerView: 3,
                    },
                    1200: {
                        slidesPerView: 4,
                    }
                },
            });


            var swiper = new Swiper('.category-activation-two', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                scrollbar: {
                    el: '.swiper-scrollbar',
                        draggable: true,
                        hide: true,
                        snapOnRelease: true
                },
                breakpoints: {
                    480: {
                      slidesPerView: 1,
                    },
                    481: {
                      slidesPerView: 2,
                    },
                    768: {
                      slidesPerView: 2,
                    },
                    992: {
                      slidesPerView: 3,
                    },

                    1200: {
                      slidesPerView: 6,
                    },
                },
            });

            var swiper = new Swiper('.category-activation-three', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                scrollbar: {
                    el: '.swiper-scrollbar',
                        draggable: true,
                        hide: true,
                        snapOnRelease: true
                },
                breakpoints: {
                    480: {
                      slidesPerView: 1,
                    },
                    481: {
                      slidesPerView: 2,
                    },
                    768: {
                      slidesPerView: 2,
                    },
                    992: {
                      slidesPerView: 3,
                    },
                    1200: {
                      slidesPerView: 4,
                    },
                },
            });
          
            var swiper = new Swiper(".category-activation-four", {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                navigation: {
                  nextEl: ".rbt-arrow-left",
                  prevEl: ".rbt-arrow-right",
                  clickable: true,
                },
                scrollbar: {
                  el: ".swiper-scrollbar",
                  draggable: true,
                  hide: true,
                  snapOnRelease: true,
                },
                breakpoints: {
                  480: {
                    slidesPerView: 1,
                  },
                  481: {
                    slidesPerView: 2,
                  },
                  768: {
                    slidesPerView: 2,
                  },
                  992: {
                    slidesPerView: 3,
                  },
                  1200: {
                    slidesPerView: 4,
                  },
                },
              });

            var swiper = new Swiper('.event-activation-1', {
                slidesPerView: 1,
                slidesPerGroup: 1,
                loop: true,
                spaceBetween: 30,
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
                scrollbar: {
                    el: '.swiper-scrollbar',
                        draggable: true,
                        hide: true,
                        snapOnRelease: true
                },
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    575: {
                      slidesPerView: 1,
                    },

                    768: {
                      slidesPerView: 2,
                    },

                    992: {
                      slidesPerView: 3,
                    },
                    1200: {
                      slidesPerView: 3,
                      slidesPerGroup: 3,
                    },
                },
            });


            var swiper = new Swiper('.banner-splash-inner-layout-active', {
                effect: 'cards',
                grabCursor: true,
                clickable: true,
                loop: true,
                pagination: {
                    el: '.rbt-swiper-pagination',
                    clickable: true,
                    type: "fraction",
                },
                navigation: {
                    prevEl: '.rbt-arrow-left',
                    nextEl: '.rbt-arrow-right',
                    clickable: true,
                },
            });



        },

 
     
	};

	function widthgen() {
		$(window).on('load resize', elementWidth);

		function elementWidth() {
			$('.elementwidth').each(function() {
				var $container = $(this),
					width = $container.outerWidth(),
					classes = $container.attr("class").split(' ');
				var classes1 = startWith(classes, 'elwidth');
				classes1 = classes1[0].split('-');
				classes1.splice(0, 1);
				var classes2 = startWith(classes, 'elmaxwidth');
				classes2.forEach(function(el) {
					$container.removeClass(el);
				});
				classes1.forEach(function(el) {
					var maxWidth = parseInt(el);

					if (width <= maxWidth) {
						$container.addClass('elmaxwidth-' + maxWidth);
					}
				});
			});
		}


		function startWith(item, stringName) {
			return $.grep(item, function(elem) {
				return elem.indexOf(stringName) == 0;
			});
		}
	}

	
	widthgen();
	

	
    function content_ready_scripts() {
		Theme.eduSwiperActive();	 
        Theme.counterUp();
        Theme.masonryActivation();
        eduJs.i();
        $("[data-background]").each(function () {
            if( $(this).attr("data-background").length > 0 ) {
                $(this).css("background-image", "url( " + $(this).attr("data-background") + "  )");
            }
        });
         
	}

	function content_load_scripts() {	
		Theme.eduSwiperActive();
         
       
	}

	$(document).ready(function() {
		content_ready_scripts();	
	});

	$(window).on('load', function() {
		content_load_scripts();	
	 
	});

	$(window).on('load resize', function() {			
		content_load_scripts();
	});	



	$(window).on('elementor/frontend/init', function() {
		if (elementorFrontend.isEditMode()) {
			elementorFrontend.hooks.addAction('frontend/element_ready/widget', function() {
				content_ready_scripts();
				content_load_scripts();

			});
		}
	});

}());