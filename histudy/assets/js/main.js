(function (window, document, $, undefined) {
  "use strict";
  // learnpress course filter
  if ($('.rainbow-learnpress-featured-course-widget-single .section').length > 0) {
    const targetElement = $('.rainbow-learnpress-featured-course-widget-single .section');
    $('.rainbow-learnpress-featured-course-widget-single .section-content').slideUp();
    targetElement.on('click', function () {
      $(this).closest('.section').find('.section-content').slideToggle();
    })
  }
  /**
   * Disable like on blog single based on blog-liked local storage value
   */
  //   popup for html5 video
  if ($(".rbt-video-html5").length > 0) {
    $(".rbt-video-html5").magnificPopup({
      type: "iframe",
    });
  }
  /**
   * Popup for youtube video
   */
  if ($(".rbt-video-youtube").length > 0) {
    $(".rbt-video-youtube").magnificPopup({
      type: "iframe",
    });
  }
  /**
   * Popup for vimeo video
   */
  if ($(".rbt-video-vimeo").length > 0) {
    $(".rbt-video-vimeo").magnificPopup({
      type: "iframe",
      iframe: {
        patterns: {
          vimeo: {
            index: "vimeo.com/",
            id: "vimeo.com/",
            src: "//player.vimeo.com/video/%id%?autoplay=1",
          },
        },
        srcAction: "iframe_src",
      },
    });
  }
  $(".rbt-category-menu-wrapper.rbt-category-update .rbt-category-btn").on(
    "click",
    function () {
      $("#mobile-sidebar-popup-menu").addClass("active");
    }
  );
  $(".footer-style-1 .footer-widget ul.menu").addClass("ft-link");
  $(document.body).on("added_to_cart", function () {
    $(".rbt-cart-side-menu").addClass("side-menu-active");
    $("body").addClass("cart-sidenav-menu-active");
  });
  $("#tutor-registration-form input").on();
  $("#tutor-registration-form input").each(function () {
    var inputValue = $(this).val();
    if (inputValue) {
      $(this).parent(".form-group").addClass("focused");
    }
  });
  var tooltipTriggerList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="tooltip"]')
  );
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  $(".tutor-btn-show-more").on("click", function () {
    $(".tutor-course-details-content").toggleClass("active");
  });
  $(".rbt-filter-rating-toggle").on("click", function () {
    $(".rbt-single-widget.rbt-widget-rating").slideToggle();
  });
  $(".write-course-review-link-btn").on("click", function () {
    $(".tutor-write-review-form").slideToggle();
  });
  $(document).on("click", function (event) {
    var target = $(event.target);
    if (
      !target.closest(".filter-select.rbt-modern-select.select-rating").length
    ) {
      $(".rbt-single-widget.rbt-widget-rating").slideUp();
    }
  });
  var eduJs = {
    i: function (e) {
      eduJs.d();
      eduJs.methods();
    },

    d: function (e) {
      (this._window = $(window)),
        (this._document = $(document)),
        (this._body = $("body")),
        (this._html = $("html")),
        (this.sideNav = $(".rbt-search-dropdown"));
    },
    methods: function (e) {
      eduJs.salActive();
      eduJs.menuCurrentLink();
      eduJs.eduBgCardHover();
      eduJs.magnigyPopup();
      eduJs.counterUp();
      eduJs.pricingPlan();
      eduJs.courseView();
      eduJs.stickyHeader();
      eduJs.masonryActivation();
      eduJs._clickDoc();
      eduJs.wowActivation();
      eduJs.radialProgress();
      eduJs.marqueImage();
      eduJs.popupMobileMenu();
      eduJs.headerSticky();
      eduJs.qtyBtn();
      eduJs.checkoutPage();
      eduJs.offCanvas();
      eduJs.onePageNav();
      eduJs.transparentHeader();
      eduJs.categoryMenuHover();
      eduJs.cartSidenav();
      eduJs.filterClickButton();
      // eduJs.moveAnimation();
      eduJs.headerTopActivation();
      eduJs.magnificPopupActivation();
      eduJs.showMoreBtn();
      eduJs.sidebarVideoHidden();
      eduJs.courseActionBottom();
      eduJs.topbarExpend();
      eduJs.categoryOffcanvas();
      eduJs.autoslidertab();
      eduJs.categoryMenuHover2();
      eduJs.clapAnimation();
      eduJs.dataCommonFunc();
      eduJs.preloaderInit();
      eduJs.multiStepForm();
      eduJs.cursorFollow();
    },
    
    multiStepForm: function () {
      document.addEventListener('DOMContentLoaded', () => {
        const DOMstrings = {
          stepsBtnClass: 'multisteps-form__progress-btn',
          stepsBtns: document.querySelectorAll('.multisteps-form__progress-btn'),
          stepsBar: document.querySelector('.multisteps-form__progress'),
          stepsForm: document.querySelector('.multisteps-form__form'),
          stepsFormTextareas: document.querySelectorAll('.multisteps-form__textarea'),
          stepFormPanelClass: 'multisteps-form__panel',
          stepFormPanels: document.querySelectorAll('.multisteps-form__panel'),
          stepPrevBtnClass: 'rbt-step-btn-prev',
          stepNextBtnClass: 'rbt-step-btn-next',
        };
      
        // Function to remove classes
        const removeClasses = (elemSet, className) => {
          elemSet.forEach(elem => {
            elem.classList.remove(className);
          });
        };
      
        // Find parent of an element
        const findParent = (elem, parentClass) => {
          let currentNode = elem;
          while (!currentNode.classList.contains(parentClass)) {
            currentNode = currentNode.parentNode;
          }
          return currentNode;
        };
      
        // Get active step
        const getActiveStep = elem => {
          return Array.from(DOMstrings.stepsBtns).indexOf(elem);
        };
      
        // Set active step
        const setActiveStep = activeStepNum => {
          removeClasses(DOMstrings.stepsBtns, 'rbt-active');
          DOMstrings.stepsBtns.forEach((elem, index) => {
            if (index <= activeStepNum) {
              elem.classList.add('rbt-active');
            }
          });
        };
      
        // Get active panel
        const getActivePanel = () => {
          let activePanel;
          DOMstrings.stepFormPanels.forEach(elem => {
            if (elem.classList.contains('rbt-active')) {
              activePanel = elem;
            }
          });
          return activePanel;
        };
      
        // Set active panel
        const setActivePanel = activePanelNum => {
          removeClasses(DOMstrings.stepFormPanels, 'rbt-active');
          DOMstrings.stepFormPanels.forEach((elem, index) => {
            if (index === activePanelNum) {
              elem.classList.add('rbt-active');
            }
          });
        };
      
        // Add event listener to stepsBar
        if (DOMstrings.stepsBar) {
          DOMstrings.stepsBar.addEventListener('click', e => {
            const eventTarget = e.target;
      
            if (!eventTarget.classList.contains(DOMstrings.stepsBtnClass)) {
              return;
            }
      
            const activeStep = getActiveStep(eventTarget);
            setActiveStep(activeStep);
            setActivePanel(activeStep);
          });
        }
      
        // Add event listener to stepsForm
        if (DOMstrings.stepsForm) {
          DOMstrings.stepsForm.addEventListener('click', e => {
            const eventTarget = e.target;
      
            if (
              !(
                eventTarget.classList.contains(DOMstrings.stepPrevBtnClass) ||
                eventTarget.classList.contains(DOMstrings.stepNextBtnClass)
              )
            ) {
              return;
            }
      
            const activePanel = findParent(eventTarget, DOMstrings.stepFormPanelClass);
            let activePanelNum = Array.from(DOMstrings.stepFormPanels).indexOf(activePanel);
      
            if (eventTarget.classList.contains(DOMstrings.stepPrevBtnClass)) {
              activePanelNum--;
            } else {
              activePanelNum++;
            }
      
            setActiveStep(activePanelNum);
            setActivePanel(activePanelNum);
          });
        }
      });      
    },

    cursorFollow: function () {
      let mouseX = 0, mouseY = 0;
      let xp = 0, yp = 0;
      const cursorFollower = document.getElementById("cursorFollower");
      const container = document.querySelector(".cf_boundary");

      if(container) {
        document.addEventListener("mousemove", (e) => {
          const rect = container.getBoundingClientRect();
          
          mouseX = e.clientX - rect.left;
          mouseY = e.clientY - rect.top;
  
          const maxX = rect.width - cursorFollower.offsetWidth / 2;
          const maxY = rect.height - cursorFollower.offsetHeight / 2;
          const minX = cursorFollower.offsetWidth / 2;
          const minY = cursorFollower.offsetHeight / 2;
  
          mouseX = Math.max(minX, Math.min(mouseX, maxX));
          mouseY = Math.max(minY, Math.min(mouseY, maxY));
        });
  
        function animateCursor() {
          xp += (mouseX - xp) * 0.1;
          yp += (mouseY - yp) * 0.1;
  
          cursorFollower.style.left = `${xp}px`;
          cursorFollower.style.top = `${yp}px`;
  
          requestAnimationFrame(animateCursor);
        }
  
        animateCursor();
      }

    },

    counterUp: function () {
      var odo = $(".odometer");
      odo.each(function () {
        $(".odometer").appear(function (e) {
          var countNumber = $(this).attr("data-count");
          $(this).html(countNumber);
        });
      });
    },
    dataCommonFunc: function () {
      $("[data-background]").each(function () {
        $(this).css(
          "background-image",
          "url( " + $(this).attr("data-background") + "  )"
        );
      });
      $("[data-min-height]").each(function () {
        $(this).css("min-height", $(this).attr("data-min-height"));
      });
    },

    clapAnimation: function () {
      $.fn.clapAnimateButton = function () {
        return this.each(function () {
          $(this).addClass("animate");
          setTimeout(() => {
            $(this).removeClass("animate");
          }, 300);
        });
      };
      $(document).on("click", ".like-button", function () {
        $(this).clapAnimateButton();
      });
    },

    autoslidertab: function (params) {
      if ($(".nav-tabs.splash-nav-tabs > li").length > 0) {
        function tabChange() {
          var tabs = $(".nav-tabs.splash-nav-tabs > li");
          var active = tabs.find("a.active");
          var next = active.parent("li").next("li").find("a");
          if (next.length === 0) {
            next = tabs.first().find("a").click();
            next.tab("show");
          }
        }
        var tabCycle = setInterval(tabChange, 5000);
      }
    },

    offCanvas: function (params) {
      if ($("#rbt-offcanvas-activation").length) {
        $("#rbt-offcanvas-activation").on("click", function () {
          $(".side-menu").addClass("side-menu-active"),
            $("body").addClass("offcanvas-menu-active");
        }),
          $(".close_side_menu").on("click", function () {
            $(".side-menu").removeClass("side-menu-active"),
              $("body").removeClass("offcanvas-menu-active");
          }),
          $(".side-menu .side-nav .navbar-nav li a").on("click", function () {
            $(".side-menu").removeClass("side-menu-active"),
              $("body").removeClass("offcanvas-menu-active");
          }),
          $("#btn_sideNavClose, #btn_sideNavClose2").on("click", function () {
            $(".side-menu").removeClass("side-menu-active"),
              $("body").removeClass("offcanvas-menu-active");
          });
      }
    },

    cartSidenav: function (params) {
      if ($(".rbt-cart-sidenav-activation").length) {
        $(".rbt-cart-sidenav-activation").on("click", function () {
         if( histudy_ajax_object.monetize_tutor_lms == 'wc') {
            $(".rbt-cart-side-menu").addClass("side-menu-active");
            $("body").addClass("cart-sidenav-menu-active");
        }
        });
      }
      if ($(".minicart-close-button").length) {
        $(".minicart-close-button").on("click", function () {
          $(".rbt-cart-side-menu").removeClass("side-menu-active"),
            $("body").removeClass("cart-sidenav-menu-active");
        });
      }
      if ($(".side-menu .side-nav .navbar-nav li a").length) {
        $(".side-menu .side-nav .navbar-nav li a").on("click", function () {
          $(".rbt-cart-side-menu").removeClass("side-menu-active"),
            $("body").removeClass("cart-sidenav-menu-active");
        });
      }
      if (
        $("#btn_sideNavClose, #btn_sideNavClose2, .close_side_menu").length > 0
      ) {
        $("#btn_sideNavClose, #btn_sideNavClose2, .close_side_menu").on(
          "click",
          function () {
            $(".rbt-cart-side-menu").removeClass("side-menu-active"),
              $("body").removeClass("cart-sidenav-menu-active");
          }
        );
      }
    },

    menuCurrentLink: function () {
      var currentPage = location.pathname.split("/"),
        current = currentPage[currentPage.length - 1];
      $(".mainmenu li a").each(function () {
        var $this = $(this);
        if ($this.attr("href") === current) {
          $this.addClass("active");
          $this.parents(".has-menu-child-item").addClass("menu-item-open");
        }
      });
    },

    salActive: function () {
      sal({
        threshold: 0.01,
        once: true,
      });
    },

    eduParalax: function () {
      var scene = document.getElementById("scene");
      var parallaxInstance = new Parallax(scene);
    },

    eduBgCardHover: function () {
      $(".rbt-hover-active").mouseenter(function () {
        var self = this;
        setTimeout(function () {
          $(".rbt-hover-active.active").removeClass("active");
          $(self).addClass("active");
        }, 0);
      });
    },

    magnigyPopup: function () {
      $(document).on("ready", function () {
        if ($(".popup-video").length > 0) {
          $(".popup-video").magnificPopup({
            type: "iframe",
          });
        }
      });
    },

    pricingPlan: function () {
      var mainPlan = $(".rbt-pricing-area");
      mainPlan.each(function () {
        var yearlySelectBtn = $(".yearly-plan-btn"),
          monthlySelectBtn = $(".monthly-plan-btn"),
          monthlyPrice = $(".monthly-pricing"),
          yearlyPrice = $(".yearly-pricing"),
          monthlyBtn = $(".monthly-btn"),
          yearlyBtn = $(".yearly-btn"),
          buttonSlide = $(".pricing-checkbox");

        $(monthlySelectBtn).on("click", function () {
          buttonSlide.prop("checked", true);
          $(this)
            .addClass("active")
            .parent(".nav-item")
            .siblings()
            .children()
            .removeClass("active");
          monthlyPrice.css("display", "block");
          yearlyPrice.css("display", "none");
          monthlyBtn.css("display", "block");
          yearlyBtn.css("display", "none");
          console.log(monthlyBtn)
        });

        $(yearlySelectBtn).on("click", function () {
          buttonSlide.prop("checked", false);
          $(this)
            .addClass("active")
            .parent(".nav-item")
            .siblings()
            .children()
            .removeClass("active");
          monthlyPrice.css("display", "none");
          yearlyPrice.css("display", "block");
          monthlyBtn.css("display", "none");
          yearlyBtn.css("display", "block");
        });

        $(buttonSlide).change(function () {
          if ($('input[class="pricing-checkbox"]:checked').length > 0) {
            monthlySelectBtn.addClass("active");
            yearlySelectBtn.removeClass("active");
            monthlyPrice.css("display", "block");
            yearlyPrice.css("display", "none");
            monthlyBtn.css("display", "block");
            yearlyBtn.css("display", "none");
          } else {
            yearlySelectBtn.addClass("active");
            monthlySelectBtn.removeClass("active");
            monthlyPrice.css("display", "none");
            yearlyPrice.css("display", "block");
            monthlyBtn.css("display", "none");
            yearlyBtn.css("display", "block");
          }
        });
      });
    },

    courseView: function () {
      var gridViewBtn = $(".rbt-grid-view"),
        listViewBTn = $(".rbt-list-view");
      var currentURL = new URL(window.location.href);

      // Check the URL on page load and set the view accordingly
      var viewParam = currentURL.searchParams.get("view");

      if (viewParam === "list") {
          // Apply list view on page load if ?view=list
          $("ul.learn-press-courses").attr("data-layout", "list");
          $(".rbt-course-grid-column").removeClass("active-grid-view");
          $(".rbt-course-grid-column").addClass("active-list-view");
          $(".rbt-card").addClass("card-list-2");
          listViewBTn.addClass("active").parent(".course-switch-item").siblings().children().removeClass("active");
      } else if (viewParam === "grid") {
          // Apply grid view on page load if ?view=grid
          $("ul.learn-press-courses").attr("data-layout", "grid");
          $(".rbt-course-grid-column").addClass("active-grid-view");
          $(".rbt-course-grid-column").removeClass("active-list-view");
          $(".rbt-card").removeClass("card-list-2");
          gridViewBtn.addClass("active").parent(".course-switch-item").siblings().children().removeClass("active");
      }

      $(gridViewBtn).on("click", function () {
        // Check if the 'view' parameter already exists
        if (!currentURL.searchParams.has("view", "grid")) {
          currentURL.searchParams.set("view", "grid");
          history.pushState(null, null, currentURL.toString());
        }
        $(this)
          .addClass("active")
          .parent(".course-switch-item")
          .siblings()
          .children()
          .removeClass("active");
        $(".rbt-course-grid-column").addClass("active-grid-view");
        $(".rbt-course-grid-column").removeClass("active-list-view");
        $(".rbt-card").removeClass("card-list-2");
        // Change the data-layout attribute to 'grid' and remove 'list'
        $("ul.learn-press-courses").attr("data-layout", "grid");
      });

      $(listViewBTn).on("click", function () {
        if (!currentURL.searchParams.has("view", "list")) {
          currentURL.searchParams.set("view", "list");
          history.pushState(null, null, currentURL.toString());
        }
        $(this)
          .addClass("active")
          .parent(".course-switch-item")
          .siblings()
          .children()
          .removeClass("active");
        $(".rbt-course-grid-column").removeClass("active-grid-view");
        $(".rbt-course-grid-column").addClass("active-list-view");
        $(".rbt-card").addClass("card-list-2");
        $("ul.learn-press-courses").attr("data-layout", "list");
      });
    },

    stickyHeader: function () {
      // Header Transparent
      if ($("header").hasClass("header-transparent")) {
        $("body").addClass("active-header-transparent");
      } else {
        $("body").removeClass("active-header-transparent");
      }
    },

    masonryActivation: function name(params) {
      $(window).on("load", function () {
        $(".masonary-wrapper-activation").imagesLoaded(function () {
          // filter items on button click
          $(".messonry-button").on("click", "button", function () {
            var filterValue = $(this).attr("data-filter");
            $(this).siblings(".is-checked").removeClass("is-checked");
            $(this).addClass("is-checked");
            $grid.isotope({
              filter: filterValue,
            });
          });
          // init Isotope
          var $grid = $(".mesonry-list").isotope({
            percentPosition: true,
            transitionDuration: "0.7s",
            layoutMode: "masonry",
            masonry: {
              columnWidth: ".resizer",
            },
          });
        });
      });

      $(window).on("load", function () {
        $(".splash-masonary-wrapper-activation").imagesLoaded(function () {
          // filter items on button click
          $(".messonry-button").on("click", "button", function () {
            var filterValue = $(this).attr("data-filter");
            $(this).siblings(".is-checked").removeClass("is-checked");
            $(this).addClass("is-checked");
            $grid.isotope({
              filter: filterValue,
            });
          });
          // init Isotope
          var $grid = $(".splash-mesonry-list").isotope({
            percentPosition: true,
            transitionDuration: "0.7s",
            layoutMode: "masonry",
            masonry: {
              columnWidth: ".resizer",
            },
          });
        });
      });
    },

    _clickDoc: function () {
      var inputblur, inputFocus, openSideNav, closeSideNav;
      inputblur = function (e) {
        if (!$(this).val()) {
          $(this).parent(".form-group").removeClass("focused");
        }
      };
      inputFocus = function (e) {
        $(this).parents(".form-group").addClass("focused");
      };
      openSideNav = function (e) {
        e.preventDefault();
        eduJs.sideNav.addClass("active");
        $(".search-trigger-active").addClass("open");
        eduJs._html.addClass("side-nav-opened");
        eduJs._html.addClass("scroll-behavior-off");
      };

      closeSideNav = function (e) {
        if (
          !$(
            '.rbt-search-dropdown, .rbt-search-dropdown *:not(".search-trigger-active, .search-trigger-active *")'
          ).is(e.target)
        ) {
          eduJs.sideNav.removeClass("active");
          $(".search-trigger-active").removeClass("open");
          eduJs._html.removeClass("side-nav-opened");
          eduJs._html.removeClass("scroll-behavior-off");
        }
      };

      eduJs._document
        .on("blur", "input,textarea,select", inputblur)
        .on(
          "focus",
          'input:not([type="radio"]),input:not([type="checkbox"]),textarea,select',
          inputFocus
        )
        .on("click", ".search-trigger-active", openSideNav)
        .on("click", ".side-nav-opened", closeSideNav);
    },

    wowActivation: function () {
      new WOW().init();
    },

    radialProgress: function () {
      $(window).scroll(function () {
        /* Check the location of each desired element */
        $(".radial-progress").each(function (i) {
          var bottom_of_object = $(this).offset().top + $(this).outerHeight();
          var bottom_of_window = $(window).scrollTop() + $(window).height();
          /* If the object is completely visible in the window, fade it in */
          if (bottom_of_window > bottom_of_object) {
            $(".radial-progress").easyPieChart({
              lineWidth: 10,
              scaleLength: 0,
              rotate: 0,
              trackColor: false,
              lineCap: "round",
              size: 180,
              onStep: function (from, to, percent) {
                $(this.el).find(".percent").text(Math.round(percent));
              },
            });
          }
        });
      });
    },

    // moveAnimation: function () {
    //   $(".scene").each(function () {
    //     new Parallax($(this)[0]);
    //   });
    // },
    
    eduParalax: function () {
      var scene = document.getElementsByClassName("scene");
      var parallaxInstance = new Parallax(scene);
    },

    marqueImage: function () {
      $(".edumarque").each(function () {
        var t = 0;
        var i = 1;
        var $this = $(this);
        setInterval(function () {
          t += i;
          $this.css("background-position-x", -t + "px");
        }, 10);
      });
    },

    preloaderInit: function(){
      eduJs._window.on('load', function () {
          $('.preloader').fadeOut('slow', function () {
              $(this).remove();
          });
      });
    },

    popupMobileMenu: function (e) {
      $(".hamberger-button").on("click", function (e) {
        $(".popup-mobile-menu").addClass("active");
      });

      $(".close-button").on("click", function (e) {
        $(".popup-mobile-menu").removeClass("active");
        $(
          ".popup-mobile-menu .mainmenu .has-dropdown > a, .popup-mobile-menu .mainmenu .with-megamenu > a"
        )
          .siblings(".submenu, .rbt-megamenu")
          .removeClass("active")
          .slideUp("400");
        $(
          ".popup-mobile-menu .mainmenu .has-dropdown > a, .popup-mobile-menu .mainmenu .with-megamenu > a"
        ).removeClass("open");
      });

      $(
        ".popup-mobile-menu .mainmenu .has-dropdown > a, .popup-mobile-menu .mainmenu .with-megamenu > a"
      ).on("click", function (e) {
        e.preventDefault();
        $(this)
          .siblings(".submenu, .rbt-megamenu")
          .toggleClass("active")
          .slideToggle("400");
        $(this).toggleClass("open");
      });

      $(".popup-mobile-menu, .popup-mobile-menu .mainmenu.onepagenav li a").on(
        "click",
        function (e) {
          e.target === this &&
            $(".popup-mobile-menu").removeClass("active") &&
            $(
              ".popup-mobile-menu .mainmenu .has-dropdown > a, .popup-mobile-menu .mainmenu .with-megamenu > a"
            )
              .siblings(".submenu, .rbt-megamenu")
              .removeClass("active")
              .slideUp("400") &&
            $(
              ".popup-mobile-menu .mainmenu .has-dropdown > a, .popup-mobile-menu .mainmenu .with-megamenu > a"
            ).removeClass("open");
        }
      );
    },

    headerSticky: function () {
      if (window.location !== window.parent.location) {
        return;
      }

      if (typeof elementorFrontend !== 'undefined' && elementorFrontend.isEditMode()) {
        return;
      }
      $(window).on("scroll", function () {
        if ($("body").hasClass("rbt-header-sticky")) {
          var stickyPlaceHolder = $(".rbt-sticky-placeholder"),
            headerConainer = $(".rbt-header-wrapper"),
            headerConainerH = headerConainer.outerHeight(),
            topHeaderH = $(".rbt-header-top").outerHeight() || 0,
            targrtScroll = topHeaderH + 200;

          if ($(window).scrollTop() > targrtScroll) {
            headerConainer.addClass("rbt-sticky");
            $('body').addClass("rbt-sticky-tutor-popup");
            stickyPlaceHolder.height(headerConainerH);
          } else {
            headerConainer.removeClass("rbt-sticky");
            $('body').removeClass("rbt-sticky-tutor-popup");
            stickyPlaceHolder.height(0);
          }
        }
      });
    },


    qtyBtn: function () {
      $(".pro-qty").prepend('<span class="dec qtybtn">-</span>');
      $(".pro-qty").append('<span class="inc qtybtn">+</span>');
      $(".qtybtn").on("click", function () {
        var $button = $(this);
        var oldValue =
          $button.parent().find("input").val().length > 0
            ? $button.parent().find("input").val()
            : 0;
        if ($button.hasClass("inc")) {
          var newVal = parseFloat(oldValue) + 1;
        } else {
          if (oldValue > 1) {
            var newVal = parseFloat(oldValue) - 1;
          } else {
            newVal = 1;
          }
        }
        $button.parent().find("input").val(newVal);
        $button.parent().find("input").change();
      });
    },

    checkoutPage: function () {
      $("[data-shipping]").on("click", function () {
        if ($("[data-shipping]:checked").length > 0) {
          $("#shipping-form").slideDown();
        } else {
          $("#shipping-form").slideUp();
        }
      });
      $('[name="payment-method"]').on("click", function () {
        var $value = $(this).attr("value");
        $(".single-method p").slideUp();
        $('[data-method="' + $value + '"]').slideDown();
      });
    },

    onePageNav: function () {
      var scrollAllowed;
      $(".onepagenav").onePageNav({
        currentClass: "current",
        changeHash: false,
        scrollSpeed: 500,
        scrollThreshold: 0.2,
        filter: "",
        begin: function (e) {
          scrollAllowed = true; // Allow scroll when a tab is clicked
        },
        end: function () {
          scrollAllowed = false; // Prevent scroll during natural scrolling
        },
        scrollChange: function ($currentListItem) {

        },
        easing: "swing",
      });
    },

    transparentHeader: function () {
      if ($(".rbt-header").hasClass("rbt-transparent-header")) {
        var mainHeader = $(".rbt-header").outerHeight();
        $("body").addClass("rbt-header-transpernt-active");
        $(".header-transperent-spacer").css("padding-top", mainHeader + "px");
      }
    },

    categoryMenuHover: function () {
      $(".vertical-nav-menu li.vertical-nav-item").hover(function () {
        $(".rbt-vertical-inner").hide();
        $(".vertical-nav-menu li.vertical-nav-item").removeClass("active");
        $(this).addClass("active");
        var selected_tab = $(this).find("a").attr("href");
        $(selected_tab).stop().fadeIn();
        return false;
      });
    },


    filterClickButton: function () {
      $(".discover-filter-activation").on("click", function () {
        // Toggle the 'open' class on the clicked button
        $(this).toggleClass("open");

        $(".default-exp-expand").not(".rbt-course-filter-modal .default-exp-expand").slideToggle("400");

      });
    },

    headerTopActivation: function () {
      $(".bgsection-activation").on("click", function () {
        $(this).parents(".rbt-header-campaign").addClass("deactive");
      });
    },

    magnificPopupActivation: function () {
      $(".parent-gallery-container").magnificPopup({
        delegate: ".child-gallery-single", // child items selector, by clicking on it popup will open
        type: "image",
        mainClass: "mfp-with-zoom",
        // other options
        gallery: {
          enabled: true,
        },
        zoom: {
          enabled: true, // By default it's false, so don't forget to enable it
          duration: 300, // duration of the effect, in milliseconds
          easing: "ease-in-out", // CSS transition easing function
          // The "opener" function should return the element from which popup will be zoomed in
          // and to which popup will be scaled down
          // By defailt it looks for an image tag:
          opener: function (openerElement) {
            // openerElement is the element on which popup was initialized, in this case its <a> tag
            // you don't need to add "opener" option if this code matches your needs, it's defailt one.
            return openerElement.is("img")
              ? openerElement
              : openerElement.find("img");
          },
        },
      });
    },

    showMoreBtn: function () {
      $.fn.hasShowMore = function () {
        return this.each(function () {
          $(this).toggleClass("active");
          $(this).text(ajax_object.show_less_text);
          $(this).parent(".has-show-more").toggleClass("active");
          if ($(this).parent(".has-show-more").hasClass("active")) {
            $(this).text(ajax_object.show_less_text);
          } else {
            $(this).text(ajax_object.show_more_text);
          }
        });
      };
      $(document).on("click", ".rbt-show-more-btn", function () {
        $(this).hasShowMore();
      });
    },

    sidebarVideoHidden: function () {
      var scrollTop = $(".sidebar-video-hidden");
      $(window).scroll(function () {
        // declare variable
        var topPos = $(this).scrollTop();
        // if user scrolls down - show scroll to top button
        if (topPos > 250) {
          $(scrollTop).css("display", "none");
        } else {
          $(scrollTop).css("display", "block");
        }
      });
    },

    courseActionBottom: function () {
      var scrollBottom = $(".rbt-course-action-bottom");
      $(window).scroll(function () {
        var topPos = $(this).scrollTop();
        var targetPossition = $(document).height() * 0.66;
        var filled =
          ($(document).scrollTop() + window.innerHeight) / $(document).height();
        if (topPos > targetPossition && filled != 1) {
          $(scrollBottom).addClass("rbt-course-action-active");
        } else {
          $(scrollBottom).removeClass("rbt-course-action-active");
        }
      });
    },

    topbarExpend: function () {
      var windowWidth = $(window).width();
      {
        if (windowWidth < 1199) {
          $(".top-bar-expended").on("click", function () {
            $(".top-expended-activation").hasClass("active")
              ? ($(".top-expended-activation").removeClass("active"),
                $(".top-expended-activation")
                  .find(".top-expended-wrapper")
                  .css({ height: "32px" }))
              : ($(".top-expended-activation").addClass("active"),
                $(".top-expended-activation")
                  .find(".top-expended-wrapper")
                  .css({
                    height: $(".top-expended-inner").outerHeight() + "px",
                  }));
          });
          $(window).on("hresize", function () {
            $(".top-expended-activation").hasClass("active") &&
              $(".top-expended-activation")
                .find(".top-expended-inner")
                .css({
                  height: $(".top-expended-inner").outerHeight() + "px",
                });
          });
        }
      }
    },

    categoryOffcanvas: function () {
      var windowWidth = $(window).width();
      if (windowWidth < 1200) {
        $(".rbt-side-offcanvas-activation").on("click", function () {
          $(".rbt-offcanvas-side-menu").addClass("active-offcanvas");
        });
        $(".rbt-close-offcanvas").on("click", function () {
          $(".rbt-offcanvas-side-menu").removeClass("active-offcanvas");
        });
        $(".rbt-offcanvas-side-menu").on("click", function (e) {
          e.target === this &&
            $(".rbt-offcanvas-side-menu").removeClass("active-offcanvas");
        });
        $(".rbt-vertical-nav-list-wrapper .vertical-nav-item a").on(
          "click",
          function (e) {
            e.preventDefault();
            $(this)
              .siblings(".vartical-nav-content-menu-wrapper")
              .toggleClass("active")
              .slideToggle("400");
            $(this).toggleClass("active");
          }
        );
      }
    },

    categoryMenuHover2: function () {
      $(".dropdown-parent-wrapper li.dropdown-parent-list").mouseover(
        function () {
          $(this).find(".dropdown-child-wrapper").show();
          $(".dropdown-parent-wrapper li.dropdown-parent-list").removeClass(
            "active"
          );
          $(this).addClass("active");
          var selected_tab = $(this).find("a").attr("href");
          return false;
        }
      );
    },
  };
  eduJs.i();
  if (document.querySelector(".rbt-callto-action.style-2")) {
    const round_image = document.querySelector(".rbt-callto-action.style-2");
    let shape_img = round_image
      .querySelector(".shape-text-image > img")
      .classList.add("rbt-rotatation-round");
  }

  $(".course-sidebar .ajax_add_to_cart").on("click", function (e) {
    e.preventDefault();
  });
  $(".single.single-courses .ajax_add_to_cart").on("click", function (e) {
    e.preventDefault();
    $('tutor-modal tutor-login-modal').removeClass('tutor-is-active');
  });
  $('body').on('click', '.lp-form-course-filter__title', function () {
    $(this).closest('.lp-form-course-filter__item').find('.lp-form-course-filter__content').slideToggle();
  });
  $('.rbt-filter-toggle-btn').on('click', function () {
   // $('.widget_course_filter .lp-form-course-filter').slideToggle();
    $('.rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter').slideToggle();
  });

  $(document).ready(function () {
    $('.rbt-course-menu-fixed-pos-bottom li').on('click', function () {
      // Remove 'active' class from all list items
      $('.rbt-course-menu-fixed-pos-bottom li').removeClass('active');
      // Add 'active' class to the clicked list item
      $(this).addClass('active');
    });
  });

  $(document).ready(function () {
    // Handle click event on any element with an href attribute
    $(document).on('click', '.mainmenu .tutor-nav-items [href^="#"]', function (e) {
      e.preventDefault();

      // Remove current class from all items in the menu
      $('.mainmenu .tutor-nav-items').removeClass('current');
      $('.mainmenu .tutor-nav-items').removeClass('active').addClass('previous-active');


      // Add current class to the clicked item
      $(this).parent().addClass('active');

      // Get the target ID from the href attribute
      var targetId = $(this).attr('href');
      var $target = $(targetId);

      if ($target.length) {
        $('html, body').animate({
          scrollTop: $target.offset().top - 220 // Adjust the offset here
        }, 200); // Adjust the duration (200ms) as needed
      }
    });
  });

  // Utility function to get query parameter value
  function getQueryParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  }

  // Function to initialize selectpicker conditionally
  function selectPicker() {
    if (getQueryParameter('course_ID') === null) {
      $(
        "body:not(.woocommerce-page.page-cart, .woocommerce-checkout) select"
      ).selectpicker();
    }
  }

  // Call the function to initialize selectpicker
  selectPicker();

  $(document).ready(function () {
    // Function to update the bottom position
    function updateBottomPosition() {
      if ($('.rbt-course-menu-fixed-pos-bottom').length) {
        $('.rbt-backto-top-active').css('bottom', '100px');
      }
    }

    // Initial check 
    updateBottomPosition();

    // Observe the DOM for changes
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
          updateBottomPosition();
        }
      });
    });

    // Start observing the document body for changes
    observer.observe(document.body, {
      childList: true,
      attributes: true,
      subtree: true
    });
  });


  document.addEventListener('DOMContentLoaded', function () {
    var modal = document.getElementById('course-filter-popup');
    var filterOpend = document.querySelector('.default-exp-expand.histudy-filter-style-1');
    var discoverFilterButton = document.querySelector('.discover-filter-button');

    // Function to show the modal with animation
    function showModal() {
      if( modal ) {
        modal.classList.add('open');
      }
      
      if (filterOpend) {
        filterOpend.style.display = 'none';
      }
    }

    // Function to hide the modal
    function hideModal() {
      if( modal ) {
        modal.classList.remove('open');
      }
      if (filterOpend) {
        filterOpend.style.display = 'none';
      }
    }

    // Open the modal when the button is clicked
    if( discoverFilterButton ) {
      discoverFilterButton.addEventListener('click', function (e) {
        if (window.innerWidth <= 767) {
          if (document.querySelector('.rbt-course-filter-modal')) {
            showModal();
          }
        }
      });
    }

    document.addEventListener('click', function (event) {

      if (event.target.matches('.close-button, .close-button *')) {
        hideModal();
      }
    });

    // Close the modal when clicking outside of the modal content
    window.addEventListener('click', function (e) {
      if (e.target === modal) {
        hideModal();
      }
    });
  });


  const targetDivs = document.querySelectorAll(
    '.default-exp-wrapper.default-exp-expand.histudy-filter-style-1:not(.rbt-course-filter-modal .default-exp-expand), ' +
    '.default-exp-wrapper.histudy-filter-style-1.rbt-mobile-view-filter-custom:not(.rbt-course-filter-modal .default-exp-expand)'
  );
  let removedDivs = [];

  // Store the original parent for each targetDiv
  targetDivs.forEach(div => {
    removedDivs.push({
      element: div,
      parent: div.parentNode,
      nextSibling: div.nextSibling // Store the next sibling to reinsert in the correct order
    });
  });

  function handleViewportChange() {
    if (window.innerWidth <= 767) {
      // Remove the target divs from the DOM
      removedDivs.forEach(item => {
        if (item.element.parentNode) {
          item.element.remove();
        }
      });
    } else {
      // Reinsert the target divs into their original parent container
      removedDivs.forEach(item => {
        if (!item.parent.contains(item.element)) {
          if (item.nextSibling) {
            item.parent.insertBefore(item.element, item.nextSibling);
          } else {
            item.parent.appendChild(item.element);
          }
        }
      });
    }
  }

  // Initial check
  handleViewportChange();

  // Listen for window resize events
  window.addEventListener('resize', handleViewportChange);


  document.addEventListener('DOMContentLoaded', function () {

    // Function to check the condition and append the markup
    function appendSelectedCourseFilters() {
      let filterShowMobile = document.querySelector('.filter-show-only-mobile-view');


      if (filterShowMobile) {
        filterShowMobile.style.display = 'none';
      }

      // Check if the window width is 767px or less
      if (window.innerWidth <= 767) {

        // Check if the specific class exists on the page
        var element = document.querySelector('.histudy-reset-filter-hide-mobile');

        if (filterShowMobile) {
          filterShowMobile.style.display = 'block';
        }

      if(element) {
        while (element.firstChild) {
          element.removeChild(element.firstChild);
        }
      }
       
        if (document.querySelector('.rbt-course-filter-modal')) {

          var targetElement = document.querySelector('.mobile-view-reset-filter');

          // Check if the target element exists
          if (targetElement) {
            // Check if the markup is already appended to prevent duplicates
            if (!targetElement.querySelector('.histudy-selected-course-filters-114')) {
              // Create the new div element
              var newDiv = document.createElement('div');
              newDiv.className = 'selected_course_filters histudy-selected-course-filters-114';

              // Create the ul element and append it to the new div
              var ulElement = document.createElement('ul');
              newDiv.appendChild(ulElement);

              // Append the new div inside the target element
              targetElement.appendChild(newDiv);
            }
          }
        }
      }
    }

    // Run the function on page load
    appendSelectedCourseFilters();

    // Optionally, run the function on window resize to dynamically adjust
    window.addEventListener('resize', appendSelectedCourseFilters);
  });

  function toggleCourseVideoDisplay() {
    const mobileElement = document.querySelector('.rbt-course-single-video-mobile .rbt-course-feature-has-video-thumbnail');
    const desktopElement = document.querySelector('.rbt-courses-single-video-desktop .rbt-course-feature-has-video-thumbnail');

    if (window.innerWidth <= 767) {
        // Show video on mobile and hide the desktop element
        if (desktopElement) {
            desktopElement.style.display = 'none';
        }
        if (mobileElement) {
            mobileElement.style.display = 'block';
        }
    } else {
        // Show video on desktop and hide the mobile element
        if (mobileElement) {
            mobileElement.style.display = 'none';
        }
        if (desktopElement) {
            desktopElement.style.display = 'block';
        }
    }
  }

  // Run on initial load
  toggleCourseVideoDisplay();

  // Run on window resize
  window.addEventListener('resize', function() {
      toggleCourseVideoDisplay();
  });

  if ($('.rbt-service.rbt-service-2.rbt-hover-02.rainbow-box-bg.bg-no-shadow .rbt-btn-link').hasClass('transparent-button')) {
    $('.rbt-service.rbt-service-2.rbt-hover-02.rainbow-box-bg.bg-no-shadow .rbt-btn-link').removeClass('transparent-button');
  }


  // Archive sort by only used learnpress
  $('#histudy-learnpress-course-filter-select').on('change', function(e){
    $('#histudy-course-sort-form').submit();
  });

  jQuery(window).on('load', function($) {
    // Initialize all sections with the 'closed' class
    jQuery('.rainbow-learnpress-featured-course-widget-single .curriculum-sections > .section').addClass('closed');

    // Listen for a click event on the .section element
    jQuery('.rainbow-learnpress-featured-course-widget-single .curriculum-sections > .section').on('click', function() {
        // If the clicked item already has the 'anyclass' class, remove it and add 'closed'
        if (jQuery(this).hasClass('anyclass')) {
            jQuery(this).removeClass('anyclass').addClass('closed');
        } 
        // If the clicked item has the 'closed' class, remove it and add 'anyclass'
        else if (jQuery(this).hasClass('closed')) {
            jQuery(this).removeClass('closed').addClass('anyclass');
        }
        // If it has neither, just add 'anyclass' class by default
        else {
            jQuery(this).addClass('anyclass');
        }
    });
});





$(document).ready(function() {
  // Ensure that selectpicker is initialized first
  $('.form-select').selectpicker();

  if (typeof $.fn.selectpicker !== 'undefined') {
      $.fn.selectpicker.Constructor.DEFAULTS.noneSelectedText = ajax_object.nothing_selected;
      $.fn.selectpicker.Constructor.DEFAULTS.selectAllText = ajax_object.select_all;
      $.fn.selectpicker.Constructor.DEFAULTS.deselectAllText = ajax_object.deselect_all;

      // Reinitialize to apply changes
      $('.form-select').selectpicker('refresh');
  }

  if ($('button.bs-placeholder').length > 0) {
    $('button.bs-placeholder').attr('title', ajax_object.nothing_selected);
    // Update the inner text that is displayed in the button
    $('button.bs-placeholder .filter-option-inner-inner').text(ajax_object.nothing_selected);
  }

  if ($('.bs-select-all').length > 0) {
    $('.bs-select-all').text(ajax_object.select_all);
    $('.bs-deselect-all').text(ajax_object.deselect_all);
  }

});


const items = document.querySelectorAll('.service-style-thirteen .service-card-6 .inner');
let activeItem = null; // Track the active item

if( items.length > 0 ) {
    items.forEach(item => {
      item.addEventListener('mouseenter', function() {
          items.forEach(innerItem => innerItem.classList.remove('active-border'));

          this.classList.add('active-border');
          activeItem = this; 
      });

      item.addEventListener('mouseleave', function() {
          if (this !== activeItem) {
              this.classList.remove('active-border');
          }
      });
  });


  const centerItem = items[Math.floor(items.length / 2)];

  if (!centerItem.classList.contains('active-border')) {
      centerItem.classList.add('active-border');
  }
}

document.addEventListener("DOMContentLoaded", function () {
  const toggleButton = document.querySelector(".topbar-expend-button");
  const topWrapper = document.querySelector(".top-expended-wrapper");

  if (toggleButton && topWrapper) {
      const icon = toggleButton.querySelector("i");

      toggleButton.addEventListener("click", function () {

        const headerLeft = document.querySelector(".rbt-header-sec-col.rbt-header-left");
    
        if (headerLeft) {
            headerLeft.style.display = "block !important"; 
        }

          if (topWrapper.classList.contains("expanded")) {
              topWrapper.classList.remove("expanded");
              icon?.classList.replace("feather-minus", "feather-plus");
          } else {
              topWrapper.classList.add("expanded");
              icon?.classList.replace("feather-plus", "feather-minus");
          }
      });
  }
});

if (document.body.classList.contains('active-dark-mode')) {
  const banner = document.querySelector('.rbt-banner-area.rbt-banner-1');
  if (banner) {
      // Remove all background-related styles
      banner.style.removeProperty('background-image');
      banner.style.removeProperty('background-position');
      banner.style.removeProperty('background-attachment');
      banner.style.removeProperty('background-origin');
      banner.style.removeProperty('background-clip');
      banner.style.removeProperty('background-color');
      banner.style.removeProperty('background-size');
      banner.style.removeProperty('background-repeat');
  }
}




})(window, document, jQuery);