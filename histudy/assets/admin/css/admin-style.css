
.appearance_page_histudy_options .redux-container .redux-main {
    margin-left: 269px;
    border-left: 1px solid rgba(0, 0, 0, .05);
    box-shadow: none;
}

.appearance_page_histudy_options .redux-sidebar {
    width: 270px;
}

.appearance_page_histudy_options .redux-container #redux-header {
    border-bottom: none;
    padding: 30px;
    background: #060e23 !important;
}
:root {
  --color-primary: #ea3940;
  --color-secondary: #10b3d6;
  --color-warning: #ffa100;
  --color-success:#0ed193;
  --color-primary-opacity: rgba(255, 69, 81, 0.40);
  --color-white: #fff;
  --color-white-2: #edeaf5;
  --color-white-3: #ececec;
  --color-white-4: #f3f2fb;
  --color-white-5: #fdfcfe;
  --color-white-6: #f4f7fb;
  --color-white-7: #f9f8ff;
  --color-black: #000;
  --color-dark-1: #04122b;
  --color-dark-3: #444;
  --color-dark-4: #192a55;
  --color-dark-blue-1: #162f6a;
  --color-dark-blue-2: #2f415f;
  --color-border-2: #ededed;
  --color-border-3: #e3e0f5;
  --color-placeholder: #2b4d9e;
  --color-body: #6b7385;
  --color-gray-2: #c1c6cb;
  --color-gray-3: #e6e6e6;
  --color-gray-5: #9facb6;
  --color-gray-6: #7085aa;
  --color-blue-2: #6e8393;
  --color-grayscale-1: #b8b8b8;
  --color-grayscale-2: #dee2ed;
  --color-grayscale-3: #d4d4d4;
  --color-grayscale-4: #efefef;
  --color-grayscale-5: #eeebff;
  --color-grayscale-7: #e5e8f0;
  --color-grayscale-8: #f3f1ff;
  --color-grayscale-11: #f8f8f8;
  --radius-full: 100%;
  --radius-xxl: 20px;
  --radius-lg: 12px;
  --f-medium: 500;
  --f-bold: 700;
  --shadow-1: 0px 10px 30px 0px rgba(19, 65, 98, 0.08);
  --transition: 0.2s;
  --font-primary: "Manrope", sans-serif;
  --font-secondary: "IBM Plex Sans", sans-serif;
  --font-size-b1: 18px;
  --font-size-b2: 16px;
  --font-size-b3: 14px;
  --font-size-b4: 12px;
  --font-size-b5: 10px;
  --h5: 24px;
  --h6: 20px;
  --line-height-b2: 1.67;
  --line-height-b3: 1.67;
  --div-gap-1: 10px;
  --gradient-primary: linear-gradient(94deg, #fe2a5e 1.16%, #ffa61b 216.18%);
  --gradient-secondary: linear-gradient(0deg, rgba(37, 132, 174, 0.00) 0%, rgba(37, 132, 174, 0.14) 100%);
}
.appearance_page_histudy_options .redux-container #redux-header h2 {
    font-style: normal;
    padding-right: 0px;
    margin: 0 !important;
    font-size: 30px !important;
    line-height: 1;
    color: #fff;
    background-image: none !important;
    background-size: auto;
    height: auto !important;
    position: relative;
    padding-left: 70px;
    text-transform: lowercase;
    padding-bottom: 4px;
}

.appearance_page_histudy_options .redux-container #redux-header h2::before {
    background-image: url(../../images/optionframework/dashbord/logo-light.png);
    content: "";
    width: 60px;
    height: 66px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    object-fit: cover;
    background-size: cover;
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input {
    padding: 10px 20px;
    text-transform: uppercase;
    font-size: 14px;
    margin-top: 0;
    border-radius: 6px;
    transition: 0.3s;
    background: no-repeat;
    box-shadow: none;
    text-shadow: none;
    outline: none !important;
    border: 2px solid #060e2330;
    color: #060e23;
    font-weight: 500;
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input:hover {
    transform: translateY(-1px);
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
}

.appearance_page_histudy_options .redux-container .redux-main .field-desc {
    font-size: 0.9em;
    color: #888;
}

.appearance_page_histudy_options .redux-container .redux-main input[type=text],
.appearance_page_histudy_options .redux-container .redux-main textarea {
    border: 1px solid rgba(0, 0, 0, .15);
    min-height: 36px;
    line-height: 36px;
    padding: 1px 15px;
}

.appearance_page_histudy_options .redux-container-image_select ul.redux-image-select li {
    margin-right: 15px !important;
}

.appearance_page_histudy_options .redux-container-image_select .redux-image-select-selected {
    background: none !important;
}

.appearance_page_histudy_options .redux-container .redux-main .input-append .add-on,
.appearance_page_histudy_options .redux-container .redux-main .input-prepend .add-on {
    width: auto;
    display: inline-block;
    min-width: 15px;
    font-size: 10px;
    font-weight: 400;
    text-align: center;
    text-shadow: none;
    background-color: #eee;
    min-height: 38px;
    padding: 0;
    margin: 0;
    line-height: 38px;
    padding-left: 10px;
    padding-right: 10px;
    border: 1px solid rgba(0, 0, 0, .15);
    border-right: none;
}

.appearance_page_histudy_options .redux-container .redux-main .mini,
.appearance_page_histudy_options .redux-container .redux-main input[type="text"].mini {
    width: 90px;
    text-align: left;
}

/* Primary */
.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input.button-primary {
    border: 2px solid #00C851;
    background: #00C851;
    color: #ffffff;
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input.button-primary:hover {
    box-shadow: 0 10px 15px 0 rgba(0, 200, 81, 0.2);
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input#redux-defaults-section-top:hover,
.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input#redux-defaults-section-bottom:hover {
    border: 2px solid #ffbb33;
    background: #ffbb33;
    color: #ffffff;
    box-shadow: 0 10px 15px 0 rgba(255, 187, 51, 0.2);
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input#redux-defaults-top:hover,
.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input#redux-defaults-bottom:hover {
    border: 2px solid #ff4444;
    background: #ff2a3d;
    color: #ffffff;
    box-shadow: 0 10px 15px 0 rgba(255, 68, 68, 0.2);
}

.appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input+input {
    margin-left: 15px;
}

.appearance_page_histudy_options .redux-main #redux-sticky #info_bar {
    height: auto;
    align-items: center;
    display: flex;
    justify-content: flex-end;
    box-shadow: none;
    border-bottom: 1px solid rgba(0, 0, 0, .05);
    background: #ffffff;
    padding-top: 15px;
    padding-bottom: 15px;
    padding-right: 15px;
    padding-left: 15px;
}

.appearance_page_histudy_options .redux-container .form-table>tbody>tr>th {
    border-right: 1px solid rgba(0, 0, 0, .05);
    padding-right: 30px;
    padding-left: 30px;
}

.appearance_page_histudy_options .redux-container .form-table td {
    padding-left: 30px;
    padding-right: 30px;
}

.appearance_page_histudy_options .redux-container .redux-group-tab h2 {
    background: rgba(255, 26, 80, 0.03);
    margin: 0;
    padding: 30px;
    font-size: 26px;
    padding-bottom: 35px;
    color: #f9004d;
    border-bottom: 1px solid rgba(0, 0, 0, .05);
}

.appearance_page_histudy_options .redux-container .redux-main {
    padding: 0;
}

.appearance_page_histudy_options .redux-main #redux-sticky {
    padding: 0;
    margin: 0;
}

.appearance_page_histudy_options .redux-container .redux-group-tab {
    margin: 0;
}

.appearance_page_histudy_options .redux-container .redux-main .form-table tr {
    border-bottom: 1px solid rgba(0, 0, 0, .05);
}

.appearance_page_histudy_options .redux-main #redux-footer-sticky {
    margin: 0;
}

.appearance_page_histudy_options .redux-container #redux-footer {
    border-top: 1px solid rgba(0, 0, 0, .05) !important;
    background: #ffffff !important;
}

.appearance_page_histudy_options .redux-container .redux_field_th {
    font-size: 20px;
}

.appearance_page_histudy_options .redux-container .redux-main span.description {
    color: #717173;
    line-height: 1.5 !important;
    font-size: 14px;
    margin-top: 10px;
    margin-bottom: 20px;
}

.appearance_page_histudy_options .redux-container .notice-yellow {
    margin: 0;
    border-bottom: 1px solid rgba(0, 0, 0, .05) !important;
    background-color: rgba(255, 187, 51, 0.2);
    color: #FF8800;
    padding: 15px 30px 15px 30px;
    text-shadow: none;
}

.appearance_page_histudy_options .redux-container .notice-green {
    margin: 0;
    border-bottom: 1px solid rgba(0, 0, 0, .05) !important;
    background-color: rgba(0, 200, 81, 0.2);
    color: #007E33;
    padding: 15px 30px 15px 30px;
    text-shadow: none;
}

.appearance_page_histudy_options .redux-action_bar {
    display: flex;
    align-items: center;
}

.appearance_page_histudy_options .redux-container .redux-action_bar .spinner {
    margin-top: 0;
}

/* Sidebar */
.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu,
.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection {
    background: #060e23 !important;
    margin-bottom: 0;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li.active a {
    background-color: #f9004d !important;
    background: linear-gradient(90deg, #f61b10, #ef0963);
    color: #fff !important;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li a {
    display: block;
    width: 100% !important;
    padding: 15px 20px 15px 30px;
    margin: 0;
    border: none;
    position: relative;
    font-size: 16px;
    font-weight: 400 !important;
    opacity: 1;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    background: transparent;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li.hasSubSections.activeChild>a {
    background-color: #f9004d !important;
    background: linear-gradient(90deg, #f61b10, #ef0963);
    color: #fff !important;
    text-shadow: none;
    opacity: 1 !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection .redux-group-tab-link-li .redux-group-tab-link-a {
    background: #000000 !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection .redux-group-tab-link-li .redux-group-tab-link-a:hover i,
.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection .redux-group-tab-link-li .redux-group-tab-link-a:hover span {
    color: #f9004d !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection .redux-group-tab-link-li .redux-group-tab-link-a:hover {
    color: #fff !important;
}

.appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon.active i,
.appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon.active a {
    color: #f9004d !important;
}

.appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon.active {
    position: relative;
    background: rgba(255, 26, 80, 0.03) !important;
}

.appearance_page_histudy_options .redux-group-menu .subsection {
    background: rgba(255, 26, 80, 0.03) !important;
}

.appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.active a,
.appearance_page_histudy_options .wp-customizer .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.active a {
    background: transparent;
    text-shadow: none;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: transparent;
}

.appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a,
.redux-sidebar .redux-group-menu li.active.hasSubSections ul.subsection li.hasIcon a,
.redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a {
    position: relative;
    padding-left: 35px !important;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li a,
.redux-sidebar .redux-group-menu li.active.hasSubSections ul.subsection li.hasIcon a,
.redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a {
    padding: 17px 20px 17px 30px !important;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li a i {
    top: 50%;
    font-size: 14px;
    color: #ffffff;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: 0.3s;
    -webkit-transition: 0.3s;
    -moz-transition: 0.3s;
    -ms-transition: 0.3s;
    -o-transition: 0.3s;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li a span.extraIconSubsections i:before {
    display: none;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li a span.extraIconSubsections i:after {
    content: url(../../images/optionframework/dashbord/arrow-icon.png);
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li a:hover i:after {
    filter: contrast(1000%);
}

.appearance_page_histudy_options .redux-sidebar .redux-group-tab-link-a span.group_title,
.redux-sidebar .redux-group-menu li.active.hasSubSections ul.subsection li.hasIcon a span.group_title,
.redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a span.group_title {
    padding-left: 25px !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li.activeChild.hasSubSections .subsection li.active a {
    font-weight: inherit !important;
    transition: 0.3s !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li a span {
    transition: 0.3s !important;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li a:hover {
    background-color: #f9004d !important;
    background: linear-gradient(90deg, #f61b10, #ef0963);
    color: #fff !important;
    opacity: 1 !important;
    text-shadow: none !important;
}

.appearance_page_histudy_options.wp-admin .redux-sidebar .subsection .redux-group-tab-link-li+.redux-group-tab-link-li {
    border-top: 1px solid #150808 !important;
}

/* Switcher Button */
.appearance_page_histudy_options .redux-container-switch .cb-enable.selected,
.appearance_page_histudy_options .redux-field-container .ui-buttonset .ui-state-active {
    border-color: #f61b10 !important;
    box-shadow: 0 10px 15px 0 rgba(255, 68, 68, 0.2) !important;
    background: linear-gradient(90deg, #f61b10, #ef0963) !important;
}

.appearance_page_histudy_options input[type=checkbox]:focus,
.appearance_page_histudy_options input[type=color]:focus,
.appearance_page_histudy_options input[type=date]:focus,
.appearance_page_histudy_options input[type=datetime-local]:focus,
.appearance_page_histudy_options input[type=datetime]:focus,
.appearance_page_histudy_options input[type=email]:focus,
.appearance_page_histudy_options input[type=month]:focus,
.appearance_page_histudy_options input[type=number]:focus,
.appearance_page_histudy_options input[type=password]:focus,
.appearance_page_histudy_options input[type=radio]:focus,
.appearance_page_histudy_options input[type=search]:focus,
.appearance_page_histudy_options input[type=tel]:focus,
.appearance_page_histudy_options input[type=text]:focus,
.appearance_page_histudy_options input[type=time]:focus,
.appearance_page_histudy_options input[type=url]:focus,
.appearance_page_histudy_options input[type=week]:focus,
.appearance_page_histudy_options select:focus,
.appearance_page_histudy_options textarea:focus {
    border-color: #f9004d !important;
    box-shadow: 0 2px 6px 0 rgba(255, 68, 68, 0.2) !important;
    outline: 2px solid transparent;
}

.appearance_page_histudy_options .redux-container-image_select .redux-image-select-selected img {
    border-color: #f9004d !important;
    box-shadow: 0 5px 10px 0 rgba(255, 68, 68, 0.2) !important;
}

.appearance_page_histudy_options .redux-container-image_select .redux-image-select img,
.appearance_page_histudy_options .redux-container-image_select .redux-image-select-selected img,
.appearance_page_histudy_options .redux-container-image_select .redux-image-select .tiles,
.appearance_page_histudy_options .redux-container-image_select .redux-image-select-selected .tiles {
    border-width: 3px;
    border-radius: 4px;
    box-sizing: border-box;
}

.appearance_page_histudy_options .redux-container .redux-sidebar .redux-group-menu li .subsection .redux-group-tab-link-li .redux-group-tab-link-a {
    font-weight: inherit !important;
    color: #ffffff !important;
    background-color: #000000 !important;
}


@media screen and (max-width: 782px) {
    .appearance_page_histudy_options .redux-sidebar {
        width: 220px;
    }

    .appearance_page_histudy_options .redux-container .redux-main {
        margin-left: 219px;
    }

    .redux-container .form-table>tbody>tr>th {
        width: auto;
    }

    .appearance_page_histudy_options .redux-container #redux-header {
        padding: 10px 30px;
    }
}

@media screen and (max-width: 600px) {
    .appearance_page_histudy_options .redux-sidebar {
        width: auto;
    }

    .appearance_page_histudy_options .redux-container .redux-main {
        margin-left: 44px;
    }

    .appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li a {
        padding: 15px !important;
        padding-bottom: 5px !important;
    }

    .appearance_page_histudy_options.admin-color-fresh .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a,
    .redux-sidebar .redux-group-menu li.active.hasSubSections ul.subsection li.hasIcon a,
    .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a {
        padding-left: 15px !important;
    }

    .appearance_page_histudy_options.wp-admin .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li a,
    .redux-sidebar .redux-group-menu li.active.hasSubSections ul.subsection li.hasIcon a,
    .redux-sidebar .redux-group-menu li.activeChild.hasSubSections ul.subsection li.hasIcon a {
        padding: 15px !important;
    }

    .appearance_page_histudy_options .redux-action_bar {
        flex-wrap: wrap;
    }

    .appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input {
        padding: 3px 7px;
        font-size: 10px;
        border-radius: 4px;
        min-height: 30px;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .appearance_page_histudy_options .redux-container .redux-main .redux-action_bar input+input {
        margin-left: 5px;
    }

    .appearance_page_histudy_options .redux-container #redux-header {
        padding: 10px 30px;
    }
}


.redux-container .ui-corner-all {
    display: none !important;
}




.ocdi__gl-item.active_demo::before {
    content: "Activated Demo \2714";
    position: absolute;
    left: 15px;
    top: 15px;
    background: linear-gradient(120deg, #1C99FE 20.69%, #7644FF 50.19%, #FD4766 79.69%);
    z-index: 9;
    color: #fff;
    padding: 10px 20px 13px 20px;
    text-transform: uppercase;
    border-radius: 4px;
    line-height: 1;
    font-weight: 500;
}

.ocdi__gl-item.active_demo {
    position: relative;
}

.ocdi__gl-item.active_demo {
    background: linear-gradient(120deg, #1C99FE 20.69%, #7644FF 50.19%, #FD4766 79.69%);
    padding: 3px;
}

.ocdi__gl-item {
    border-radius: 10px;
    overflow: hidden;
    border: 0px solid transparent;
    box-shadow: 0px 14px 30px #d3d4d7;
}

.ocdi__gl-item-image-container {
    border-radius: 10px 10px 0 0;
}

.ocdi__gl-item-footer.ocdi__gl-item-footer--with-preview {
    border-radius: 0 0 10px 10px;
    background: #fff;
}

.ocdi__theme-about {
    padding: 15px;
    box-shadow: 0px 14px 30px #d3d4d7;
    border-radius: 10px;
}

#rainbow_options-rainbow_project_popup_layout li.redux-image-select {
    width: calc(33.33% - 20px);
}

.redux-container .redux-group-tab .redux-section-desc {
    margin-bottom: 0;
    color: #666;
    padding: 10px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, .05);
}




/* New */
.select2-container--default .select2-selection--single {
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    outline: 0;
}

.select2-container--default .select2-selection--single:focus {
    border: 1px solid rgba(0, 0, 0, .15)
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #666;
    line-height: 40px;
    font-size: 14px;
    padding-left: 15px;
    padding-right: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 500;
    height: 100%;
    margin-right: 40px;
    font-size: 13px;
    color: #666;
    min-width: 25px;
}

.select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: #333;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #999;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    background-color: #ffffff;
    border: none;
    border-left: 1px solid rgba(0, 0, 0, .15);
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 38px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 35px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #666 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}



.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    border: none;
    border-right: 1px solid rgba(0, 0, 0, .15);
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    left: 1px;
    right: auto;
}

.select2-container--default.select2-container--open .select2-selection--single {
    border: 1px solid rgba(0, 0, 0, .15)
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow {
    background: transparent;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #666 transparent;
    border-width: 0 4px 5px 4px;
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection--single {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.select2-container--default .select2-selection--multiple {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    cursor: text;
    outline: 0;
    min-height: 40px;
    padding: 3px;
}

.select2-container--default .select2-selection--multiple:focus {
    border: 1px solid rgba(0, 0, 0, .15)
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    display: none;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: rgba(0, 0, 0, .15);
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    display: inline-block;
    padding: 0;
    font-size: 14px;
    margin: 5px !important;
    line-height: 12px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    cursor: default;
    padding-left: 2px;
    padding-right: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    background-color: transparent;
    border: none;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    color: #666;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    padding: 0 4px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #555;
    outline: none;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__display {
    padding-left: 5px;
    padding-right: 2px;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.select2-container--default.select2-container--open .select2-selection--multiple {
    border: 1px solid rgba(0, 0, 0, .15)
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid rgba(0, 0, 0, .15);
    outline: 0;
    border-radius: 4px;
    font-size: 14px;
    padding: 7px 10px;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus,
.select2-container--default .select2-search--dropdown .select2-search__field:active {
    border-color: #f9004d !important;
}

.select2-search--dropdown {
    display: block;
    padding: 5px 15px;
    margin-top: 5px;
}

.select2-results__option {
    font-size: 14px;
    color: #666;
    border-radius: 4px;
    margin: 5px;

}

.select2-container--default .select2-search--inline .select2-search__field {
    outline: 0;
    box-shadow: none;
}

.select2-container--default .select2-dropdown {
    background-color: white;
    border: 1px solid transparent;
}

.select2-container--default .select2-dropdown--above {
    border-bottom: none;
}

.select2-container--default .select2-dropdown--below {
    border-top: none;
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}

.select2-container--default .select2-results__option--group {
    padding: 0;
}

.select2-container--default .select2-results__option--disabled {
    color: #666;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background-color: #f9004d !important;
    color: white;
}

.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px;
}

.select2-container--default.select2-container--open .select2-dropdown {
    border-color: rgba(0, 0, 0, .15);
}


.select2-container .select2-selection--single {
    height: 40px;
    line-height: 40px;
}

.select2-container .select2-search--inline .select2-search__field {
    height: 30px;
}

.appearance_page_histudy_options .redux-field-container .ui-buttonset .ui-state-active.ui-corner-right {
    box-shadow: 0 10px 15px 0 rgb(0 0 0 / 20%) !important;
    background-color: #646464 !important;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #929292), color-stop(100%, #646464)) !important;
    background-image: -webkit-linear-gradient(top, #929292, #646464) !important;
    border-color: #767676 !important;
}

.appearance_page_histudy_options .redux-container .redux-main .redux-field-container {
    padding: 20px 0 !important;
}

.appearance_page_histudy_options .redux-main .redux-typography-container .input_wrapper {
    margin: 5px 5px 5px 0;
}

.appearance_page_histudy_options .redux-container .redux-main .input-append .add-on,
.appearance_page_histudy_options .redux-container .redux-main .input-prepend .add-on {
    min-height: 36px !important;
    line-height: 36px !important;
    padding: 0 10px !important;
    margin: 0 !important;
    border: none;
    background: rgba(0, 0, 0, .15);
}

.appearance_page_histudy_options .redux-container #redux-header span {
    display: inline-block;
    margin-inline-start: 10px;
    padding: 4px 8px;
    border: 2px solid rgba(252, 252, 252, 0.13);
    border-radius: 4px;
    font-size: 15px;
    line-height: 15px;
    color: rgba(255, 255, 255, 0.6) !important;
    margin-top: 4px;
}

.appearance_page_histudy_options .redux-container #redux-header .display_header {
    display: flex;
    align-items: center;
}

ul.redux-image-select {
    display: flex;
    flex-wrap: wrap;
    grid-area: 30px;
}

.redux-container .redux-main .form-table {
    width: auto;
}

.redux-container-repeater .redux-repeater-accordion-repeater h3.ui-accordion-header {
    display: block !important;
}

span.select2-selection.select2-selection--multiple .select2-search__field {
    border: 0;
    box-shadow: none !important;
}

.rbt-no-pointer-activity {
    pointer-events: none;
    opacity: .5;
}

/**
 * License css
*/
/* Admin CSS*/
.el-license-container {
    margin: 20px;
    padding: 35px;
    background: #fff;
    border-radius: 4px
}

.el-license-container .el-license-field {
    display: block;
    margin-bottom: 15px
}

.el-license-container .el-license-field input {
    font-size: 200%;
    padding: 8px 10px 10px
}

.el-license-container .notice-error,
.el-license-container div.error {
    background: rgba(220, 50, 50, 0.11);
    margin: 0
}

.el-license-container .el-license-title {
    margin-top: 0;
    font-size: 30px
}
.el-license-container .el-license-title img {
    vertical-align: middle;
    margin-top: -5px;
    margin-right: 2px;
}
.el-license-container p {
    color: #717189;
    font-size: 16px;
}
a.rbt-tab-content-link img {
  transition: all .3s;
}
.el-license-field label {
    color: #717188;
    font-size: 16px;
    display: block;
    padding-bottom: 10px;
}
.rbt-tab-buttons a:focus {
  box-shadow: none;
  border: 0;
}
.el-license-active-btn input#submit {
  background: #3458F0;
  padding: 1px 19px;
  font-size: 20px;
}
.el-license-container .el-license-info li {
    list-style: none;
    padding: 0
}

.el-license-container .el-license-info-title {
    width: 150px;
    display: inline-block;
    position: relative;
    padding-right: 5px
}
.rbt-license-tab-form .el-license-container .el-license-field input {
    border: 2px solid rgba(9, 2, 47, 0.1);
    border-radius: 10px;
    height: 70px;
}
.el-license-container .el-license-info-title:after {
    content: ":";
    position: absolute;
    right: 2px
}

.el-license-container .el-license-valid,
.el-license-container .el-license-invalid {
    padding: 0 5px 2px;
    color: #fff;
    background-color: #8fcc77;
    border-radius: 3px
}

.el-license-container .el-license-invalid {
    background-color: #f44336
}

.el-license-container .el-license-key {
    font-weight: 700;
    opacity: .8
}

.el-license-container .el-green-btn {
    padding: 0 5px 2px;
    color: #fff;
    background-color: #8fcc77;
    border-radius: 3px;
    text-decoration: none;
    -webkit-box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38);
    -moz-box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38);
    box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38)
}

.el-license-container .el-green-btn:hover {
    color: #fff;
    background-color: #84bc6c
}

.el-license-container .el-blue-btn {
    padding: 0 5px 2px;
    color: #fff;
    background-color: #20b1d2;
    border-radius: 3px;
    text-decoration: none;
    -webkit-box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38);
    -moz-box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38);
    box-shadow: 0 0 3px -1px rgba(0, 0, 0, 0.38)
}

.el-license-container .el-blue-btn:hover {
    color: #fff;
    background-color: #219dbf
}

.el-license-container .el-license-field label {
    display: block;
    margin-bottom: 5px
}

.el-license-container .el-license-active-btn {
    margin-top: 25px
}


.rbt-inactive--page .row {
    display: flex;
}

.rbt-inactive--page .row .col {
    width: 50%;
}

.rainbow-theme-not-activated .redux-container form.redux-form-wrapper#redux-form-wrapper {
    display: none;
}

.rainbow-theme-not-activated .redux-container {
    position: relative;
    height: 500px;
}

.rainbow-theme-not-activated .redux-container::after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #d5d5d5;
    content: "Please activate theme";
    font-size: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}
.rbt-doc-btn {
	background: #524cff;
	padding: 20px 30px;
	display: inline-block;
	margin-bottom: 30px;
	margin-top: 27px;
	color: #fff;
	border-radius: 30px;
	text-decoration: none;
	font-size: 20px;
	font-weight: 500;
}
.rbt-doc-btn:hover, .rbt-doc-btn:focus {
    color: #fff;
    outline: none;
}
.rbt-mt-0-i {
    margin-top: 0 !important;
}

.p-0 {
    padding: 0 !important;
}

/**
===============================================
* Dashboard CSS
===============================================
*/
.rbt-text-start {
    text-align: left;
}

.rbt-text-center {
    text-align: center;
}

.rbt-text-right {
    text-align: right;
}
.rbt-plugin-card {
    position: relative;
}
.rbt-plugin-card span.alert.alert-warning {
    position: absolute;
    right: 10px;
    top: 5px;
}
.rbt-row {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 24px;
}

/* Grid CSS */
/*!
 * Bootstrap Grid v5.0.2 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
 .container,
 .container-fluid,
 .container-xxl,
 .container-xl,
 .container-lg,
 .container-md,
 .container-sm {
   width: 100%;
   padding-right: var(--bs-gutter-x, 0.75rem);
   padding-left: var(--bs-gutter-x, 0.75rem);
   margin-right: auto;
   margin-left: auto;
 }
 
 @media (min-width: 576px) {
   .container-sm, .container {
     max-width: 540px;
   }
 }
 @media (min-width: 768px) {
   .container-md, .container-sm, .container {
     max-width: 720px;
   }
 }
 @media (min-width: 992px) {
   .container-lg, .container-md, .container-sm, .container {
     max-width: 960px;
   }
 }
 @media (min-width: 1200px) {
   .container-xl, .container-lg, .container-md, .container-sm, .container {
     max-width: 1140px;
   }
 }
 @media (min-width: 1400px) {
   .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
     max-width: 1320px;
   }
 }
 .row {
   --bs-gutter-x: 1.5rem;
   --bs-gutter-y: 0;
   display: flex;
   flex-wrap: wrap;
   margin-top: calc(var(--bs-gutter-y) * -1);
   margin-right: calc(var(--bs-gutter-x) * -.5);
   margin-left: calc(var(--bs-gutter-x) * -.5);
 }
 .row > * {
   box-sizing: border-box;
   flex-shrink: 0;
   width: 100%;
   max-width: 100%;
   padding-right: calc(var(--bs-gutter-x) * .5);
   padding-left: calc(var(--bs-gutter-x) * .5);
   margin-top: var(--bs-gutter-y);
 }
 
 .col {
   flex: 1 0 0%;
 }
 
 .row-cols-auto > * {
   flex: 0 0 auto;
   width: auto;
 }
 
 .row-cols-1 > * {
   flex: 0 0 auto;
   width: 100%;
 }
 
 .row-cols-2 > * {
   flex: 0 0 auto;
   width: 50%;
 }
 
 .row-cols-3 > * {
   flex: 0 0 auto;
   width: 33.3333333333%;
 }
 
 .row-cols-4 > * {
   flex: 0 0 auto;
   width: 25%;
 }
 
 .row-cols-5 > * {
   flex: 0 0 auto;
   width: 20%;
 }
 
 .row-cols-6 > * {
   flex: 0 0 auto;
   width: 16.6666666667%;
 }
 
 @media (min-width: 576px) {
   .col-sm {
     flex: 1 0 0%;
   }
 
   .row-cols-sm-auto > * {
     flex: 0 0 auto;
     width: auto;
   }
 
   .row-cols-sm-1 > * {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .row-cols-sm-2 > * {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .row-cols-sm-3 > * {
     flex: 0 0 auto;
     width: 33.3333333333%;
   }
 
   .row-cols-sm-4 > * {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .row-cols-sm-5 > * {
     flex: 0 0 auto;
     width: 20%;
   }
 
   .row-cols-sm-6 > * {
     flex: 0 0 auto;
     width: 16.6666666667%;
   }
 }
 @media (min-width: 768px) {
   .col-md {
     flex: 1 0 0%;
   }
 
   .row-cols-md-auto > * {
     flex: 0 0 auto;
     width: auto;
   }
 
   .row-cols-md-1 > * {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .row-cols-md-2 > * {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .row-cols-md-3 > * {
     flex: 0 0 auto;
     width: 33.3333333333%;
   }
 
   .row-cols-md-4 > * {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .row-cols-md-5 > * {
     flex: 0 0 auto;
     width: 20%;
   }
 
   .row-cols-md-6 > * {
     flex: 0 0 auto;
     width: 16.6666666667%;
   }
 }
 @media (min-width: 992px) {
   .col-lg {
     flex: 1 0 0%;
   }
 
   .row-cols-lg-auto > * {
     flex: 0 0 auto;
     width: auto;
   }
 
   .row-cols-lg-1 > * {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .row-cols-lg-2 > * {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .row-cols-lg-3 > * {
     flex: 0 0 auto;
     width: 33.3333333333%;
   }
 
   .row-cols-lg-4 > * {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .row-cols-lg-5 > * {
     flex: 0 0 auto;
     width: 20%;
   }
 
   .row-cols-lg-6 > * {
     flex: 0 0 auto;
     width: 16.6666666667%;
   }
 }
 @media (min-width: 1200px) {
   .col-xl {
     flex: 1 0 0%;
   }
 
   .row-cols-xl-auto > * {
     flex: 0 0 auto;
     width: auto;
   }
 
   .row-cols-xl-1 > * {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .row-cols-xl-2 > * {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .row-cols-xl-3 > * {
     flex: 0 0 auto;
     width: 33.3333333333%;
   }
 
   .row-cols-xl-4 > * {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .row-cols-xl-5 > * {
     flex: 0 0 auto;
     width: 20%;
   }
 
   .row-cols-xl-6 > * {
     flex: 0 0 auto;
     width: 16.6666666667%;
   }
 }
 @media (min-width: 1400px) {
   .col-xxl {
     flex: 1 0 0%;
   }
 
   .row-cols-xxl-auto > * {
     flex: 0 0 auto;
     width: auto;
   }
 
   .row-cols-xxl-1 > * {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .row-cols-xxl-2 > * {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .row-cols-xxl-3 > * {
     flex: 0 0 auto;
     width: 33.3333333333%;
   }
 
   .row-cols-xxl-4 > * {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .row-cols-xxl-5 > * {
     flex: 0 0 auto;
     width: 20%;
   }
 
   .row-cols-xxl-6 > * {
     flex: 0 0 auto;
     width: 16.6666666667%;
   }
 }
 .col-auto {
   flex: 0 0 auto;
   width: auto;
 }
 
 .col-1 {
   flex: 0 0 auto;
   width: 8.33333333%;
 }
 
 .col-2 {
   flex: 0 0 auto;
   width: 16.66666667%;
 }
 
 .col-3 {
   flex: 0 0 auto;
   width: 25%;
 }
 
 .col-4 {
   flex: 0 0 auto;
   width: 33.33333333%;
 }
 
 .col-5 {
   flex: 0 0 auto;
   width: 41.66666667%;
 }
 
 .col-6 {
   flex: 0 0 auto;
   width: 50%;
 }
 
 .col-7 {
   flex: 0 0 auto;
   width: 58.33333333%;
 }
 
 .col-8 {
   flex: 0 0 auto;
   width: 66.66666667%;
 }
 
 .col-9 {
   flex: 0 0 auto;
   width: 75%;
 }
 
 .col-10 {
   flex: 0 0 auto;
   width: 83.33333333%;
 }
 
 .col-11 {
   flex: 0 0 auto;
   width: 91.66666667%;
 }
 
 .col-12 {
   flex: 0 0 auto;
   width: 100%;
 }
 
 .offset-1 {
   margin-left: 8.33333333%;
 }
 
 .offset-2 {
   margin-left: 16.66666667%;
 }
 
 .offset-3 {
   margin-left: 25%;
 }
 
 .offset-4 {
   margin-left: 33.33333333%;
 }
 
 .offset-5 {
   margin-left: 41.66666667%;
 }
 
 .offset-6 {
   margin-left: 50%;
 }
 
 .offset-7 {
   margin-left: 58.33333333%;
 }
 
 .offset-8 {
   margin-left: 66.66666667%;
 }
 
 .offset-9 {
   margin-left: 75%;
 }
 
 .offset-10 {
   margin-left: 83.33333333%;
 }
 
 .offset-11 {
   margin-left: 91.66666667%;
 }
 
 .g-0,
 .gx-0 {
   --bs-gutter-x: 0;
 }
 
 .g-0,
 .gy-0 {
   --bs-gutter-y: 0;
 }
 
 .g-1,
 .gx-1 {
   --bs-gutter-x: 0.25rem;
 }
 
 .g-1,
 .gy-1 {
   --bs-gutter-y: 0.25rem;
 }
 
 .g-2,
 .gx-2 {
   --bs-gutter-x: 0.5rem;
 }
 
 .g-2,
 .gy-2 {
   --bs-gutter-y: 0.5rem;
 }
 
 .g-3,
 .gx-3 {
   --bs-gutter-x: 1rem;
 }
 
 .g-3,
 .gy-3 {
   --bs-gutter-y: 1rem;
 }
 
 .g-4,
 .gx-4 {
   --bs-gutter-x: 1.5rem;
 }
 
 .g-4,
 .gy-4 {
   --bs-gutter-y: 1.5rem;
 }
 
 .g-5,
 .gx-5 {
   --bs-gutter-x: 3rem;
 }
 
 .g-5,
 .gy-5 {
   --bs-gutter-y: 3rem;
 }
 
 @media (min-width: 576px) {
   .col-sm-auto {
     flex: 0 0 auto;
     width: auto;
   }
 
   .col-sm-1 {
     flex: 0 0 auto;
     width: 8.33333333%;
   }
 
   .col-sm-2 {
     flex: 0 0 auto;
     width: 16.66666667%;
   }
 
   .col-sm-3 {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .col-sm-4 {
     flex: 0 0 auto;
     width: 33.33333333%;
   }
 
   .col-sm-5 {
     flex: 0 0 auto;
     width: 41.66666667%;
   }
 
   .col-sm-6 {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .col-sm-7 {
     flex: 0 0 auto;
     width: 58.33333333%;
   }
 
   .col-sm-8 {
     flex: 0 0 auto;
     width: 66.66666667%;
   }
 
   .col-sm-9 {
     flex: 0 0 auto;
     width: 75%;
   }
 
   .col-sm-10 {
     flex: 0 0 auto;
     width: 83.33333333%;
   }
 
   .col-sm-11 {
     flex: 0 0 auto;
     width: 91.66666667%;
   }
 
   .col-sm-12 {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .offset-sm-0 {
     margin-left: 0;
   }
 
   .offset-sm-1 {
     margin-left: 8.33333333%;
   }
 
   .offset-sm-2 {
     margin-left: 16.66666667%;
   }
 
   .offset-sm-3 {
     margin-left: 25%;
   }
 
   .offset-sm-4 {
     margin-left: 33.33333333%;
   }
 
   .offset-sm-5 {
     margin-left: 41.66666667%;
   }
 
   .offset-sm-6 {
     margin-left: 50%;
   }
 
   .offset-sm-7 {
     margin-left: 58.33333333%;
   }
 
   .offset-sm-8 {
     margin-left: 66.66666667%;
   }
 
   .offset-sm-9 {
     margin-left: 75%;
   }
 
   .offset-sm-10 {
     margin-left: 83.33333333%;
   }
 
   .offset-sm-11 {
     margin-left: 91.66666667%;
   }
 
   .g-sm-0,
 .gx-sm-0 {
     --bs-gutter-x: 0;
   }
 
   .g-sm-0,
 .gy-sm-0 {
     --bs-gutter-y: 0;
   }
 
   .g-sm-1,
 .gx-sm-1 {
     --bs-gutter-x: 0.25rem;
   }
 
   .g-sm-1,
 .gy-sm-1 {
     --bs-gutter-y: 0.25rem;
   }
 
   .g-sm-2,
 .gx-sm-2 {
     --bs-gutter-x: 0.5rem;
   }
 
   .g-sm-2,
 .gy-sm-2 {
     --bs-gutter-y: 0.5rem;
   }
 
   .g-sm-3,
 .gx-sm-3 {
     --bs-gutter-x: 1rem;
   }
 
   .g-sm-3,
 .gy-sm-3 {
     --bs-gutter-y: 1rem;
   }
 
   .g-sm-4,
 .gx-sm-4 {
     --bs-gutter-x: 1.5rem;
   }
 
   .g-sm-4,
 .gy-sm-4 {
     --bs-gutter-y: 1.5rem;
   }
 
   .g-sm-5,
 .gx-sm-5 {
     --bs-gutter-x: 3rem;
   }
 
   .g-sm-5,
 .gy-sm-5 {
     --bs-gutter-y: 3rem;
   }
 }
 @media (min-width: 768px) {
   .col-md-auto {
     flex: 0 0 auto;
     width: auto;
   }
 
   .col-md-1 {
     flex: 0 0 auto;
     width: 8.33333333%;
   }
 
   .col-md-2 {
     flex: 0 0 auto;
     width: 16.66666667%;
   }
 
   .col-md-3 {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .col-md-4 {
     flex: 0 0 auto;
     width: 33.33333333%;
   }
 
   .col-md-5 {
     flex: 0 0 auto;
     width: 41.66666667%;
   }
 
   .col-md-6 {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .col-md-7 {
     flex: 0 0 auto;
     width: 58.33333333%;
   }
 
   .col-md-8 {
     flex: 0 0 auto;
     width: 66.66666667%;
   }
 
   .col-md-9 {
     flex: 0 0 auto;
     width: 75%;
   }
 
   .col-md-10 {
     flex: 0 0 auto;
     width: 83.33333333%;
   }
 
   .col-md-11 {
     flex: 0 0 auto;
     width: 91.66666667%;
   }
 
   .col-md-12 {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .offset-md-0 {
     margin-left: 0;
   }
 
   .offset-md-1 {
     margin-left: 8.33333333%;
   }
 
   .offset-md-2 {
     margin-left: 16.66666667%;
   }
 
   .offset-md-3 {
     margin-left: 25%;
   }
 
   .offset-md-4 {
     margin-left: 33.33333333%;
   }
 
   .offset-md-5 {
     margin-left: 41.66666667%;
   }
 
   .offset-md-6 {
     margin-left: 50%;
   }
 
   .offset-md-7 {
     margin-left: 58.33333333%;
   }
 
   .offset-md-8 {
     margin-left: 66.66666667%;
   }
 
   .offset-md-9 {
     margin-left: 75%;
   }
 
   .offset-md-10 {
     margin-left: 83.33333333%;
   }
 
   .offset-md-11 {
     margin-left: 91.66666667%;
   }
 
   .g-md-0,
 .gx-md-0 {
     --bs-gutter-x: 0;
   }
 
   .g-md-0,
 .gy-md-0 {
     --bs-gutter-y: 0;
   }
 
   .g-md-1,
 .gx-md-1 {
     --bs-gutter-x: 0.25rem;
   }
 
   .g-md-1,
 .gy-md-1 {
     --bs-gutter-y: 0.25rem;
   }
 
   .g-md-2,
 .gx-md-2 {
     --bs-gutter-x: 0.5rem;
   }
 
   .g-md-2,
 .gy-md-2 {
     --bs-gutter-y: 0.5rem;
   }
 
   .g-md-3,
 .gx-md-3 {
     --bs-gutter-x: 1rem;
   }
 
   .g-md-3,
 .gy-md-3 {
     --bs-gutter-y: 1rem;
   }
 
   .g-md-4,
 .gx-md-4 {
     --bs-gutter-x: 1.5rem;
   }
 
   .g-md-4,
 .gy-md-4 {
     --bs-gutter-y: 1.5rem;
   }
 
   .g-md-5,
 .gx-md-5 {
     --bs-gutter-x: 3rem;
   }
 
   .g-md-5,
 .gy-md-5 {
     --bs-gutter-y: 3rem;
   }
 }
 @media (min-width: 992px) {
   .col-lg-auto {
     flex: 0 0 auto;
     width: auto;
   }
 
   .col-lg-1 {
     flex: 0 0 auto;
     width: 8.33333333%;
   }
 
   .col-lg-2 {
     flex: 0 0 auto;
     width: 16.66666667%;
   }
 
   .col-lg-3 {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .col-lg-4 {
     flex: 0 0 auto;
     width: 33.33333333%;
   }
 
   .col-lg-5 {
     flex: 0 0 auto;
     width: 41.66666667%;
   }
 
   .col-lg-6 {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .col-lg-7 {
     flex: 0 0 auto;
     width: 58.33333333%;
   }
 
   .col-lg-8 {
     flex: 0 0 auto;
     width: 66.66666667%;
   }
 
   .col-lg-9 {
     flex: 0 0 auto;
     width: 75%;
   }
 
   .col-lg-10 {
     flex: 0 0 auto;
     width: 83.33333333%;
   }
 
   .col-lg-11 {
     flex: 0 0 auto;
     width: 91.66666667%;
   }
 
   .col-lg-12 {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .offset-lg-0 {
     margin-left: 0;
   }
 
   .offset-lg-1 {
     margin-left: 8.33333333%;
   }
 
   .offset-lg-2 {
     margin-left: 16.66666667%;
   }
 
   .offset-lg-3 {
     margin-left: 25%;
   }
 
   .offset-lg-4 {
     margin-left: 33.33333333%;
   }
 
   .offset-lg-5 {
     margin-left: 41.66666667%;
   }
 
   .offset-lg-6 {
     margin-left: 50%;
   }
 
   .offset-lg-7 {
     margin-left: 58.33333333%;
   }
 
   .offset-lg-8 {
     margin-left: 66.66666667%;
   }
 
   .offset-lg-9 {
     margin-left: 75%;
   }
 
   .offset-lg-10 {
     margin-left: 83.33333333%;
   }
 
   .offset-lg-11 {
     margin-left: 91.66666667%;
   }
 
   .g-lg-0,
 .gx-lg-0 {
     --bs-gutter-x: 0;
   }
 
   .g-lg-0,
 .gy-lg-0 {
     --bs-gutter-y: 0;
   }
 
   .g-lg-1,
 .gx-lg-1 {
     --bs-gutter-x: 0.25rem;
   }
 
   .g-lg-1,
 .gy-lg-1 {
     --bs-gutter-y: 0.25rem;
   }
 
   .g-lg-2,
 .gx-lg-2 {
     --bs-gutter-x: 0.5rem;
   }
 
   .g-lg-2,
 .gy-lg-2 {
     --bs-gutter-y: 0.5rem;
   }
 
   .g-lg-3,
 .gx-lg-3 {
     --bs-gutter-x: 1rem;
   }
 
   .g-lg-3,
 .gy-lg-3 {
     --bs-gutter-y: 1rem;
   }
 
   .g-lg-4,
 .gx-lg-4 {
     --bs-gutter-x: 1.5rem;
   }
 
   .g-lg-4,
 .gy-lg-4 {
     --bs-gutter-y: 1.5rem;
   }
 
   .g-lg-5,
 .gx-lg-5 {
     --bs-gutter-x: 3rem;
   }
 
   .g-lg-5,
 .gy-lg-5 {
     --bs-gutter-y: 3rem;
   }
 }
 @media (min-width: 1200px) {
   .col-xl-auto {
     flex: 0 0 auto;
     width: auto;
   }
 
   .col-xl-1 {
     flex: 0 0 auto;
     width: 8.33333333%;
   }
 
   .col-xl-2 {
     flex: 0 0 auto;
     width: 16.66666667%;
   }
 
   .col-xl-3 {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .col-xl-4 {
     flex: 0 0 auto;
     width: 33.33333333%;
   }
 
   .col-xl-5 {
     flex: 0 0 auto;
     width: 41.66666667%;
   }
 
   .col-xl-6 {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .col-xl-7 {
     flex: 0 0 auto;
     width: 58.33333333%;
   }
 
   .col-xl-8 {
     flex: 0 0 auto;
     width: 66.66666667%;
   }
 
   .col-xl-9 {
     flex: 0 0 auto;
     width: 75%;
   }
 
   .col-xl-10 {
     flex: 0 0 auto;
     width: 83.33333333%;
   }
 
   .col-xl-11 {
     flex: 0 0 auto;
     width: 91.66666667%;
   }
 
   .col-xl-12 {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .offset-xl-0 {
     margin-left: 0;
   }
 
   .offset-xl-1 {
     margin-left: 8.33333333%;
   }
 
   .offset-xl-2 {
     margin-left: 16.66666667%;
   }
 
   .offset-xl-3 {
     margin-left: 25%;
   }
 
   .offset-xl-4 {
     margin-left: 33.33333333%;
   }
 
   .offset-xl-5 {
     margin-left: 41.66666667%;
   }
 
   .offset-xl-6 {
     margin-left: 50%;
   }
 
   .offset-xl-7 {
     margin-left: 58.33333333%;
   }
 
   .offset-xl-8 {
     margin-left: 66.66666667%;
   }
 
   .offset-xl-9 {
     margin-left: 75%;
   }
 
   .offset-xl-10 {
     margin-left: 83.33333333%;
   }
 
   .offset-xl-11 {
     margin-left: 91.66666667%;
   }
 
   .g-xl-0,
 .gx-xl-0 {
     --bs-gutter-x: 0;
   }
 
   .g-xl-0,
 .gy-xl-0 {
     --bs-gutter-y: 0;
   }
 
   .g-xl-1,
 .gx-xl-1 {
     --bs-gutter-x: 0.25rem;
   }
 
   .g-xl-1,
 .gy-xl-1 {
     --bs-gutter-y: 0.25rem;
   }
 
   .g-xl-2,
 .gx-xl-2 {
     --bs-gutter-x: 0.5rem;
   }
 
   .g-xl-2,
 .gy-xl-2 {
     --bs-gutter-y: 0.5rem;
   }
 
   .g-xl-3,
 .gx-xl-3 {
     --bs-gutter-x: 1rem;
   }
 
   .g-xl-3,
 .gy-xl-3 {
     --bs-gutter-y: 1rem;
   }
 
   .g-xl-4,
 .gx-xl-4 {
     --bs-gutter-x: 1.5rem;
   }
 
   .g-xl-4,
 .gy-xl-4 {
     --bs-gutter-y: 1.5rem;
   }
 
   .g-xl-5,
 .gx-xl-5 {
     --bs-gutter-x: 3rem;
   }
 
   .g-xl-5,
 .gy-xl-5 {
     --bs-gutter-y: 3rem;
   }
 }
 @media (min-width: 1400px) {
   .col-xxl-auto {
     flex: 0 0 auto;
     width: auto;
   }
 
   .col-xxl-1 {
     flex: 0 0 auto;
     width: 8.33333333%;
   }
 
   .col-xxl-2 {
     flex: 0 0 auto;
     width: 16.66666667%;
   }
 
   .col-xxl-3 {
     flex: 0 0 auto;
     width: 25%;
   }
 
   .col-xxl-4 {
     flex: 0 0 auto;
     width: 33.33333333%;
   }
 
   .col-xxl-5 {
     flex: 0 0 auto;
     width: 41.66666667%;
   }
 
   .col-xxl-6 {
     flex: 0 0 auto;
     width: 50%;
   }
 
   .col-xxl-7 {
     flex: 0 0 auto;
     width: 58.33333333%;
   }
 
   .col-xxl-8 {
     flex: 0 0 auto;
     width: 66.66666667%;
   }
 
   .col-xxl-9 {
     flex: 0 0 auto;
     width: 75%;
   }
 
   .col-xxl-10 {
     flex: 0 0 auto;
     width: 83.33333333%;
   }
 
   .col-xxl-11 {
     flex: 0 0 auto;
     width: 91.66666667%;
   }
 
   .col-xxl-12 {
     flex: 0 0 auto;
     width: 100%;
   }
 
   .offset-xxl-0 {
     margin-left: 0;
   }
 
   .offset-xxl-1 {
     margin-left: 8.33333333%;
   }
 
   .offset-xxl-2 {
     margin-left: 16.66666667%;
   }
 
   .offset-xxl-3 {
     margin-left: 25%;
   }
 
   .offset-xxl-4 {
     margin-left: 33.33333333%;
   }
 
   .offset-xxl-5 {
     margin-left: 41.66666667%;
   }
 
   .offset-xxl-6 {
     margin-left: 50%;
   }
 
   .offset-xxl-7 {
     margin-left: 58.33333333%;
   }
 
   .offset-xxl-8 {
     margin-left: 66.66666667%;
   }
 
   .offset-xxl-9 {
     margin-left: 75%;
   }
 
   .offset-xxl-10 {
     margin-left: 83.33333333%;
   }
 
   .offset-xxl-11 {
     margin-left: 91.66666667%;
   }
 
   .g-xxl-0,
 .gx-xxl-0 {
     --bs-gutter-x: 0;
   }
 
   .g-xxl-0,
 .gy-xxl-0 {
     --bs-gutter-y: 0;
   }
 
   .g-xxl-1,
 .gx-xxl-1 {
     --bs-gutter-x: 0.25rem;
   }
 
   .g-xxl-1,
 .gy-xxl-1 {
     --bs-gutter-y: 0.25rem;
   }
 
   .g-xxl-2,
 .gx-xxl-2 {
     --bs-gutter-x: 0.5rem;
   }
 
   .g-xxl-2,
 .gy-xxl-2 {
     --bs-gutter-y: 0.5rem;
   }
 
   .g-xxl-3,
 .gx-xxl-3 {
     --bs-gutter-x: 1rem;
   }
 
   .g-xxl-3,
 .gy-xxl-3 {
     --bs-gutter-y: 1rem;
   }
 
   .g-xxl-4,
 .gx-xxl-4 {
     --bs-gutter-x: 1.5rem;
   }
 
   .g-xxl-4,
 .gy-xxl-4 {
     --bs-gutter-y: 1.5rem;
   }
 
   .g-xxl-5,
 .gx-xxl-5 {
     --bs-gutter-x: 3rem;
   }
 
   .g-xxl-5,
 .gy-xxl-5 {
     --bs-gutter-y: 3rem;
   }
 }
 .d-inline {
   display: inline !important;
 }
 
 .d-inline-block {
   display: inline-block !important;
 }
 
 .d-block {
   display: block !important;
 }
 
 .d-grid {
   display: grid !important;
 }
 
 .d-table {
   display: table !important;
 }
 
 .d-table-row {
   display: table-row !important;
 }
 
 .d-table-cell {
   display: table-cell !important;
 }
 
 .d-flex {
   display: flex !important;
 }
 
 .d-inline-flex {
   display: inline-flex !important;
 }
 
 .d-none {
   display: none !important;
 }
 
 .flex-fill {
   flex: 1 1 auto !important;
 }
 
 .flex-row {
   flex-direction: row !important;
 }
 
 .flex-column {
   flex-direction: column !important;
 }
 
 .flex-row-reverse {
   flex-direction: row-reverse !important;
 }
 
 .flex-column-reverse {
   flex-direction: column-reverse !important;
 }
 
 .flex-grow-0 {
   flex-grow: 0 !important;
 }
 
 .flex-grow-1 {
   flex-grow: 1 !important;
 }
 
 .flex-shrink-0 {
   flex-shrink: 0 !important;
 }
 
 .flex-shrink-1 {
   flex-shrink: 1 !important;
 }
 
 .flex-wrap {
   flex-wrap: wrap !important;
 }
 
 .flex-nowrap {
   flex-wrap: nowrap !important;
 }
 
 .flex-wrap-reverse {
   flex-wrap: wrap-reverse !important;
 }
 
 .justify-content-start {
   justify-content: flex-start !important;
 }
 
 .justify-content-end {
   justify-content: flex-end !important;
 }
 
 .justify-content-center {
   justify-content: center !important;
 }
 
 .justify-content-between {
   justify-content: space-between !important;
 }
 
 .justify-content-around {
   justify-content: space-around !important;
 }
 
 .justify-content-evenly {
   justify-content: space-evenly !important;
 }
 
 .align-items-start {
   align-items: flex-start !important;
 }
 
 .align-items-end {
   align-items: flex-end !important;
 }
 
 .align-items-center {
   align-items: center !important;
 }
 
 .align-items-baseline {
   align-items: baseline !important;
 }
 
 .align-items-stretch {
   align-items: stretch !important;
 }
 
 .align-content-start {
   align-content: flex-start !important;
 }
 
 .align-content-end {
   align-content: flex-end !important;
 }
 
 .align-content-center {
   align-content: center !important;
 }
 
 .align-content-between {
   align-content: space-between !important;
 }
 
 .align-content-around {
   align-content: space-around !important;
 }
 
 .align-content-stretch {
   align-content: stretch !important;
 }
 
 .align-self-auto {
   align-self: auto !important;
 }
 
 .align-self-start {
   align-self: flex-start !important;
 }
 
 .align-self-end {
   align-self: flex-end !important;
 }
 
 .align-self-center {
   align-self: center !important;
 }
 
 .align-self-baseline {
   align-self: baseline !important;
 }
 
 .align-self-stretch {
   align-self: stretch !important;
 }
 
 .order-first {
   order: -1 !important;
 }
 
 .order-0 {
   order: 0 !important;
 }
 
 .order-1 {
   order: 1 !important;
 }
 
 .order-2 {
   order: 2 !important;
 }
 
 .order-3 {
   order: 3 !important;
 }
 
 .order-4 {
   order: 4 !important;
 }
 
 .order-5 {
   order: 5 !important;
 }
 
 .order-last {
   order: 6 !important;
 }
 
 .m-0 {
   margin: 0 !important;
 }
 
 .m-1 {
   margin: 0.25rem !important;
 }
 
 .m-2 {
   margin: 0.5rem !important;
 }
 
 .m-3 {
   margin: 1rem !important;
 }
 
 .m-4 {
   margin: 1.5rem !important;
 }
 
 .m-5 {
   margin: 3rem !important;
 }
 
 .m-auto {
   margin: auto !important;
 }
 
 .mx-0 {
   margin-right: 0 !important;
   margin-left: 0 !important;
 }
 
 .mx-1 {
   margin-right: 0.25rem !important;
   margin-left: 0.25rem !important;
 }
 
 .mx-2 {
   margin-right: 0.5rem !important;
   margin-left: 0.5rem !important;
 }
 
 .mx-3 {
   margin-right: 1rem !important;
   margin-left: 1rem !important;
 }
 
 .mx-4 {
   margin-right: 1.5rem !important;
   margin-left: 1.5rem !important;
 }
 
 .mx-5 {
   margin-right: 3rem !important;
   margin-left: 3rem !important;
 }
 
 .mx-auto {
   margin-right: auto !important;
   margin-left: auto !important;
 }
 
 .my-0 {
   margin-top: 0 !important;
   margin-bottom: 0 !important;
 }
 
 .my-1 {
   margin-top: 0.25rem !important;
   margin-bottom: 0.25rem !important;
 }
 
 .my-2 {
   margin-top: 0.5rem !important;
   margin-bottom: 0.5rem !important;
 }
 
 .my-3 {
   margin-top: 1rem !important;
   margin-bottom: 1rem !important;
 }
 
 .my-4 {
   margin-top: 1.5rem !important;
   margin-bottom: 1.5rem !important;
 }
 
 .my-5 {
   margin-top: 3rem !important;
   margin-bottom: 3rem !important;
 }
 
 .my-auto {
   margin-top: auto !important;
   margin-bottom: auto !important;
 }
 
 .mt-0 {
   margin-top: 0 !important;
 }
 
 .mt-1 {
   margin-top: 0.25rem !important;
 }
 
 .mt-2 {
   margin-top: 0.5rem !important;
 }
 
 .mt-3 {
   margin-top: 1rem !important;
 }
 
 .mt-4 {
   margin-top: 1.5rem !important;
 }
 
 .mt-5 {
   margin-top: 3rem !important;
 }
 
 .mt-auto {
   margin-top: auto !important;
 }
 
 .me-0 {
   margin-right: 0 !important;
 }
 
 .me-1 {
   margin-right: 0.25rem !important;
 }
 
 .me-2 {
   margin-right: 0.5rem !important;
 }
 
 .me-3 {
   margin-right: 1rem !important;
 }
 
 .me-4 {
   margin-right: 1.5rem !important;
 }
 
 .me-5 {
   margin-right: 3rem !important;
 }
 
 .me-auto {
   margin-right: auto !important;
 }
 
 .mb-0 {
   margin-bottom: 0 !important;
 }
 
 .mb-1 {
   margin-bottom: 0.25rem !important;
 }
 
 .mb-2 {
   margin-bottom: 0.5rem !important;
 }
 
 .mb-3 {
   margin-bottom: 1rem !important;
 }
 
 .mb-4 {
   margin-bottom: 1.5rem !important;
 }
 
 .mb-5 {
   margin-bottom: 3rem !important;
 }
 
 .mb-auto {
   margin-bottom: auto !important;
 }
 
 .ms-0 {
   margin-left: 0 !important;
 }
 
 .ms-1 {
   margin-left: 0.25rem !important;
 }
 
 .ms-2 {
   margin-left: 0.5rem !important;
 }
 
 .ms-3 {
   margin-left: 1rem !important;
 }
 
 .ms-4 {
   margin-left: 1.5rem !important;
 }
 
 .ms-5 {
   margin-left: 3rem !important;
 }
 
 .ms-auto {
   margin-left: auto !important;
 }
 
 .p-0 {
   padding: 0 !important;
 }
 
 .p-1 {
   padding: 0.25rem !important;
 }
 
 .p-2 {
   padding: 0.5rem !important;
 }
 
 .p-3 {
   padding: 1rem !important;
 }
 
 .p-4 {
   padding: 1.5rem !important;
 }
 
 .p-5 {
   padding: 3rem !important;
 }
 
 .px-0 {
   padding-right: 0 !important;
   padding-left: 0 !important;
 }
 
 .px-1 {
   padding-right: 0.25rem !important;
   padding-left: 0.25rem !important;
 }
 
 .px-2 {
   padding-right: 0.5rem !important;
   padding-left: 0.5rem !important;
 }
 
 .px-3 {
   padding-right: 1rem !important;
   padding-left: 1rem !important;
 }
 
 .px-4 {
   padding-right: 1.5rem !important;
   padding-left: 1.5rem !important;
 }
 
 .px-5 {
   padding-right: 3rem !important;
   padding-left: 3rem !important;
 }
 
 .py-0 {
   padding-top: 0 !important;
   padding-bottom: 0 !important;
 }
 
 .py-1 {
   padding-top: 0.25rem !important;
   padding-bottom: 0.25rem !important;
 }
 
 .py-2 {
   padding-top: 0.5rem !important;
   padding-bottom: 0.5rem !important;
 }
 
 .py-3 {
   padding-top: 1rem !important;
   padding-bottom: 1rem !important;
 }
 
 .py-4 {
   padding-top: 1.5rem !important;
   padding-bottom: 1.5rem !important;
 }
 
 .py-5 {
   padding-top: 3rem !important;
   padding-bottom: 3rem !important;
 }
 
 .pt-0 {
   padding-top: 0 !important;
 }
 
 .pt-1 {
   padding-top: 0.25rem !important;
 }
 
 .pt-2 {
   padding-top: 0.5rem !important;
 }
 
 .pt-3 {
   padding-top: 1rem !important;
 }
 
 .pt-4 {
   padding-top: 1.5rem !important;
 }
 
 .pt-5 {
   padding-top: 3rem !important;
 }
 
 .pe-0 {
   padding-right: 0 !important;
 }
 
 .pe-1 {
   padding-right: 0.25rem !important;
 }
 
 .pe-2 {
   padding-right: 0.5rem !important;
 }
 
 .pe-3 {
   padding-right: 1rem !important;
 }
 
 .pe-4 {
   padding-right: 1.5rem !important;
 }
 
 .pe-5 {
   padding-right: 3rem !important;
 }
 
 .pb-0 {
   padding-bottom: 0 !important;
 }
 
 .pb-1 {
   padding-bottom: 0.25rem !important;
 }
 
 .pb-2 {
   padding-bottom: 0.5rem !important;
 }
 
 .pb-3 {
   padding-bottom: 1rem !important;
 }
 
 .pb-4 {
   padding-bottom: 1.5rem !important;
 }
 
 .pb-5 {
   padding-bottom: 3rem !important;
 }
 
 .ps-0 {
   padding-left: 0 !important;
 }
 
 .ps-1 {
   padding-left: 0.25rem !important;
 }
 
 .ps-2 {
   padding-left: 0.5rem !important;
 }
 
 .ps-3 {
   padding-left: 1rem !important;
 }
 
 .ps-4 {
   padding-left: 1.5rem !important;
 }
 
 .ps-5 {
   padding-left: 3rem !important;
 }
 
 @media (min-width: 576px) {
   .d-sm-inline {
     display: inline !important;
   }
 
   .d-sm-inline-block {
     display: inline-block !important;
   }
 
   .d-sm-block {
     display: block !important;
   }
 
   .d-sm-grid {
     display: grid !important;
   }
 
   .d-sm-table {
     display: table !important;
   }
 
   .d-sm-table-row {
     display: table-row !important;
   }
 
   .d-sm-table-cell {
     display: table-cell !important;
   }
 
   .d-sm-flex {
     display: flex !important;
   }
 
   .d-sm-inline-flex {
     display: inline-flex !important;
   }
 
   .d-sm-none {
     display: none !important;
   }
 
   .flex-sm-fill {
     flex: 1 1 auto !important;
   }
 
   .flex-sm-row {
     flex-direction: row !important;
   }
 
   .flex-sm-column {
     flex-direction: column !important;
   }
 
   .flex-sm-row-reverse {
     flex-direction: row-reverse !important;
   }
 
   .flex-sm-column-reverse {
     flex-direction: column-reverse !important;
   }
 
   .flex-sm-grow-0 {
     flex-grow: 0 !important;
   }
 
   .flex-sm-grow-1 {
     flex-grow: 1 !important;
   }
 
   .flex-sm-shrink-0 {
     flex-shrink: 0 !important;
   }
 
   .flex-sm-shrink-1 {
     flex-shrink: 1 !important;
   }
 
   .flex-sm-wrap {
     flex-wrap: wrap !important;
   }
 
   .flex-sm-nowrap {
     flex-wrap: nowrap !important;
   }
 
   .flex-sm-wrap-reverse {
     flex-wrap: wrap-reverse !important;
   }
 
   .justify-content-sm-start {
     justify-content: flex-start !important;
   }
 
   .justify-content-sm-end {
     justify-content: flex-end !important;
   }
 
   .justify-content-sm-center {
     justify-content: center !important;
   }
 
   .justify-content-sm-between {
     justify-content: space-between !important;
   }
 
   .justify-content-sm-around {
     justify-content: space-around !important;
   }
 
   .justify-content-sm-evenly {
     justify-content: space-evenly !important;
   }
 
   .align-items-sm-start {
     align-items: flex-start !important;
   }
 
   .align-items-sm-end {
     align-items: flex-end !important;
   }
 
   .align-items-sm-center {
     align-items: center !important;
   }
 
   .align-items-sm-baseline {
     align-items: baseline !important;
   }
 
   .align-items-sm-stretch {
     align-items: stretch !important;
   }
 
   .align-content-sm-start {
     align-content: flex-start !important;
   }
 
   .align-content-sm-end {
     align-content: flex-end !important;
   }
 
   .align-content-sm-center {
     align-content: center !important;
   }
 
   .align-content-sm-between {
     align-content: space-between !important;
   }
 
   .align-content-sm-around {
     align-content: space-around !important;
   }
 
   .align-content-sm-stretch {
     align-content: stretch !important;
   }
 
   .align-self-sm-auto {
     align-self: auto !important;
   }
 
   .align-self-sm-start {
     align-self: flex-start !important;
   }
 
   .align-self-sm-end {
     align-self: flex-end !important;
   }
 
   .align-self-sm-center {
     align-self: center !important;
   }
 
   .align-self-sm-baseline {
     align-self: baseline !important;
   }
 
   .align-self-sm-stretch {
     align-self: stretch !important;
   }
 
   .order-sm-first {
     order: -1 !important;
   }
 
   .order-sm-0 {
     order: 0 !important;
   }
 
   .order-sm-1 {
     order: 1 !important;
   }
 
   .order-sm-2 {
     order: 2 !important;
   }
 
   .order-sm-3 {
     order: 3 !important;
   }
 
   .order-sm-4 {
     order: 4 !important;
   }
 
   .order-sm-5 {
     order: 5 !important;
   }
 
   .order-sm-last {
     order: 6 !important;
   }
 
   .m-sm-0 {
     margin: 0 !important;
   }
 
   .m-sm-1 {
     margin: 0.25rem !important;
   }
 
   .m-sm-2 {
     margin: 0.5rem !important;
   }
 
   .m-sm-3 {
     margin: 1rem !important;
   }
 
   .m-sm-4 {
     margin: 1.5rem !important;
   }
 
   .m-sm-5 {
     margin: 3rem !important;
   }
 
   .m-sm-auto {
     margin: auto !important;
   }
 
   .mx-sm-0 {
     margin-right: 0 !important;
     margin-left: 0 !important;
   }
 
   .mx-sm-1 {
     margin-right: 0.25rem !important;
     margin-left: 0.25rem !important;
   }
 
   .mx-sm-2 {
     margin-right: 0.5rem !important;
     margin-left: 0.5rem !important;
   }
 
   .mx-sm-3 {
     margin-right: 1rem !important;
     margin-left: 1rem !important;
   }
 
   .mx-sm-4 {
     margin-right: 1.5rem !important;
     margin-left: 1.5rem !important;
   }
 
   .mx-sm-5 {
     margin-right: 3rem !important;
     margin-left: 3rem !important;
   }
 
   .mx-sm-auto {
     margin-right: auto !important;
     margin-left: auto !important;
   }
 
   .my-sm-0 {
     margin-top: 0 !important;
     margin-bottom: 0 !important;
   }
 
   .my-sm-1 {
     margin-top: 0.25rem !important;
     margin-bottom: 0.25rem !important;
   }
 
   .my-sm-2 {
     margin-top: 0.5rem !important;
     margin-bottom: 0.5rem !important;
   }
 
   .my-sm-3 {
     margin-top: 1rem !important;
     margin-bottom: 1rem !important;
   }
 
   .my-sm-4 {
     margin-top: 1.5rem !important;
     margin-bottom: 1.5rem !important;
   }
 
   .my-sm-5 {
     margin-top: 3rem !important;
     margin-bottom: 3rem !important;
   }
 
   .my-sm-auto {
     margin-top: auto !important;
     margin-bottom: auto !important;
   }
 
   .mt-sm-0 {
     margin-top: 0 !important;
   }
 
   .mt-sm-1 {
     margin-top: 0.25rem !important;
   }
 
   .mt-sm-2 {
     margin-top: 0.5rem !important;
   }
 
   .mt-sm-3 {
     margin-top: 1rem !important;
   }
 
   .mt-sm-4 {
     margin-top: 1.5rem !important;
   }
 
   .mt-sm-5 {
     margin-top: 3rem !important;
   }
 
   .mt-sm-auto {
     margin-top: auto !important;
   }
 
   .me-sm-0 {
     margin-right: 0 !important;
   }
 
   .me-sm-1 {
     margin-right: 0.25rem !important;
   }
 
   .me-sm-2 {
     margin-right: 0.5rem !important;
   }
 
   .me-sm-3 {
     margin-right: 1rem !important;
   }
 
   .me-sm-4 {
     margin-right: 1.5rem !important;
   }
 
   .me-sm-5 {
     margin-right: 3rem !important;
   }
 
   .me-sm-auto {
     margin-right: auto !important;
   }
 
   .mb-sm-0 {
     margin-bottom: 0 !important;
   }
 
   .mb-sm-1 {
     margin-bottom: 0.25rem !important;
   }
 
   .mb-sm-2 {
     margin-bottom: 0.5rem !important;
   }
 
   .mb-sm-3 {
     margin-bottom: 1rem !important;
   }
 
   .mb-sm-4 {
     margin-bottom: 1.5rem !important;
   }
 
   .mb-sm-5 {
     margin-bottom: 3rem !important;
   }
 
   .mb-sm-auto {
     margin-bottom: auto !important;
   }
 
   .ms-sm-0 {
     margin-left: 0 !important;
   }
 
   .ms-sm-1 {
     margin-left: 0.25rem !important;
   }
 
   .ms-sm-2 {
     margin-left: 0.5rem !important;
   }
 
   .ms-sm-3 {
     margin-left: 1rem !important;
   }
 
   .ms-sm-4 {
     margin-left: 1.5rem !important;
   }
 
   .ms-sm-5 {
     margin-left: 3rem !important;
   }
 
   .ms-sm-auto {
     margin-left: auto !important;
   }
 
   .p-sm-0 {
     padding: 0 !important;
   }
 
   .p-sm-1 {
     padding: 0.25rem !important;
   }
 
   .p-sm-2 {
     padding: 0.5rem !important;
   }
 
   .p-sm-3 {
     padding: 1rem !important;
   }
 
   .p-sm-4 {
     padding: 1.5rem !important;
   }
 
   .p-sm-5 {
     padding: 3rem !important;
   }
 
   .px-sm-0 {
     padding-right: 0 !important;
     padding-left: 0 !important;
   }
 
   .px-sm-1 {
     padding-right: 0.25rem !important;
     padding-left: 0.25rem !important;
   }
 
   .px-sm-2 {
     padding-right: 0.5rem !important;
     padding-left: 0.5rem !important;
   }
 
   .px-sm-3 {
     padding-right: 1rem !important;
     padding-left: 1rem !important;
   }
 
   .px-sm-4 {
     padding-right: 1.5rem !important;
     padding-left: 1.5rem !important;
   }
 
   .px-sm-5 {
     padding-right: 3rem !important;
     padding-left: 3rem !important;
   }
 
   .py-sm-0 {
     padding-top: 0 !important;
     padding-bottom: 0 !important;
   }
 
   .py-sm-1 {
     padding-top: 0.25rem !important;
     padding-bottom: 0.25rem !important;
   }
 
   .py-sm-2 {
     padding-top: 0.5rem !important;
     padding-bottom: 0.5rem !important;
   }
 
   .py-sm-3 {
     padding-top: 1rem !important;
     padding-bottom: 1rem !important;
   }
 
   .py-sm-4 {
     padding-top: 1.5rem !important;
     padding-bottom: 1.5rem !important;
   }
 
   .py-sm-5 {
     padding-top: 3rem !important;
     padding-bottom: 3rem !important;
   }
 
   .pt-sm-0 {
     padding-top: 0 !important;
   }
 
   .pt-sm-1 {
     padding-top: 0.25rem !important;
   }
 
   .pt-sm-2 {
     padding-top: 0.5rem !important;
   }
 
   .pt-sm-3 {
     padding-top: 1rem !important;
   }
 
   .pt-sm-4 {
     padding-top: 1.5rem !important;
   }
 
   .pt-sm-5 {
     padding-top: 3rem !important;
   }
 
   .pe-sm-0 {
     padding-right: 0 !important;
   }
 
   .pe-sm-1 {
     padding-right: 0.25rem !important;
   }
 
   .pe-sm-2 {
     padding-right: 0.5rem !important;
   }
 
   .pe-sm-3 {
     padding-right: 1rem !important;
   }
 
   .pe-sm-4 {
     padding-right: 1.5rem !important;
   }
 
   .pe-sm-5 {
     padding-right: 3rem !important;
   }
 
   .pb-sm-0 {
     padding-bottom: 0 !important;
   }
 
   .pb-sm-1 {
     padding-bottom: 0.25rem !important;
   }
 
   .pb-sm-2 {
     padding-bottom: 0.5rem !important;
   }
 
   .pb-sm-3 {
     padding-bottom: 1rem !important;
   }
 
   .pb-sm-4 {
     padding-bottom: 1.5rem !important;
   }
 
   .pb-sm-5 {
     padding-bottom: 3rem !important;
   }
 
   .ps-sm-0 {
     padding-left: 0 !important;
   }
 
   .ps-sm-1 {
     padding-left: 0.25rem !important;
   }
 
   .ps-sm-2 {
     padding-left: 0.5rem !important;
   }
 
   .ps-sm-3 {
     padding-left: 1rem !important;
   }
 
   .ps-sm-4 {
     padding-left: 1.5rem !important;
   }
 
   .ps-sm-5 {
     padding-left: 3rem !important;
   }
 }
 @media (min-width: 768px) {
   .d-md-inline {
     display: inline !important;
   }
 
   .d-md-inline-block {
     display: inline-block !important;
   }
 
   .d-md-block {
     display: block !important;
   }
 
   .d-md-grid {
     display: grid !important;
   }
 
   .d-md-table {
     display: table !important;
   }
 
   .d-md-table-row {
     display: table-row !important;
   }
 
   .d-md-table-cell {
     display: table-cell !important;
   }
 
   .d-md-flex {
     display: flex !important;
   }
 
   .d-md-inline-flex {
     display: inline-flex !important;
   }
 
   .d-md-none {
     display: none !important;
   }
 
   .flex-md-fill {
     flex: 1 1 auto !important;
   }
 
   .flex-md-row {
     flex-direction: row !important;
   }
 
   .flex-md-column {
     flex-direction: column !important;
   }
 
   .flex-md-row-reverse {
     flex-direction: row-reverse !important;
   }
 
   .flex-md-column-reverse {
     flex-direction: column-reverse !important;
   }
 
   .flex-md-grow-0 {
     flex-grow: 0 !important;
   }
 
   .flex-md-grow-1 {
     flex-grow: 1 !important;
   }
 
   .flex-md-shrink-0 {
     flex-shrink: 0 !important;
   }
 
   .flex-md-shrink-1 {
     flex-shrink: 1 !important;
   }
 
   .flex-md-wrap {
     flex-wrap: wrap !important;
   }
 
   .flex-md-nowrap {
     flex-wrap: nowrap !important;
   }
 
   .flex-md-wrap-reverse {
     flex-wrap: wrap-reverse !important;
   }
 
   .justify-content-md-start {
     justify-content: flex-start !important;
   }
 
   .justify-content-md-end {
     justify-content: flex-end !important;
   }
 
   .justify-content-md-center {
     justify-content: center !important;
   }
 
   .justify-content-md-between {
     justify-content: space-between !important;
   }
 
   .justify-content-md-around {
     justify-content: space-around !important;
   }
 
   .justify-content-md-evenly {
     justify-content: space-evenly !important;
   }
 
   .align-items-md-start {
     align-items: flex-start !important;
   }
 
   .align-items-md-end {
     align-items: flex-end !important;
   }
 
   .align-items-md-center {
     align-items: center !important;
   }
 
   .align-items-md-baseline {
     align-items: baseline !important;
   }
 
   .align-items-md-stretch {
     align-items: stretch !important;
   }
 
   .align-content-md-start {
     align-content: flex-start !important;
   }
 
   .align-content-md-end {
     align-content: flex-end !important;
   }
 
   .align-content-md-center {
     align-content: center !important;
   }
 
   .align-content-md-between {
     align-content: space-between !important;
   }
 
   .align-content-md-around {
     align-content: space-around !important;
   }
 
   .align-content-md-stretch {
     align-content: stretch !important;
   }
 
   .align-self-md-auto {
     align-self: auto !important;
   }
 
   .align-self-md-start {
     align-self: flex-start !important;
   }
 
   .align-self-md-end {
     align-self: flex-end !important;
   }
 
   .align-self-md-center {
     align-self: center !important;
   }
 
   .align-self-md-baseline {
     align-self: baseline !important;
   }
 
   .align-self-md-stretch {
     align-self: stretch !important;
   }
 
   .order-md-first {
     order: -1 !important;
   }
 
   .order-md-0 {
     order: 0 !important;
   }
 
   .order-md-1 {
     order: 1 !important;
   }
 
   .order-md-2 {
     order: 2 !important;
   }
 
   .order-md-3 {
     order: 3 !important;
   }
 
   .order-md-4 {
     order: 4 !important;
   }
 
   .order-md-5 {
     order: 5 !important;
   }
 
   .order-md-last {
     order: 6 !important;
   }
 
   .m-md-0 {
     margin: 0 !important;
   }
 
   .m-md-1 {
     margin: 0.25rem !important;
   }
 
   .m-md-2 {
     margin: 0.5rem !important;
   }
 
   .m-md-3 {
     margin: 1rem !important;
   }
 
   .m-md-4 {
     margin: 1.5rem !important;
   }
 
   .m-md-5 {
     margin: 3rem !important;
   }
 
   .m-md-auto {
     margin: auto !important;
   }
 
   .mx-md-0 {
     margin-right: 0 !important;
     margin-left: 0 !important;
   }
 
   .mx-md-1 {
     margin-right: 0.25rem !important;
     margin-left: 0.25rem !important;
   }
 
   .mx-md-2 {
     margin-right: 0.5rem !important;
     margin-left: 0.5rem !important;
   }
 
   .mx-md-3 {
     margin-right: 1rem !important;
     margin-left: 1rem !important;
   }
 
   .mx-md-4 {
     margin-right: 1.5rem !important;
     margin-left: 1.5rem !important;
   }
 
   .mx-md-5 {
     margin-right: 3rem !important;
     margin-left: 3rem !important;
   }
 
   .mx-md-auto {
     margin-right: auto !important;
     margin-left: auto !important;
   }
 
   .my-md-0 {
     margin-top: 0 !important;
     margin-bottom: 0 !important;
   }
 
   .my-md-1 {
     margin-top: 0.25rem !important;
     margin-bottom: 0.25rem !important;
   }
 
   .my-md-2 {
     margin-top: 0.5rem !important;
     margin-bottom: 0.5rem !important;
   }
 
   .my-md-3 {
     margin-top: 1rem !important;
     margin-bottom: 1rem !important;
   }
 
   .my-md-4 {
     margin-top: 1.5rem !important;
     margin-bottom: 1.5rem !important;
   }
 
   .my-md-5 {
     margin-top: 3rem !important;
     margin-bottom: 3rem !important;
   }
 
   .my-md-auto {
     margin-top: auto !important;
     margin-bottom: auto !important;
   }
 
   .mt-md-0 {
     margin-top: 0 !important;
   }
 
   .mt-md-1 {
     margin-top: 0.25rem !important;
   }
 
   .mt-md-2 {
     margin-top: 0.5rem !important;
   }
 
   .mt-md-3 {
     margin-top: 1rem !important;
   }
 
   .mt-md-4 {
     margin-top: 1.5rem !important;
   }
 
   .mt-md-5 {
     margin-top: 3rem !important;
   }
 
   .mt-md-auto {
     margin-top: auto !important;
   }
 
   .me-md-0 {
     margin-right: 0 !important;
   }
 
   .me-md-1 {
     margin-right: 0.25rem !important;
   }
 
   .me-md-2 {
     margin-right: 0.5rem !important;
   }
 
   .me-md-3 {
     margin-right: 1rem !important;
   }
 
   .me-md-4 {
     margin-right: 1.5rem !important;
   }
 
   .me-md-5 {
     margin-right: 3rem !important;
   }
 
   .me-md-auto {
     margin-right: auto !important;
   }
 
   .mb-md-0 {
     margin-bottom: 0 !important;
   }
 
   .mb-md-1 {
     margin-bottom: 0.25rem !important;
   }
 
   .mb-md-2 {
     margin-bottom: 0.5rem !important;
   }
 
   .mb-md-3 {
     margin-bottom: 1rem !important;
   }
 
   .mb-md-4 {
     margin-bottom: 1.5rem !important;
   }
 
   .mb-md-5 {
     margin-bottom: 3rem !important;
   }
 
   .mb-md-auto {
     margin-bottom: auto !important;
   }
 
   .ms-md-0 {
     margin-left: 0 !important;
   }
 
   .ms-md-1 {
     margin-left: 0.25rem !important;
   }
 
   .ms-md-2 {
     margin-left: 0.5rem !important;
   }
 
   .ms-md-3 {
     margin-left: 1rem !important;
   }
 
   .ms-md-4 {
     margin-left: 1.5rem !important;
   }
 
   .ms-md-5 {
     margin-left: 3rem !important;
   }
 
   .ms-md-auto {
     margin-left: auto !important;
   }
 
   .p-md-0 {
     padding: 0 !important;
   }
 
   .p-md-1 {
     padding: 0.25rem !important;
   }
 
   .p-md-2 {
     padding: 0.5rem !important;
   }
 
   .p-md-3 {
     padding: 1rem !important;
   }
 
   .p-md-4 {
     padding: 1.5rem !important;
   }
 
   .p-md-5 {
     padding: 3rem !important;
   }
 
   .px-md-0 {
     padding-right: 0 !important;
     padding-left: 0 !important;
   }
 
   .px-md-1 {
     padding-right: 0.25rem !important;
     padding-left: 0.25rem !important;
   }
 
   .px-md-2 {
     padding-right: 0.5rem !important;
     padding-left: 0.5rem !important;
   }
 
   .px-md-3 {
     padding-right: 1rem !important;
     padding-left: 1rem !important;
   }
 
   .px-md-4 {
     padding-right: 1.5rem !important;
     padding-left: 1.5rem !important;
   }
 
   .px-md-5 {
     padding-right: 3rem !important;
     padding-left: 3rem !important;
   }
 
   .py-md-0 {
     padding-top: 0 !important;
     padding-bottom: 0 !important;
   }
 
   .py-md-1 {
     padding-top: 0.25rem !important;
     padding-bottom: 0.25rem !important;
   }
 
   .py-md-2 {
     padding-top: 0.5rem !important;
     padding-bottom: 0.5rem !important;
   }
 
   .py-md-3 {
     padding-top: 1rem !important;
     padding-bottom: 1rem !important;
   }
 
   .py-md-4 {
     padding-top: 1.5rem !important;
     padding-bottom: 1.5rem !important;
   }
 
   .py-md-5 {
     padding-top: 3rem !important;
     padding-bottom: 3rem !important;
   }
 
   .pt-md-0 {
     padding-top: 0 !important;
   }
 
   .pt-md-1 {
     padding-top: 0.25rem !important;
   }
 
   .pt-md-2 {
     padding-top: 0.5rem !important;
   }
 
   .pt-md-3 {
     padding-top: 1rem !important;
   }
 
   .pt-md-4 {
     padding-top: 1.5rem !important;
   }
 
   .pt-md-5 {
     padding-top: 3rem !important;
   }
 
   .pe-md-0 {
     padding-right: 0 !important;
   }
 
   .pe-md-1 {
     padding-right: 0.25rem !important;
   }
 
   .pe-md-2 {
     padding-right: 0.5rem !important;
   }
 
   .pe-md-3 {
     padding-right: 1rem !important;
   }
 
   .pe-md-4 {
     padding-right: 1.5rem !important;
   }
 
   .pe-md-5 {
     padding-right: 3rem !important;
   }
 
   .pb-md-0 {
     padding-bottom: 0 !important;
   }
 
   .pb-md-1 {
     padding-bottom: 0.25rem !important;
   }
 
   .pb-md-2 {
     padding-bottom: 0.5rem !important;
   }
 
   .pb-md-3 {
     padding-bottom: 1rem !important;
   }
 
   .pb-md-4 {
     padding-bottom: 1.5rem !important;
   }
 
   .pb-md-5 {
     padding-bottom: 3rem !important;
   }
 
   .ps-md-0 {
     padding-left: 0 !important;
   }
 
   .ps-md-1 {
     padding-left: 0.25rem !important;
   }
 
   .ps-md-2 {
     padding-left: 0.5rem !important;
   }
 
   .ps-md-3 {
     padding-left: 1rem !important;
   }
 
   .ps-md-4 {
     padding-left: 1.5rem !important;
   }
 
   .ps-md-5 {
     padding-left: 3rem !important;
   }
 }
 @media (min-width: 992px) {
   .d-lg-inline {
     display: inline !important;
   }
 
   .d-lg-inline-block {
     display: inline-block !important;
   }
 
   .d-lg-block {
     display: block !important;
   }
 
   .d-lg-grid {
     display: grid !important;
   }
 
   .d-lg-table {
     display: table !important;
   }
 
   .d-lg-table-row {
     display: table-row !important;
   }
 
   .d-lg-table-cell {
     display: table-cell !important;
   }
 
   .d-lg-flex {
     display: flex !important;
   }
 
   .d-lg-inline-flex {
     display: inline-flex !important;
   }
 
   .d-lg-none {
     display: none !important;
   }
 
   .flex-lg-fill {
     flex: 1 1 auto !important;
   }
 
   .flex-lg-row {
     flex-direction: row !important;
   }
 
   .flex-lg-column {
     flex-direction: column !important;
   }
 
   .flex-lg-row-reverse {
     flex-direction: row-reverse !important;
   }
 
   .flex-lg-column-reverse {
     flex-direction: column-reverse !important;
   }
 
   .flex-lg-grow-0 {
     flex-grow: 0 !important;
   }
 
   .flex-lg-grow-1 {
     flex-grow: 1 !important;
   }
 
   .flex-lg-shrink-0 {
     flex-shrink: 0 !important;
   }
 
   .flex-lg-shrink-1 {
     flex-shrink: 1 !important;
   }
 
   .flex-lg-wrap {
     flex-wrap: wrap !important;
   }
 
   .flex-lg-nowrap {
     flex-wrap: nowrap !important;
   }
 
   .flex-lg-wrap-reverse {
     flex-wrap: wrap-reverse !important;
   }
 
   .justify-content-lg-start {
     justify-content: flex-start !important;
   }
 
   .justify-content-lg-end {
     justify-content: flex-end !important;
   }
 
   .justify-content-lg-center {
     justify-content: center !important;
   }
 
   .justify-content-lg-between {
     justify-content: space-between !important;
   }
 
   .justify-content-lg-around {
     justify-content: space-around !important;
   }
 
   .justify-content-lg-evenly {
     justify-content: space-evenly !important;
   }
 
   .align-items-lg-start {
     align-items: flex-start !important;
   }
 
   .align-items-lg-end {
     align-items: flex-end !important;
   }
 
   .align-items-lg-center {
     align-items: center !important;
   }
 
   .align-items-lg-baseline {
     align-items: baseline !important;
   }
 
   .align-items-lg-stretch {
     align-items: stretch !important;
   }
 
   .align-content-lg-start {
     align-content: flex-start !important;
   }
 
   .align-content-lg-end {
     align-content: flex-end !important;
   }
 
   .align-content-lg-center {
     align-content: center !important;
   }
 
   .align-content-lg-between {
     align-content: space-between !important;
   }
 
   .align-content-lg-around {
     align-content: space-around !important;
   }
 
   .align-content-lg-stretch {
     align-content: stretch !important;
   }
 
   .align-self-lg-auto {
     align-self: auto !important;
   }
 
   .align-self-lg-start {
     align-self: flex-start !important;
   }
 
   .align-self-lg-end {
     align-self: flex-end !important;
   }
 
   .align-self-lg-center {
     align-self: center !important;
   }
 
   .align-self-lg-baseline {
     align-self: baseline !important;
   }
 
   .align-self-lg-stretch {
     align-self: stretch !important;
   }
 
   .order-lg-first {
     order: -1 !important;
   }
 
   .order-lg-0 {
     order: 0 !important;
   }
 
   .order-lg-1 {
     order: 1 !important;
   }
 
   .order-lg-2 {
     order: 2 !important;
   }
 
   .order-lg-3 {
     order: 3 !important;
   }
 
   .order-lg-4 {
     order: 4 !important;
   }
 
   .order-lg-5 {
     order: 5 !important;
   }
 
   .order-lg-last {
     order: 6 !important;
   }
 
   .m-lg-0 {
     margin: 0 !important;
   }
 
   .m-lg-1 {
     margin: 0.25rem !important;
   }
 
   .m-lg-2 {
     margin: 0.5rem !important;
   }
 
   .m-lg-3 {
     margin: 1rem !important;
   }
 
   .m-lg-4 {
     margin: 1.5rem !important;
   }
 
   .m-lg-5 {
     margin: 3rem !important;
   }
 
   .m-lg-auto {
     margin: auto !important;
   }
 
   .mx-lg-0 {
     margin-right: 0 !important;
     margin-left: 0 !important;
   }
 
   .mx-lg-1 {
     margin-right: 0.25rem !important;
     margin-left: 0.25rem !important;
   }
 
   .mx-lg-2 {
     margin-right: 0.5rem !important;
     margin-left: 0.5rem !important;
   }
 
   .mx-lg-3 {
     margin-right: 1rem !important;
     margin-left: 1rem !important;
   }
 
   .mx-lg-4 {
     margin-right: 1.5rem !important;
     margin-left: 1.5rem !important;
   }
 
   .mx-lg-5 {
     margin-right: 3rem !important;
     margin-left: 3rem !important;
   }
 
   .mx-lg-auto {
     margin-right: auto !important;
     margin-left: auto !important;
   }
 
   .my-lg-0 {
     margin-top: 0 !important;
     margin-bottom: 0 !important;
   }
 
   .my-lg-1 {
     margin-top: 0.25rem !important;
     margin-bottom: 0.25rem !important;
   }
 
   .my-lg-2 {
     margin-top: 0.5rem !important;
     margin-bottom: 0.5rem !important;
   }
 
   .my-lg-3 {
     margin-top: 1rem !important;
     margin-bottom: 1rem !important;
   }
 
   .my-lg-4 {
     margin-top: 1.5rem !important;
     margin-bottom: 1.5rem !important;
   }
 
   .my-lg-5 {
     margin-top: 3rem !important;
     margin-bottom: 3rem !important;
   }
 
   .my-lg-auto {
     margin-top: auto !important;
     margin-bottom: auto !important;
   }
 
   .mt-lg-0 {
     margin-top: 0 !important;
   }
 
   .mt-lg-1 {
     margin-top: 0.25rem !important;
   }
 
   .mt-lg-2 {
     margin-top: 0.5rem !important;
   }
 
   .mt-lg-3 {
     margin-top: 1rem !important;
   }
 
   .mt-lg-4 {
     margin-top: 1.5rem !important;
   }
 
   .mt-lg-5 {
     margin-top: 3rem !important;
   }
 
   .mt-lg-auto {
     margin-top: auto !important;
   }
 
   .me-lg-0 {
     margin-right: 0 !important;
   }
 
   .me-lg-1 {
     margin-right: 0.25rem !important;
   }
 
   .me-lg-2 {
     margin-right: 0.5rem !important;
   }
 
   .me-lg-3 {
     margin-right: 1rem !important;
   }
 
   .me-lg-4 {
     margin-right: 1.5rem !important;
   }
 
   .me-lg-5 {
     margin-right: 3rem !important;
   }
 
   .me-lg-auto {
     margin-right: auto !important;
   }
 
   .mb-lg-0 {
     margin-bottom: 0 !important;
   }
 
   .mb-lg-1 {
     margin-bottom: 0.25rem !important;
   }
 
   .mb-lg-2 {
     margin-bottom: 0.5rem !important;
   }
 
   .mb-lg-3 {
     margin-bottom: 1rem !important;
   }
 
   .mb-lg-4 {
     margin-bottom: 1.5rem !important;
   }
 
   .mb-lg-5 {
     margin-bottom: 3rem !important;
   }
 
   .mb-lg-auto {
     margin-bottom: auto !important;
   }
 
   .ms-lg-0 {
     margin-left: 0 !important;
   }
 
   .ms-lg-1 {
     margin-left: 0.25rem !important;
   }
 
   .ms-lg-2 {
     margin-left: 0.5rem !important;
   }
 
   .ms-lg-3 {
     margin-left: 1rem !important;
   }
 
   .ms-lg-4 {
     margin-left: 1.5rem !important;
   }
 
   .ms-lg-5 {
     margin-left: 3rem !important;
   }
 
   .ms-lg-auto {
     margin-left: auto !important;
   }
 
   .p-lg-0 {
     padding: 0 !important;
   }
 
   .p-lg-1 {
     padding: 0.25rem !important;
   }
 
   .p-lg-2 {
     padding: 0.5rem !important;
   }
 
   .p-lg-3 {
     padding: 1rem !important;
   }
 
   .p-lg-4 {
     padding: 1.5rem !important;
   }
 
   .p-lg-5 {
     padding: 3rem !important;
   }
 
   .px-lg-0 {
     padding-right: 0 !important;
     padding-left: 0 !important;
   }
 
   .px-lg-1 {
     padding-right: 0.25rem !important;
     padding-left: 0.25rem !important;
   }
 
   .px-lg-2 {
     padding-right: 0.5rem !important;
     padding-left: 0.5rem !important;
   }
 
   .px-lg-3 {
     padding-right: 1rem !important;
     padding-left: 1rem !important;
   }
 
   .px-lg-4 {
     padding-right: 1.5rem !important;
     padding-left: 1.5rem !important;
   }
 
   .px-lg-5 {
     padding-right: 3rem !important;
     padding-left: 3rem !important;
   }
 
   .py-lg-0 {
     padding-top: 0 !important;
     padding-bottom: 0 !important;
   }
 
   .py-lg-1 {
     padding-top: 0.25rem !important;
     padding-bottom: 0.25rem !important;
   }
 
   .py-lg-2 {
     padding-top: 0.5rem !important;
     padding-bottom: 0.5rem !important;
   }
 
   .py-lg-3 {
     padding-top: 1rem !important;
     padding-bottom: 1rem !important;
   }
 
   .py-lg-4 {
     padding-top: 1.5rem !important;
     padding-bottom: 1.5rem !important;
   }
 
   .py-lg-5 {
     padding-top: 3rem !important;
     padding-bottom: 3rem !important;
   }
 
   .pt-lg-0 {
     padding-top: 0 !important;
   }
 
   .pt-lg-1 {
     padding-top: 0.25rem !important;
   }
 
   .pt-lg-2 {
     padding-top: 0.5rem !important;
   }
 
   .pt-lg-3 {
     padding-top: 1rem !important;
   }
 
   .pt-lg-4 {
     padding-top: 1.5rem !important;
   }
 
   .pt-lg-5 {
     padding-top: 3rem !important;
   }
 
   .pe-lg-0 {
     padding-right: 0 !important;
   }
 
   .pe-lg-1 {
     padding-right: 0.25rem !important;
   }
 
   .pe-lg-2 {
     padding-right: 0.5rem !important;
   }
 
   .pe-lg-3 {
     padding-right: 1rem !important;
   }
 
   .pe-lg-4 {
     padding-right: 1.5rem !important;
   }
 
   .pe-lg-5 {
     padding-right: 3rem !important;
   }
 
   .pb-lg-0 {
     padding-bottom: 0 !important;
   }
 
   .pb-lg-1 {
     padding-bottom: 0.25rem !important;
   }
 
   .pb-lg-2 {
     padding-bottom: 0.5rem !important;
   }
 
   .pb-lg-3 {
     padding-bottom: 1rem !important;
   }
 
   .pb-lg-4 {
     padding-bottom: 1.5rem !important;
   }
 
   .pb-lg-5 {
     padding-bottom: 3rem !important;
   }
 
   .ps-lg-0 {
     padding-left: 0 !important;
   }
 
   .ps-lg-1 {
     padding-left: 0.25rem !important;
   }
 
   .ps-lg-2 {
     padding-left: 0.5rem !important;
   }
 
   .ps-lg-3 {
     padding-left: 1rem !important;
   }
 
   .ps-lg-4 {
     padding-left: 1.5rem !important;
   }
 
   .ps-lg-5 {
     padding-left: 3rem !important;
   }
 }
 @media (min-width: 1200px) {
   .d-xl-inline {
     display: inline !important;
   }
 
   .d-xl-inline-block {
     display: inline-block !important;
   }
 
   .d-xl-block {
     display: block !important;
   }
 
   .d-xl-grid {
     display: grid !important;
   }
 
   .d-xl-table {
     display: table !important;
   }
 
   .d-xl-table-row {
     display: table-row !important;
   }
 
   .d-xl-table-cell {
     display: table-cell !important;
   }
 
   .d-xl-flex {
     display: flex !important;
   }
 
   .d-xl-inline-flex {
     display: inline-flex !important;
   }
 
   .d-xl-none {
     display: none !important;
   }
 
   .flex-xl-fill {
     flex: 1 1 auto !important;
   }
 
   .flex-xl-row {
     flex-direction: row !important;
   }
 
   .flex-xl-column {
     flex-direction: column !important;
   }
 
   .flex-xl-row-reverse {
     flex-direction: row-reverse !important;
   }
 
   .flex-xl-column-reverse {
     flex-direction: column-reverse !important;
   }
 
   .flex-xl-grow-0 {
     flex-grow: 0 !important;
   }
 
   .flex-xl-grow-1 {
     flex-grow: 1 !important;
   }
 
   .flex-xl-shrink-0 {
     flex-shrink: 0 !important;
   }
 
   .flex-xl-shrink-1 {
     flex-shrink: 1 !important;
   }
 
   .flex-xl-wrap {
     flex-wrap: wrap !important;
   }
 
   .flex-xl-nowrap {
     flex-wrap: nowrap !important;
   }
 
   .flex-xl-wrap-reverse {
     flex-wrap: wrap-reverse !important;
   }
 
   .justify-content-xl-start {
     justify-content: flex-start !important;
   }
 
   .justify-content-xl-end {
     justify-content: flex-end !important;
   }
 
   .justify-content-xl-center {
     justify-content: center !important;
   }
 
   .justify-content-xl-between {
     justify-content: space-between !important;
   }
 
   .justify-content-xl-around {
     justify-content: space-around !important;
   }
 
   .justify-content-xl-evenly {
     justify-content: space-evenly !important;
   }
 
   .align-items-xl-start {
     align-items: flex-start !important;
   }
 
   .align-items-xl-end {
     align-items: flex-end !important;
   }
 
   .align-items-xl-center {
     align-items: center !important;
   }
 
   .align-items-xl-baseline {
     align-items: baseline !important;
   }
 
   .align-items-xl-stretch {
     align-items: stretch !important;
   }
 
   .align-content-xl-start {
     align-content: flex-start !important;
   }
 
   .align-content-xl-end {
     align-content: flex-end !important;
   }
 
   .align-content-xl-center {
     align-content: center !important;
   }
 
   .align-content-xl-between {
     align-content: space-between !important;
   }
 
   .align-content-xl-around {
     align-content: space-around !important;
   }
 
   .align-content-xl-stretch {
     align-content: stretch !important;
   }
 
   .align-self-xl-auto {
     align-self: auto !important;
   }
 
   .align-self-xl-start {
     align-self: flex-start !important;
   }
 
   .align-self-xl-end {
     align-self: flex-end !important;
   }
 
   .align-self-xl-center {
     align-self: center !important;
   }
 
   .align-self-xl-baseline {
     align-self: baseline !important;
   }
 
   .align-self-xl-stretch {
     align-self: stretch !important;
   }
 
   .order-xl-first {
     order: -1 !important;
   }
 
   .order-xl-0 {
     order: 0 !important;
   }
 
   .order-xl-1 {
     order: 1 !important;
   }
 
   .order-xl-2 {
     order: 2 !important;
   }
 
   .order-xl-3 {
     order: 3 !important;
   }
 
   .order-xl-4 {
     order: 4 !important;
   }
 
   .order-xl-5 {
     order: 5 !important;
   }
 
   .order-xl-last {
     order: 6 !important;
   }
 
   .m-xl-0 {
     margin: 0 !important;
   }
 
   .m-xl-1 {
     margin: 0.25rem !important;
   }
 
   .m-xl-2 {
     margin: 0.5rem !important;
   }
 
   .m-xl-3 {
     margin: 1rem !important;
   }
 
   .m-xl-4 {
     margin: 1.5rem !important;
   }
 
   .m-xl-5 {
     margin: 3rem !important;
   }
 
   .m-xl-auto {
     margin: auto !important;
   }
 
   .mx-xl-0 {
     margin-right: 0 !important;
     margin-left: 0 !important;
   }
 
   .mx-xl-1 {
     margin-right: 0.25rem !important;
     margin-left: 0.25rem !important;
   }
 
   .mx-xl-2 {
     margin-right: 0.5rem !important;
     margin-left: 0.5rem !important;
   }
 
   .mx-xl-3 {
     margin-right: 1rem !important;
     margin-left: 1rem !important;
   }
 
   .mx-xl-4 {
     margin-right: 1.5rem !important;
     margin-left: 1.5rem !important;
   }
 
   .mx-xl-5 {
     margin-right: 3rem !important;
     margin-left: 3rem !important;
   }
 
   .mx-xl-auto {
     margin-right: auto !important;
     margin-left: auto !important;
   }
 
   .my-xl-0 {
     margin-top: 0 !important;
     margin-bottom: 0 !important;
   }
 
   .my-xl-1 {
     margin-top: 0.25rem !important;
     margin-bottom: 0.25rem !important;
   }
 
   .my-xl-2 {
     margin-top: 0.5rem !important;
     margin-bottom: 0.5rem !important;
   }
 
   .my-xl-3 {
     margin-top: 1rem !important;
     margin-bottom: 1rem !important;
   }
 
   .my-xl-4 {
     margin-top: 1.5rem !important;
     margin-bottom: 1.5rem !important;
   }
 
   .my-xl-5 {
     margin-top: 3rem !important;
     margin-bottom: 3rem !important;
   }
 
   .my-xl-auto {
     margin-top: auto !important;
     margin-bottom: auto !important;
   }
 
   .mt-xl-0 {
     margin-top: 0 !important;
   }
 
   .mt-xl-1 {
     margin-top: 0.25rem !important;
   }
 
   .mt-xl-2 {
     margin-top: 0.5rem !important;
   }
 
   .mt-xl-3 {
     margin-top: 1rem !important;
   }
 
   .mt-xl-4 {
     margin-top: 1.5rem !important;
   }
 
   .mt-xl-5 {
     margin-top: 3rem !important;
   }
 
   .mt-xl-auto {
     margin-top: auto !important;
   }
 
   .me-xl-0 {
     margin-right: 0 !important;
   }
 
   .me-xl-1 {
     margin-right: 0.25rem !important;
   }
 
   .me-xl-2 {
     margin-right: 0.5rem !important;
   }
 
   .me-xl-3 {
     margin-right: 1rem !important;
   }
 
   .me-xl-4 {
     margin-right: 1.5rem !important;
   }
 
   .me-xl-5 {
     margin-right: 3rem !important;
   }
 
   .me-xl-auto {
     margin-right: auto !important;
   }
 
   .mb-xl-0 {
     margin-bottom: 0 !important;
   }
 
   .mb-xl-1 {
     margin-bottom: 0.25rem !important;
   }
 
   .mb-xl-2 {
     margin-bottom: 0.5rem !important;
   }
 
   .mb-xl-3 {
     margin-bottom: 1rem !important;
   }
 
   .mb-xl-4 {
     margin-bottom: 1.5rem !important;
   }
 
   .mb-xl-5 {
     margin-bottom: 3rem !important;
   }
 
   .mb-xl-auto {
     margin-bottom: auto !important;
   }
 
   .ms-xl-0 {
     margin-left: 0 !important;
   }
 
   .ms-xl-1 {
     margin-left: 0.25rem !important;
   }
 
   .ms-xl-2 {
     margin-left: 0.5rem !important;
   }
 
   .ms-xl-3 {
     margin-left: 1rem !important;
   }
 
   .ms-xl-4 {
     margin-left: 1.5rem !important;
   }
 
   .ms-xl-5 {
     margin-left: 3rem !important;
   }
 
   .ms-xl-auto {
     margin-left: auto !important;
   }
 
   .p-xl-0 {
     padding: 0 !important;
   }
 
   .p-xl-1 {
     padding: 0.25rem !important;
   }
 
   .p-xl-2 {
     padding: 0.5rem !important;
   }
 
   .p-xl-3 {
     padding: 1rem !important;
   }
 
   .p-xl-4 {
     padding: 1.5rem !important;
   }
 
   .p-xl-5 {
     padding: 3rem !important;
   }
 
   .px-xl-0 {
     padding-right: 0 !important;
     padding-left: 0 !important;
   }
 
   .px-xl-1 {
     padding-right: 0.25rem !important;
     padding-left: 0.25rem !important;
   }
 
   .px-xl-2 {
     padding-right: 0.5rem !important;
     padding-left: 0.5rem !important;
   }
 
   .px-xl-3 {
     padding-right: 1rem !important;
     padding-left: 1rem !important;
   }
 
   .px-xl-4 {
     padding-right: 1.5rem !important;
     padding-left: 1.5rem !important;
   }
 
   .px-xl-5 {
     padding-right: 3rem !important;
     padding-left: 3rem !important;
   }
 
   .py-xl-0 {
     padding-top: 0 !important;
     padding-bottom: 0 !important;
   }
 
   .py-xl-1 {
     padding-top: 0.25rem !important;
     padding-bottom: 0.25rem !important;
   }
 
   .py-xl-2 {
     padding-top: 0.5rem !important;
     padding-bottom: 0.5rem !important;
   }
 
   .py-xl-3 {
     padding-top: 1rem !important;
     padding-bottom: 1rem !important;
   }
 
   .py-xl-4 {
     padding-top: 1.5rem !important;
     padding-bottom: 1.5rem !important;
   }
 
   .py-xl-5 {
     padding-top: 3rem !important;
     padding-bottom: 3rem !important;
   }
 
   .pt-xl-0 {
     padding-top: 0 !important;
   }
 
   .pt-xl-1 {
     padding-top: 0.25rem !important;
   }
 
   .pt-xl-2 {
     padding-top: 0.5rem !important;
   }
 
   .pt-xl-3 {
     padding-top: 1rem !important;
   }
 
   .pt-xl-4 {
     padding-top: 1.5rem !important;
   }
 
   .pt-xl-5 {
     padding-top: 3rem !important;
   }
 
   .pe-xl-0 {
     padding-right: 0 !important;
   }
 
   .pe-xl-1 {
     padding-right: 0.25rem !important;
   }
 
   .pe-xl-2 {
     padding-right: 0.5rem !important;
   }
 
   .pe-xl-3 {
     padding-right: 1rem !important;
   }
 
   .pe-xl-4 {
     padding-right: 1.5rem !important;
   }
 
   .pe-xl-5 {
     padding-right: 3rem !important;
   }
 
   .pb-xl-0 {
     padding-bottom: 0 !important;
   }
 
   .pb-xl-1 {
     padding-bottom: 0.25rem !important;
   }
 
   .pb-xl-2 {
     padding-bottom: 0.5rem !important;
   }
 
   .pb-xl-3 {
     padding-bottom: 1rem !important;
   }
 
   .pb-xl-4 {
     padding-bottom: 1.5rem !important;
   }
 
   .pb-xl-5 {
     padding-bottom: 3rem !important;
   }
 
   .ps-xl-0 {
     padding-left: 0 !important;
   }
 
   .ps-xl-1 {
     padding-left: 0.25rem !important;
   }
 
   .ps-xl-2 {
     padding-left: 0.5rem !important;
   }
 
   .ps-xl-3 {
     padding-left: 1rem !important;
   }
 
   .ps-xl-4 {
     padding-left: 1.5rem !important;
   }
 
   .ps-xl-5 {
     padding-left: 3rem !important;
   }
 }
 @media (min-width: 1400px) {
   .d-xxl-inline {
     display: inline !important;
   }
 
   .d-xxl-inline-block {
     display: inline-block !important;
   }
 
   .d-xxl-block {
     display: block !important;
   }
 
   .d-xxl-grid {
     display: grid !important;
   }
 
   .d-xxl-table {
     display: table !important;
   }
 
   .d-xxl-table-row {
     display: table-row !important;
   }
 
   .d-xxl-table-cell {
     display: table-cell !important;
   }
 
   .d-xxl-flex {
     display: flex !important;
   }
 
   .d-xxl-inline-flex {
     display: inline-flex !important;
   }
 
   .d-xxl-none {
     display: none !important;
   }
 
   .flex-xxl-fill {
     flex: 1 1 auto !important;
   }
 
   .flex-xxl-row {
     flex-direction: row !important;
   }
 
   .flex-xxl-column {
     flex-direction: column !important;
   }
 
   .flex-xxl-row-reverse {
     flex-direction: row-reverse !important;
   }
 
   .flex-xxl-column-reverse {
     flex-direction: column-reverse !important;
   }
 
   .flex-xxl-grow-0 {
     flex-grow: 0 !important;
   }
 
   .flex-xxl-grow-1 {
     flex-grow: 1 !important;
   }
 
   .flex-xxl-shrink-0 {
     flex-shrink: 0 !important;
   }
 
   .flex-xxl-shrink-1 {
     flex-shrink: 1 !important;
   }
 
   .flex-xxl-wrap {
     flex-wrap: wrap !important;
   }
 
   .flex-xxl-nowrap {
     flex-wrap: nowrap !important;
   }
 
   .flex-xxl-wrap-reverse {
     flex-wrap: wrap-reverse !important;
   }
 
   .justify-content-xxl-start {
     justify-content: flex-start !important;
   }
 
   .justify-content-xxl-end {
     justify-content: flex-end !important;
   }
 
   .justify-content-xxl-center {
     justify-content: center !important;
   }
 
   .justify-content-xxl-between {
     justify-content: space-between !important;
   }
 
   .justify-content-xxl-around {
     justify-content: space-around !important;
   }
 
   .justify-content-xxl-evenly {
     justify-content: space-evenly !important;
   }
 
   .align-items-xxl-start {
     align-items: flex-start !important;
   }
 
   .align-items-xxl-end {
     align-items: flex-end !important;
   }
 
   .align-items-xxl-center {
     align-items: center !important;
   }
 
   .align-items-xxl-baseline {
     align-items: baseline !important;
   }
 
   .align-items-xxl-stretch {
     align-items: stretch !important;
   }
 
   .align-content-xxl-start {
     align-content: flex-start !important;
   }
 
   .align-content-xxl-end {
     align-content: flex-end !important;
   }
 
   .align-content-xxl-center {
     align-content: center !important;
   }
 
   .align-content-xxl-between {
     align-content: space-between !important;
   }
 
   .align-content-xxl-around {
     align-content: space-around !important;
   }
 
   .align-content-xxl-stretch {
     align-content: stretch !important;
   }
 
   .align-self-xxl-auto {
     align-self: auto !important;
   }
 
   .align-self-xxl-start {
     align-self: flex-start !important;
   }
 
   .align-self-xxl-end {
     align-self: flex-end !important;
   }
 
   .align-self-xxl-center {
     align-self: center !important;
   }
 
   .align-self-xxl-baseline {
     align-self: baseline !important;
   }
 
   .align-self-xxl-stretch {
     align-self: stretch !important;
   }
 
   .order-xxl-first {
     order: -1 !important;
   }
 
   .order-xxl-0 {
     order: 0 !important;
   }
 
   .order-xxl-1 {
     order: 1 !important;
   }
 
   .order-xxl-2 {
     order: 2 !important;
   }
 
   .order-xxl-3 {
     order: 3 !important;
   }
 
   .order-xxl-4 {
     order: 4 !important;
   }
 
   .order-xxl-5 {
     order: 5 !important;
   }
 
   .order-xxl-last {
     order: 6 !important;
   }
 
   .m-xxl-0 {
     margin: 0 !important;
   }
 
   .m-xxl-1 {
     margin: 0.25rem !important;
   }
 
   .m-xxl-2 {
     margin: 0.5rem !important;
   }
 
   .m-xxl-3 {
     margin: 1rem !important;
   }
 
   .m-xxl-4 {
     margin: 1.5rem !important;
   }
 
   .m-xxl-5 {
     margin: 3rem !important;
   }
 
   .m-xxl-auto {
     margin: auto !important;
   }
 
   .mx-xxl-0 {
     margin-right: 0 !important;
     margin-left: 0 !important;
   }
 
   .mx-xxl-1 {
     margin-right: 0.25rem !important;
     margin-left: 0.25rem !important;
   }
 
   .mx-xxl-2 {
     margin-right: 0.5rem !important;
     margin-left: 0.5rem !important;
   }
 
   .mx-xxl-3 {
     margin-right: 1rem !important;
     margin-left: 1rem !important;
   }
 
   .mx-xxl-4 {
     margin-right: 1.5rem !important;
     margin-left: 1.5rem !important;
   }
 
   .mx-xxl-5 {
     margin-right: 3rem !important;
     margin-left: 3rem !important;
   }
 
   .mx-xxl-auto {
     margin-right: auto !important;
     margin-left: auto !important;
   }
 
   .my-xxl-0 {
     margin-top: 0 !important;
     margin-bottom: 0 !important;
   }
 
   .my-xxl-1 {
     margin-top: 0.25rem !important;
     margin-bottom: 0.25rem !important;
   }
 
   .my-xxl-2 {
     margin-top: 0.5rem !important;
     margin-bottom: 0.5rem !important;
   }
 
   .my-xxl-3 {
     margin-top: 1rem !important;
     margin-bottom: 1rem !important;
   }
 
   .my-xxl-4 {
     margin-top: 1.5rem !important;
     margin-bottom: 1.5rem !important;
   }
 
   .my-xxl-5 {
     margin-top: 3rem !important;
     margin-bottom: 3rem !important;
   }
 
   .my-xxl-auto {
     margin-top: auto !important;
     margin-bottom: auto !important;
   }
 
   .mt-xxl-0 {
     margin-top: 0 !important;
   }
 
   .mt-xxl-1 {
     margin-top: 0.25rem !important;
   }
 
   .mt-xxl-2 {
     margin-top: 0.5rem !important;
   }
 
   .mt-xxl-3 {
     margin-top: 1rem !important;
   }
 
   .mt-xxl-4 {
     margin-top: 1.5rem !important;
   }
 
   .mt-xxl-5 {
     margin-top: 3rem !important;
   }
 
   .mt-xxl-auto {
     margin-top: auto !important;
   }
 
   .me-xxl-0 {
     margin-right: 0 !important;
   }
 
   .me-xxl-1 {
     margin-right: 0.25rem !important;
   }
 
   .me-xxl-2 {
     margin-right: 0.5rem !important;
   }
 
   .me-xxl-3 {
     margin-right: 1rem !important;
   }
 
   .me-xxl-4 {
     margin-right: 1.5rem !important;
   }
 
   .me-xxl-5 {
     margin-right: 3rem !important;
   }
 
   .me-xxl-auto {
     margin-right: auto !important;
   }
 
   .mb-xxl-0 {
     margin-bottom: 0 !important;
   }
 
   .mb-xxl-1 {
     margin-bottom: 0.25rem !important;
   }
 
   .mb-xxl-2 {
     margin-bottom: 0.5rem !important;
   }
 
   .mb-xxl-3 {
     margin-bottom: 1rem !important;
   }
 
   .mb-xxl-4 {
     margin-bottom: 1.5rem !important;
   }
 
   .mb-xxl-5 {
     margin-bottom: 3rem !important;
   }
 
   .mb-xxl-auto {
     margin-bottom: auto !important;
   }
 
   .ms-xxl-0 {
     margin-left: 0 !important;
   }
 
   .ms-xxl-1 {
     margin-left: 0.25rem !important;
   }
 
   .ms-xxl-2 {
     margin-left: 0.5rem !important;
   }
 
   .ms-xxl-3 {
     margin-left: 1rem !important;
   }
 
   .ms-xxl-4 {
     margin-left: 1.5rem !important;
   }
 
   .ms-xxl-5 {
     margin-left: 3rem !important;
   }
 
   .ms-xxl-auto {
     margin-left: auto !important;
   }
 
   .p-xxl-0 {
     padding: 0 !important;
   }
 
   .p-xxl-1 {
     padding: 0.25rem !important;
   }
 
   .p-xxl-2 {
     padding: 0.5rem !important;
   }
 
   .p-xxl-3 {
     padding: 1rem !important;
   }
 
   .p-xxl-4 {
     padding: 1.5rem !important;
   }
 
   .p-xxl-5 {
     padding: 3rem !important;
   }
 
   .px-xxl-0 {
     padding-right: 0 !important;
     padding-left: 0 !important;
   }
 
   .px-xxl-1 {
     padding-right: 0.25rem !important;
     padding-left: 0.25rem !important;
   }
 
   .px-xxl-2 {
     padding-right: 0.5rem !important;
     padding-left: 0.5rem !important;
   }
 
   .px-xxl-3 {
     padding-right: 1rem !important;
     padding-left: 1rem !important;
   }
 
   .px-xxl-4 {
     padding-right: 1.5rem !important;
     padding-left: 1.5rem !important;
   }
 
   .px-xxl-5 {
     padding-right: 3rem !important;
     padding-left: 3rem !important;
   }
 
   .py-xxl-0 {
     padding-top: 0 !important;
     padding-bottom: 0 !important;
   }
 
   .py-xxl-1 {
     padding-top: 0.25rem !important;
     padding-bottom: 0.25rem !important;
   }
 
   .py-xxl-2 {
     padding-top: 0.5rem !important;
     padding-bottom: 0.5rem !important;
   }
 
   .py-xxl-3 {
     padding-top: 1rem !important;
     padding-bottom: 1rem !important;
   }
 
   .py-xxl-4 {
     padding-top: 1.5rem !important;
     padding-bottom: 1.5rem !important;
   }
 
   .py-xxl-5 {
     padding-top: 3rem !important;
     padding-bottom: 3rem !important;
   }
 
   .pt-xxl-0 {
     padding-top: 0 !important;
   }
 
   .pt-xxl-1 {
     padding-top: 0.25rem !important;
   }
 
   .pt-xxl-2 {
     padding-top: 0.5rem !important;
   }
 
   .pt-xxl-3 {
     padding-top: 1rem !important;
   }
 
   .pt-xxl-4 {
     padding-top: 1.5rem !important;
   }
 
   .pt-xxl-5 {
     padding-top: 3rem !important;
   }
 
   .pe-xxl-0 {
     padding-right: 0 !important;
   }
 
   .pe-xxl-1 {
     padding-right: 0.25rem !important;
   }
 
   .pe-xxl-2 {
     padding-right: 0.5rem !important;
   }
 
   .pe-xxl-3 {
     padding-right: 1rem !important;
   }
 
   .pe-xxl-4 {
     padding-right: 1.5rem !important;
   }
 
   .pe-xxl-5 {
     padding-right: 3rem !important;
   }
 
   .pb-xxl-0 {
     padding-bottom: 0 !important;
   }
 
   .pb-xxl-1 {
     padding-bottom: 0.25rem !important;
   }
 
   .pb-xxl-2 {
     padding-bottom: 0.5rem !important;
   }
 
   .pb-xxl-3 {
     padding-bottom: 1rem !important;
   }
 
   .pb-xxl-4 {
     padding-bottom: 1.5rem !important;
   }
 
   .pb-xxl-5 {
     padding-bottom: 3rem !important;
   }
 
   .ps-xxl-0 {
     padding-left: 0 !important;
   }
 
   .ps-xxl-1 {
     padding-left: 0.25rem !important;
   }
 
   .ps-xxl-2 {
     padding-left: 0.5rem !important;
   }
 
   .ps-xxl-3 {
     padding-left: 1rem !important;
   }
 
   .ps-xxl-4 {
     padding-left: 1.5rem !important;
   }
 
   .ps-xxl-5 {
     padding-left: 3rem !important;
   }
 }
 @media print {
   .d-print-inline {
     display: inline !important;
   }
 
   .d-print-inline-block {
     display: inline-block !important;
   }
 
   .d-print-block {
     display: block !important;
   }
 
   .d-print-grid {
     display: grid !important;
   }
 
   .d-print-table {
     display: table !important;
   }
 
   .d-print-table-row {
     display: table-row !important;
   }
 
   .d-print-table-cell {
     display: table-cell !important;
   }
 
   .d-print-flex {
     display: flex !important;
   }
 
   .d-print-inline-flex {
     display: inline-flex !important;
   }
 
   .d-print-none {
     display: none !important;
   }
 }
 /**
 dashbaord
 */
 
.rbt-dashboard-top-wrapper-main {
  /* Admin dashboard */
  a,
  button {
      transition: all .3s;
  }
  
  .rainbow-dashboard-box {
      margin-right: 20px;
      background: #fff;
      border-radius: 5px;
      padding: 30px;
      margin-top: 30px;
  }
  
  .rainbow-dashboard-box-single {
      padding: 30px;
      padding: 50px 60px;
      border-radius: 5px;
  }
  
  .bg-cover {
      background-size: cover;
      background-position: center center;
  }
  
  .rainbow-dashboard-box-single .rainbow-subtitle {
      font-size: 12px;
      color: #fff;
      margin-bottom: 20px;
      display: block;
  }
  
  .rainbow-dashboard-box-single .rainbow-title,
  .rbt-dashboard-single-card .rainbow-title {
      font-size: 30px;
      color: #fff;
      margin: 0;
      line-height: 1.2;
      margin-bottom: 10px;
  }
  
  .rainbow-dashboard-box-single .rainbow-content {
      font-size: 16px;
      margin: 0;
      color: #fff;
      margin-bottom: 30px;
  }
  
  .rainbow-dashboard-btn {
      background: #fff;
      border: 1px solid rgba(255, 255, 255, 0.33);
      box-shadow: 0px 23px 20.9px -21px #5B048B;
      border-radius: 4px;
      display: inline-block;
      text-decoration: none;
      padding: 10px 26px;
      color: #fff;
      color: #000;
  }
  
  .rainbow-dashboard-btn:hover {
      outline: none;
      box-shadow: none;
      color: #fff;
      background-color: #029CFF;
      border-color: transparent;
  }
  
  .m-0 {
      margin: 0 !important;
  }
  
  .rbt-dashboard-single-card {
      text-align: center;
      border-radius: 10px;
  }
  .rbt-support-banner a {
      display: block;
      text-align: right;
      background-size: cover;
      background-position: center left;
  }
  .rbt-dashboard-single-card .rainbow-dashboard-btn {
      margin-top: 10px;
      font-weight: 500;
      box-shadow: 0px 20px 30px -18px #073983;
      width: fit-content;
      margin-left: auto;
      margin-right: auto;
  }
  
  .bg-default {
      background-size: cover;
      background-position: center center;
  }
  .rbt-dashboard-single-card {
    position: relative;
    min-height: 416px;
  }
  
  .rbt-dashboard-single-card .content {
    padding: 30px;
    padding-bottom: 0;
    text-align: left;
  }
  
  .rbt-dashboard-single-card .image {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: flex-end;
  }
  .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner {
      padding: 0;
  }
  
  .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner .content {
      padding: 30px;
      padding-bottom: 0;
  }
  div[data-background] {
      background-size: cover;
  }
  
  img {
      max-width: 100%;
  }
  
  .rbt-dashboard-single-card .rainbow-title {
      font-size: 30px;
      letter-spacing: -1px;
  }
  
  .rbt-content-inner .rbt-support-img {
      transform: translateY(5px);
  }
  .rbt-text-start.rbt-support-box-content {
      padding: 30px;
      padding-bottom: 0;
  }
  .rbt-content-inner .rbt-support-img img {
      margin-top: 42px;
  }
  
  .rbt-plugin-card {
      background: #FFFFFF;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0px 6px 34px rgba(215, 216, 222, 0.41);
      ;
      padding: 16px;
  }
  
  .rainbow-dashboard-box {
      margin: 12px !important;
      margin-bottom: 0 !important;
  }
  
  
  
  .el-license-valid {
      padding: 1px 8px;
  }
  
  .plugin-name {
      margin-top: 0;
  }
  
  .version {
      margin-top: 0;
  }
  
  .rbt-plugin-list {
      margin-top: 36px;
  }
  
  .screenshot img {
      border-radius: 6px;
  }
  .rbt-plugin-card {
      padding: 30px 25px;
  }
  
  span.alert.alert-warning {color: inherit;}
  
  .rbt-tab-buttons .rbt-tab-content-left {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 10px 0;
  }
  .rbt-dashboard-tab-area .ocdi__content-container {
      background: transparent;
      padding: 0;
  }
  .rbt-dashboard-tab-area .ocdi__content-container *{
      text-decoration: none;
  }
  .rbt-dashboard-tab-area .ocdi__theme-about {
      background: #fff;
      box-shadow: none;
      padding: 50px;
  }
  .rbt-plugin-card .version {
      margin-bottom: 23px;
  }
  
  .rbt-plugin-card {
      min-height: 168px;
      border: 0;
  }
  
  .rbt-plugin-card a {
      text-decoration: none;
  }
  
  .directivate-btn {
      background: #dc3545;
      color: #fff;
      padding: 5px 7px;
      text-decoration: none;
      border-radius: 5px;
      margin-left: 5px;
  }
  .activate-btn {
      background: #28a745;
      color: #fff;
      padding: 5px 7px;
      text-decoration: none;
      border-radius: 5px;
      margin-left: 5px;
  }
  .install-btn {
      background: #ffc107;
      color: #fff;
      padding: 5px 7px;
      text-decoration: none;
      border-radius: 5px;
      margin-left: 5px;
  }
  .directivate-btn:hover, .install-btn:hover, .activate-btn:hover {
      color: #fff;
  }
  .rbt-tab-content-wrapper a {
      text-decoration: none;
      border-radius: 5px;
  }
  .rbt-tab-content-wrapper a {
      text-decoration: none;
  }
  
  a.rbt-tab-content-link {
      display: flex;
      align-items: center;
      grid-gap: 0 5px;
      color: #394EF4;
  }
  .rbt-tab-buttons {
      background: #fff;
      padding: 15px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;
  }
  a.rbt-tab-content-link:hover {
      background: #007aff !important;
      color: #fff;
  }
  
  a.rbt-tab-content-link:hover img {
      filter: brightness(100);
  }
  .rbt-tab-buttons button, .rbt-tab-buttons a {
      background: #eaeefe;
      color: var(--color-dark-1);
      border: 0;
      padding: 15px 30px;
      cursor: pointer;
      margin-right: 10px;
      border-radius: 5px;
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
  }
  .rbt-tab-buttons button.active {
    background: #007aff !important;
    position: relative;
    color: #fff;
  }
  .rbt-tab-buttons button:hover, .rbt-tab-buttons a:hover {
    background: #d0d7f4;
  }
  .rbt-tab-buttons button.active::after {
      position: absolute;
      width: 21px;
      height: 7.51px;
      content: "";
      left: 50%;
      transform: translateX(-50%);
      bottom: -6.51px;
      clip-path: polygon(0 0, 100% 0, 50% 100%);
      background: #007aff;
  }
  
  .rainbow-dashboard-box-wrapper {
      margin: 0 12px;
      margin-top: 30px;
  }
  .rbt-m-0-i {
      margin: 0 !important;
  }
  .rbt-mt-30-i {
      margin-top: 30px !important;
  }
  .rbt-license-tab-form {
      background: #fff;
      border-radius: 10px;
      margin-top: 30px;
      padding: 30px;
  }
  
  .rbt-license-tab-form .el-license-container {
      margin: 0;
      padding: 0;
  }
  
  .rbt-license-tab-form .el-license-title {
      font-size: 36px;
      color: #1B182C;
      margin: 0;
      line-height: 1;
  }
  .rbt-inactive--page.rbt-license-wrapper {
    background: #fff;
    margin-left: 20px;
    margin-top: 20px;
    padding: 50px;
    border-radius: 10px;
  }
  ul.el-license-info.rbt-el-license-info p {
    font-size: 16px;
    margin: 0;
    color: #717189;
    line-height: 26px;
    margin-bottom: 10px;
  }
  .rbt-license-tab-form  hr {display: none;}
  .rbt--video-container iframe {
      width: 100%;
      height: 100%;
      border-radius: 10px;
  }
  
  .rbt-inactive--page.rbt-license-wrapper .el-license-container {
      background: transparent;
      padding: 0;
      margin: 0;
  }
  .rbt-tab-content-right a {
      color: #394EF4;
  }
  .rbt-tab-content .ocdi__title-container {
      background: transparent;
      box-shadow: none;
      padding: 43px 0px;
      padding-bottom: 20px;
  }
  .rbt-tab-content .ocdi__title-container-title {display: block;font-size: 36px;font-weight: 500;}
  .rainbow-dashboard-box-wrapper {
      background: #F6F7FE;
      padding: 30px;
      border-radius: 10px;
  }
  .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title .theme-version {
      position: absolute;
      top: -2px;
      right: -100%;
      background: linear-gradient(126.67deg, #748BFC -5.73%, #FF72BA 96.56%);
      border-radius: 30px;
      padding: 5px 12px;
      color: #fff;
      font-size: 14px;
  }
  
  .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title {
      display: inline-block;
      position: relative;
  }
  
  .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title .theme-version::after {
      position: absolute;
      left: 10px;
      top: 86%;
      content: "";
      width: 9.53px;
      height: 8px;
      clip-path: polygon(0 0, 100% 50%, 0 100%);
      background: #a582e5;
  }
  .rbt-tab-content 
   .ocdi__theme-about-info .theme-description {
      color: #717189;
  }
  .rbt-tab-content .ocdi__theme-about-info .theme-tags {
      color: #717189;
  }
  .rbt-tab-content 
   .ocdi__theme-about-info .ocdi-import-mode-switch {
      background: #3458F0;
      color: #fff;
      padding: 12px;
      border-radius: 5px;
  }
  .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl hr {
      display: none;
  }
  
  .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl.js-ocdi-gl::before {
      content: "Import Pre-Built Demos";
      text-align: center;
      display: block;
      color: #1B182C;
      font-size: 36px;
      font-weight: 500;
      padding-bottom: 80px;
      padding-top: 50px;
  }
  .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
      position: relative;
      -ms-flex: 0 0 calc(25% - 30px);
      flex: 0 0 calc(25% - 30px);
      padding: 0 10px;
      padding-top: 30px;
      background: #fff;
      border: 0;
      box-shadow: 0px 0px 35.4466px 14.6974px rgba(0, 0, 0, 0.05);
      border-radius: 10px;
  }
  .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item::after {
      position: absolute;
      left: 15px;
      top: 5px;
      content: url(../../../assets/images/dashboard/three-dot.svg);
  }
  .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:first-child, .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:last-child {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(9, 2, 47, 0.1);
      background: #fff !important;
      padding: 0 12px;
      border-radius: 4px;
      color: rgba(9, 2, 47, 0.6);
  }
  .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:last-child {
      border: 0;
      background-color: #3458F0 !important;
      color: #fff;
  }
  .rbt-dashboard-tab-area .ocdi__gl-item:hover .ocdi__gl-item-footer {
      border: 0;
      padding: 12px 0;
  }
  .rbt-dashboard-tab-area .ocdi__gl-item:hover .ocdi__gl-item-buttons {
      display: flex;
      justify-content: center;
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      padding: 19px 0;
  }
  .rbt-dashboard-tab-area .ocdi__gl-item-footer.ocdi__gl-item-footer--with-preview {
      border: 0;
  }
  
  .rbt-justify-content-center {
      justify-content: center;
  }
  /**
  * table css
  **/
  .rbt-box.rbt-status.rbt-theme-style {
      background: #fff;
      padding: 30px;
      margin-right: 20px;
      border-radius: 5px;
      margin-top: 20px;
  }
  .rbt-table {
      margin: 0;
      padding: 0;
      border-collapse: collapse;
      width: 100%;
  }
  
  .rbt-table-row {
      display: flex;
  }
  
  .rbt-table-row > div {
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
  }
  
  /* Header styles */
  .rbt-box-header h3, .rbt-box-header h4 {
      margin: 0;
  }
  
  /* Theme and Server section styles */
  .rbt-box-content {
      margin-top: 10px;
  }
  
  .rbt-box-content h4 {
      margin-top: 20px;
  }
  
  /* Error message styles */
  .rbt-status-error {
      color: #ff0000;
  }
  
  /* Odd row styles */
  .rbt-odd .rbt-table-row:nth-child(odd) > div {
      background-color: #f9f9f9;
  }
  
  /* Hover effect */
  .rbt-table-row:hover > div {
      background-color: #f1f1f1;
  }
  
  .rbt-content-inner .rbt-content-right {
      position: absolute;
      right: -1px;
      top: 0;
      height: 100%;
  }
  .rainbow-dashboard-box-single {
      position: relative;}
  
  .rbt-content-inner .rbt-content-right img {
      height: 100%;
  }
  .rbt-license-tab-form .el-license-container .el-license-field input {
    max-width: 100%;
  }
  
  .rbt-support-author img {
    width: 100%;
    object-fit: cover;
    max-height: 416px;
    border-radius: 10px;
  }
  .rbt-tab-content-right a {
    margin-right: 0;
  }
  /**
  * responsive
  */
  @media (max-width: 1399px) {
      .rbt-content-inner .rbt-content-right img {
          display: none;
      }
  }
  @media (max-width: 1199px) {
    .rbt-tab-buttons button, .rbt-tab-buttons a {
        padding: 9px 18px;
    }
  }
  @media (min-width: 1200px) and (max-width: 1399px), (min-width: 992px) and (max-width: 1199px), (min-width: 768px) and (max-width: 991px) {
      .rbt-tab-buttons .rbt-tab-content-left {
          flex: 0 0 70%;
      }
      .rbt-tab-buttons button {
          margin-bottom: 15px;
      }
      .rbt-content-inner .rbt-support-img img {
          margin-top: 0px;
      }
      .rbt-tab-buttons button, .rbt-tab-buttons a {
          margin-bottom: 15px;
      }
  }
  @media (min-width: 768px) and (max-width: 991px) {
    .rbt-dashboard-single-card {
        min-height: 396px;
    }
    .rbt-dashboard-single-card .rainbow-title {
        font-size: 21px;
    }
  }
  @media (max-width: 767px) {
    .rbt-dashboard-single-card .rainbow-title {
        font-size: 21px;
    }
    .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner .content {
        padding: 20px;
    }
    .rbt-dashboard-single-card {
        min-height: 336px;
    }
    .rbt-dashboard-single-card .content {
        padding: 20px;
        padding-bottom: 0;
    }
    .rbt-dashboard-single-card .rainbow-dashboard-btn {
        padding: 10px 12px;
    }
  }
  @media (min-width: 992px) and (max-width: 1199px) {
     
      .rbt-dashboard-single-card.bg-default.padding-box .rbt-content-inner {
          padding: 0;
      }
      .rbt-dashboard-single-card.padding-box:not(p-0) {
          padding-bottom: 0;
      }
      .rbt-dashboard-single-card {
          border-radius: 10px;
      }
      .rbt-support-banner {
          margin-top: 0;
      }
      .rbt--video-container {
          margin-left: 0;
      }
      .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
          flex: 0 0 calc(33.33% - 30px);
      }
      .rbt-dashboard-tab-area .ocdi__content-container .ocdi__theme-about-screenshots {
          flex: 0 0 100%;
      }
      .rbt-dashboard-tab-area .ocdi__theme-about {
          flex-wrap: wrap;
          grid-gap: 30px 0;
      }
  }
  @media (min-width: 768px) and (max-width: 991px) {
      .rbt-dashboard-single-card.rbt-no-box-padding.bg-default {
        height: 100%;
      }
      .rbt-support-banner {
          margin-top: 0;
      }
      .rbt--video-container {
          margin-left: 0;
      }
      .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
          flex: 0 0 calc(50% - 30px);
      }
      .rbt-dashboard-single-card.bg-default.padding-box .rbt-content-inner {
          padding: 0;
      }
  }
  @media (max-width: 767px) {
      .rbt-tab-buttons {
          display: block;
      }
      .rbt-dashboard-single-card .rainbow-title {
        font-size: 25px;
      }
      .rbt-support-banner a {
          background-size: contain;
          background-repeat: no-repeat;
          min-height: auto;
      }
      .rbt-support-banner a {
          background-size: contain;
          background-repeat: no-repeat;
          min-height: auto;
      }
      .rainbow-dashboard-box-single.bg-default .rbt-content-inner {
          padding: 0;
      }
      .rainbow-dashboard-box-single.bg-default .rbt-content-inner .rainbow-title {
          font-size: 28px;
      }
      .rbt-tab-buttons button, .rbt-tab-buttons a {
          margin-bottom: 15px;
      }
      .rbt-tab-content-right a.rbt-tab-content-link {
          display: inline-block;
          margin-bottom: 0;
      }
      .rbt--video-container {
          margin-left: 0;
      }
      .rbt-license-tab-form {
          padding: 10px 10px;
      }
      .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
          flex: 0 0 calc(100%);
      }
      
      .rbt-dashboard-tab-area .ocdi__content-container .bottom-content {
          margin-top: 20px;
        }
        .rbt-dashboard-tab-area .ocdi__content-container * .ocdi-import-mode-switch {
          float: none;
        }
  }
  @media (max-width: 575px) {
      .rbt-tab-content .ocdi__title-container {
          padding: 50px 19px;
        }
        .rbt-tab-content .ocdi__title-container .ocdi__title-container-title {
          font-size: 26px;
        }
        .rbt-dashboard-tab-area .ocdi__content-container .ocdi__intro-text {
          padding: 0 22px;
        }
      .rainbow-dashboard-box-single {
          padding: 21px;
      }
      .rbt-tab-buttons button, .rbt-tab-buttons a {
          font-size: 12px;
      }
  
      .rbt-dashboard-tab-area .ocdi__theme-about {
          padding: 20px;
      }
      .rbt-dashboard-tab-area .ocdi__theme-about {
          padding: 20px;
        }
        
      .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl.js-ocdi-gl::before {
          font-size: 20px;
          line-height: 1.4;
      }
  }
  
  @media (max-width: 1599px) {
      .rbt-order-last-max-xxl {
          order: 9;
      }
  }
  /**
  * modal style
  */
  /*-- Variables --*/

  .backdrop {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(7.5px);
      padding-right: 0px !important;
  }
  
  .modal-content {
      width: 745px;
      border-radius: var(--radius-lg);
  }
  .modal-content.w--fit {
      width: fit-content;
      max-width: 100%;
  }
  .modal-dialog {
      max-width: fit-content;
  }
  .modal-close-btn {
      position: absolute;
      top: -24px;
      right: -24px;
      display: inline-block;
      background: var(--color-white);
      border: 1px solid #e3e0f5;
      width: 48px;
      height: 48px;
      border-radius: var(--radius-full);
  }
  @media only screen and (min-width: 768px) and (max-width: 991px) {
      .modal-close-btn {
          top: 0;
          right: 0;
          width: 32px;
          height: 32px;
          background: transparent;
          border: none;
          font-size: var(--h6);
     }
  }
  @media only screen and (max-width: 767px) {
      .modal-close-btn {
          top: 0;
          right: 0;
          width: 32px;
          height: 32px;
          background: transparent;
          border: none;
          font-size: var(--h6);
     }
  }
  .rbt-modal-title {
      color: var(--color-dark-1);
      font-family: var(--font-primary);
      font-size: var(--h5);
      font-weight: var(--f-bold);
      line-height: 141.667%;
      border-bottom: 1px solid #e3e0f5;
      padding-bottom: 24px;
      margin-bottom: 32px;
  }
  .rbt-modal-title.inner-title {
      font-size: var(--font-size-b1);
      line-height: 140%;
      border: none;
      margin-bottom: 0;
  }
  .rbt-product-changelog {
      margin-bottom: 40px;
  }
  .changelog-info {
      padding: 12px 16px;
  }
  @media only screen and (max-width: 767px) {
      .changelog-info {
          padding: 8px;
     }
  }
  .rbt-product-changelog .changelog-info:nth-child(even) {
    background: #f8f8f8;
  }
  .changelog-info:last-child {
      border-radius: 0 0 8px 8px;
  }
  .changelog-info.filled {
      background: var(--color-grayscale-11);
  }
  .changelog-info .info-text {
      color: var(--color-blue-2);
      font-family: var(--font-secondary);
      font-size: var(--font-size-b2);
      font-weight: var(--f-medium);
      line-height: 150%;
  }
  .rbt-license-tab-form .notice.notice-error.is-dismissible {
    margin-bottom: 12px;
  }
  @media only screen and (max-width: 767px) {
      .changelog-info .info-text {
        font-size: var(--font-size-b3);
     }
  }
  .rbt-badge {
    padding: 3px 7px;
    border-radius: var(--radius-xxl);
    color: var(--color-white);
    font-family: var(--font-secondary);
    font-size: var(--font-size-b4);
    font-weight: var(--f-medium);
    line-height: 100%;
    letter-spacing: 0.6px;
    background: var(--color-secondary);
    font-size: 12px;
    border-radius: 3px;
  }
  @media only screen and (max-width: 767px) {
      .rbt-badge {
          font-size: var(--font-size-b5);
     }
  }
  .el-license-title .rbt-badge {
    text-wrap: nowrap;
  }
  .el-license-container .el-license-field input {
    max-width: 100%;
    border-color: #f1f2f3;
  }
  .el-license-active-btn input#submit:focus {
    box-shadow: none;
  }
  .el-license-container .el-license-field small {
    color: #717188;
    font-size: 16px;
    margin-top: 16px;
    display: block;
    line-height: 1.4;
  }
  .text-heading {
    color: #1d2327 !important;
  }
  .rbt-badge.badge-bg-2 {
      background: var(--color-primary);
  }
  .rbt-badge.badge-bg-3 {
      background: var(--color-success);
  }
  .rbt-badge.badge-bg-4 {
      background: var(--color-primary);
  }
  .rbt-badge.badge-bg-5 {
      background: var(--color-warning);
  }
  .inspired-badge {
      position: absolute;
      top: 24px;
      right: 24px;
  }
  @media only screen and (max-width: 767px) {
      .inspired-badge {
          top: 8px;
          right: 8px;
     }
  }
  .rbt-modal-wrapper > .rbt-modal-title {
    margin: 0;
    border: none;
    color: var(--color-dark-1);
    background: #eaeefe;
    padding: 12px 20px;
    border-top-left-radius: 9px;
    border-top-right-radius: 10px;
    font-size: 18px;
    border-bottom: 1px solid #e3e0f5;
  }
  .rbt-product-changelog {
    padding: 0 20px;
  }
  .rbt-modal-wrapper {
      position: sticky;
      top: 60px;
  }
  .rbt-modal-wrapper {
    border: 1px solid #e3e0f5;
    border-radius: 10px;
    padding-top: 0;
  }
  .rbt-changelog-body {
    max-height: 700px;
    overflow-y: auto;
  }
  .changelog-info.filled .row {
      display: flex;
  }
  
  
  .changelog-info .row .item:last-child {
      width: calc(100% - 84px);
  }
  .rbt-text-left {
      text-align: left;
  }
  .changelog-info .row .item:first-child {
      width: 60px;
  }
  @media (min-width: 1600px) and (max-width: 1799px) {
      .rbt-dashboard-single-card {
          min-height: 390px;
      }
  }
  @media (min-width: 1200px) and (max-width: 1399px) {
      .rbt-dashboard-single-card.rbt-no-box-padding {
          padding: 30px;
      }
  }
  .rbt-danger {
      color: red;
  }
  .redux-container .redux-sidebar .redux-group-menu li.hasSubSections.redux-section-hover>a {
      background: linear-gradient(90deg, #f61b10, #ef0963);
  }
  
  
  /**
  * OCDI CSS
  */
  .ocdi {
      max-width: none;
  }
  .ocdi h2 {
      text-align: inherit;
      font-size: 32px;
      line-height: 39px;
      font-weight: 500;
      margin-bottom: 20px;
  }
  .ocdi h2:first-child, .ocdi h3:first-child {
      margin-top: 0;
  }
  .ocdi hr {
      margin: 30px 0;
  }
  .ocdi .notice, .ocdi .update-nag, .ocdi #update-nag {
      display: block !important;
      margin: 0 0 30px 0;
  }
  .ocdi-notices-wrapper {
      display: none;
  }
  .ocdi-notices-wrapper .notice, .ocdi-notices-wrapper .update-nag, .ocdi-notices-wrapper #update-nag {
      margin: 0 0 15px 0;
  }
  .ocdi-notices-wrapper .notice:last-child, .ocdi-notices-wrapper .update-nag:last-child, .ocdi-notices-wrapper #update-nag:last-child {
      margin-bottom: 30px;
  }
  .ocdi__admin-notices .ocdi-notices-wrapper {
      display: block;
  }
  .ocdi-button-disabled {
      opacity: 0.6 !important;
      cursor: not-allowed !important;
  }
  .ocdi-content-notice {
      padding: 25px;
      background: #e5f5fa;
      border-radius: 6px;
      margin: 30px;
  }
  .ocdi-content-notice p {
      font-size: 14px;
      line-height: 24px;
      color: #444;
      margin: 0 0 10px 0;
  }
  .ocdi-content-notice p:last-child {
      margin-bottom: 0;
  }
  .ocdi-content-notice--warning {
      background: #fff8e5;
      margin-top: 0;
  }
  .ocdi-content-notice--warning p {
      font-size: 16px;
      line-height: 24px;
  }
  .ocdi-importing, .ocdi-imported {
      display: none;
      text-align: center;
      padding: 30px;
  }
  .ocdi-importing p, .ocdi-imported p {
      font-size: 16px;
      line-height: 19px;
      font-weight: 300;
      color: #444;
      margin: 0 0 10px 0;
  }
  .ocdi-importing p:last-child, .ocdi-imported p:last-child {
      margin-bottom: 0;
  }
  .ocdi-importing-header h2, .ocdi-imported-header h2 {
      font-size: 24px;
      line-height: 29px;
      margin-bottom: 10px;
  }
  .ocdi-importing-header p, .ocdi-imported-header p {
      margin-bottom: 5px;
  }
  .ocdi-importing-header p:last-child, .ocdi-imported-header p:last-child {
      margin-bottom: 0;
  }
  .ocdi-importing-content, .ocdi-imported-content {
      margin: 0 20px;
  }
  .ocdi-importing-content-importing, .ocdi-imported-content-importing {
      width: 415px;
      height: 228px;
      margin: 50px 0 20px 0;
  }
  .ocdi-importing-content-imported, .ocdi-imported-content-imported {
      margin: 80px 0 50px 0;
  }
  .ocdi-importing-content-imported--success, .ocdi-imported-content-imported--success {
      width: 156px;
      height: 124px;
  }
  .ocdi-importing-content-imported--error, .ocdi-imported-content-imported--error, .ocdi-importing-content-imported--warning, .ocdi-imported-content-imported--warning {
      width: 124px;
      height: 124px;
  }
  .ocdi-importing-content .notice, .ocdi-imported-content .notice {
      text-align: left;
      margin: 30px 0;
  }
  .ocdi-importing-content .notice p, .ocdi-imported-content .notice p {
      padding: 15px 3px;
      font-size: 14px;
      line-height: 22px;
      color: #777;
  }
  .ocdi-importing-footer, .ocdi-imported-footer {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      min-height: 100px;
      background-color: #f8f8f8;
      border-top: 1px solid #ddd;
      margin: 30px -30px -30px;
  }
  .ocdi-importing-footer a.button, .ocdi-imported-footer a.button {
      flex: 0 1 25%;
      margin-right: 30px;
  }
  .ocdi-importing-footer a.button:last-child, .ocdi-imported-footer a.button:last-child {
      margin-right: 0;
  }
  .ocdi .button.button-hero {
      font-size: 16px;
      line-height: 20px;
      font-weight: 500;
      min-height: 40px;
      padding: 9px 18px;
  }
  .ocdi .button.button-hero.ocdi__button.button-primary:disabled, .ocdi .button.button-hero.ocdi__button.button-primary[disabled] {
      color: #fff !important;
      background: #999 !important;
      border-color: #999 !important;
      opacity: 0.5;
  }
  .ocdi__redux-option-name-input {
      margin-left: 10px;
      width: 137px;
      border-radius: 3px !important;
      padding: 0 10px !important;
      font-size: 13px !important;
      line-height: 16px !important;
  }
  .ocdi-hide-input {
      width: 0.1px !important;
      height: 0.1px !important;
      opacity: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      z-index: -1 !important;
  }
  .feature-section + hr {
      margin-top: 0;
  }
  #wpbody select {
      height: auto;
      padding: 0.62em;
      line-height: inherit;
  }
  .ocdi__title-container {
      height: 30px;
      background-color: #fff;
      padding: 20px 30px;
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.07);
      display: flex;
      align-items: center;
      justify-content: space-between;
  }
  .ocdi__title-container-title {
      margin: 0;
      font-size: 24px;
      line-height: 29px;
      font-weight: 700;
  }
  .ocdi__title-container-icon {
      width: 19px;
      height: 19px;
  }
  .ocdi__content-container {
      padding: 30px;
  }
  .ocdi__content-container-content {
      display: flex;
  }
  .ocdi__content-container-content--main {
      flex: 1;
      margin-right: 30px;
      background: #fff;
      box-sizing: border-box;
      border: 1px solid #ddd;
      box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.07);
  }
  .ocdi__content-container-content--side {
      width: 373px;
  }
  .ocdi__content-container-content--side .ocdi__card-content {
      padding: 0;
  }
  .ocdi__content-container-content--side .screenshot.blank {
      height: 278px;
      border: 1px solid #ccd0d4;
      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAALElEQVQYGWO8d+/efwYkoKioiMRjYGBC4WHhUK6A8T8QIJt8//59ZC493AAAQssKpBK4F5AAAAAASUVORK5CYII=);
  }
  .ocdi__content-container-content--side .ocdi__card-footer {
      padding: 15px;
  }
  .ocdi__content-container-content--side .ocdi__card-footer h3 {
      font-size: 16px;
      line-height: 19px;
      font-weight: 500;
      margin: 0;
  }
  .ocdi__content-container-content--side img {
      width: 100%;
      display: block;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header, .ocdi__content-container-content .ocdi-create-content-header {
      padding: 30px;
      border-bottom: 1px solid #ddd;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header h2, .ocdi__content-container-content .ocdi-create-content-header h2 {
      font-size: 22px;
      line-height: 26px;
      font-weight: normal;
      margin: 0 0 6px 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header p, .ocdi__content-container-content .ocdi-create-content-header p {
      font-size: 16px;
      line-height: 22px;
      font-weight: 300;
      color: #444;
      margin: 0 0 10px 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header p:last-child, .ocdi__content-container-content .ocdi-create-content-header p:last-child {
      margin: 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header .notice, .ocdi__content-container-content .ocdi-create-content-header .notice {
      margin: 30px 0 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-header .notice p, .ocdi__content-container-content .ocdi-create-content-header .notice p {
      margin: 0.5em 0;
      font-size: 13px;
      line-height: 1.5;
      color: #3c434a;
      font-weight: normal;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item, .ocdi__content-container-content .ocdi-create-content-content .plugin-item, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item, .ocdi__content-container-content .ocdi-create-content-content .content-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 30px;
      padding: 25px 0;
      border-bottom: 1px solid #eee;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .ocdi-loading, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .ocdi-loading, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .ocdi-loading, .ocdi__content-container-content .ocdi-create-content-content .content-item .ocdi-loading {
      display: none;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active, .ocdi__content-container-content .ocdi-create-content-content .content-item--active {
      cursor: default;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox {
      border: none;
      background: #64b450;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox .ocdi-check-icon {
      display: inline-block;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked + .checkbox::after {
      display: none !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--required, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--required, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--required, .ocdi__content-container-content .ocdi-create-content-content .content-item--required {
      cursor: default;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .ocdi-loading, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .ocdi-loading, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .ocdi-loading, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .ocdi-loading {
      display: block;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox {
      border: none !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox .ocdi-lock-icon {
      display: none !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox] + .checkbox::after {
      display: none !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content, .ocdi__content-container-content .ocdi-create-content-content .content-item-content {
      margin-right: 15px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title, .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title {
      display: flex;
      align-items: center;
      margin: 0 0 4px 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title span, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title span, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title span, .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title span {
      margin-left: 5px;
      background-color: #ffb900;
      width: 16px;
      height: 16px;
      border-radius: 8px;
      margin-top: -1px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title span img, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title span img, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title span img, .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title span img {
      width: 10px;
      height: 10px;
      margin: 3px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-info p, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-info p, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-info p, .ocdi__content-container-content .ocdi-create-content-content .content-item-info p {
      color: #00a32a !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-error p, .ocdi__content-container-content .ocdi-create-content-content .plugin-item-error p, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-error p, .ocdi__content-container-content .ocdi-create-content-content .content-item-error p {
      color: #d63638 !important;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item:last-child, .ocdi__content-container-content .ocdi-create-content-content .plugin-item:last-child, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item:last-child, .ocdi__content-container-content .ocdi-create-content-content .content-item:last-child {
      padding-bottom: 30px;
      border-bottom: none;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item label, .ocdi__content-container-content .ocdi-create-content-content .plugin-item label, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item label, .ocdi__content-container-content .ocdi-create-content-content .content-item label {
      display: block;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item h3, .ocdi__content-container-content .ocdi-create-content-content .plugin-item h3, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item h3, .ocdi__content-container-content .ocdi-create-content-content .content-item h3 {
      font-size: 18px;
      line-height: 22px;
      color: #444;
      font-weight: 500;
      margin: 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item p, .ocdi__content-container-content .ocdi-create-content-content .plugin-item p, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item p, .ocdi__content-container-content .ocdi-create-content-content .content-item p {
      font-size: 14px;
      line-height: 17px;
      color: #777;
      margin: 0 0 6px 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item p:last-child, .ocdi__content-container-content .ocdi-create-content-content .plugin-item p:last-child, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item p:last-child, .ocdi__content-container-content .ocdi-create-content-content .content-item p:last-child {
      margin: 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox, .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox {
      box-sizing: border-box;
      position: relative;
      display: block;
      background: #f1f1f1;
      width: 32px;
      height: 32px;
      border-radius: 16px;
      border: 1px solid #ddd;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox .ocdi-check-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox .ocdi-check-icon {
      display: none;
      width: 20px;
      height: 20px;
      margin: 6px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox .ocdi-lock-icon, .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox .ocdi-lock-icon {
      position: absolute;
      width: 14px;
      height: 17px;
      bottom: -5px;
      right: -2px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox input[type=checkbox], .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox input[type=checkbox] {
      display: none;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox input[type=checkbox]:checked + .checkbox::after, .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox input[type=checkbox]:checked + .checkbox::after {
      content: '';
      display: block;
      font-size: 10px;
      background: #007cba;
      width: 20px;
      height: 20px;
      border-radius: 10px;
      margin: 5px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content-notice, .ocdi__content-container-content .ocdi-create-content-content-notice {
      display: none;
      padding: 25px;
      background: #e5f5fa;
      border-radius: 6px;
      margin: -10px 30px 30px 30px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content-notice p, .ocdi__content-container-content .ocdi-create-content-content-notice p {
      font-size: 14px;
      line-height: 24px;
      color: #444;
      margin: 0 0 10px 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content-notice p:last-child, .ocdi__content-container-content .ocdi-create-content-content-notice p:last-child {
      margin-bottom: 0;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content span.ocdi-recommended-star, .ocdi__content-container-content .ocdi-create-content-content span.ocdi-recommended-star {
      background-color: #ffb900;
      width: 16px;
      height: 16px;
      border-radius: 8px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-content span.ocdi-recommended-star img, .ocdi__content-container-content .ocdi-create-content-content span.ocdi-recommended-star img {
      width: 10px;
      height: 10px;
      margin: 2px 3px;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-footer, .ocdi__content-container-content .ocdi-create-content-footer {
      padding: 30px;
      background: #fafafa;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-footer .button, .ocdi__content-container-content .ocdi-create-content-footer .button {
      font-size: 16px;
      line-height: 19px;
      padding: 10px 18px;
      display: flex;
      align-items: center;
  }
  .ocdi__content-container-content .ocdi-install-plugins-content-footer .button img, .ocdi__content-container-content .ocdi-create-content-footer .button img {
      width: 16px;
      height: auto;
      margin-right: 5px;
  }
  .ocdi__intro-text p {
      font-size: 18px;
      line-height: 26px;
      font-weight: 300;
      color: #666;
      margin: 0 0 24px;
  }
  .ocdi__intro-text ul {
      padding: 0 4%;
      list-style-type: square;
  }
  .ocdi__theme-about {
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
  }
  .ocdi__theme-about-screenshots {
      flex: 11;
      margin-right: 30px;
  }
  .ocdi__theme-about-screenshots .screenshot {
      box-sizing: border-box;
  }
  .ocdi__theme-about-screenshots .screenshot img {
      border: 1px solid #ccd0d4;
      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
  }
  .ocdi__theme-about-screenshots .screenshot.blank {
      border: 1px solid #ccd0d4;
      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
      min-height: 500px;
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAALElEQVQYGWO8d+/efwYkoKioiMRjYGBC4WHhUK6A8T8QIJt8//59ZC493AAAQssKpBK4F5AAAAAASUVORK5CYII=);
  }
  .ocdi__theme-about-screenshots img {
      width: 100%;
  }
  .ocdi__theme-about-info {
      flex: 10;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
  }
  .ocdi__theme-about-info .theme-title {
      display: flex;
      align-items: baseline;
  }
  .ocdi__theme-about-info .theme-title .theme-name {
      margin-bottom: 16px;
  }
  .ocdi__theme-about-info .theme-title .theme-version {
      margin-left: 10px;
      color: #72777c;
      font-size: 13px;
      line-height: 16px;
  }
  .ocdi__theme-about-info .theme-author {
      font-size: 16px;
      line-height: 19px;
      color: #72777c;
      margin: 0 0 20px;
  }
  .ocdi__theme-about-info .theme-description {
      font-size: 16px;
      line-height: 24px;
      color: #555;
      margin: 0 0 20px;
  }
  .ocdi__theme-about-info .theme-tags {
      font-size: 13px;
      line-height: 20px;
      color: #555;
      margin: 4px 0 0;
  }
  .ocdi__theme-about-info .theme-tags span {
      font-weight: 700;
  }
  .ocdi__theme-about-info .ocdi-import-mode-switch {
      float: right;
      font-size: 14px;
      line-height: 17px;
  }
  @media (max-width: 880px) {
      .ocdi__theme-about {
          flex-direction: column;
     }
      .ocdi__theme-about-screenshots {
          margin: 0 0 30px 0;
     }
  }
  .ocdi__demo-import-files {
      width: 100%;
  }
  .ocdi__demo-import-preview-image-message {
      font-style: italic;
  }
  .ocdi__title:before {
      width: auto;
      height: auto;
      font-size: inherit;
  }
  .ocdi__multi-select-import, .ocdi__demo-import-notice:not(:empty) {
      padding: 20px;
      margin: 30px 0;
      font-size: 14px;
      line-height: 19px;
      background-color: #fff;
      border: 1px solid #e5e5e5;
  }
  .ocdi__file-upload-container {
      border: 1px solid #ddd;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.07);
  }
  .ocdi__file-upload-container--header {
      padding: 30px;
      border-bottom: 1px solid #ddd;
      background-color: #fff;
  }
  .ocdi__file-upload-container--header h2 {
      font-size: 22px;
      line-height: 27px;
      margin: 0;
  }
  .ocdi__file-upload-container-items {
      padding: 30px 30px 0 30px;
      background-color: #fff;
      display: flex;
      flex-wrap: wrap;
  }
  .ocdi__file-upload-container-items--second-row {
      padding-top: 0;
  }
  .ocdi__file-upload-container-items .ocdi__card {
      box-shadow: none;
  }
  .ocdi__file-upload-container-items .ocdi__card-content {
      position: relative;
  }
  .ocdi__file-upload-container-items .ocdi__card-content-info {
      position: absolute;
      right: 10px;
      top: 10px;
  }
  .ocdi__file-upload-container-items .ocdi__card-content-info img {
      width: 16px;
      height: 16px;
  }
  .ocdi__file-upload-container--footer {
      padding: 30px;
      background-color: #fafafa;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
  }
  .ocdi__demo-import-notice:not(:empty) {
      border: 0;
      border-left: 4px solid #00a0d2;
      box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
  }
  [dir="rtl"] .ocdi__demo-import-notice:not(:empty) {
      border: 0;
      border-right: 4px solid #00a0d2;
  }
  .ocdi__button-container {
      margin-top: 30px;
  }
  .ocdi__ajax-loader {
      font-size: 1.5em;
  }
  .ocdi__ajax-loader .spinner {
      display: inline-block;
      float: none;
      visibility: visible;
      margin-bottom: 6px;
  }
  .ocdi__gl-navigation li a {
      box-shadow: none;
  }
  .ocdi__gl-item-container {
      display: flex;
      flex-wrap: wrap;
  }
  .ocdi__gl-item {
      flex: 0 0 100%;
      margin-bottom: 20px;
      border: 1px solid #ddd;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.07);
      box-sizing: border-box;
      background-color: #fafafa;
  }
  .ocdi__gl-item-image-container {
      display: block;
      overflow: hidden;
      position: relative;
      -webkit-backface-visibility: hidden;
      transition: opacity 0.2s ease-in-out;
  }
  .ocdi__gl-item-image-container::after {
      content: "";
      display: block;
      padding-top: 66.66666%;
  }
  .ocdi__gl-item-image {
      height: auto;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      transition: opacity 0.2s ease-in-out;
  }
  .ocdi__gl-item-image--no-image {
      display: inline-block;
      width: 50%;
      text-align: center;
      position: absolute;
      top: 45%;
      right: 25%;
      left: 25%;
  }
  .ocdi__gl-item-footer {
      margin: 0;
      padding: 8px 10px;
      border-top: 1px solid #ddd;
      background: #fff;
      background: rgba(255, 255, 255, 0.65);
  }
  .ocdi__gl-item-title {
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
      margin: 0;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
      font-weight: 500;
      color: #23282d;
      padding: 5px 0 6px;
  }
  @media (max-width: 782px) {
      .ocdi__gl-item-title {
          padding: 12px 0 13px;
     }
  }
  .ocdi__gl-item-buttons {
      display: none;
      text-align: center;
  }
  .ocdi__gl-item-button + .ocdi__gl-item-button {
      margin-left: 15px;
  }
  @media (max-width: 782px) {
      .ocdi__gl-item-button {
          width: calc(50% - 10px);
          margin-bottom: 10px;
     }
      .ocdi__gl-item-button + .ocdi__gl-item-button {
          float: left;
     }
  }
  .ocdi__gl-item:hover .ocdi__gl-item-buttons {
      display: block;
  }
  .ocdi__gl-item:hover .ocdi__gl-item-title {
      display: none;
  }
  .ocdi__gl-item:hover .ocdi__gl-item-footer {
      background: #fff;
  }
  .ocdi__gl-header {
      display: inline-block;
      width: calc(100% - 24px);
      background-color: #fff;
      border: 1px solid #ccd0d4;
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.07);
      margin-bottom: 30px;
      padding: 0 11px;
  }
  .ocdi__gl-navigation {
      font-size: 13px;
      line-height: 16px;
      width: 100%;
      float: left;
  }
  .ocdi__gl-navigation ul {
      list-style-type: none;
      margin: 0;
      padding: 0;
      overflow: hidden;
  }
  .ocdi__gl-navigation li {
      margin: 0;
  }
  .ocdi__gl-navigation li.active a span, .ocdi__gl-navigation li.active a:hover span {
      padding-bottom: 14px;
      border-bottom: 4px solid #666;
  }
  .ocdi__gl-navigation li a {
      display: block;
      text-align: center;
      text-decoration: none;
      color: #23282d;
      padding: 18px 10px;
  }
  .ocdi__gl-navigation li a span {
      padding-bottom: 14px;
      border-bottom: 4px solid #fff;
  }
  .ocdi__gl-navigation li a:hover {
      color: #00a0d2;
      cursor: pointer;
  }
  .ocdi__gl-navigation li a:hover span {
      border-bottom: 4px solid #fff;
  }
  .ocdi__gl-search-input {
      width: 100%;
      margin: 10px 0;
      font-size: 13px;
      line-height: 16px;
      color: #72777c !important;
  }
  @media (min-width: 640px) {
      .ocdi__gl-navigation {
          width: calc(100% - 280px);
     }
      .ocdi__gl-navigation li {
          margin: 0 15px;
          float: left;
     }
      .ocdi__gl-navigation li a {
          padding: 18px 10px;
     }
      .ocdi__gl-search-input {
          display: inline-block;
          width: 280px;
          height: 30px;
          margin: 0;
          margin-top: 11px;
     }
      .ocdi__gl-item-container {
          margin-right: -20px;
     }
      .ocdi__gl-item {
          flex: 0 0 calc(50% - 20px);
          margin-right: 20px;
     }
  }
  @media (min-width: 1120px) {
      .ocdi__gl-item-container {
          margin-right: -30px;
     }
      .ocdi__gl-item {
          flex: 0 0 calc(33.33% - 30px);
          margin-bottom: 30px;
          margin-right: 30px;
     }
  }
  .ocdi__card {
      background: #fff;
      text-align: center;
      box-sizing: border-box;
      border: 1px solid #ddd;
      box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.07);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
  }
  .ocdi__card-content {
      padding: 30px;
  }
  .ocdi__card-content .ocdi-icon--content {
      width: 53px;
      height: 53px;
  }
  .ocdi__card-content .ocdi-icon--widgets {
      width: 56px;
      height: 49px;
  }
  .ocdi__card-content .ocdi-icon--brush {
      width: 55px;
      height: 52px;
  }
  .ocdi__card-content .ocdi-icon--redux {
      width: 82px;
      height: 70px;
  }
  .ocdi__card-content .ocdi-icon--plugins {
      width: 65px;
      height: 64px;
  }
  .ocdi__card-content .ocdi-icon--copy {
      width: 42px;
      height: 52px;
  }
  .ocdi__card-content .ocdi-icon--layout {
      width: 53px;
      height: 52px;
  }
  .ocdi__card-content .ocdi-icon-container {
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
  }
  .ocdi__card-content h3 {
      margin: 5px 0;
      font-size: 16px;
      line-height: 20px;
      font-weight: 500;
  }
  .ocdi__card-content p {
      margin: 0;
      font-size: 13px;
      line-height: 16px;
      color: #666;
  }
  .ocdi__card-footer {
      background: #fafafa;
      box-shadow: 0px -1px 0px #ddd;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
  }
  .ocdi__card-footer .button-secondary {
      background: #fafafa;
  }
  .ocdi__card-footer input[type="file"] {
      width: 200px;
      padding: 4px 0;
  }
  .ocdi__card-footer .button-disabled {
      cursor: not-allowed;
  }
  .ocdi__card--three {
      flex: 0 0 100%;
      margin-bottom: 20px;
  }
  @media (min-width: 768px) {
      .ocdi__card--three {
          flex: 0 0 calc(50% - 10px);
          margin-right: 20px;
     }
      .ocdi__card--three:nth-child(2n) {
          margin-right: 0;
     }
  }
  @media (min-width: 1120px) {
      .ocdi__card--three {
          flex: 0 0 calc(33.33% - 20px);
          margin-bottom: 30px;
          margin-right: 30px;
     }
      .ocdi__card--three:nth-child(2n) {
          margin-right: 30px;
     }
      .ocdi__card--three:nth-child(3n) {
          margin-right: 0;
     }
  }
  .ocdi__card--four {
      flex: 0 0 100%;
      margin-bottom: 20px;
  }
  @media (min-width: 768px) {
      .ocdi__card--four {
          flex: 0 0 calc(50% - 10px);
          margin-right: 20px;
     }
      .ocdi__card--four:nth-child(2n) {
          margin-right: 0;
     }
  }
  @media (min-width: 1120px) {
      .ocdi__card--four {
          flex: 0 0 calc(50% - 15px);
          margin-bottom: 30px;
          margin-right: 30px;
     }
  }
  @keyframes ocdi-fade {
      from {
          opacity: 1;
     }
      to {
          opacity: 0;
     }
  }
  .ocdi-is-fadeout {
      animation: ocdi-fade linear 200ms 1 forwards;
  }
  .ocdi-is-fadein {
      animation: ocdi-fade linear 200ms 1 reverse forwards;
  }
  .ocdi__modal-image-container {
      width: 100%;
      height: 180px;
      margin: 0;
      overflow: hidden;
  }
  .ocdi__modal-image-container img {
      width: 100%;
  }
  .ocdi__modal-item-title {
      margin-top: 0.5em;
      font-weight: bold;
  }
  .ocdi__modal-notice.ocdi__demo-import-notice:not(:empty) {
      border: 1px solid #e5e5e5;
      border-left: 4px solid #00a0d2;
      margin: 20px 0 0;
  }
  .ocdi-loading {
      animation: 0.65s linear infinite ocdi-loading-spin;
  }
  .ocdi-loading-md {
      width: 32px;
      height: 32px;
  }
  .ocdi-loading-sm {
      width: 16px;
      height: 16px;
  }
  @keyframes ocdi-loading-spin {
      0% {
          transform: rotateZ(270deg);
     }
      100% {
          transform: rotateZ(630deg);
     }
  }
  /**
  * Customize scrollbar
  */
  .rbt-changelog-body::-webkit-scrollbar {
    width: 12px; /* Adjust the width of the scrollbar */
    height: 12px; /* Adjust the height of the scrollbar (for horizontal scroll) */
  }
  
  /* Customize the scrollbar thumb */
  .rbt-changelog-body::-webkit-scrollbar-thumb {
    background: #e3e0f5; /* Change the color of the scrollbar thumb */
    border-radius: 6px; /* Make the scrollbar thumb rounded */
  }
  
  /* Customize the scrollbar track */
  .rbt-changelog-body::-webkit-scrollbar-track {
    background: #f1f1f1; /* Change the background color of the scrollbar track */
    border-radius: 6px; /* Make the scrollbar track rounded */
  }
  
  /* Customize the scrollbar thumb when hovered */
  .rbt-changelog-body::-webkit-scrollbar-thumb:hover {
    background: var(--color-secondary); /* Change the color of the scrollbar thumb on hover */
  }
  
  .rbt-inactive--page {
    margin-right: 20px;
  }
  .el-license-container {
    margin-right: 0;
  }
  
  /**
    * Generic style
  */
  
  .rbt-inactive--page ul.el-license-info > li:nth-child(odd) {
    background: #f8f8f8;
  }
  .rbt-inactive--page .el-license-container .el-license-info li {
    padding: 6px 13px;
  }
  .rbt-inactive--page h3.el-license-title img {
    max-width: 50px;
  }
  .el-license-title.text-heading .rbt-badge.badge-bg-5 {
    transform: translateY(-3px);
    display: inline-block;
  }
  .rbt-el-license-info ul.el-license-info {
    margin-top: 30px;
  }
  .rbt--video-container {
    position: relative;
  }
  
  .rbt--video-container a.rbt-video-play-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 77.73px;
    height: 77.73px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: 115% 220%;
    text-align: center;
    border-radius: 50%;
    background-color: #2f57ef;
    color: #fff;
    font-size: 13.818px;
    font-style: normal;
    font-weight: 600;
    text-decoration: none;
  }
  a.rbt-help-center-btn:focus, .rbt-video-splash-play-btn-box a {
    box-shadow: none;
    border: 0;
    outline: none;
  }
  .rbt--video-container.text-xl-end.rbt-has-license-activated-play-btn a.rbt-video-play-btn:hover {
    width: 90px;
    height: 90px;
  }
  .text-end {
    text-align: right;
  }
  @keyframes scaleUP {
    0% {
        opacity: 1;
    }
    100% {
        width: 130%;
        height: 130%;
        opacity: 0;
    }
  }
  
  .rbt--video-container img {
    border-radius: 10px;
  }
  .rbt--video-container a.rbt-video-play-btn:hover {
    background-position: 102% 0;
  }
  .rbt--video-container img {
    padding: 10px;
    border: 1px dashed #b6c1e929;
}
  .el-license-container .el-license-field input, .el-license-container .el-license-field input::placeholder{
    font-size: 18px !important;
  }
  .el-license-container .el-license-field input::placeholder {
    color: #717189 !important;
  }
  
  .el-license-field label {
    font-weight: 500;
    color: #1d2327;
  }
  .el-license-container .el-license-title {
    color: #8fcc77;
  }
  @media only screen and (max-width: 767px) {
   .rbt-inactive--page.rbt-license-wrapper {
        padding: 20px;
    }
    .el-license-container .el-license-title {
        font-size: 23px;
        line-height: 1.4;
    }
    .el-license-container .el-license-field input {
        font-size: 16px;
    }
    .rbt--video-container a.rbt-video-play-btn {
        width: 57.73px;
        height: 57.73px;
        line-height: 57.73px;
    }
    .rbt-inactive--page.rbt-license-wrapper .el-license-container .el-license-title {
        font-size: 20px;
    }
    .el-license-active-btn input#submit {
        height: 20px;
        line-height: 20px;
        font-size: 15px;
    }
  }
  .rbt-dashboard-tab-area .ocdi__content-container p.about-description {
    margin-top: 14px;
  }
  .ranbow-dashboard-box-wrapper .ocdi {
    background: #eaedff;
    padding: 30px;
  }
  .rbt-success-alert {
    margin-bottom: 20px;
    background: #5CC659;
    color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 30px;
    border-radius: 6px;
    margin-top: 20px;
  }
  
  .rbt-success-alert p {
    margin: 0;
    font-size: 16px;
  }
  .rbt-success-alert img {
    cursor: pointer;
  }
  .rbt-success-alert > * {
    text-wrap: wrap;
    word-wrap: break-word;
  }
  /**
  * dashboard play button style
  */
  .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
    font-size: 80px;
    font-weight: 600;
    color: #fff;
    line-height: 1;
    margin: 0;
  }
  .rbt-video-splash-play-btn-box a {
    width: 266.727px;
    height: 266.727px;
    border-radius: 50%;
    background: linear-gradient(90deg, #FE61CA 0.47%, #81F 105.96%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .rbt-video-splash-play-btn-box img {
    filter: brightness(1);
  }
  
  .rbt-video-splash-play-btn-box a img {
    border: 0;
    border-radius: 0;
  }
  
  .rbt-video-splash-play-btn-box {
    position: absolute;
    right: -103px;
    top: 50%;
    transform: translateY(-50%);
  }
  
  .rbt-video-splash-play-box {
    position: relative;
    height: 358.25px;
    background-size: cover;
    background-position: center;
    overflow: hidden;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 0 40px;
  }
  .rbt-video-splash-area .rbt-video-splash-title {
      font-size: 24px;
      font-weight: 600;
      line-height: 1.3;
      color: #03031F;
      margin: 0;
      margin-bottom: 14px;
  }
  .rbt-video-splash-area p {
      font-size: 16px;
      color: rgba(3, 3, 31, 0.74);
      margin: 0;
  }
  
  .rbt-video-splash-play-box {
      margin-top: 29px;
  }
  .rbt-video-splash-play-btn-box a img {
    transform: translateX(-34px);
  }
  .rbt-splash-text {
    display: inline-block;
    position: absolute;
    left: 0;
    font-size: 80px;
    top: 50%;
    font-weight: 500;
    transform: translateY(-50%) rotate(-90deg);
    color: rgb(255 255 255 / 19%);
  }
  .rbt-video-splash-area .rbt-video-splash-title {
      font-size: 24px;
      font-weight: 600;
      color: #03031F;
      margin: 0;
      margin-bottom: 14px;
  }
  .rbt-video-splash-area p {
      font-size: 16px;
      color: rgba(3, 3, 31, 0.74);
      margin: 0;
  }
  .rbt-video-splash-play-box {
      margin-top: 29px;
  }
  .rbt-video-splash-area {
    background: #F5F7FF;
    padding: 50px;
    border-radius: 10px;
    max-width: 720px;
    margin-left: auto;
  }
  .el-license-container .el-license-title.rbt-has-license-activated-title img {
    max-width: 30px;
  }
  .rbt-has-license-activated-play-btn img {
    width: 100%;
  }
  a.rbt-help-center-btn {
    display: block;
    text-align: center;
    color: #03031fc7;
    font-weight: 500;
    margin-top: 25px;
  }
  .rbt-video-splash-play-btn-box a::after {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border: 1.5px solid #FFFFFF;
    opacity: .3;
    content: "";
    border-radius: 50%;
    animation-name: scaleUP;
    animation-duration: 2s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  @media (min-width: 1400px) and (max-width: 1699px) {
    .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
        font-size: 60px;
        font-weight: 600;
        color: #fff;
        line-height: 1;
        margin: 0;
    }
    .rbt-video-splash-play-btn-box a {
        width: 226.727px;
        height: 226.727px;
    }
  }
  @media (max-width: 1399px){
    .rbt-video-splash-area {
      margin-left: 0;
    }
  }
  @media (min-width: 768px) and (max-width: 991px) {
    .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
        font-size: 68px;
    }
  }
  
  @media (max-width: 767px) {
    .rbt-video-splash-area {
        padding: 20px;
    }
    .rbt-video-splash-play-btn-box {
        bottom: -130px;
        right: 50%;
        transform: translateX(50%);
    }
    .rbt-video-splash-play-btn-box a img {
        transform: translateY(-40px);
    }
    .rbt-splash-text {
        font-size: 50px;
        left: 50%;
        transform: translateX(-50%) rotate(-90deg) translateY(-49%);
        top: 28%;
    }
    .rbt-video-splash-play-box {
        height: 330px;
        padding: 30px 12px;
        display: block;
    }
    .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
        font-size: 35px;
    }
  }
  .rainbow-dashboard-box-single .rbt-content-left {
    position: relative;
    z-index: 1;
  }
  .rainbow-dashboard-box-single .wp-core-ui .notice.is-dismissible {
    margin-left: 0;
    margin-top: 10px;
    max-width: 680px;
  }
  
  .rainbow-dashboard-box-single .notice.notice-warning {
    background-image: linear-gradient(to right, #2f57ef, #b966e7, #b966e7, #2f57ef) !important;
    color: #fff;
    border: 0;
    border-left: 3px solid #ffffff9c;
    margin-left: 0;
    max-width: 650px;
    background-size: 300% 100%;
  }
  .rainbow-dashboard-box-single .notice.notice-warning a {
    color: #fff;
  }
  
  .rainbow-dashboard-box-single .notice.notice-warning button::before {
    color: #fff;
  }
  .rainbow-dashboard-box-single #setting-error-tgmpa strong > span > a {
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 0.33);
    box-shadow: 0px 23px 20.9px -21px #5B048B;
    border-radius: 4px;
    display: inline-block;
    text-decoration: none;
    padding: 10px 26px;
    color: #fff;
    color: #000;
    margin-top: 10px;
  }
  
  .rainbow-dashboard-box-single #setting-error-tgmpa strong > span > a:hover {background: #007aff;color: #fff;border-color: transparent;}
  
  .rainbow-dashboard-box-single #setting-error-tgmpa strong > span > a.dismiss-notice {
    background-color: var(--color-primary);
    color: #fff;
    border-color: transparent;
  }
  @media (min-width: 1400px) and ( max-width: 1599px) {
    .rbt-content-inner .rbt-content-right img {
        height: 100%;
        transform: translateX(270px);
    }
    .rainbow-dashboard-box-single.bg-default {
        overflow: hidden;
    }
  }
  @media (max-width: 767px) {
      .rainbow-dashboard-box-single .notice.notice-warning {
          padding-right: 15px;
      }
      .rainbow-dashboard-box-single #setting-error-tgmpa strong > span > a {
          padding: 9px 9px;
      }
    }
    .notice.notice-warning.mc4wp-is-dismissible {
      position: relative;
    }
  } 
.el-license-active-btn.rbt-license-deactivate input[type="submit"] {
  background: red !important;
  border: 0;
}
/*----------------------
    RBT Video Styles  
-----------------------*/
.mfp-bg {
  z-index: 9999;
}

.mfp-wrap {
  z-index: 9999;
}
.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
  opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}

html.picture {
  margin: 0 !important;
}

img.mfp-img {
  padding-bottom: 0;
}
.mfp-bg {
  background: rgba(0,0,0,0.8);
  opacity: 1;
  backdrop-filter: blur(15px);
}

.mfp-iframe-scaler iframe {
  border-radius: 10px;
}
.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 1;
}


.mfp-iframe-holder .mfp-close,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  top: -60px;
  right: -50px;
  font-size: 30px;
  font-weight: 400;
  transition: 0.4s;
  opacity: 1;
  width: 50px;
  height: 50px;
  background: #19233550;
  text-align: center;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  color: #fff;
}

.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  top: -10px;
  right: -50px;
}
.side-nav-opened body::before,
.close_side_menu,
.popup-mobile-menu,
.rbt-offcanvas-side-menu {
  background: rgba(0,0,0,0.8);
  backdrop-filter: blur(15px);
}



.notice.notice-warning.mc4wp-is-dismissible {
  position: relative;
}
html[dir="rtl"] .redux-sidebar .redux-group-tab-link-li.hasSubSections.redux-section-hover .subsection {
  left: auto;
  right: 200px;
}
.redux-container .ui-buttonset .ui-button {
  height: auto;
  border-radius: 0;
  margin: 0 !important;
}

.redux-container .ui-buttonset {
  display: inline-block;
}