/*-----------------------------
    <PERSON><PERSON>vas Menu  
--------------------------------*/

.module-nav-trigger .module-trigger {
    display: inline-block;
    width: 80px;
    height: 70px;
    padding: 17px 20px;
    color: #000;
    position: relative;
    cursor: pointer;
    font-size: 0.25em;
}



.module-nav-trigger .bars {
    display: inline-block;
    width: 40px;
    margin: 0;
    vertical-align: middle;
}

.module-nav-trigger.style3 .bars {
    height: 24px;
}

.module-nav-trigger.style3 .bars span {
    width: 100%;
    position: relative;
    overflow: hidden;
    margin-bottom: 9px;
    background-color: transparent;
    transition: transform 0.3s, opacity 0.3s, width 0.3s, background-color 0.3s 0.3s, -webkit-transform 0.3s;
}



.module-nav-trigger .bars span {
    display: inline-block;
    height: 2px;
    margin-left: 0;
    -webkit-transform-origin: left center;
    transform-origin: left center;
    background-color: #000;
}


.module-nav-trigger.style3 .bars span:before, 
.module-nav-trigger.style3 .bars span:after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #090909;
}

.module-nav-trigger.style3 .bars span:before {
    transform: translate(-100%, 0);
    transition: all 0.325s cubic-bezier(0.38, 0.98, 0.4, 1);
}

.module-nav-trigger.style3 .bars span:after {
    transition: all 0.325s cubic-bezier(0.38, 0.98, 0.4, 1);
}


.module-nav-trigger.style3 .module-trigger:hover .bars span:before {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:after {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:first-child:before {
    -webkit-transition-delay: 0.18s;
    transition-delay: 0.18s;
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:after {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:nth-child(2):before {
    -webkit-transition-delay: 0.24s;
    transition-delay: 0.24s;
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:nth-child(2):after {
    -webkit-transition-delay: 0.06s;
    transition-delay: 0.06s;
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:last-child:before {
    -webkit-transition-delay: 0.30s;
    transition-delay: 0.30s;
}

.module-nav-trigger.style3 .module-trigger:hover .bars span:last-child:after {
    -webkit-transition-delay: 0.12s;
    transition-delay: 0.12s;
}
.module-nav-trigger.style3 .module-trigger:hover .bars span:last-child:after {
    -webkit-transition-delay: 0.12s;
    transition-delay: 0.12s;
}





/*-------------------------------
    Offcanvas Menu Sidebar  
--------------------------------*/

.side-menu {
    width: 30%;
    position: fixed;
    right: 0;
    top: 0;
    background: var(--color-white);
    z-index: 9999;
    height: 100%;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    -webkit-transition: -webkit-transform .5s ease;
    transition: -webkit-transform .5s ease;
    -o-transition: -o-transform .5s ease;
    -o-transition: transform .5s ease;
    overflow: hidden;
    .inner-wrapper {
        padding: 60px 50px;
        height: 100%;
        position: relative;
        overflow-y: auto;

        .inner-top {
            border-bottom: 1px solid var(--primary-opacity);
            padding-bottom: 25px;
            margin-bottom: 25px;
        }
        .content {
            display: flex;
            justify-content: space-between;
        }
        
        .description {
            padding-right: 18%;
            margin-top: 20px;
        }
    }


    .side-nav {
        margin-bottom: 30px;
        display: block;
        .navbar-nav {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
            .nav-item {
                display: block;
                margin: 10px 0;
                padding: 0 !important;
                opacity: 0;
                transition: all 0.8s ease 500ms;
                transform: translateY(30px);

                .nav-link {
                    color: var(--color-heading);
                    font-size: var(--font-size-b1);
                    font-weight: 500;
                }

                &:first-child {
                    -webkit-transition-delay: .1s;
                    -o-transition-delay: .1s;
                    transition-delay: .1s;
                }

                &:nth-child(2) {
                    -webkit-transition-delay: .2s;
                    -o-transition-delay: .2s;
                    transition-delay: .2s;
                }

                &:nth-child(3) {
                    transition-delay: .3s;
                }

                &:nth-child(4) {
                    transition-delay: .4s;
                }

                &:nth-child(5) {
                    transition-delay: .5s;
                }


            }
        }
    }


    &.side-menu-active {
        transform: translate3d(0, 0, 0);
        .side-nav {
            .navbar-nav {
                .nav-item {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
        }
    }
}

#close_side_menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    -webkit-transition: opacity 300ms cubic-bezier(0.895, 0.03, 0.685, 0.22);
    -o-transition: opacity 300ms cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: opacity 300ms cubic-bezier(0.895, 0.03, 0.685, 0.22);
    display: none;
    z-index: 1031;
    opacity: 0.4;
}


.page-home-marketplace .rbt-cart-sidenav-activation .feather-shopping-cart {
	margin-right: 10px;
}