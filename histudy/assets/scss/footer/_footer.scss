/*===========================
    Start Footer Area 
=============================*/
/*-----------------------
    Footer Style One  
-------------------------*/
.footer-style-1 {
    .footer-top {
        padding-top: 80px;
        padding-bottom: 90px;
        @media #{$sm-layout} {
            padding-top: 60px;
            padding-bottom: 60px;
        }
    }
    .ft-title {
        color: var(--color-heading);
        margin-bottom: 20px;
        @media #{$lg-layout} {
            font-size: 18px;
        }
    }
    .description {
        font-size: 16px;
        line-height: 25px;
    }
    .newsletter-form,.mc4wp-form  {
        input {
            min-width: 100%;
        }
        .right-icon {
            display: block;
            input {
                padding-right: 30px;
            }
            &::after {
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                font-family: 'feather' !important;
            }
            &.icon-email {
                &::after {
                    content: "\e98a";
                }
            }
        }


    }


    
}

.footer-widget {
    .ft-link {
        @extend %liststyle;
        li {
            margin: 0;
            color: var(--color-body);
            font-size: 16px;
            line-height: 25px;
            span {
                font-weight: 500;
            }
            a {
                color: var(--color-body);
                display: inline-block;
                position: relative;
                &:after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 2px;
                    bottom: 0;
                    left: 0;
                    background-color: currentColor;
                    transform: scaleX(0);
                    transform-origin: bottom right;
                    transition: transform 0.3s;
                }
                &:hover {
                    color: var(--color-primary);
                    &:after {
                        transform-origin: bottom left;
                        transform: scaleX(1);
                    }
                }
            }
            & + li {
                margin-top: 10px;
            }
        }
    }
    .form-group {
        label {
            font-size: 16px;
            line-height: 31px;
        }
    }
}


/* Footer Style two  */
.footer-style-2 {
    .logo {
        margin-bottom: 30px;
        position: relative;
    }
    .text {
        p {
            margin-top: 30px;
            a {
                transition: 0.3s;
                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }
}


/* Footer Style Three  */
.footer-style-3 {
    .shape-gd-1 {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    // .shape-1 {
    //     margin-bottom: -25px;
    // }

    .big-title {
        text-align: center;
        font-size: 200px;
        font-weight: 600;
        line-height: 100%;
        text-transform: uppercase;
        background: linear-gradient(180deg, rgba(47, 87, 239, 0.12) 0%, rgba(197, 134, 238, 0.00) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;

        @media #{$laptop-device} {
            font-size: 150px;
        }

        @media #{$lg-layout} {
            font-size: 120px;
            margin-top: 15px;
        }

        @media #{$md-layout} {
            font-size: 100px;
            margin-top: 20px;
        }

        @media #{$sm-layout} {
            font-size: 70px;
            margin-top: 20px;
        }

        @media #{$large-mobile} {
            font-size: 60px;
            margin-top: 20px;
            margin-bottom: 30px;
        }
    }
}


.footer-widget.widget.widget_mc4wp_form_widget .ft-title {
    font-size: var(--h6);
    line-height: 1.25;
}
.footer-widget.widget.widget_mc4wp_form_widget {
    display: block;
    margin-top: 20px;
}

@media (max-width: 420px) {
    .rbt-category-update .update-category-dropdown {
        display: none;
    }
}
.tutor-modal-content .tutor-form-check input.tutor-form-check-input {
	display: none;
}

.footer-style-2 .logo {
    display: inline-block;
}

footer.rbt-footer.footer-style-1.bg-color-white.overflow-hidden:not(.has-rainbow-footer-style-1) .social-default.icon-naked {
	display:flex;
}
.rbt-cta-5 .title {
    padding-right: 26%!important;
}

.page-home-university-about .main-page-wrapper + 
.rbt-separator-mid,.page-home-online-courses .main-page-wrapper + 
.rbt-separator-mid,.page-home-online-course-education .main-page-wrapper + 
.rbt-separator-mid,.page-home-gym-coachings .custom-footer-two > .rbt-separator-mid,
.page-home-online-school .custom-footer-two > .rbt-separator-mid,.page-home-language-academy  .custom-footer-two > .rbt-separator-mid {
		display:none;
}
.custom-footer-two .social-icon {
    display:none;
}

.footer-style-1 .mc4wp-form .right-icon::after {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-family: "feather" !important;
}
.footer-style-1 .mc4wp-form .right-icon.icon-email::after {
    content: "\e98a";
}
/**
 *Extra css
*/
footer.rbt-footer.footer-style-1:not(.has-elementor-full-width) > .footer-top {
    padding-top: 0;
}
footer.rbt-footer.footer-style-1:not(.has-elementor-full-width) > .footer-top > .container {
    padding-top: 80px;
    border-top: 1px solid var(--color-border);
}
.single-course_event footer.rbt-footer.footer-style-1:not(.has-elementor-full-width) > .footer-top > .container {
    border: 0;
}
body.blog footer.rbt-footer.footer-style-1 > .footer-top {
    padding-top: 0;
}
body.blog footer.rbt-footer.footer-style-1 > .footer-top > .container {
    padding-top: 0;
}
body.blog footer.rbt-footer.footer-style-1 > .footer-top > .container {
    padding-top: 80px;
    border-top: 1px solid var(--color-border);
}

.rbt-mega-menu-list.hover-border-enable ul > li > a {
    position: relative;
    display: inline-block;
} 

.rbt-mega-menu-list.hover-border-enable ul > li > a:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s;
}

.rbt-mega-menu-list.hover-border-enable ul > li > a:hover:after { 
    transform-origin: bottom left;
    transform: scaleX(1);
}

.hover-animation-off.rbt-newsletter-area .btn-icon,
.hover-animation-off.rbt-newsletter-area .rbt-btn:hover.hover-icon-reverse .btn-icon + .btn-icon,
.hover-animation-off.rbt-newsletter-area .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon  {
    display: none;
}

.hover-animation-off.rbt-newsletter-area .btn-icon .rbt-btn:hover.hover-icon-reverse .btn-text,
.hover-animation-off.rbt-newsletter-area .rbt-btn:hover.hover-icon-reverse .btn-text {
    transform: translateX(0);
}

.hover-animation-off.rbt-newsletter-area .rbt-btn.hover-icon-reverse .btn-text {
    margin-inline-start: 0;
}

.rbt-newsletter-area.style-five .newsletter-form-1 .rbt-btn {
    position: static;
    right: 0;
    top: unset; 
    transform: unset;
    border-radius: 500px !important;
    padding: 0 22px;
    height: 45px;
    line-height: 43px;
}

.rbt-newsletter-area.style-five .newsletter-form-1 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.rbt-newsletter-area.style-five .newsletter-form-1 input {
    width: auto;
    margin-right: 7px;
    height: 45px;
    font-size: 16px;
    font-weight: 400;
    border: 0;
    border-bottom: 2px solid var(--color-border);
    background-color: transparent;
    padding-bottom: 8px;
    padding-top: 20px;
    border-radius: 0;
    padding-left: 0;
    box-shadow: none;
    padding-bottom: 8px;
    margin-bottom: 20px;
    width: 100%;
}

.rbt-newsletter-area.style-five .newsletter-form-1::after {
    position: absolute;
    content: "\e98a";
    right: 0;
    top: 25%;
    transform: translateY(-50%);
    font-family: "feather" !important;
}

.rbt-newsletter-area.style-five .newsletter-form-1 input:focus {
    border-color: var(--color-primary);
}

.rbt-header.menu-bottom-underline-on .mainmenu-nav .mainmenu > li > a {
    position: relative;
    padding: 0;
}

.rbt-header.menu-bottom-underline-on .mainmenu-nav .mainmenu > li > a::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s;
}

.rbt-header.menu-bottom-underline-on .mainmenu-nav .mainmenu > li > a:hover::after {
    transform-origin: bottom left;
    transform: scaleX(1);
}

.rbt-header.menu-underline-on .mainmenu-nav .mainmenu > li {
    position: relative;
}

.rbt-header.menu-underline-on .mainmenu-nav .mainmenu > li::after {
    position: absolute;
    content: "";
    height: 14px;
    width: 1px;
    background: var(--color-border);
    left: -1px;
    top: 50%;
    transform: translateY(-50%);
}

.rbt-header.menu-underline-on .mainmenu-nav .mainmenu > li:first-child:after {
    display: none;
}

.menu-bottom-border.single-mega-item .mega-menu-item li {
    border-top: 1px solid var(--color-border);
}

.menu-bottom-border.single-mega-item .mega-menu-item li:first-child {
    border: none;
}

.menu-left-arrow.single-mega-item  .mega-menu-item li {
    position: relative;
}
.menu-left-arrow.single-mega-item .mega-menu-item li::before {
    position: absolute;
    content: "\e930";
    font-family: "feather";
    left: 0;
    top: 50%;
    font-size: 18px;
    opacity: 0.7;
    color: var(--color-primary);
    transform: translateY(-50%);
}

.menu-left-arrow.mega-menu-item li.menu-item a {
    margin: 0;
}

.rbt-mega-menu-list.menu-left-arrow .mega-menu-item li ,
.rbt-mega-menu-list.menu-left-arrow .mega-menu-item {
    margin: 0 0;
}
    