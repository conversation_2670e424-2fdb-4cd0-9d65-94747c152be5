.active-dark-mode {
    background: var(--color-darker);
    color: var(--color-white);

    .my_switcher ul {
        background: var(--color-darker);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        border: 2px solid var(--dark-color-border);
    }

    //================> Global Class START
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    .h1,
    .h2,
    .h3,
    .h4,
    .h5,
    .h6 {
        color: var(--color-white)!important;
    }

    .bg-color-white {
        background: var(--color-darker) !important;
        // background: var(--color-bodyest) !important;
    }

    .color-white-dark {
        background: var(--color-white-dark);
    }

    .gradient-dark {
        background: var(--gradient-dark);
    }

    .gradient-dark-2 {
        background: var(--gradient-dark-2);
    }

    .color-bodyest {
        background: var(--color-bodyest);
    }

    .color-bodyest-2 {
        background: var(--color-bodyest-2);
    }

    .color-bodyest-opacity {
        background: var(--color-bodyest-opacity);
    }

    .heading-opacity {
        color: var(--color-white-off) !important;
    }

    .dark-bg-color-white {
        background: var(--color-bodyest) !important;
    }

    .bg-primary-opacity {
        background: var(--color-primary) !important;
        color: var(--color-white) !important;
    }

    .rbt-team-area {
        &.bg-color-extra2 {
            background: var(--color-grey) !important;
        }
    }

    .rbt-blockquote {
        &.bg-color-gray-light {
            background-color: var(--color-darker) !important;
        }
    }

    .bg-color-extra2 {
        background: #333d51!important;
    }


    .rbt-counterup-area {
        &.bg-gradient-1 {
            background: var(--color-darker) !important;
        }
    }

    .rbt-course-block {
        &.bg-primary-opacity {
            background: var(--primary-opacity) !important;
        }
    }

    .bg-gradient-1 {
        background: var(--color-bodyest) !important;
    }

    .bg-gradient-3 {
        background: var(--gradient-dark) !important;
    }

    .bg-color-light {
        background: var(--color-bodyest) !important;
    }

    .bg-color-secondary-alt {
        background: var(--color-bodyest);
    }

    .bg-gradient-8 {
        background: var(--gradient-dark) !important;

    }

    .bg-gradient-11 {
        background: linear-gradient(180deg, rgb(74 90 110 / 93%), #192335) !important;
    }

    .rbt-team-modal-thumb .rbt-team-thumbnail {
        box-shadow: var(--dark-shadow-2);
        background: var(--color-bodyest-opacity);
    }

    .bg-gradient-2 {
        // background: var(--color-bodyest) !important;
        background: linear-gradient(180deg, rgb(74 90 110 / 23%), #192335) !important;
    }

    .rbt-service-area {
        &.bg-gradient-2 {
            background: var(--color-darker) !important;
        }
    }

    .color-danger {
        color: #ff6347 !important;
    }

    .rbt-banner-area.rbt-banner-8.variation-01.bg_image.bg_image--9 {
        position: relative;

        &::before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: 1;
            background: linear-gradient(180deg, #152131b8, #162130);
        }

        .wrapper {
            position: relative;
            z-index: 9;
        }
    }

    .rbt-page-banner-wrapper {
        .rbt-banner-image {
            background: var(--gradient-dark-2) !important;

            &::after {
                background: transparent;
            }
        }
    }

    //================> Global Class END

    .rbt-splash-slider {
        background-image: url(../images/dark/bg/bg-image-10.jpg);

        &::after {
            background-image: url(../images/dark/bg/banner-bg-shape-1.svg);
        }
    }

    .rbt-header .rbt-header-wrapper.dark-header-transparent {
        background: transparent !important;
        box-shadow: none !important;

        &.rbt-sticky {
            background-color: var(--color-bodyest) !important;
            box-shadow: var(--dark-shadow-3);
        }

        .mainmenu-nav {
            .mainmenu {
                li {
                    a {
                        color: var(--color-white) !important;
                    }
                }
            }
        }
    }

    .top-features-box {
        box-shadow: var(--dark-shadow-3);
    }

    .splash-layout-presentation .wrapper .splash-layout-presentation-box::after {
        background: linear-gradient(0deg, rgb(25 35 53) 0%, rgb(57 68 89) 100%) !important;
    }

    .scroll-animation-all-wrapper::before,
    .scroll-animation-all-wrapper::after {
        background:
            linear-gradient(90deg, #192335 0, hsla(0, 0%, 100%, 0));
    }

    .rbt-categori-list a {
        background: var(--color-bodyest);
        color: var(--color-white-dark);
        box-shadow: var(--dark-shadow-3);

        &:hover {
            background: var(--color-primary);
        }
    }

    .rbt-splash-feature-box {
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-splash-feature-box .inner .content .description strong {
        color: var(--color-white-off) !important;
    }

    .rbt-splash-service {
        border: 2px dashed var(--dark-color-border);
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-splash-service .inner .content .title a {
        color: var(--color-white-off);
    }

    .rbt-splash-service .liststyle li {
        color: var(--color-white-dark);
    }

    .rbt-splash-feature-box {
        background-color: var(--color-bodyest);
    }

    .single-demo .single-demo-link {
        box-shadow: var(--dark-shadow-2);
        background: var(--color-bodyest);
    }

    .single-demo .content .title {
        color: var(--color-white-dark);
    }

    .single-demo .mobile-view {
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-feature-plugin .inner .thumbnail {
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-breadcrumb-default {
        .title {
            color: var(--color-white-dark);
        }

        .page-list {
            li {
                a {
                    color: var(--color-white-off);
                }
            }
        }
    }

    .page-list li {
        a {
            color: var(--color-white-dark);
        }

        &.active {
            color: var(--color-white-dark);
        }
    }

    .rbt-page-banner-wrapper .rbt-banner-content-top .description {
        color: var(--color-white-off);
    }

    .rbt-sorting-list .course-switch-layout li button {
        color: var(--color-white-dark);

        &.active {
            color: var(--color-white-dark);
            background: var(--color-bodyest);

            i {
                color: var(--color-white-dark);
            }
        }
    }

    .rbt-short-item .course-index {
        color: var(--color-white-off);
    }

    .rbt-overlay-page-wrapper .breadcrumb-image-container {
        background: var(--color-bodyest);
    }

    .rbt-overlay-page-wrapper .breadcrumb-image-container .breadcrumb-image-wrapper {
        background: linear-gradient(252deg, rgba(25, 35, 53, 0) 35.97%, rgba(47, 87, 239, 0.3) 100.98%), linear-gradient(110deg, rgba(25, 35, 53, 0) 38.37%, rgba(185, 102, 231, 0.4) 102.05%);
    }

    .rbt-default-form label {
        color: var(--color-white-off);
    }

    .course-field.edu-bg-gray {
        background: var(--color-darker);
    }

    .course-field {
        small {
            a {
                color: var(--color-white-off);
            }
        }
    }

    .pro-quantity .pro-qty {
        border: 1px solid var(--dark-color-border);

        input {
            border: 2px solid transparent;
        }
    }

    .modal-footer {
        border-top: 1px solid var(--dark-color-border-2);
    }

    .rbt-default-tab-button li a {
        &.active {
            &::after {
                background: var(--color-primary);
            }
        }

        &::after {
            background: var(--color-white-off);
        }

        &:hover {
            &::after {
                background: var(--color-primary);
            }
        }
    }

    .rbt-lesson-content-wrapper .rbt-lesson-leftsidebar {
        background-color: var(--color-bodyest);
        border-right: 1px solid var(--dark-color-border-2);
    }

    // .rbt-feature-area.rbt-single-course-features {
    //     background: var(--color-bodyest) !important;
    // }

    .rbt-lesson-rightsidebar .rbt-checkbox-wrapper label,
    .rbt-lesson-rightsidebar .rbt-form-check label {
        border: 2px solid var(--dark-color-border-2);
        color: var(--color-white-off);
    }

    .rbt-article-content-wrapper {
        box-shadow: var(--dark-shadow-3);
        background: var(--color-bodyest);
    }

    .rbt-single-product .product-feature li span {
        color: var(--color-white-off);
    }

    .rbt-accordion-style .card .card-header button.collapsed {
        color: var(--color-white);
    }

    .rbt-cat-box-1.variation-3 {
        .inner {
            border: 1px solid transparent;
            background-color: transparent;
            box-shadow: none;
        }
    }

    .rbt-course-category {
        .rbt-categori-leftbar {
            .rbt-categori-list {
                a {
                    background: var(--color-bodyest);
                    color: var(--color-white-dark);
                    box-shadow: var(--dark-shadow-3);

                    &:hover {
                        background: var(--color-primary);
                    }
                }
            }
        }
    }

    .rbt-inner-onepage-navigation {
        background: var(--dark-color-border-2);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li a::after {
        background: var(--color-bodyest);
    }

    .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li.current a::after,
    .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li:hover a::after {
        background: var(--color-primary);
    }

    .rbt-course-feature-box.overview-wrapper.rbt-shadow-box.has-show-more,
    .about-author-list.rbt-shadow-box.featured-wrapper.has-show-more {
        .rbt-show-more-btn {
            &::before {
                background: linear-gradient(rgba(68, 68, 68, 0) 0%, #273041 100%);
            }
        }
    }

    .rbt-instructor .about-author .media-body .rbt-meta li,
    a {
        color: var(--color-white);
    }

    .nav-category-item .thumbnail a {
        color: var(--color-bodyest);

        &:hover {
            color: var(--color-primary);
        }
    }

    .rbt-categori-list.medium-size {
        a {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-2);

            &:hover {
                background: var(--color-primary);
            }
        }
    }

    .rbt-conatct-area.bg-gradient-11.rbt-section-gap {
        background: linear-gradient(180deg, rgb(74 90 110 / 93%), #192335) !important;
    }

    .course-sidebar .subtitle {
        color: var(--color-white-off);
    }

    .rbt-inner-onepage-navigation .mainmenu-nav .mainmenu li a {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
        color: var(--color-white-off) !important;
    }

    .comment-list .comment .single-comment .comment-img img {
        border: 2px solid var(--dark-color-border);
        background: var(--dark-color-border);
    }

    .rbt-course-top-wrapper .select-label {
        color: var(--color-white-dark);
    }

    .rbt-course-top-wrapper .price__output--wrap .price--output input,
    .rbt-course-top-wrapper .price__output--wrap .price--output span {
        color: var(--color-white-dark);
    }

    input[type=checkbox]~label::before,
    input[type=radio]~label::before {
        background-color: var(--color-body);
        border: 2px solid var(--color-body);
    }

    input[type=checkbox]:checked~label::before,
    input[type=radio]:checked~label::before {
        background-color: var(--color-primary);
        border-color: var(--color-primary);
    }

    .rating-box .rating-number {
        color: var(--color-white-dark);
    }

    .rbt-course-action-bottom {
        box-shadow: var(--dark-shadow-3);
        z-index: 999;
        background: var(--color-bodyest);
    }

    .rbt-course-action-bottom.rbt-course-action-active {
        h5 {
            color: var(--color-white);
        }
    }

    .card-info .inner {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);

        .name {
            color: var(--color-white-off);
        }
    }

    .rbt-course-feature-box {

        p,
        .rbt-list-style-1 li {
            color: var(--color-white-off);
        }
    }

    .product-description-content .comment-list .comment .commenter a {
        color: var(--color-white-dark);
    }

    .comment-list .comment .time-spent {
        color: var(--color-white-dark);
        opacity: 0.8;
    }

    .comment-list .comment .comment-text p {
        color: var(--color-white-off);
    }

    .comment-list .comment:first-child {
        border-top: transparent;
    }

    .comment-list .comment .children .comment {
        border-top: 1px solid var(--dark-color-border);
    }

    .comment-list .comment {
        border-top: 1px solid var(--dark-color-border);
    }

    .cart-table .table tbody tr td {
        border-bottom: 1px solid var(--dark-color-border-2);
    }

    .blog-content-wrapper {
        &.rbt-article-content-wrapper {
            .content {
                p {
                    color: var(--color-white-off);
                }
            }
        }
    }

    .rbt-avatars img {
        border: 2px solid var(--dark-color-border);
        background: var(--dark-color-border);
    }

    .cart-table .table tbody tr:hover {
        box-shadow: var(--dark-shadow-3);
    }

    .cart-table td.pro-title a {
        color: var(--color-white-dark);

        &:hover {
            color: var(--color-primary);
        }
    }

    .cart-table td.pro-quantity .pro-qty {
        border: 1px solid var(--dark-color-border-2);
    }

    .cart-table .pro-remove a::after {
        background: var(--color-bodyest);
    }

    .cart-table .table {
        border-bottom: 8px solid var(--dark-color-border);
    }

    .rbt-accordion-style.rbt-accordion-04 .card {
        border: 2px solid var(--dark-color-border);
    }

    .rbt-default-tab .tab-button .tabs__tab .nav-link.active {
        background: var(--color-bodyest);
        color: var(--color-white);
    }

    .edu-bg-shade {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-cart-area .section-title .title {
        border-bottom: 1px dashed var(--dark-color-border-2);
    }

    .cart-summary .cart-summary-wrap {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
    }

    .cart-summary .cart-summary-wrap h2 {
        border-top: 2px solid var(--dark-color-border);
        color: var(--color-white);
    }

    .cart-summary .cart-summary-wrap p {
        color: var(--color-white-off);
    }

    .cart-summary .cart-summary-wrap p+p {
        border-top: 1px dashed var(--dark-color-border-2);
    }

    .rbt-my-account-inner h3 {
        border-bottom: 1px dashed var(--dark-color-border-2);
    }

    .checkout-content-wrapper {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .checkout-form label {
        color: var(--color-white);
        opacity: 0.9;
    }

    .checkout-cart-total p {
        color: var(--color-white);
        opacity: 0.9;
    }

    .checkout-form input {
        border: 2px solid var(--dark-color-border);
    }


    .checkout-cart-total ul {
        border-bottom: 1px solid var(--dark-color-border-2);
    }

    .checkout-cart-total {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-my-account-inner {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-my-account-tab-button a.active,
    .rbt-my-account-tab-button a:hover {
        background-color: var(--color-primary);
        background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
        color: #fff;
        background-size: 300% 100%;
    }

    .rbt-my-account-tab-button a {
        background: var(--color-bodyest);
        border: 1px solid var(--dark-color-border-2);
    }

    .rbt-my-account-tab-button {
        box-shadow: var(--dark-shadow-3);
    }

    .checkout-cart-total p {
        border-bottom: 1px solid var(--dark-color-border-2);
    }

    .checkout-payment-method {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .checkout-cart-total ul li {
        color: var(--color-white);
        opacity: 0.9;
    }

    .checkout-cart-total ul li span {
        color: var(--color-white);
        opacity: 0.9;
    }

    .calculate-shipping form input,
    .discount-coupon form input {
        border: 1px solid var(--dark-color-border-2);
    }

    .rbt-default-tab .rainbow-tab-content {
        background: var(--color-bodyest);
    }

    .rbt-default-tab .tab-button .tabs__tab .nav-link {
        color: var(--color-white);
    }

    .theme-shape::after {
        background: var(--color-white-dark);
        opacity: 0.5;
        filter: blur(300px);
    }

    .rbt-default-badge {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
        color: var(--color-white);
    }

    .rbt-badge-4 {
        color: var(--color-white-dark);
    }

    .rbt-badge-5 {
        background: var(--color-body);
        color: var(--color-white);
    }

    .section-title {
        p {
            color: var(--color-white-dark);
        }
    }

    .brand-style-1,
    .brand-style-3 {
        li {
            a {
                filter: contrast(0.5) grayscale(0.5) invert(0.5);
            }
        }
    }

    .rbt-search-dropdown {
        background: var(--color-darker);
        border-top: 1px solid var(--dark-color-border-2);

        .wrapper {
            form {
                input {
                    border: 2px solid var(--dark-color-border-2);
                }
            }
        }
    }

    .plan-offer-list.rbt-list-primary-opacity {
        li {
            color: var(--color-white-off);
        }
    }

    .rbt-banner-1 {
        background: linear-gradient(252deg, rgba(25, 35, 53, 0.00) 35.97%, rgba(47, 87, 239, 0.30) 100.98%),
            linear-gradient(110deg, rgba(25, 35, 53, 0.00) 38.37%, rgba(185, 102, 231, 0.40) 102.05%)!important;
           

        &:after {
            background: url(../images/dark/bg/banner-bg-shape-1.svg);
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
        }

        .content {
            .inner {
                .title {
                    color: var(--color-white) !important;
                }

                .description {
                    color: var(--color-white-dark);

                    strong {
                        color: var(--color-white);
                    }
                }
            }
        }
    }

    .rbt-banner-2 {
        background: linear-gradient(90deg, rgb(32 67 201 / 65%) 0%, rgb(152 99 187 / 45%) 100%);
    }

    .rbt-badge-2 {
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-address {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-address .inner p a {
        color: var(--color-white);
    }

    .rbt-gif-banner-area {
        box-shadow: var(--dark-shadow-3);
    }

    .course-sidebar .social-share-wrapper {
        background-color: transparent;
    }

    .rbt-banner-3 .edumarque {
        opacity: 0.4;
    }

    .rbt-banner-8 {
        .content {
            .inner {
                .row {
                    a {
                        color: var(--color-white-off);

                        &:hover {
                            color: var(--color-primary);
                        }
                    }
                }
            }
        }

        &.variation-03 {
            .inner {
                .follow-us-text {
                    color: var(--color-white-dark);
                }
            }
        }
    }

    .rbt-header-top.rbt-header-top-1 {
        background: var(--color-primary) !important;
    }

    .rbt-information-list {
        li {
            a {
                color: var(--color-white-dark);

                &:hover {
                    color: var(--color-white);
                }
            }
        }
    }

    hr {
        background-color: var(--color-white);
        opacity: 0.1;
    }

    .nav-category-item {
        background: var(--color-white);

        &::before {
            position: absolute;
            width: 100%;
            height: 100%;
            background: var(--color-darkest);
            opacity: 0.2;
            content: "";
            top: 0;
            left: 0;
        }
    }

    .rbt-pagination {
        li {
            a {
                background: var(--color-bodyest);
                box-shadow: var(--dark-shadow-3);

                &:hover {
                    background: var(--color-primary);
                    box-shadow: none;
                }
            }

        }

        .active {
            a {
                background: var(--color-primary);
            }
        }
    }

    .cd-headline.clip .cd-words-wrapper::after {
        background-color: var(--dark-color-border);
    }

    .rbt-card.variation-03 .card-information img {
        border: 2px solid var(--dark-color-border);
    }

    .footer-style-1 .description {
        color: var(--color-white-off);
    }

    // .rbt-progress-parent {
    //     &::after {
    //         color: var(--color-white);
    //     }

    //     svg {
    //         path {
    //             fill: none;
    //         }

    //         &.rbt-back-circle {
    //             path {
    //                 stroke: var(--color-white-dark);
    //             }
    //         }
    //     }
    // }

    .rbt-modern-select .dropdown-menu {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
    }

    .rbt-modern-select .bootstrap-select button.actions-btn:hover {
        background: var(--color-primary) !important;
    }

    .form-control {
        background-color: var(--color-darker);
        color: var(--color-white);
    }

    .rbt-modern-select .bs-searchbox .form-control {
        border: 2px solid var(--dark-color-border);
    }

    .rbt-badge.variation-02 {
        background: var(--color-white) !important;
        color: var(--color-primary) !important;
        font-weight: 500;
    }

    .rbt-cta-default.style-2 .content-wrapper {
        background: var(--color-darker);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-btn.btn-border-gradient.radius-round {
        z-index: 0;
    }

    .hanger-line>.col-lg-3::after {
        background: var(--color-darker);
    }

    .rbt-header-top-news .inner .content .news-text,
    ul.rbt-dropdown-menu>li>a,
    .rbt-search-with-category .search-field .serach-btn,
    .rbt-secondary-menu li a {
        color: var(--color-white-dark);
    }

    .rbt-header {
        .rbt-header-wrapper {
            background-color: var(--color-bodyest);
            box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
        }

        &.rbt-transparent-header {
            .rbt-header-wrapper {
                &.rbt-sticky {
                    background-color: var(--color-bodyest) !important;
                    box-shadow: var(--dark-shadow-3);
                }
            }

        }

        &.rbt-header-8 {
            .mainbar-row {
                background-color: var(--color-bodyest);
                box-shadow: var(--dark-shadow-2);

                // .mainmenu-nav {
                //     .mainmenu {
                //         li {
                //             a {
                //                 color: var(--color-darker) !important;
                //             }
                //         }
                //     }
                // }
            }
        }
    }

    .rbt-contact-form {
        &.contact-form-style-1 {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-1);
        }
    }

    .profile-share {
        a {
            img {
                border: 3px solid var(--dark-color-border);
                box-shadow: var(--dark-shadow-2);
            }
        }
    }

    .rbt-category-btn {
        background: var(--color-darker);
        color: var(--color-white-dark);
    }

    .rbt-header .mainmenu-nav .mainmenu>li>a {
        color: var(--color-white-dark);
    }

    .quick-access>li>a {
        color: var(--color-white-dark);

        &:hover {
            color: var(--color-primary);
        }
    }

    .rbt-round-btn::after {
        background: var(--color-darker);
    }

    ul.rbt-dropdown-menu li .sub-menu {
        background: var(--color-darker);
        box-shadow: var(--dark-shadow-2);
        border-top: 1px solid transparent;

        &a :hover {
            background: var(--color-white);
            opacity: 0.1;
        }
    }

    .rbt-header {
        .mainmenu-nav {
            .mainmenu {
                li {


                    // Dropdown Menu 
                    &.has-dropdown {
                        .submenu {
                            background-color: var(--color-darker);
                            box-shadow: var(--dark-shadow-1);

                            li {

                                a {
                                    color: var(--color-white);

                                    &:hover {
                                        background: var(--color-bodyest);
                                        color: var(--color-white) !important;
                                    }

                                    &.active {
                                        background: var(--color-bodyest);
                                        color: var(--color-white) !important;
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
    }

    .rbt-single-widget .rbt-widget-title {
        border-bottom: 2px solid var(--dark-color-border-2);
    }

    .blog-meta li {
        color: var(--color-white-off);
    }

    .rbt-sidebar-list-wrapper {
        li {
            .content {
                .title {
                    a {
                        color: var(--color-white-dark);

                        &:hover {
                            color: var(--color-primary);
                        }
                    }
                }
            }
        }
    }


    .author-info {
        a {
            strong {
                color: var(--color-white);
            }
        }

    }

    .meta-list li span {
        color: var(--color-white-off);
    }

    .wp-block-quote p,
    blockquote p,
    cite a {
        color: var(--color-white-dark);
    }

    .tagcloud a {
        color: var(--color-white-dark);
        border: 1px solid var(--dark-color-border);

        &:hover {
            border: 1px solid var(--color-primary);
        }
    }

    .blog-content-wrapper .social-share-block .post-like a i {
        border: 1px solid var(--dark-color-border);
    }

    .instagram-grid a img {
        object-fit: cover;
        width: 100%;
    }

    .blog-content-wrapper .social-share-block .post-like {
        a {
            span {
                color: var(--color-white-dark);
            }

            &:hover span {
                color: var(--color-primary);
            }
        }
    }

    .wp-block-quote,
    blockquote {
        border: 1px solid var(--dark-color-border);
    }

    .about-author,
    .rbt-total-comment-post {
        border-bottom: 1px solid var(--dark-color-border);
    }

    .comment-list .comment .commenter {
        a {
            color: var(--color-white-dark);
        }
    }

    .comment-list .comment .reply-edit a.comment-reply-link::before {
        background: var(--color-white-off);
    }

    .blog-content-wrapper .social-share-block {
        border-bottom: 1px solid var(--dark-color-border);
    }

    .about-author .media-body .author-info .subtitle {
        color: var(--color-white-off);
    }

    .about-author .media-body .content .description {
        color: var(--color-white-off);
    }

    ul {
        &.rbt-dropdown-menu {

            li {
                .sub-menu {
                    li {
                        a {
                            background: var(--color-darker);
                            color: var(--color-white);

                            &:hover {
                                background: var(--color-bodyest);
                            }
                        }
                    }
                }
            }
        }
    }

    .bg-color-darker .rbt-separator::after {
        background: var(--color-white);
        opacity: 0.1;
    }

    .rbt-btn {
        &.btn-white {
            box-shadow: var(--dark-shadow-3);
            background: var(--color-bodyest);
            color: var(--color-white-dark);

            &:hover {
                background: var(--color-primary);
                color: var(--color-white);
            }
        }
    }

    .rbt-btn.btn-border-gradient {
        color: var(--color-white-dark);
        box-shadow: var(--dark-shadow-3);

        &::before {
            background: var(--color-darker);
        }
    }

    .logo-dark {
        display: none;
    }

    .breadcrumb-dark {
        display: none;
    }

    .logo-light {
        display: block !important;
    }

    .rbt-header .mainmenu-nav .mainmenu li.has-dropdown .submenu {
        border-top: 1px solid transparent;
    }

    .rbt-user-wrapper .rbt-user-menu-list-wrapper {
        background: var(--color-darker);
        box-shadow: var(--dark-shadow-1);
        border: 1px solid transparent;

        .user-list-wrapper {
            li {
                a {
                    color: var(--color-white-dark);
                }

                &:hover a {
                    background: var(--color-bodyest);
                    color: var(--color-white);
                }
            }
        }
    }

    .rbt-new-badge {

        &.rbt-new-badge-one {
            color: var(--color-white);
            box-shadow: var(--dark-shadow-3);
            background: #4D5572;


            &:before {
                background: #4D5572;
                opacity: 0.4;
                -webkit-box-shadow: var(--dark-shadow-3);
                box-shadow: var(--dark-shadow-3);
            }
        }
    }

    .rbt-category-menu-wrapper .category-dropdown-menu .category-menu-item .rbt-vertical-nav {
        border-right: 1px solid var(--dark-color-border);
    }

    .rbt-category-menu-wrapper .category-dropdown-menu .category-menu-item .rbt-vertical-nav-list-wrapper li:hover a {
        color: var(--color-primary) !important;
    }

    .rbt-admin-profile {
        .admin-thumbnail {
            img {
                box-shadow: var(--dark-shadow-1);
            }
        }
    }

    .rbt-admin-profile .admin-info {
        text-align: left;

        .name {
            color: var(--color-white);
        }
    }

    .quick-access>li.account-access::after {
        background-color: var(--color-white);
        opacity: 0.1;
    }

    .rbt-header .mainmenu-nav .mainmenu li.with-megamenu .rbt-megamenu .wrapper {
        background-color: var(--color-darker);
        box-shadow: var(--dark-shadow-1);
        border-top: 1px solid transparent;
    }

    .rbt-short-title {
        color: var(--color-white-dark)!important;
    }

    .rbt-list-style-1.rbt-course-list li i {
        color: #b3b7c1 !important;
    }

    .rbt-modern-select select,
    .rbt-select-dark {
        color: var(--color-white-off);
        border: 2px solid var(--dark-color-border-2);
        background: transparent;

        option {
            background: var(--color-darker);
        }
    }

    .rbt-border-2 {
        border: 2px solid var(--dark-color-border-2) !important;
    }

    .rbt-course-wrape {
        border: 1px solid var(--dark-color-border-2) !important;

        .rbt-list-style-1.rbt-course-list {
            span {
                color: var(--color-white-dark);
            }

            .dropdown-menu {
                background: var(--color-darker);

                .dropdown-item {

                    &:hover,
                    &:focus {
                        color: var(--color-white-off);
                        background: var(--color-bodyest);
                    }
                }
            }
        }
    }

    .accordion-button:not(.collapsed) {
        color: var(--color-white);
    }

    .rbt-course .rbt-course-icon::before,
    .rbt-course .rbt-course-icon::after {
        color: var(--color-white);
    }

    .rbt-header .mainmenu-nav .mainmenu li.with-megamenu .rbt-megamenu .wrapper .mega-menu-item {
        li {
            a {
                color: var(--color-white);

                &:hover {
                    background: var(--color-bodyest);
                    color: var(--color-white) !important;
                }

                &.active {
                    background: var(--color-bodyest);
                    color: var(--color-white) !important;
                }
            }
        }
    }

    .nav-quick-access {
        background: var(--color-bodyest);

        li {
            a {
                color: var(--color-white-dark);

                &:hover {
                    color: var(--color-white);
                }
            }
        }
    }

    .rbt-category-menu-wrapper {

        .category-dropdown-menu,
        .update-category-dropdown {
            background-color: var(--color-darker);
            box-shadow: var(--dark-shadow-1);
            border-top: 1px solid transparent;

            .category-menu-item {
                .rbt-vertical-nav-list-wrapper {
                    li {
                        a {
                            color: var(--color-white);
                        }

                        &.active {
                            a {
                                color: var(--color-white);
                            }
                        }

                        &:hover,
                        &.active {
                            a {
                                color: var(--color-white);
                                background: var(--color-bodyest);
                            }
                        }
                    }
                }

                .rbt-vertical-nav-content {
                    padding-left: 20px;
                    height: 100%;
                    width: auto;
                    min-width: 320px;

                    .vartical-nav-content-menu {
                        li {
                            a {
                                padding: 5px 0px;
                            }

                            &:hover,
                            &.active {
                                a {
                                    background: transparent;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    a,
    button,
    span {
        &.rbt-btn-link {
            color: var(--color-white) !important;

            &:hover {
                color: var(--color-white-off) !important;
            }
        }

        &.transparent-button {
            color: var(--color-white);

            i {
                svg {
                    g {
                        stroke: var(--color-white);
                    }
                }
            }
        }
    }

    .rbt-sidebar-widget-wrapper {
        box-shadow: var(--dark-shadow-3);
        background: var(--color-darker);
    }

    .rbt-single-widget .rbt-widget-title-2 {
        color: var(--color-white-off);
        border-bottom: 2px solid var(--dark-color-border);
    }


    .rbt-single-widget {
        background: transparent;
    }

    .rbt-show-more-btn::before {
        background: linear-gradient(rgba(68, 68, 68, 0) 0%, #192335 100%);
    }

    .rbt-single-widget .rbt-lable {
        background: var(--color-body);
        color: var(--color-white-off);
    }

    .rbt-tag-list a {
        color: var(--color-white-dark);
        background: var(--white-opacity);
        box-shadow: var(--dark-shadow-3);

        &:hover {
            background: var(--color-primary);
        }
    }

    .account-details-form input {
        border: 2px solid var(--dark-color-border);
    }

    .rbt-breadcrumb-style-3 .title {
        color: var(--color-white-darks) !important;
    }

    .rbt-breadcrumb-style-3 .description {
        color: var(--color-white-off);
    }

    .rbt-breadcrumb-style-3 .total-student span {
        color: var(--color-white-off);
    }

    .rbt-breadcrumb-style-3 .rbt-badge-4 {
        color: var(--color-white-off) !important;
    }

    .rbt-my-account-inner {
        .rbt-link-hover {
            a {
                color: var(--color-white-off);

                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }

    .rbt-border-dashed {
        border: 2px dashed var(--dark-color-border) !important;
    }

    .rbt-default-sidebar-wrapper .rbt-default-sidebar-list li+li {
        border-top: 1px solid var(--dark-color-border);
    }

    .pro-qty {
        border: 1px solid var(--dark-color-border);
    }

    .progress {
        background: var(--color-bodyest-2);
    }

    .rbt-saved-message {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-my-account-table .table th,
    .rbt-my-account-table table th {
        color: var(--color-white-dark);
        border: 1px solid var(--dark-color-border);
    }

    .rbt-my-account-table .table td,
    .rbt-my-account-table table td {
        color: var(--color-white-dark);
        border: 1px solid var(--dark-color-border);
    }

    .rbt-card {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-1);

        .rbt-card-body {
            .rbt-card-title {
                a {
                    color: var(--color-white);

                    &:hover {
                        color: var(--color-primary);
                    }
                }
            }

            .rbt-card-text {
                color: var(--color-white-dark);
            }
        }
    }

    .rbt-meta {
        li {
            color: var(--color-white-dark) !important;
        }
    }

    .rbt-review {
        .rating-count {
            color: var(--color-white-dark);
        }
    }

    .rbt-swiper-pagination {
        .swiper-pagination-bullet {
            opacity: 1;
            background: var(--color-primary-100);
            box-shadow: inset 0 0 0 5px var(--color-primary-100);

            &.swiper-pagination-bullet-active {
                background: transparent;
                box-shadow: inset 0 0 0 1px var(--color-primary);
            }
        }
    }

    .rbt-cat-box-1 {
        .inner {
            background-color: var(--color-bodyest);
            box-shadow: var(--dark-shadow-2);
            border: 1px solid var(--dark-color-border);
        }

        &.variation-5 {
            .inner {
                .content {
                    background: var(--color-bodyest);

                    &:hover {
                        .title {
                            a {
                                color: var(--color-primary) !important;
                            }
                        }
                    }

                    .title {
                        color: var(--color-white-dark);
                    }

                    .read-more-btn {
                        a {
                            color: var(--color-white-dark);
                        }
                    }
                }
            }
        }
    }

    .rbt-bookmark-btn {
        .rbt-round-btn {
            color: var(--color-white-dark);

            &:hover {
                color: var(--color-primary);
            }
        }
    }

    .rbt-author-meta {
        .rbt-author-info {

            a {
                color: var(--color-white);

                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }

    .rbt-feature {
        &.feature-style-2 {
            &:hover {
                box-shadow: var(--dark-shadow-2dow-2);
                background: var(--color-bodyest);
            }
        }
    }

    .rbt-callto-action {
        &.callto-action-default {
            box-shadow: var(--dark-shadow-3);
        }
    }

    .rbt-category {

        a {
            border: 1px solid var(--dark-color-border);
            color: var(--color-white-dark);


            &:hover {
                background-color: var(--color-grey);
            }
        }
    }

    .rbt-counterup {
        background: var(--color-darker);
        box-shadow: var(--dark-shadow-4);

        .rbt-round-icon {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-2);

            .feather-heart {
                color: var(--color-white) !important;
            }
        }

        &.style-2,
        &.style-3 {
            background: transparent !important;
            box-shadow: none !important;
        }

        .top-circle-shape {
            background: linear-gradient(90deg, #2F57EF 0%, #C586EE 100%);

            &::before {
                background: var(--color-darker);
            }
        }


        &.rbt-hover-active {

            &.active {
                background: var(--color-bodyest);
                box-shadow: var(--dark-shadow-3);

                .inner {
                    .content {
                        .counter {
                            color: var(--color-white);
                        }

                        .subtitle {
                            color: var(--color-white);
                        }
                    }
                }
            }
        }

    }

    .top-circle-shape {
        &::before {
            background: var(--color-darker);
        }

    }

    .rbt-testimonial-box {
        &.bg-gradient-7 {
            background: var(--color-bodyest) !important;
        }

        .inner {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-4);
        }
    }

    .rbt-btn {
        &.btn-border {
            background: transparent;
            border: 2px solid var(--dark-color-border);
            color: var(--color-white-dark);

            &:hover {
                background: var(--color-primary);
                border: 1px solid transparent;
                color: var(--color-white);
            }
        }
    }

    .bg-gradient-3 {
        .rbt-swiper-pagination {
            .swiper-pagination-bullet {
                box-shadow: inset 0 0 0 5px var(--color-white-off);

                &.swiper-pagination-bullet-active {
                    box-shadow: inset 0 0 0 1px var(--color-white);
                }
            }
        }
    }

    .rbt-team-tab-content {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-team-tab-content {
        .rbt-team-details {
            p {
                color: var(--color-white-dark);
            }

            .team-form {
                color: var(--color-white-dark);
            }
        }
    }

    .rbt-badge-2 {
        border: 1px solid var(--dark-color-border);
        background: var(--color-bodyest);
        color: var(--color-white-dark);
    }

    .rbt-breadcrumb-default.rbt-breadcrumb-style-3::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        background: linear-gradient(252deg, rgba(25, 35, 53, 0) 35.97%, rgba(47, 87, 239, 0.3) 100.98%),
            linear-gradient(110deg, rgba(25, 35, 53, 0) 38.37%, rgba(185, 102, 231, 0.4) 102.05%);
        top: 0;
        z-index: 1;
    }

    .rbt-feature {
        .icon {
            i {
                color: var(--color-white-off) !important;
            }
        }
    }

    .rbt-team-tab-thumb {
        li {
            .rbt-team-thumbnail {
                background: var(--color-bodyest);
                box-shadow: var(--dark-shadow-3);
            }
        }
    }

    .rbt-moderbt-btn {
        .moderbt-btn-text {
            color: var(--color-white) !important;
        }

        i {
            color: var(--color-white-dark);
        }
    }

    .footer-style-1 {
        .ft-title {
            color: var(--color-white);
        }
    }

    .footer-widget {
        .ft-link {
            li {
                color: var(--color-white-dark);

                span,
                a {
                    color: var(--color-white-dark);
                }
            }
        }
    }

    .copyright-style-1 {
        p {
            color: var(--color-white-dark);
        }

        .copyright-link {
            li {
                a {
                    color: var(--color-white-dark);
                }

                &+li {
                    &::after {
                        background: var(--color-white);
                        opacity: 0.1;
                    }
                }
            }
        }

        .rbt-link-hover {
            a {
                color: var(--color-white);

                &:hover {
                    color: var(--color-primary) !important;
                }
            }
        }
    }

    .side-menu,
    .rbt-cart-side-menu {
        background: var(--color-bodyest);

        .inner-wrapper {
            .inner-top {
                border-bottom: 1px solid var(--dark-color-border-2);
            }
        }

        .rbt-cart-subttotal {
            p {
                color: var(--color-white);
            }
        }

        .side-nav {
            .navbar-nav {
                li {
                    a {
                        color: var(--color-white-dark);
                    }
                }
            }
        }
    }

    .rbt-course-main-content.liststyle {
        li {
            a {
                color: var(--color-white-dark);

                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }

    .footer-style-2 {
        .inner {
            p>a {
                color: var(--color-white-off);

                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }

    .about-style-2 .about-wrapper {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
    }

    .minicart-close-button {
        i {
            color: var(--color-white-off);
        }
    }

    .minicart-item {
        .close-btn {
            button {
                color: var(--color-white-off);
            }
        }
    }

    .rbt-minicart-wrapper {
        .minicart-item {
            &+.minicart-item {
                border-top: 1px dashed var(--dark-color-border-2);
            }
        }

        .product-content {
            .title {
                a {
                    color: var(--color-white);
                }
            }
        }

        .quantity {
            color: var(--color-white-off);
        }
    }

    .card-minimal {
        .meta-list {
            .list-item {
                color: var(--color-white-off);
            }
        }
    }

    .rbt-round-bottom-shape {

        &::after {
            background: url(../images/dark/bg/banner-bg-shape-1.svg);
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
        }
    }

    // .rbt-progress-parent {
    //     box-shadow: var(--dark-shadow-3);
    // }

    .rbt-border {
        border: 1px solid var(--dark-color-border) !important;
    }

    .rbt-border-bottom {
        border-bottom: 1px solid var(--dark-color-border);
    }

    .rbt-search-field {
        border: 1px solid var(--dark-color-border);
    }

    .search-field {

        .serach-btn {
            color: var(--color-white-off);

            &:hover {
                color: var(--color-primary);
            }
        }
    }

    .rbt-arrow-between .rbt-swiper-arrow {
        background-color: var(--color-darkest);

        &::after {
            background-color: var(--color-darkest);
        }
    }

    .bg_image--13 {
        position: relative;
        overflow-y: hidden;

        &::before {
            position: absolute;
            content: "";
            width: 100%;
            height: 100%;
            background: var(--gradient-dark);
        }
    }

    .rbt-list-style-2 {
        li {
            color: var(--color-white-dark);
        }
    }

    &.rbt-dark-header-8 {
        .rbt-header-8 {
            background: var(--gradient-dark) !important;
        }
    }

    .rbt-dark-header-8 {
        &.rbt-header-8 {
            background: var(--gradient-dark) !important;
        }
    }

    .default-exp-wrapper .filter-select select {
        background: var(--color-darker);
        box-shadow: none;
    }

    .pro-price-amount {
        color: var(--color-white-dark);
    }

    .filter-select.rbt-modern-select.search-by-category select {
        background: var(--color-darker);
        box-shadow: none;
    }

    .rbt-accordion-style.rbt-accordion-02 .card .card-body p {
        color: var(--color-white-off);
    }

    .table>:not(caption)>*>* {
        background-color: transparent;
    }

    // .filter-select {
    //     box-shadow: var(--dark-shadow-3);

    //     select {
    //         background: var(--color-darker);
    //         border-radius: 500px;
    //         box-shadow: none;
    //     }
    // }

    .rbt-header.rbt-header-8 .rbt-header-wrapper.rbt-sticky {
        background-color: transparent !important;
        box-shadow: none !important;
    }


    .rbt-swiper-thumb .swiper-slide::before {
        background: var(--dark-color-border-2);
    }

    .rbt-swiper-thumb .swiper-slide.swiper-slide-thumb-active::before {
        background: linear-gradient(90deg, var(--color-secondary), var(--color-primary));
    }

    .rbt-flipbox {
        .rbt-flipbox-wrap {
            .rbt-flipbox-front {
                .content {
                    .title {
                        a {
                            color: var(--color-darker) !important;
                        }
                    }

                    p {
                        color: var(--color-bodyest);
                    }

                    .stretched-link {
                        color: var(--color-darker) !important;
                    }
                }
            }

            .rbt-flipbox-back {
                .rbt-list-style-3 {
                    li {
                        color: var(--color-darker) !important;
                    }
                }
            }
        }
    }

    .shape-wrapper {
        .top-shape {
            img {
                opacity: 0.05;
            }
        }
    }

    .pricing-table {
        &.style-3 {
            &.active {
                background: var(--color-darker);
                box-shadow: var(--dark-shadow-1);
            }

            .pricing-header {
                border-top: 1px solid var(--dark-color-border);
                border-bottom: 1px solid var(--dark-color-border);
            }
        }
    }

    .rbt-badge {
        background: var(--dark-color-border);
        color: var(--color-white);
    }

    .team-style-default,
    .rbt-default-card {
        .inner {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-3);
        }
    }

    .rbt-testimonial-content {
        .inner {
            &::before {
                opacity: 0.1;
            }
        }
    }

    ul {
        &.testimonial-thumb-wrapper {
            li {
                .thumb {
                    img {
                        background: var(--color-darker);
                        box-shadow: var(--dark-shadow-3);
                    }

                    &::after {
                        background-color: var(--color-darker);
                        opacity: 0.2;
                    }
                }
            }
        }
    }

    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    input[type="tel"],
    input[type="date"],
    textarea {
        box-shadow: none;
        color: var(--color-white-off);

        &::placeholder {
            color: var(--color-white-off);
            /* Firefox */
            opacity: 1;
        }

        &:-ms-input-placeholder {
            /* Internet Explorer 10-11 */
            color: var(--color-white-off);
            opacity: 1;
        }
    }

    .form-group {

        input,
        textarea {
            border: 0;
            border-bottom: 2px solid var(--dark-color-border);
        }
    }

    .single-method p {
        color: var(--color-white-offs);
    }

    .form-check-input {
        background-color: var(--color-body);
    }

    .footer-style-1 {
        .newsletter-form {
            .right-icon {
                &.icon-email {
                    &::after {
                        content: "\e98a";
                        color: var(--color-white-dark);
                        opacity: 0.5;
                    }
                }
            }
        }
    }

    .newsletter-style-1 {
        background: var(--color-grey) !important;

        &::after {
            filter: invert(1);
        }
    }

    .social-share-transparent {
        li {
            a {
                color: var(--color-white-off);

                &::before {
                    background: var(--color-darker);
                }
            }
        }
    }

    .rbt-secondary-menu {
        li {
            a {
                &:hover {
                    color: var(--color-white);
                }
            }
        }
    }

    .advance-tab-button-1 {
        .tab-button-list {

            .tab-button {
                .title {
                    color: var(--color-white);
                }

                .description {
                    color: var(--color-white-off);
                }

                &::before {
                    background: var(--color-bodyest);
                    box-shadow: var(--dark-shadow-3);
                }

                &::after {
                    border-left: 12px solid var(--color-bodyest);
                }
            }
        }
    }

    .splash-layout-presentation .advance-tab-button-1 .tab-button-list .tab-button .title img {
        filter: invert(0.8);
    }

    .single-demo .mobile-view {
        border: 2px solid var(--dark-color-border-2);
        background: rgb(39 48 65 / 42%);

        &::before {
            background: var(--color-bodyest);
        }
    }

    .theme-gradient.new-big-heading-gradient {
        background: linear-gradient(180deg, rgb(76 90 238 / 20%) 0%, rgb(78 90 236 / 5%) 80%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
        white-space: nowrap;
    }

    .rbt-accordion-style {
        &.rbt-accordion-01 {
            .card {
                background: var(--color-bodyest);
                box-shadow: var(--dark-shadow-3);

                .card-header {
                    button {
                        &.collapsed {
                            color: var(--color-white);

                            &::before {
                                content: "\e9b1";
                            }
                        }
                    }
                }

                .card-body {
                    background: var(--color-bodyest);
                }
            }
        }

        &.rbt-accordion-06 {
            .card {

                .card-body {
                    background: transparent;
                    border-top: 1px solid var(--dark-color-border);
                }
            }
        }
    }

    .rbt-table {

        thead,
        tfoot {
            tr {

                th,
                td {
                    color: var(--color-white);
                }
            }
        }

        tfoot {
            tr {

                th,
                td {
                    background: var(--color-darker);
                }
            }
        }

        tbody {
            tr {

                th,
                td {
                    color: var(--color-white-off);
                    // background: var(--color-grey);
                }
            }
        }
    }

    .newsletter-form-1 {

        input {
            background: var(--color-bodyest);
        }
    }

    .scroll-animation-wrapper {
        position: relative;
        z-index: 1;

        &::before,
        &::after {
            position: absolute;
            height: 100%;
            width: 100px;
            background: -webkit-gradient(linear, left top, right top, from(#192335), to(hsla(0, 0%, 100%, 0)));
            background: -webkit-linear-gradient(left, #192335, hsla(0, 0%, 100%, 0));
            background: -moz-linear-gradient(left, #192335 0, hsla(0, 0%, 100%, 0) 100%);
            background: linear-gradient(90deg, #192335 0, hsla(0, 0%, 100%, 0));
            z-index: 2;
            content: "";

            @media #{$sm-layout} {
                width: 50px;
            }
        }

        &::before {
            top: 0;
            left: 0;
        }
    }

    .rbt-splite-style {

        .split-inner {
            .title {
                color: var(--color-white);
            }

            .split-list {

                li {
                    color: var(--color-white-off);
                }
            }
        }
    }

    .pricing-billing-duration {

        ul {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-3);

            .nav-item {
                .nav-link {
                    color: var(--color-white-off);

                    &.active {
                        color: var(--color-white);
                    }
                }
            }
        }
    }

    .pricing-badge::after {
        border-color: transparent transparent transparent var(--color-bodyest);
    }

    .advance-pricing {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);

        .pricing-left {
            background: var(--color-darker);
        }
    }

    .advance-pricing .pricing-left .price-wrapper .price-amount {
        color: var(--color-white-dark);
    }

    .advance-pricing .pricing-left .subtitle {
        color: var(--color-white-off);
    }

    .pricing-table {
        background: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);

        .pricing-body {
            .list-item {
                li {
                    color: var(--color-white-off);
                }
            }
        }
    }

    .rbt-cta-default {
        &.style-4 {
            .content {
                .title {
                    color: var(--color-white);
                }
            }
        }
    }

    .rbt-service {
        &.rbt-service-2 {
            background: var(--color-bodyest);
            box-shadow: var(--dark-shadow-1);

            .inner {
                .content {
                    color: var(--color-bodyest);

                    p {
                        color: var(--color-white-off);
                    }

                    i {
                        svg {
                            g {
                                stroke: var(--color-dark);
                            }
                        }
                    }
                }
            }
        }
    }

    .rbt-service.rbt-service-2.rbt-hover-02.bg-no-shadow {
        .inner {
            .content {
                .title {
                    a {
                        color: var(--color-darker) !important;
                    }
                }

                .transparent-button {
                    color: var(--color-bodyest);
                }

                p {
                    color: var(--color-darker) !important;
                    opacity: 0.8;
                }
            }
        }
    }

    .rbt-testimonial-box .clint-info-wrapper .thumb {
        border: 2px solid var(--dark-color-border);
        background: var(--dark-color-border);
    }

    .service-card-5 .inner .content {
        h6 {
            color: var(--color-white);
        }
    }

    .service-card-5 .inner {
        box-shadow: var(--dark-shadow-3);
    }

    .service-card-5.variation-2 .inner {
        box-shadow: none !important;
    }

    .service-card-5 .inner::before {
        background: var(--color-bodyest);
    }

    .service-card-5 .inner .content .description {
        color: var(--color-white);
    }

    .social-default.transparent-with-border li a {
        background: transparent !important;
        border: 2px solid var(--dark-color-border);
    }

    .social-default li a {
        background-color: var(--color-bodyest-2);
        color: var(--color-white-dark);
        line-height: 44px;
    }

    .service-card-6 .inner {
        border: 1px solid var(--dark-color-border);
    }

    .service-card-6 .inner .content .description {
        color: var(--color-white-off);
    }

    .filter-button-default,
    .filter-tab-button {

        button,
        a {
            background: var(--color-darker);
            color: var(--color-white-dark);
            box-shadow: var(--dark-shadow-1);

            &.is-checked,
            &.active,
            &:hover {
                background-color: var(--color-primary);
                color: var(--color-white) !important;
            }
        }
    }

    .react-select .react-select__control {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
        color: var(--color-white-off);
        border: 0 none;
    }

    .react-select .react-select__menu-list {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-2);
    }

    #formElements {
        overflow: visible;
    }

    .feather-x {
        color: var(--color-white-off);
    }

    .rbt-search-field,
    .rbt-search-with-category {
        border: 1px solid var(--dark-color-border);
    }

    .rbt-team-modal,
    .rbt-default-modal {
        background: var(--color-darker);

        .modal-dialog {
            .modal-content {
                background: var(--color-bodyest);
                box-shadow: var(--dark-shadow-3);
            }
        }
    }

    .tab-button-style-2 {
        border-bottom: 2px solid var(--dark-color-border-2);

        li {
            a {
                color: var(--color-white-dark);
            }

            .active {
                color: var(--color-primary);
            }
        }
    }

    .rbt-search-with-category .search-by-category::after {
        background: var(--dark-color-border-2);
    }

    .rbt-modern-select .bootstrap-select button.btn-light {
        background-color: var(--color-bodyest);
        box-shadow: var(--dark-shadow-3);
        color: var(--color-white-off);
    }

    .rbt-accordion-style.rbt-accordion-04 .card .card-body {
        border-top: 1px solid var(--white-opacity);
        color: var(--color-white-off);
    }

    .rbt-accordion-style.rbt-accordion-01 .card .card-body {
        border-top: 1px solid var(--white-opacity);
        color: var(--color-white-off);
    }

    .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list>a:hover {
        background: var(--color-bodyest);
        color: var(--color-primary);
    }

    .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper {
        background-color: var(--color-darker);
        border-left: 1px solid var(--dark-color-border);
    }

    .react-select__indicator {
        svg {
            color: var(--color-body);
        }
    }

    .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper .child-inner .dropdown-child-list a {
        color: var(--color-white-off);

        &:hover {
            color: var(--color-primary);
        }
    }

    .react-select2 .react-select__control {
        background-color: transparent;
        border-radius: 28px 0 0 28px;
        height: 45px;
        min-width: 250px;

        .react-select__input-container {
            padding-top: 0;
        }
    }

    .react-select {
        max-width: 250px;
    }

    .rbt-table thead tr th,
    .rbt-table thead tr td {
        background: var(--primary-opacity);
    }

    .rbt-modern-select.bg-transparent .bootstrap-select button.btn-light {
        border: 1px solid var(--dark-color-border);
    }

    .rbt-search-style-1 {
        input {
            border: 2px solid var(--dark-color-border);
        }

        .search-btn {
            i {
                color: var(--color-white-off);
            }
        }
    }

    .rbt-title-style-3,
    .rbt-short-title {
        border-bottom: 2px solid var(--dark-color-border);
    }

    .rbt-shadow-box {
        box-shadow: var(--dark-shadow-2);
        background: var(--color-bodyest);
    }

    .rbt-style-guide-area .rbt-shadow-box:not(.rbt-gradient-border) {
        box-shadow: var(--dark-shadow-2);
        background: var(--color-bodyest) !important;
    }

    .rbt-dashboard-content.bg-color-white.rbt-shadow-box {
        box-shadow: var(--dark-shadow-2);
        background: var(--color-bodyest-2) !important;
    }

    .cd-headline.type .cd-words-wrapper::after {
        background: var(--dark-color-border);
    }

    .rbt-accordion-style.rbt-accordion-02 .card .card-header {
        border-bottom: 1px solid var(--dark-color-border);
    }

    .color-box-inner .color-box {
        box-shadow: var(--dark-shadow-3);
    }

    .rbt-widget-details .rbt-course-details-list-wrapper {
        li {
            a {
                color: var(--color-white-off);
            }

            &+li {
                border-top: 1px solid var(--dark-color-border-2);
            }
        }
    }

    .rbt-gradient-border::before {
        background: var(--color-darker);
    }

    .rbt-search-style input {
        border: 2px solid var(--dark-color-border);
    }

    .rbt-search-field .rbt-modern-select .bootstrap-select button.btn-light,
    .rbt-search-with-category .rbt-modern-select .bootstrap-select button.btn-light {
        background: transparent !important;
    }

    .bg-color-white {

        .modern-course-features-box .inner .content .title,
        .course-feature-list li .feature-content .featute-title {
            color: var(--color-white-dark);
        }

        .modern-course-features-box .inner .content p,
        .course-feature-list li .feature-content .featute-title span {
            color: var(--color-white-off) !important;
        }

        .modern-course-features-box {
            background: var(--primary-opacity);
        }
    }

    .rbt-offcanvas-trigger {
        .offcanvas-trigger {
            display: block;

            .offcanvas-bars {

                span {

                    &::before,
                    &::after {
                        background-color: var(--color-white-off);
                    }
                }
            }
        }
    }

    .rbt-testimonial-box {
        .inner {
            &::before {
                opacity: 0.5;
            }
        }
    }

    .brand-style-2 {
        li {
            a {
                img {
                    filter: contrast(0.5);
                }
            }
        }
    }

    .rbt-default-sidebar-wrapper .rbt-default-sidebar-list li {
        a {
            color: var(--color-white-dark);

            i {
                color: var(--color-white-dark);
            }

            &:hover {
                color: var(--color-primary);
            }

            &.active {
                color: var(--color-primary);

                i {
                    color: var(--color-primary) !important;
                }
            }
        }
    }

    .rbt-counterup {
        // border: 2px solid var(--dark-color-border-2) !important;

        &.bg-primary-opacity {
            background: var(--primary-opacity) !important;

            .inner {
                .rbt-round-icon {
                    &.bg-primary-opacity {
                        background: var(--primary-opacity) !important;
                        // box-shadow: var(--dark-shadow-1) !important;
                    }
                }

                .bg-violet-opacity {
                    .content {
                        .counter {
                            span {
                                color: var(--color-white) !important;
                            }
                        }
                    }
                }
            }
        }
    }

    .rbt-dashboard-content {
        .content {
            .rbt-dashboard-table {
                .table-borderless {
                    tbody {
                        tr {
                            a {
                                color: var(--color-white-dark);
                            }
                        }
                    }
                }
            }
        }
    }

    .rbt-default-form input,
    .rbt-default-form textarea {
        border: 2px solid var(--dark-color-border-2);
    }

    .active-icon-white {
        color: var(--color-white);
         
    }
 
    .side-nav-opened .active-dark-mode .rbt-header.rbt-transparent-header .rbt-header-wrapper:not(.bg-not-transparent) {
        background-color: var(--color-bodyest) !important;
        box-shadow: var(--dark-shadow-3);
    }

    .jodit-toolbar-editor-collection.jodit-toolbar-editor-collection_mode_horizontal.jodit-toolbar-editor-collection_size_middle svg {
        filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(360deg) brightness(100%) contrast(100%);
    }

    .jodit-toolbar__box:not(:empty):not(:empty) {
        background-color: #192335;
    }

    .jodit-toolbar__box:not(:empty) {
        border: 1px solid #6b7385;
    }

    .jodit-container:not(.jodit_inline) .jodit-workplace {
        background-color: #273041;
        border: 0 solid #6b7385;
    }

    .jodit-toolbar__box:not(:empty) .jodit-toolbar-editor-collection:after {
        background-color: #6b7385;
    }

    .jodit-ui-group_separated_true:not(:last-child):not(.jodit-ui-group_before-spacer_true):after {
        border-right: 1px solid #6b7385;
    }

    .jodit-container:not(.jodit_inline) {
        background-color: #273041;
        border: 1px solid #6b7385;
    }

    .jodit-workplace+.jodit-status-bar:not(:empty) {
        border-top: 1px solid #6b7385;
    }

    .jodit-status-bar {
        background-color: #273041;
    }
}

.active-dark-mode .courses-elements-dark-mode .e-con-inner > .elementor-element {
    background-color: var(--color-bodyest)!important;
}

.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-text,
.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-text,
.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-text {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-item a:hover,
.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-item a:hover,
.active-dark-mode .courses-elements-dark-mode .elementor-widget-container .elementor-icon-list-item a:hover {
    color: var(--color-white)!important;
}

.active-dark-mode .rbt-copyright-content-top {
    border-top: 1px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .widget_mc4wp_form_widget input[type=email] {
    border: 2px solid var(--dark-color-border) !important;
}

.active-dark-mode .blog-post-dark-mode {
    background-color: #333d51!important;
}   

.active-dark-mode .our-top-popular-course-darkmode {
    background-color: #333d51!important;
}

.active-dark-mode .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list > a,
.active-dark-mode .rbt-category-update .update-category-dropdown .inner .dropdown-parent-wrapper .dropdown-parent-list .dropdown-child-wrapper .child-inner .dropdown-child-list a {
    color: var(--color-white);
}

.active-dark-mode .categories-area-dark-mode {
    background-color: var(--color-darker) !important;
}

.active-dark-mode .student-feedback-section-dark-mode {
    background-color: #333d51 !important;
}

.active-dark-mode .divider-dark-mode-custom .elementor-divider-separator {
    border-block-start: 1px solid  #333d51!important;
}

.active-dark-mode .rbt-card.variation-01.rbt-hover .list-item-button a.tutor-btn.tutor-btn-outline-primary,
.active-dark-mode a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    font-size: 14px;
    color: var(--color-white)!important;
}

.active-dark-mode .rbt-header.menu-underline-on .mainmenu-nav .mainmenu > li::after {
    background: rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .rbt-header .mainmenu-nav .mainmenu > li > a {
    color: var(--color-white-dark) !important;
}

.active-dark-mode .rbt-mega-menu-list .mega-menu-item li a {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .rbt-mega-menu-list .mega-menu-item li a:hover {
    color: var(--color-primary) !important; 
}

.active-dark-mode .rbt-banner-dark-mode-kindergarden {
    position: relative;
}

.active-dark-mode .rbt-banner-dark-mode-kindergarden::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
}

.active-dark-mode .rainbow-has-online-class-card,
.active-dark-mode .teacher-banner-dark-mode {
    background: var(--color-bodyest) !important;
}

.active-dark-mode .instructor-banner-dark-mode {
    background: var(--color-darker) !important;
}

.active-dark-mode .footer-widget .ft-link li span:hover , 
.active-dark-mode .footer-widget .ft-link li a:hover {
    color: var(--color-primary);
}
.active-dark-mode .rbt-arrow-between .rbt-swiper-arrow::after {
    background-color: var(--color-darkest);
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .online-school-darkmode-popular-course .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn {
    width: 40px;
    height: 40px;
    line-height: 41px;
    text-align: center;
    border-radius: 100%;
    position: relative!important;
    z-index: 1;
    background: transparent;
    padding: 0;
    border: 0 none;
    display: block;
    position: relative;
    color: var(--color-white-dark);
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .online-school-darkmode-popular-course .rbt-bookmark-btn .tutor-course-wishlist-btn::after,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn::after,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: 0.4s;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    z-index: -1;
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .online-school-darkmode-popular-course .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn:hover:after {
    opacity: 1;
    transform: scale(1);
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .online-school-darkmode-popular-course .rbt-bookmark-btn .tutor-course-wishlist-btn::after,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn:after {
    background: var(--color-darker);
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .online-school-darkmode-popular-course .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:hover,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn:hover {
    color: var(--color-primary);
}

.active-dark-mode .elegant-course .rbt-bookmark-btn .tutor-course-wishlist-btn::after,
.active-dark-mode .course-tab-dark-mode-school .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .tutor-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-related-course-area .rbt-bookmark-btn .tutor-course-wishlist-btn:after,
.active-dark-mode .rbt-card-body .rbt-bookmark-btn .tutor-course-wishlist-btn:after {
    background: var(--color-darker);
}

.active-dark-mode #call-to-action-darkmode {
    background: var(--color-bodyest);
}

.active-dark-mode .brand-style-1 li a, 
.active-dark-mode .brand-style-3 li a,
.active-dark-mode .brand-style-1 li img , 
.active-dark-mode .brand-style-3 li img ,
.active-dark-mode .rbt-event-sponsor-single a img {
    filter: contrast(0.5) grayscale(0.5) invert(0.5);
}

.active-dark-mode .online-school-darkmode-popular-course {
    background-color: #333d51!important;
}

.active-dark-mode .online-school-darkmode-popular-course,
.active-dark-mode .course-tab-dark-mode-school,
.active-dark-mode .service-widget-dark-mode {
    background-color: #333d51!important;
}

.active-dark-mode .rbt-header .mainmenu-nav.onepagenav .mainmenu li.current a {
    color: var(--color-primary)!important;
}

.active-dark-mode span.min-lables.rainbow-course-home-duration {
    color: var(--color-white-off) !important;
}

.active-dark-mode .rainbow-featured-single-tutor-course .tutor-course-content-list-item .tutor-d-flex.tutor-align-center{
    color: var(--color-white-dark);
}

.active-dark-mode .technology-brand-dark-mode {
    background: var(--color-bodyest)!important;
}

.active-dark-mode .testimonial-dark-mode-technology {
    background-color: var(--color-darker) !important;
}

.active-dark-mode .academy-cat-seciton,.active-dark-mode .online-courses-tab-darkmode {
    background-color: #333d51!important;
}

.active-dark-mode .online-course-school-darkmode-version {
    background: linear-gradient(90deg, rgba(32, 67, 201, 0.65) 0%, rgba(152, 99, 187, 0.45) 100%)!important;
}

body.active-dark-mode .main-page-wrapper .rbt-course-area .rainbow-title {
    color: var(--color-white) !important;
}

.active-dark-mode .online-course-event-darkmode {
    background-color: #333d51!important;
}

.active-dark-mode .online-school-darkmode-blog-post {
    background-color: var(--color-darker) !important;
}

.active-dark-mode.post-type-archive-courses .rbt-page-banner-wrapper {
    background: unset!important;
}

.active-dark-mode.post-type-archive-courses .rbt-page-banner-wrapper .rbt-search-style input {
    background-color: transparent;
}

.active-dark-mode.post-type-archive-courses .rbt-page-banner-wrapper .rbt-search-style .rbt-search-btn i {
    color: var(--color-white);
}

.active-dark-mode.post-type-archive-courses .rbt-page-banner-wrapper .rbt-search-style .rbt-search-btn:hover i {
    color:var(--color-primary)
}

.active-dark-mode button.rbt-filter-rating-toggle,
.active-dark-mode .rbt-course-top-wrapper .default-exp-wrapper .filter-inner .bootstrap-select {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
}

.active-dark-mode .rbt-course-top-wrapper .rbt-single-widget.rbt-widget-rating {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
}

.active-dark-mode .tutor-course-archive-page .rbt-author-info,
.active-dark-mode .tutor-course-archive-page .rbt-card-bottom .current-price,
.active-dark-mode.single-courses .current-price,
.active-dark-mode .rbt-breadcrumb-default  .rbt-author-info,
.active-dark-mode .rbt-card-body  .rbt-author-info {
    color: var(--color-white);
}

.active-dark-mode .tutor-course-archive-page .rbt-card-bottom .off-price,
.active-dark-mode.single-courses .off-price {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode footer.rbt-footer.footer-style-1:not(.has-elementor-full-width) > .footer-top > .container {
    border-top: 1px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .rbt-sidebar-list-wrapper::-webkit-scrollbar-track {
    background-color: var(--color-body);
}

.active-dark-mode .rbt-sidebar-list-wrapper::-webkit-scrollbar-thumb {
    background-color: var(--color-body);
}
.active-dark-mode .rbt-sidebar-list-wrapper {
    scrollbar-color: rgba(from var(--color-white) r g b / 0.4) var(--color-body); 
}

.active-dark-mode .rbt-search-style .course_search_input:focus + .rbt-search-btn::after {
    background: var(--color-darker)!important;
}

.active-dark-mode .rbt-search-style .course_search_input:focus + .rbt-search-btn i {
    color: var(--color-primary);
}

.active-dark-mode .rbt-course-details-right-sidebar,
.active-dark-mode .rbt-course-details-left-sidebar {
    box-shadow: var(--dark-shadow-3);
    background: var(--color-darker);
    border-radius: 10px;
}

.active-dark-mode.single-courses .rbt-breadcrumb-default.rbt-breadcrumb-style-3 .breadcrumb-inner,
.active-dark-mode.single-course_event .rbt-breadcrumb-default.rbt-breadcrumb-style-3 .breadcrumb-inner {
    display: none;
}

.active-dark-mode .rbt-breadcrumb-default .title {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .rbt-course-details-area .course-details-content .tutor-tab-items > div {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest);
}

.active-dark-mode .tutor-toggle-more-collapsed:before {
    background: var(--color-bodyest)!important;
}

.active-dark-mode .tutor-toggle-more-collapsed li {
    color: var(--color-white);
    opacity: 0.5;
}

.active-dark-mode .rbt-course-details-area .about-author .content p,
.active-dark-mode .tutor-ratings.tutor-ratings- .tutor-ratings-average, 
.active-dark-mode .tutor-ratings.tutor-ratings- .tutor-ratings-count {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-content-list-item {
    background: transparent;
}

.active-dark-mode .rbt-course-feature-inner .tutor-course-content-list-item i {
    color: var(--color-white-dark);
}

.active-dark-mode .rbt-tutor-course-details-widebar-widget-load-more {
    border-top: 1px solid rgba(from var(--color-white) r g b / 0.3);
}

.active-dark-mode .rbt-event-sponsor-flex-wrapper {
    border-top: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .content-item-content .tutor-card {
    background: transparent;
} 

.active-dark-mode .enrolment-expire-info {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-bundle-author-list .tutor-fs-5, 
.active-dark-mode .tutor-course-details-widget-title, 
.active-dark-mode .single-course-bundle .tutor-courses-instructors .tutor-form-label {
    border-bottom:2px solid rgba(from var(--color-white) r g b / 0.3);
}

.active-dark-mode #tutor-certificate-showcase .tutor-cs-text>div {
    background: #192335;
}

.active-dark-mode #tutor-certificate-showcase .tutor-cs-text {
    background: -webkit-gradient(linear, left top, right top, from(rgba(from var(--color-white) r g b / 0.3)), to(#192335));
}

.active-dark-mode .rbt-lesson-content-wrapper .rbt-lesson-leftsidebar {
    background-color: var(--color-bodyest);
    border-right: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .modal .modal-dialog .modal-content, 
.active-dark-mode .rbt-team-area .modal {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .rbt-default-tab .tab-button .tabs__tab .nav-link.active,
.active-dark-mode .rbt-default-tab .rainbow-tab-content {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
    color: var(--color-white-off);
}

.active-dark-mode .rbt-schedule-author-box-single {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest);
}

.active-dark-mode .tutor-user-public-profile .tutor-user-profile-content .profile-content .col-12,
.active-dark-mode .tutor-instructor-list-wrapper .team-form .location,
.active-dark-mode .tutor-instructor-list-wrapper .team-form i {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-instructors .tutor-form-control {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
}

.active-dark-mode .row.teacher-row-gutter .bootstrap-select > .dropdown-toggle {
    border-color: var(--color-bodyest);
    outline: none;
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
}

.active-dark-mode .row.teacher-row-gutter .bootstrap-select > .dropdown-toggle:focus {
    outline:none;
}

.active-dark-mode .dropdown-toggle::after {
    border-top: 5px solid;
    border-right: 5px solid transparent;
    border-bottom: 0;
    border-left: 5px solid transparent;
    opacity: 0.5;
}

.active-dark-mode .row.teacher-row-gutter .dropdown.bootstrap-select .dropdown-menu ,
.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu{
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
}

.active-dark-mode .row.teacher-row-gutter .dropdown.bootstrap-select .dropdown-menu .dropdown-item,
.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu .dropdown-item {
    color: var(--color-body);
}
.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu .dropdown-item:hover,
.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu .dropdown-item.active {
    color: var(--color-primary);
    background-color: var(--black-opacity)!important;
}


.active-dark-mode .row.teacher-row-gutter .dropdown.bootstrap-select .dropdown-menu .dropdown-item:focus, 
.active-dark-mode .row.teacher-row-gutter .dropdown.bootstrap-select .dropdown-menu .dropdown-item:hover {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest);
}

.active-dark-mode aside[tutor-instructors-filters] {
    box-shadow: var(--dark-shadow-3);
    background: var(--color-darker);
}

.active-dark-mode .tutor-form-check-input {
    background-color: var(--color-body);
    border: 2px solid var(--color-body);
}

.active-dark-mode aside[tutor-instructors-filters] .tutor-widget-title {
    border-bottom: 2px solid var(--dark-color-border-2);
}

.active-dark-mode .teacher-row-gutter .tutor-pagination,
.active-dark-mode .comment-list .comment .single-comment, 
.active-dark-mode .comment-list .pingback .single-comment, 
.active-dark-mode .comment-list .trackback .single-comment,
.woocommerce.active-dark-mode #review_form #respond textarea {
    border: 1px solid var(--dark-color-border-2);
}

.active-dark-mode.page-instructor .tutor-toggle-more-collapsed:before {
    background: transparent!important;
}

.active-dark-mode .tutor-btn.tutor-btn-ghost.tutor-btn-ghost-custom,
.active-dark-mode .tutor-btn-show-more.tutor-btn.tutor-btn-ghost {
    color: var(--color-primary);
}

.active-dark-mode .tutor-instructor-list-wrapper .tutor-pagination ul.tutor-pagination-numbers .page-numbers,
.woocommerce.active-dark-mode nav.woocommerce-pagination ul li a, 
.woocommerce.active-dark-mode nav.woocommerce-pagination ul li span {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .tutor-instructor-list-wrapper .tutor-pagination ul.tutor-pagination-numbers .page-numbers.current,
.woocommerce.active-dark-mode nav.woocommerce-pagination ul li span.current, 
.woocommerce.active-dark-mode nav.woocommerce-pagination ul li a:focus ,
.woocommerce.active-dark-mode nav.woocommerce-pagination ul li a:hover { 
    background: var(--color-primary);
    box-shadow: none;
}

.active-dark-mode .tutor-pagination-hints > div,
.active-dark-mode .tutor-pagination-hints > div .tutor-color-black {
    color: var(--color-white-off);
}

.active-dark-mode.privacy-policy .rn-entry-content p {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-short-item button.btn.dropdown-toggle.btn-light {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
}

.active-dark-mode  .rating .woocommerce-product-rating a,
.active-dark-mode .rbt-related-product .team-form .location {
    color: var(--color-white-off);
}

.active-dark-mode .comment-respond .btn.dropdown-toggle.bs-placeholder {
    background-color: var(--color-darker);
    color: var(--color-white);
    border: 1px solid var(--dark-color-border-2);
}

.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu li a.selected span, 
.woocommerce.active-dark-mode .bootstrap-select .dropdown-menu li a:active span {
    color: var(--color-primary);
}

.woocommerce #review_form #respond p.stars span a {
    color: var(--color-white-off);
}

.woocommerce.active-dark-mode #review_form #respond textarea,
.woocommerce.active-dark-mode .bootstrap-select .dropdown-toggle .filter-option-inner-inner { 
    color: var(--color-white-off);
}

.active-dark-mode .is-large.wc-block-cart .wc-block-cart-items {
    border-bottom: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .price.wc-block-components-product-price del {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode tr.wc-block-cart-items__row .wc-block-cart-item__wrap a {
    color: var(--color-white);
}

.active-dark-mode .wc-block-components-quantity-selector:after {
    border: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .wc-block-components-quantity-selector .wc-block-components-quantity-selector__button:focus {
    box-shadow: inset 0 0 1px 1px var(--dark-color-border-2);
}

.active-dark-mode .wc-block-components-sidebar.wc-block-cart__sidebar .wp-block-woocommerce-cart-order-summary-block {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .wc-block-components-totals-wrapper,
.active-dark-mode .wp-block-woocommerce-cart-order-summary-totals-block,
.active-dark-mode .wc-block-checkout__terms.wc-block-checkout__terms--with-separator {
    border-top: 1px solid var(--dark-color-border-2);
    color: var(--color-white-off);
}

.active-dark-mode .wc-block-components-text-input.wc-block-components-totals-coupon__input input[type="text"]{
    border: 1px solid var(--dark-color-border-2)!important;
    color: var(--color-white-off);
    background: transparent;
}

.active-dark-mode .wc-block-components-text-input.wc-block-components-totals-coupon__input label {
    color: var(--color-white-off);
}

.active-dark-mode .wc-block-components-form,
.active-dark-mode .wp-block-woocommerce-checkout-order-summary-block {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .wc-block-components-form .wc-block-components-text-input input[type=email], 
.active-dark-mode .wc-block-components-form .wc-block-components-text-input input[type=number], 
.active-dark-mode .wc-block-components-form .wc-block-components-text-input input[type=tel], 
.active-dark-mode .wc-block-components-form .wc-block-components-text-input input[type=text], 
.active-dark-mode .wc-block-components-form .wc-block-components-text-input input[type=url], 
.active-dark-mode .wc-block-components-text-input input[type=email], 
.active-dark-mode .wc-block-components-text-input input[type=number], 
.active-dark-mode .wc-block-components-text-input input[type=tel], 
.active-dark-mode .wc-block-components-text-input input[type=text], 
.active-dark-mode .wc-block-components-text-input input[type=url], 
.active-dark-mode .components-combobox-control__suggestions-container input[type=text] {
    border: 1px solid var(--dark-color-border-2)!important;
    color: var(--color-white-off)!important;
    background: transparent!important;;
}
.active-dark-mode .wc-block-components-form .wc-block-components-text-input label, 
.active-dark-mode .wc-block-components-text-input label {
    color: var(--color-white-off);
}

.active-dark-mode .wc-block-components-checkbox .wc-block-components-checkbox__input[type=checkbox],
.active-dark-mode .wc-block-components-checkbox .wc-block-components-checkbox__input[type=checkbox]:checked {
    background: var(--color-body);
    border: 2px solid var(--color-body);
}

.active-dark-mode .wc-block-components-checkbox .wc-block-components-checkbox__mark {
    fill: var(--color-white-off);
}

.active-dark-mode .wc-block-checkout__add-note .wc-block-components-textarea,
.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__container {
    border-color: var(--dark-color-border-2);
    background: transparent;
}

.active-dark-mode .wc-block-components-checkbox .wc-block-components-checkbox__input[type=checkbox]:focus {
    border-color: 2px solid var(--dark-color-border-2)!important;
}
.wc-block-components-checkbox .wc-block-components-checkbox__input[type=checkbox]:checked {
    outline: none!important;
}
.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__select,
.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__label {
    color: var(--color-white-off);
}

.active-dark-mode .wc-block-components-notice-banner.is-error {
    background: var(--color-darker) !important;
    color: var(--color-white-off);
}

.active-dark-mode .wc-block-components-order-summary .wc-block-components-order-summary__button-text,
.active-dark-mode .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-totals-footer-item .wc-block-components-totals-item__label,
.active-dark-mode .wp-block-woocommerce-checkout-totals-block .wc-block-components-totals-footer-item .wc-block-components-totals-item__value {
    color: var(--color-white);
}

.active-dark-mode .wc-block-checkout__sidebar .wc-block-components-product-name,
.active-dark-mode .wc-block-components-formatted-money-amount {
    color: var(--color-white);
    opacity: 0.9;
}

.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__select, 
.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__label {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .woocommerce .woocommerce-customer-details address,
.active-dark-mode .woocommerce table.shop_table,
.active-dark-mode table th, 
.active-dark-mode table td, 
.active-dark-mode .wp-block-calendar tbody th, 
.active-dark-mode .wp-block-calendar tbody td, 
.active-dark-mode .wp-block-table td, .wp-block-table th {
    border: 1px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .woocommerce table.shop_table td {
    border-bottom: 1px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode table thead th, 
.active-dark-mode .wp-calendar-table thead th ,
.active-dark-mode table th,
.active-dark-mode.woocommerce-checkout table.shop_table td {
    color: var(--color-white);
}

.active-dark-mode table a, 
.active-dark-mode table a:link, 
.active-dark-mode table a:visited {
    color: var(--color-white-off);
}

.active-dark-mode.woocommerce-checkout table.shop_table tr:hover {
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .woocommerce ul.order_details li {
    border-right: 1px dashed rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .tutor-login-form-wrapper .tutor-color-black {
    color: var(--color-white);
}

.active-dark-mode .tutor-login-form-wrapper,
.active-dark-mode .rbt-brand-logo-darkversion,
.active-dark-mode .call-toaction-inner-darkmode ,
.active-dark-mode .categories-graycolor-darkmode,
.active-dark-mode .teacher-darkmode-section,
.active-dark-mode .pricing-one-darkmode-gray,
.active-dark-mode .testimonial-styletwo-darkmode {
    background: var(--color-bodyest)!important;
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .rbt-pagination li a, 
.active-dark-mode .rbt-pagination li .current {
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .tutor-login-form-wrapper .tutor-color-muted,
.active-dark-mode .tutor-login-form-wrapper .tutor-color-secondary {
    color: var(--color-white);
}

.active-dark-mode .tutor-login-form-wrapper input[type="text"],
.active-dark-mode .tutor-login-form-wrapper input[type="password"] {
    background: transparent;
    border: 1px solid var(--dark-color-border-2)!important;
    color: var(--color-white-off);
}

.active-dark-mode .rbt-contact-form.contact-form-style-1 #tutor-pro-social-authentication {
    border: none;
}

.active-dark-mode .woocommerce-ResetPassword .woocommerce-Input--text, 
.active-dark-mode .woocommerce-ResetPassword .woocommerce-Input--text {
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .rbt-table.table>:not(:last-child)>:last-child>*  {
    border: none;
}

.active-dark-mode .brand-list.brand-style-2 {
    filter: contrast(0.5);
}

.active-dark-mode .brand-style-1 li {
    border-color:  var(--dark-color-border-2)!important;
}

.active-dark-mode .download-and-mobile-app-darkmode,
.active-dark-mode .counter-up-custom2,
.active-dark-mode .categories-dark-naviblue-color,
.active-dark-mode .services-styletwo-darkmode {
    background-color: #333d51!important;
}

.active-dark-mode .bg-color-extra2 .rbt-counterup.style-2 .odometer {
    color: var(--color-white);
}

.active-dark-mode .teacher-dakmode-small-section {
    background-color: var(--color-grey) !important;
}

.active-dark-mode .pricing-two-darkmode,
.active-dark-mode .services-styleone-darkmode {
    background: var(--color-darker) !important;
}

.active-dark-mode .services-styleone-darkmode {
    background: var(--color-darker) !important;
}

.active-dark-mode ul.blog-meta li a {
    color: var(--color-white-off);
}

.active-dark-mode .widget_search input[type="text"]{
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .footer-layout-4 .histudy-search .search-button, 
.active-dark-mode .rbt-sidebar-widget-wrapper .histudy-search .search-button {
    color: var(--color-white-off);
}

.active-dark-mode .footer-layout-4 .wp-block-heading, 
.active-dark-mode .footer-layout-4 .rbt-widget-title, 
.active-dark-mode .footer-layout-4 .ft-title, 
.active-dark-mode .rbt-sidebar-widget-wrapper .wp-block-heading, 
.active-dark-mode .rbt-sidebar-widget-wrapper .rbt-widget-title, 
.active-dark-mode .rbt-sidebar-widget-wrapper .ft-title {
    border-bottom: 2px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .footer-layout-4 .widget_recent_comments ul li + li, 
.active-dark-mode .footer-layout-4 .widget_recent_comments ol li + li, 
.active-dark-mode .footer-layout-4 .widget_recent_entries ul li + li, 
.footer-layout-4 .widget_recent_entries ol li + li, 
.active-dark-mode .footer-layout-4 .widget_archive ul li + li, 
.footer-layout-4 .widget_archive ol li + li, 
.active-dark-mode .footer-layout-4 .widget_categories ul li + li, 
.active-dark-mode .footer-layout-4 .widget_categories ol li + li, 
.active-dark-mode .footer-layout-4 .widget_meta ul li + li,
.active-dark-mode .footer-layout-4 .widget_meta ol li + li, 
.active-dark-mode .footer-layout-4 .widget_pages ul li + li, 
.active-dark-mode .footer-layout-4 .widget_pages ol li + li, 
.active-dark-mode .footer-layout-4 .widget_nav_menu ul li + li, 
.active-dark-mode .footer-layout-4 .widget_nav_menu ol li + li, 
.active-dark-mode .footer-layout-4 .widget_block ul li + li, 
.active-dark-mode .footer-layout-4 .widget_block ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_recent_comments ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_recent_comments ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_recent_entries ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_recent_entries ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_archive ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_archive ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_categories ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_categories ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_meta ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_meta ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_pages ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_pages ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_nav_menu ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_nav_menu ol li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_block ul li + li, 
.active-dark-mode .rbt-sidebar-widget-wrapper .widget_block ol li + li {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .rbt-sidebar-list-wrapper li .content .title a {
    color: var(--color-white-dark) !important;
}

.active-dark-mode .footer-widget li a,
.active-dark-mode .footer-widget li {
    color: var(--color-white-off) !important;
}

.active-dark-mode .footer-widget li a:hover,
.active-dark-mode .footer-widget li:hover {
    color: var(--color-primary) !important;
}

.active-dark-mode .tagcloud a {
    background: var(--color-bodyest);
}

.active-dark-mode .footer-layout-4 .tagcloud a, 
.active-dark-mode .rbt-sidebar-widget-wrapper .tagcloud a {
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .post-like.pt-like-it.rainbow-blog-details-like .like-button,
.active-dark-mode .rbt-blog-details-area p a, .active-dark-mode .entry-content p a {
    color: var(--color-white);
    opacity: 0.9;
}

.active-dark-mode .breadcrumb-content-top .meta-list li {
    color: var(--color-white-off) !important;
}

.active-dark-mode .content-item-content.rbt-default-sidebar-wrapper .mainmenu-nav li:not(:last-child) {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-row.tutor-frontend-dashboard-maincontent .rbt-title-style-3,
.active-dark-mode .tutor-row.tutor-frontend-dashboard-maincontent .content .tutor-fs-5 {
    color: var(--color-white);
}

.active-dark-mode .rbt-dashboard-table.table-responsive {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-card:not(.tutor-no-border) {
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
    border-bottom: 1px solid var(--dark-color-border)!important;
}

.tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-link,
.rbt-default-sidebar-wrapper ul li a .tutor-dashboard-menu-item-text {
    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
}

.active-dark-mode .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-link,
.active-dark-mode .rbt-default-sidebar-wrapper ul li a .tutor-dashboard-menu-item-text {
    color: var(--color-white-dark);
    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
}

.active-dark-mode .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-link:hover,
.active-dark-mode .rbt-default-sidebar-wrapper ul li a .tutor-dashboard-menu-item-text:hover {
    color: var(--color-primary);
}

.active-dark-mode .tutor-dashboard .tutor-card ,
.active-dark-mode .analytics-export-wrapper,
.active-dark-mode .tutor-thumbnail-uploader .thumbnail-wrapper,
.active-dark-mode .tutor-thumbnail-uploader .thumbnail-preview {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .analytics-export-wrapper .tutor-color-black,
.active-dark-mode .analytics-export-wrapper .tutor-color-secondary,
.active-dark-mode .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_photo_meta_area > span,
.active-dark-mode .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_photo_meta_area > span > span {
    color: var(--color-white-dark);
}
.active-dark-mode .tutor-dashboard-setting-profile .tutor-row .tutor-color-secondary {
    color: var(--color-white-off);
}

.tutor-dashboard .tutor-course-ratings .tutor-ratings-average {
    margin-top: 0;
}
.active-dark-mode .tutor-dashboard .tutor-card-body .tutor-ratings .tutor-ratings-count,
.active-dark-mode .tutor-dashboard .tutor-card-body .tutor-color-muted,
.active-dark-mode .tutor-dashboard .tutor-card-body .tutor-color-black,
.active-dark-mode .tutor-dashboard .tutor-course-card .tutor-course-name, 
.active-dark-mode .tutor-dashboard .tutor-course-card .tutor-course-name a,
.active-dark-mode .tutor-dashboard .tutor-course-ratings .tutor-ratings-average, 
.active-dark-mode .tutor-dashboard .tutor-course-ratings .tutor-ratings-count {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-dashboard .tutor-progress-bar {
    background: #899cbdb3;
}
.tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-icon {
    font-size: 16px;
}
.active-dark-mode .rbt-profile-wrapper-main-layout-1 .rbt-profile-content,
.active-dark-mode .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-icon {
    color: var(--color-white);
    opacity: 0.8;
}

.active-dark-mode .tutor-course-progress .tutor-color-secondary span {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-dashboard-content-inner.enrolled-courses .list-item-button a, 
.active-dark-mode .tutor-dashboard-content-inner.my-wishlist .list-item-button a {
    color: var(--color-white)!important;
}

.tutor-dashboard-content-inner.enrolled-courses .list-item-button a, 
.tutor-dashboard-content-inner.my-wishlist .list-item-button a {
    font-size: 14px;
}

.active-dark-mode .tutor-empty-state  .tutor-color-secondary,
.active-dark-mode .tutor-fs-3.tutor-fw-medium.tutor-color-black.tutor-mt-48.tutor-mb-12,
.active-dark-mode .tutor-fs-6.tutor-color-muted {
    color: var(--color-white)!important;
}

.active-dark-mode .tutor-modal-window .tutor-modal-content {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
    border: 1px solid var(--dark-color-border-2)!important;
}


.active-dark-mode .tutor-modal-window  .tutor-modal-content-container .tutor-color-black,
.active-dark-mode .tutor-modal-window  .tutor-modal-content-container .tutor-color-muted,
.active-dark-mode .tutor-dashboard-content-inner.my-wishlist .list-item-price ins .woocommerce-Price-amount,
.active-dark-mode table.tutor-table.tutor-table-quiz-attempts tbody tr td ,
.active-dark-mode table.tutor-table.tutor-table-quiz-attempts .tutor-color-secondary,
.active-dark-mode table.tutor-table.tutor-table-quiz-attempts .tutor-color-muted {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .tutor-modal-body textarea {
    background: transparent;
    border: 2px solid var(--dark-color-border-2)!important;
    color: var(--color-white-off);
}

.active-dark-mode .tutor-card.tutor-card-md.tutor-sidebar-card {
    border: none !important;
}

.active-dark-mode .tutor-card.tutor-card-md.tutor-sidebar-card .tutor-course-progress-wrapper .progress-steps,
.active-dark-mode .tutor-card.tutor-card-md.tutor-sidebar-card .tutor-course-progress-wrapper .progress-percentage,
.active-dark-mode .tutor-sidebar-card .tutor-enrolled-info-text {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-course-review.about-author .rbt-course-badge-5 {
    background: var(--color-body);
    color: var(--color-white);
}

.active-dark-mode .review-wrapper .tutor-ratings-average,
.active-dark-mode .review-wrapper span.value-text {
    color: var(--color-white);
}

.active-dark-mode .rbt-course-details-area .course-details-content #tutor-course-details-tab-announcements {
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .rbt-course-feature-box .tutor-empty-state,
.active-dark-mode .tutor-empty-state {
    background: var(--color-bodyest);
}

.active-dark-mode .rbt-course-feature-box .tutor-empty-state img,
.active-dark-mode .tutor-empty-state  img  {
    filter: brightness(0.4) contrast(1.5) grayscale(0.3) invert(0.4);
}

.active-dark-mode .tutor-dashboard-content-inner.my-wishlist .tutor-card-footer:not(.tutor-no-border) {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

.tutor-dashboard-content-inner.my-wishlist .tutor-bundle-discount-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 60px;
    top: 40px;
    padding: 8px 10px;
    left: 40px;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.active-dark-mode .tutor-table-responsive .rbt-table .tutor-color-secondary,
.active-dark-mode .tutor-table-responsive .rbt-table span.tutor-fw-normal.tutor-color-muted,
.active-dark-mode .tutor-given-review-actions span.tutor-btn.tutor-btn-ghost span,
.active-dark-mode .tutor-given-review-actions span.tutor-btn.tutor-btn-ghost i {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-dashboard .tutor-table tr td {
    background: transparent;
}

.active-dark-mode .tutor-dashboard .tutor-pagination,
.active-dark-mode #tutor_calendar_wrapper .tutor-calendar-events-wrapper {
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-quiz-attempt-details-wrapper .tutor-color-secondary,
.active-dark-mode .tutor-dashboard .tutor-table-responsive tr td,
.active-dark-mode .tutor-dashboard .tutor-table-responsive tr td .tutor-color-black ,
.active-dark-mode .tutor-dashboard .tutor-fs-6.tutor-fw-medium.tutor-color-black.tutor-mt-24 {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .tutor-dashboard .tutor-table tr th,
.active-dark-mode .react-datepicker__input-container .tutor-form-control,
.active-dark-mode .tutor-form-control:not(.bootstrap-select),
.active-dark-mode #tutor_calendar_wrapper .tutor-calendar-wrapper {
    background: var(--color-bodyest);
    color: var(--color-white);
    border: 1px solid var(--dark-color-border-2)!important;
    
}

.active-dark-mode .react-datepicker__input-container .tutor-form-icon-reverse,
.active-dark-mode span.tutor-fs-7.tutor-color-secondary.tutor-mr-20,
.active-dark-mode .tutor-calendar-dropdown .tutor-calendar-dropdown-label  {
    color: var(--color-white);
}

.active-dark-mode .tutor-calendar-dropdown-label svg path {
    stroke:  var(--color-white);
}

.active-dark-mode .tutor-form-select.is-active .tutor-form-select-dropdown ,
.active-dark-mode .tutor-dashboard-my-courses .tutor-card.tutor-course-card {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-form-select.is-active .tutor-form-select-dropdown .tutor-form-select-options {
    color: var(--color-white-off);
}


.active-dark-mode .tutor-form-select-option:hover, .tutor-form-select-option.is-active {
    background-color: var(--black-opacity);
    color: var(--color-primary);
}

.active-dark-mode #tutor_calendar_wrapper .tutor-custom-calendar .tutor-calendar-heading,
.active-dark-mode #tutor_calendar_wrapper .tutor-custom-calendar .tutor-calendar-heading>div {
    color: var(--color-white-dark)!important;
}
.active-dark-mode #tutor_calendar_wrapper .tutor-custom-calendar .tutor-calendar-body > .tutor-calendar-date a {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-dashboard-content-inner .rbt-card .tutor-meta span,
.active-dark-mode .tutor-dashboard-my-courses .tutor-card.tutor-course-card .tutor-meta span {
    color: var(--color-white-off)!important;
}

.active-dark-mode .tutor-dashboard-content-inner .tutor-card-footer:not(.tutor-no-border) {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-dashboard-content-inner .tutor-card-footer .tutor-color-muted,
.active-dark-mode span.tutor-fs-7.tutor-fw-medium.tutor-color-black {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .tutor-dashboard-content-inner .tutor-card-footer .tutor-color-black del .woocommerce-Price-amount {
    color: var(--color-white)!important;
    opacity: 0.4;
    text-decoration: line-through;
}

.active-dark-mode .tutor-pagination ul.tutor-pagination-numbers .page-numbers {
    color: var(--color-white-dark)!important;
}

.tutor-dashboard-my-courses .tutor-card.tutor-course-card {
    padding: 30px;
    border-radius: var(--radius);
    box-shadow: var(--shadow-1);
    background: var(--color-white);
    position: relative;
    height: 100%;
}

.active-dark-mode .tutor-dashboard .tutor-dashboard-my-courses .tutor-course-card:hover .tutor-iconic-btn.tutor-my-course-edit i,
.active-dark-mode .tutor-form-label,
.active-dark-mode .tutor-form-control span.tutor-form-select-label,
.active-dark-mode .tutor-assignment-review-header > div,
.active-dark-mode .assignment-info > div {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-form-control.tutor-form-select.tutor-js-form-select,
.active-dark-mode .current-withdraw-account-wrap.tutor-d-flex.tutor-mt-20,
.active-dark-mode .tutor-dashboard-announcement-sorting-input label {
    color: var(--color-white);
}

.active-dark-mode .bootstrap-select>.dropdown-toggle,
.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs-container,
.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs .tutor-nav-link.is-active {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
}

.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs .tutor-color-secondary {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs-container .tutor-nav-tabs {
    background-color: var(--color-darker);
    color: var(--color-white-dark);
}

.active-dark-mode .dropdown.bootstrap-select.tutor-announcement-order-sorting.tutor-form-control.dropup {
    border: 2px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs-container .tutor-nav-tabs .tutor-nav-link {
    border-right: 2px solid var(--dark-color-border-2)!important;
    border-bottom: 2px solid var(--dark-color-border-2)!important;
}
.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs-container .tutor-nav-tabs .tutor-nav-link.is-active { 
    border-bottom: none!important;
}

.active-dark-mode .tutor-analytics-widget-body .tutor-color-secondary span {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-analytics-graph .tutor-nav-tabs-container {
    border: 1px solid var(--dark-color-border-2)!important;
}


.active-dark-mode .tutor-table-responsive,
.active-dark-mode .select2-container--default .select2-results>.select2-results__options {
    scrollbar-color: rgba(from var(--color-white) r g b / 0.4) var(--color-body); 
}

.active-dark-mode .tutor-table-responsive::-webkit-scrollbar-track {
    background: #192335 
}

.active-dark-mode .tutor-table-responsive::-webkit-scrollbar-thumb {
    background-color: var(--color-bodyest); 
}


.active-dark-mode .tutor-announcement-order-sorting .btn.dropdown-toggle,
.active-dark-mode .tutor-announcement-order-sorting {
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-announcement-order-sorting.bootstrap-select .dropdown-menu {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
}

.active-dark-mode .tutor-announcement-order-sorting.bootstrap-select .dropdown-menu .dropdown-item:hover {
    background-color: var(--black-opacity);
    color: var(--color-primary);
}

.active-dark-mode .tutor-analytics-info-cards .tutor-card .tutor-fw-bold,
.active-dark-mode .tutor-analytics-info-cards .tutor-card .tutor-color-secondary {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-table-responsive tbody .tutor-meta span,
.active-dark-mode .tutor-table-responsive tbody .tutor-color-muted,
.active-dark-mode .tutor-table-responsive tbody .tutor-td-top .tutor-ml-16 > div,
.active-dark-mode .tutor-table-responsive tbody .tutor-td-top > span,
.active-dark-mode .tutor-table-responsive tbody .tutor-color-secondary,
.active-dark-mode .analytics-course-details .course-summary .label-value {
    color: var(--color-white-off)!important;
}

.active-dark-mode .tutor-course-builder-section.tutor-course-builder-info .tutor-color-secondary {
    color: var(--color-white);
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-course-builder-section .tutor-course-builder-section-title {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.active-dark-mode #tutor-metabox-course-settings-tabs .course-settings-tabs-container .settings-tabs-navs-wrap,
body.tutor-screen-course-builder.active-dark-mode .tutor-course-available-instructors .added-instructor-item {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
    color: var(--color-white-off);
}

body.tutor-screen-course-builder.active-dark-mode .settings-tabs-navs-wrap li a,
body.tutor-screen-course-builder.active-dark-mode .settings-tabs-navs-wrap li a i {
    color: var(--color-white)!important;
}

body.tutor-screen-course-builder.active-dark-mode #tutor-metabox-course-settings-tabs .course-settings-tabs-container .settings-tabs-container,
body.tutor-screen-course-builder.active-dark-mode #tutor-metabox-course-settings-tabs .course-settings-tabs-container .settings-tabs-navs-wrap ul.settings-tabs-navs li.active a ,
body.tutor-screen-course-builder.active-dark-mode #wpcontent .select2-container li.select2-selection__choice, body.tutor-screen-course-builder #tutor-frontend-course-builder .select2-container li.select2-selection__choice,
body.tutor-screen-course-builder.active-dark-mode .html5-video-data .tutor-card {
    background: #192335;
    color: var(--color-white-dark);
}

body.tutor-screen-course-builder.active-dark-mode #tutor-metabox-course-settings-tabs ,
.active-dark-mode .tutor-thumbnail-uploader .thumbnail-wrapper:not(.tutor-is-borderless) {
    border: 1px solid var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.active-dark-mode #tutor-metabox-course-settings-tabs .course-settings-tabs-container .settings-tabs-navs-wrap ul.settings-tabs-navs li.active a {
    border-bottom-color: var(--dark-color-border-2)!important;
    border-top-color: var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-course-builder-upload-tips ul li,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .course-settings-tabs-container .tutor-color-muted,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode #tutor-frontend-course-builder p,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-course-builder-section-content .tutor-color-muted,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .video-metabox-source-html5-upload .tutor-color-black,
body.tutor-screen-course-builder.active-dark-mode .select2-container .select2-search__field,
.active-dark-mode .tutor-lesson-wrapper,
.active-dark-mode .tutor-lesson-wrapper ul li,
.active-dark-mode .tutor-lesson-wrapper ol li {
    color: var(--color-white-off);
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-form-toggle input~.tutor-form-toggle-control,
body.tutor-screen-course-builder.active-dark-mode .tutor-video-upload-wrap .video_source_wrap_html5,
body.tutor-screen-course-builder.active-dark-mode .tutor-video-upload-wrap .video_source_wrap_html5 .video-metabox-source-html5-upload,
body.tutor-screen-course-builder.active-dark-mode .tutor-modal-header, 
body.tutor-screen-course-builder.active-dark-mode .tutor-modal-footer {
    background-color: var(--color-bodyest);
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-form-toggle input:checked~.tutor-form-toggle-control {
    background-color: var(--tutor-color-primary);
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-color-secondary,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-modal-title {
    color: var(--color-white-dark);
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .instructor-intro > div,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-form-feedback > div ,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-form-feedback > i {
    color: var(--color-white-dark);
}

body.tutor-screen-course-builder.active-dark-mode .tutor-course-available-instructors .added-instructor-item {
    border: 1px solid var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.active-dark-mode .tutor-modal-header,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-dashboard-builder-header {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.active-dark-mode .tutor-modal-footer {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode  .select2-container--default .select2-selection--multiple,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-option-field textarea:focus, 
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-option-field input:not([type=submit]):focus, 
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-form-group textarea:focus, .tutor-form-group input:not([type=submit]):focus,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .select2-dropdown {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-off);
    border-color: var(--dark-color-border-2)!important;
}

.tutor-screen-course-builder-frontend.active-dark-mode .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--black-opacity);
    color: var(--color-primary);
}

.active-dark-mode .tutor-iconic-btn:hover, 
.active-dark-mode .tutor-iconic-btn:focus, 
.active-dark-mode .tutor-iconic-btn:active,
.active-dark-mode span.tutor-fs-6.tutor-fw-bold.tutor-color-secondary {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-certificate-tabs.tutor-nav,
.active-dark-mode .tutor-course-single-sidebar-wrapper,
.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar .tutor-course-single-sidebar-title,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body ,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a,
.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-quiz-sidebar .tutor-course-single-sidebar-title {
    background-color: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-quiz-sidebar .tutor-course-single-sidebar-title .tutor-color-secondary {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar .tutor-accordion-item-header,
.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar .tutor-course-single-sidebar-title,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header,
.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-quiz-sidebar .tutor-course-single-sidebar-title  {
    border-bottom: 1px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar {
    border-right: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar .tutor-color-secondary {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-course-topic-item-icon,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item .tutor-course-topic-item-title, 
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item .tutor-course-topic-item-icon, 
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item .tutor-course-topic-item-title,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item .tutor-course-topic-item-duration,
.active-dark-mode .tutor-accordion-item-header .tutor-course-topic-summary {
    color: var(--color-white-dark);
}
.active-dark-mode .tutor-course-topic-item-icon:hover,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover ,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-title, 
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-icon, 
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-title,
.active-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-duration,
.active-dark-mode .tutor-accordion-item-header.is-active .tutor-course-topic-summary,
.active-dark-mode .tutor-accordion-item-header.is-active .tutor-course-topic-title {
    color: var(--color-primary);
}

.active-dark-mode .tutor-course-certificate-tabs.tutor-nav>li>a.is-active,
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend.active-dark-mode .tutor-dashboard-builder-header {
    background: #192335;
}

.active-dark-mode .tutor-certificate-template .tutor-certificate-template-inner a,
.active-dark-mode .tutor-certificate-template-overlay .tutor-btn-primary:hover {
    color: var(--color-primary);
}

.active-dark-mode .tutor-dashboard-builder-logo img {
    filter: invert(1) brightness(2) contrast(1.5) grayscale(100%);
}

.active-dark-mode .histudy-course-profile-card-archive .rbt-card .rbt-price .current-price {
    color: var(--color-white);
}

.active-dark-mode .histudy-course-profile-card-archive .rbt-card .rbt-price .off-price {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode .rbt-modern-select .dropdown-menu {
    scrollbar-color: rgba(from var(--color-white) r g b / 0.4) var(--color-body); 
}

.active-dark-mode .tutor-course-spotlight-wrapper .tutor-conversation {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-lesson-content-drip-wrap .tutor-alert {
    background-color: var(--color-bodyest);
    color: var(--color-white-dark);
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-quiz-attempt-details tr th {
    background: #2f3e57;
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-quiz-attempt-details tr td {
    background: var(--color-bodyest);
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .quiz-meta-info {
    box-shadow: unset;
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .tutor-fs-7,
.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .header-title  {
    color: var(--color-white);
}

.active-dark-mode #tutor-quiz-image-matching-choice .tutor-border-top {
    border-top: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode #tutor-quiz-image-matching-choice .tutor-border-bottom {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-table-quiz-attempts tr td {
    background: transparent!important;
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .tutor-btn.tutor-btn-outline-primary {
    color: var(--color-primary);
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .tutor-btn.tutor-btn-outline-primary,
.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-start-quiz-wrapper {
    border: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-course-single-sidebar-wrapper {
    border-right: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-wrap .tutor-btn.tutor-btn-outline-primary:hover,
.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-start-quiz-wrapper .tutor-start-quiz-title .tutor-fs-6 {
    color: var(--color-white);
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-start-quiz-wrapper .tutor-start-quiz-title .tutor-fs-4 {
    color: var(--color-white);
}

.active-dark-mode nav.woocommerce-MyAccount-navigation ul,
.active-dark-mode.woocommerce-account .woocommerce-MyAccount-content,
.active-dark-mode .select2-container--open .select2-dropdown {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-dark);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode nav.woocommerce-MyAccount-navigation ul li a,
.active-dark-mode .woocommerce form .form-row input.input-text, 
.active-dark-mode .woocommerce form .form-row textarea,
.active-dark-mode .woocommerce-MyAccount-content fieldset,
.active-dark-mode .select2-container--default .select2-selection--single,
.active-dark-mode .select2-container--open .select2-dropdown,
.active-dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--dark-color-border)!important;
}

.active-dark-mode .woocommerce-error, 
.active-dark-mode .woocommerce-info, 
.active-dark-mode .woocommerce-message {
    background: #2f3e57;
    color: var(--color-white-dark);
}

.active-dark-mode .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--color-white);
    opacity: .4;
}

.active-dark-mode .tutor-border-top-light {
    border-top: 1px solid rgba(from var(--color-white) r g b / 0.4) !important;
}

.active-dark-mode .tutor-modal-body .tutor-color-secondary,
.active-dark-mode .tutor-modal-body .tutor-color-black ,
.active-dark-mode .rbt-cart-side-menu .rbt-cart-subttotal .subtotal{
    color: var(--color-white-dark);
}

.active-dark-mode .wc-block-components-radio-control--highlight-checked .wc-block-components-radio-control-accordion-option--checked-option-highlighted, 
.active-dark-mode .wc-block-components-radio-control--highlight-checked label.wc-block-components-radio-control__option--checked-option-highlighted {
    box-shadow: inset 0 0 0 2px var(--dark-color-border);
}

.active-dark-mode .comment-list .comment .reply-edit a.comment-edit-link, 
.active-dark-mode .comment-list .pingback .reply-edit a.comment-edit-link, 
.active-dark-mode .comment-list .trackback .reply-edit a.comment-edit-link {
    color: var(--color-white-dark);
}

.active-dark-mode .comment-list .comment .reply-edit .reply a.comment-reply-link:hover, 
.active-dark-mode .comment-list .pingback .reply-edit .reply a.comment-reply-link:hover, 
.active-dark-mode .comment-list .trackback .reply-edit .reply a.comment-reply-link:hover {
    color: var(--color-primary);
}

.active-dark-mode .signature-image img,
.active-dark-mode .service-card-6 .inner .icon img,
.active-dark-mode .rbt-minicart-wrapper .rbt-no-cart-item-exits img {
    filter: invert(1) brightness(2) contrast(1.5) grayscale(100%);
    opacity: 0.7;
}

.active-dark-mode .tutor-dashboard-menu-divider {
    background-color: var(--color-white);
    opacity: 0.1;
}

.active-dark-mode .rbt-user-wrapper .rbt-user-menu-list-wrapper .user-list-wrapper li a {
    color: var(--color-white-dark);
    opacity: 0.9;
}

ul.quick-access .access-icon.shopping-cart a.rbt-cart-sidenav-activation.rbt-cart-sidenav-activation i {
    margin-right: 2px;
}

.active-dark-mode .rbt-separator::after {
    background: var(--color-white);
    opacity: 0.1;
}

.active-dark-mode .footer-widget li a:hover i,
.active-dark-mode .footer-widget li:hover i {
    color: var(--color-white);
}

.active-dark-mode .rbt-service.rbt-service-2.rbt-hover-02.bg-no-shadow .inner .content .transparent-button {
    color: var(--color-bodyest)!important;
}

.rbt-user-menu-list-wrapper .user-list-wrapper li:not(.tutor-dashboard-menu-divider) {
    text-align: left;
}

span.wpcf7-not-valid-tip {
    margin-top: 20px;
    padding-bottom: 10px;
}

.active-dark-mode .rbt-scroll-max-height.rbt-scroll {
    scrollbar-color: rgba(from var(--color-white) r g b / 0.4) var(--color-body); 
}

.active-dark-mode .rbt-modern-select .filter-option-inner .filter-option-inner-inner,
.active-dark-mode .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
    color: var(--color-white-off);
}

.woocommerce-password-strength.bad,
.active-dark-mode .woocommerce-password-strength.short {
    margin-top: 10px;
}

.active-dark-mode .woocommerce-password-strength.bad,
.active-dark-mode .woocommerce-password-strength.short {
    background: var(--color-darker);
    color: var(--color-white-dark);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .post-like.pt-like-it.rainbow-blog-details-like .like-button i,
.active-dark-mode section.no-results.not-found.rainbow-search-no-result-found ,
.active-dark-mode section.no-results.not-found.rainbow-search-no-result-found .histudy-search.form-group {
    border: 2px solid var(--dark-color-border)!important;
}

.active-dark-mode.single-course-bundle .tutor-course-details-widget .inner-title,
.active-dark-mode.single-course-bundle .tutor-bundle-author-list .tutor-fs-5, 
.active-dark-mode.single-course-bundle .tutor-course-details-widget-title, 
.active-dark-mode.single-course-bundle .tutor-courses-instructors .tutor-form-label {
    border-bottom: 2px solid var(--dark-color-border)!important;
}

.active-dark-mode section.no-results.not-found.rainbow-search-no-result-found .histudy-search.form-group button.search-button {
    background: transparent;
}

.active-dark-mode .rbt-overlay-page-wrapper .breadcrumb-image-container .breadcrumb-image-wrapper img {
    display: none;
}

.active-dark-mode.single-course-bundle .rbt-breadcrumb-default .breadcrumb-inner img {
    display: none;
}

.active-dark-mode .course-bundle-breadcrumb .tutor-course-wishlist-btn i, 
.active-dark-mode .course-bundle-breadcrumb .tutor-course-wishlist-btn, 
.active-dark-mode .course-bundle-breadcrumb .tutor-btn, 
.active-dark-mode .course-bundle-breadcrumb .tutor-btn i,
.active-dark-mode .course-bundle-breadcrumb-meta,
.active-dark-mode .course-bundle-breadcrumb-meta a {
    color: var(--color-white-off);
}

.active-dark-mode .course-bundle-breadcrumb-meta a:hover {
    color: var(--color-primary);
}

.active-dark-mode .tutor-bundle-courses-wrapper .rbt-card-bottom .current-price {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-bundle-courses-wrapper .rbt-price .off-price  {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode.single-course-bundle  .tutor-sidebar-card,
.active-dark-mode.single-course-bundle .tutor-single-course-sidebar-more > div:first-child {
    background-color: transparent;
    color: var(--color-white-dark);
}

.active-dark-mode.single-course-bundle  .tutor-sidebar-card .tutor-color-black {
    color: var(--color-white-dark);
}

.active-dark-mode.single-course-bundle .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a {
    background: var(--color-bodyest);
    color: var(--color-white-dark);
    box-shadow: none;
}

.active-dark-mode.single-course-bundle .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a:hover {
    background: var(--color-primary);
    color: var(--color-white-dark);
}

.active-dark-mode.single-course-bundle .tutor-card-footer:not(.tutor-no-border) {
    border-top: 1px solid var(--dark-color-border)!important;
}

.active-dark-mode .tutor-border-top-light {
    border-top: 1px solid var(--dark-color-border)!important;
}

.active-dark-mode #tutor-login-form .tutor-form-check label,
.active-dark-mode #tutor-login-form .tutor-btn-ghost {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .woocommerce-Reviews .form-control {
    background-color: transparent;
    border: none!important;
}

.active-dark-mode .woocommerce-Reviews .form-group textarea,
.active-dark-mode .woocommerce-Reviews .form-control {
    border-bottom: 2px solid var(--dark-color-border)!important;
}

.active-dark-mode .bootstrap-select > .dropdown-toggle {
    background-color: var(--color-bodyest)!important;
    border-color: var(--dark-color-border);
}

.active-dark-mode .bootstrap-select > .dropdown-toggle:hover,
.active-dark-mode .bootstrap-select > .dropdown-toggle:focus {
    border-color: var(--dark-color-border);
}

.active-dark-mode .my_switcher ul li .setColor.dark {
    display: none;
}

.active-light-mode .my_switcher ul li .setColor.light {
    display: none;
}

.active-dark-mode .why-choose-histudy-darkmode .rbt-counterup-area {
	padding-top: 120px;
}

.active-dark-mode .tutor-progress-bar {
    background: var(--dark-color-border-2);
}

.active-dark-mode .tutor-no-announcements .tutor-color-secondary {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-attachment.tutor-card.tutor-card-sm {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-off);
}

.active-dark-mode div#tutor-course-details-tab-announcements {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-attachment .tutor-fs-6.tutor-fw-medium.tutor-color-black.tutor-text-ellipsis.tutor-mb-4 {
    color: var(--color-white-off);
}

.active-dark-mode .privacy-darkmode-banner {
	background-image: none!important;
	background: var(--gradient-dark-2) !important;
}

.active-dark-mode div#tutor-course-details-tab-announcements,
.active-dark-mode .privacy-policy-topbanner {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-off);
	box-shadow:none;
}

.active-dark-mode .bg-color-extra2.card-style-six-custom .bg-color-white {
    background: #333d51 !important;
}

.active-dark-mode .testimonial-one-dark {
    background: var(--color-darker) !important;
}


.active-dark-mode .testimonial-two-dark {
    background: #333d51 !important;
}

.active-dark-mode .inner-darkpage-cat {
    background: #333d51!important;
}

.active-dark-mode .demo-rtl > button.rtl {
    background: var(--color-darker);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--dark-color-border);
    color: var(--color-white-dark);
}


.active-dark-mode .demo-ltr > button.rtl {
    background: var(--color-darker);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--dark-color-border);
    color: var(--color-white-dark);
}

active-dark-mode .why-choose-histudy-darkmode .rbt-counterup-area {
	padding-top: 120px;
}

.active-dark-mode .tutor-progress-bar {
    background: var(--dark-color-border-2);
}

.active-dark-mode .tutor-no-announcements .tutor-color-secondary {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-attachment.tutor-card.tutor-card-sm {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-attachment .tutor-fs-6.tutor-fw-medium.tutor-color-black.tutor-text-ellipsis.tutor-mb-4 {
    color: var(--color-white-off);
}

.active-dark-mode div#tutor-course-details-tab-announcements,
.active-dark-mode .privacy-policy-topbanner {
    background-color: var(--color-bodyest)!important;
    color: var(--color-white-off);
	box-shadow:none;
}

.active-dark-mode .privacy-darkmode-banner {
	background-image: none!important;
	background: var(--gradient-dark-2) !important;
}


.active-dark-mode .bg-color-extra2.card-style-six-custom .bg-color-white {
    background: #333d51 !important;
}

.active-dark-mode .testimonial-one-dark {
    background: var(--color-darker) !important;
}

.active-dark-mode .testimonial-two-dark {
    background: #333d51 !important;
}

.active-dark-mode .inner-darkpage-cat {
    background: #333d51!important;
	padding-top: 120px;
}

body.blog footer.rbt-footer.footer-style-1 > .footer-top > .container {
    border-top: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .rbt-course-menu-fixed-pos-bottom,
.active-dark-mode .popup-mobile-menu .inner-wrapper {
    background-color: var(--color-bodyest);
    box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
}

.active-dark-mode .rbt-course-menu-fixed-pos-bottom li span,
.active-dark-mode .rbt-course-menu-fixed-pos-bottom li i,
.active-dark-mode .popup-mobile-menu .inner-wrapper,
.active-dark-mode .popup-mobile-menu .inner-wrapper .menu-item  a ,
.active-dark-mode .popup-mobile-menu .mainmenu li.with-megamenu a::after {
    color: var(--color-white-dark);
}

.active-dark-mode .popup-mobile-menu .mainmenu li + li {
    border-top: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .popup-mobile-menu .mainmenu .mega-menu-item li:last-child,
.active-dark-mode .popup-mobile-menu  .elementor-icon-list-items {
    border-bottom: 1px solid var(--dark-color-border-2)!important;
}

.active-dark-mode .popup-mobile-menu .inner-wrapper .inner-top .close-button {
    background: transparent;
}

.active-dark-mode .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li:hover a:after {
    background: var(--color-primary);
}

.active-dark-mode .rbt-tutor-course-details-widebar-widget-load-more > *:not(:last-child) {
    border-bottom: 1px solid rgba(from var(--color-white) r g b / 0.3);
}


@media only screen and (max-width: 767px) {
    .my_switcher {
        left: 4px;
        z-index: 99999;
        bottom: 100px;
    }
}

@media (max-width: 1199px) {
    .active-dark-mode .popup-mobile-menu .rbt-mega-menu-home-demos {
        background: var(--color-bodyest) !important;
        box-shadow: unset!important;
    }

    .active-dark-mode .popup-mobile-menu .rbt-megamenu .elementor-element {
        box-shadow: none;
    }
}


.active-dark-mode .histudy-bg-common-dark {
    background-color: var(--color-darker) !important;
}

.active-dark-mode .histudy-bg-gray-dark {
    background-color: #333d51 !important;
}

.active-dark-mode .popup-mobile-menu .mainmenu li.has-dropdown > a::after,
.active-dark-mode .popup-mobile-menu .mainmenu li.has-dropdown > a::after {
    color: var(--color-white-dark);
}

.active-dark-mode .rbt-banner-area.rbt-banner-2.bg-image-transparent .single-slide .rbt-service .rbt-btn-link {
    color: var(--color-heading)!important;
}

.active-dark-mode .course-filter-modal-content {
    background-color: var(--color-bodyest);
    box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
}

.active-dark-mode .rbt-course-filter-modal .dropdown.bootstrap-select.form-select ,
.active-dark-mode .rbt-course-filter-modal .bootstrap-select > .dropdown-toggle,
.active-dark-mode .rbt-course-filter-modal button.rbt-filter-rating-toggle {
    background: #192335 !important;
    box-shadow: unset!important;
}

.active-dark-mode .course-filter-modal-content .close-button {
    background: #192335 !important;
}

.active-dark-mode .rbt-single-rating-widget-padding.rbt-widget-rating {
    background-color: var(--color-bodyest);
    box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
}

.active-dark-mode .rbt-banner-content-top.rbt-breadcrumb-style-3 .tutor-ratings .tutor-ratings-average,
.active-dark-mode .rbt-banner-content-top.rbt-breadcrumb-style-3 .tutor-ratings .tutor-ratings-count {
    color: var(--color-white-dark);
}

.active-dark-mode .hamberger .hamberger-button {
    color: var(--color-white-dark);
}

/*learnpress dark mode css*/

.active-dark-mode .rbt-breadcrumb-style-3 .rbt-price .current-price {
    color: var(--color-white-dark) !important;
}

.active-dark-mode .course-sidebar .content-item-content .current-price {
    color: var(--color-white-dark) !important;
}

.active-dark-mode .lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row strong, 
.active-dark-mode .lp-archive-courses .course-summary-content .histudy-course-time-single-page .course-time-row .entry-date.expire {
    color: var(--color-white-off) !important;
}

.active-dark-mode .rbt-widget-details .rbt-course-details-list-wrapper li span.rbt-feature-value {
    color: var(--color-white) !important;
}

.active-dark-mode .rbt-bookmark-btn .rbt-round-btn {
    color: var(--color-white-dark);
}

.active-dark-mode.single-lp_course .lp-entry-content.lp-content-area .course-curriculum .section-left .section-title { 
    color: var(--color-primary)!important;
}

.active-dark-mode.single-lp_course .lp-entry-content.lp-content-area .section.closed .course-curriculum .section-left .section-title { 
    color: var(--color-white)!important;
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist::before {
    color: var(--color-white-dark);
}

.active-dark-mode.single-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::after, 
.active-dark-mode.post-type-archive-lp_course .rbt-bookmark-btn button.lp-btn-wishlist::after {
    background: var(--color-darker);
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist:hover {
    background: transparent!important;
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist:hover {
    border: none!important;
}

.active-dark-mode .course-curriculum .section-header,
.active-dark-mode.single-lp_course .course-curriculum .section-header:last-child, 
.active-dark-mode div#tab-curriculum ul.curriculum-sections li:last-child .section-header {
    border-bottom: 1px solid var(--dark-color-border)!important;
}

.active-dark-mode.single-lp_course .lp-entry-content.lp-content-area .course-curriculum .section.closed .section-left .section-title,
.active-dark-mode.single-lp_course .course-curriculum .section.closed .section-title, 
.active-dark-mode.single-lp_course .course-curriculum .section.closed .section-toggle i {
    color: var(--color-white)!important;
}

.active-dark-mode .lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link .course-item-info span, 
.active-dark-mode .lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link::before,
.active-dark-mode .course-curriculum .section-content .course-item-meta .item-meta {
    color: var(--color-white-dark)!important;
}



.active-dark-mode #popup-sidebar .section-header {
    background-color: transparent;
}

.active-dark-mode.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a, 
.active-dark-mode.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a i {
    color: var(--color-white-dark)!important;
}

.active-dark-mode.single-lp_course .course-tab-panel-instructor.course-tab-panel .social-icon.social-default li a:hover i {
    color: var(--color-primary)!important;
}

.active-dark-mode .course-tab-panel-faqs .course-faqs-box,
.active-dark-mode.single-lp_course .course-extra-box {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode.single-lp_course .course-tab-panel-faqs .course-faqs-box .course-faqs-box__content-inner {
    border-top: 2px solid var(--dark-color-border);
}

.active-dark-mode.single-lp_course .course-extra-box__content li {
    border-bottom: 1px solid var(--dark-color-border);
    color: var(--color-white-off)!important;
}

.active-dark-mode.single-lp_course .course-extra-box__content li:first-child {
    border-top: 1px solid var(--dark-color-border);
}

.active-dark-mode.single-lp_course .course-summary-content .course-tab-panel-faqs .course-faqs-box .course-faqs-box__content-inner {
    color: var(--color-white-off)!important;
}

.active-dark-mode.single-lp_course .course-tab-panel-reviews.course-tab-panel .lp-rating-reviews .course-rate,
.active-dark-mode .learnpress-course-review .review-form,
.active-dark-mode .course-tab-panel-reviews.course-tab-panel #course-reviews {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest);
}

.active-dark-mode .learnpress-course-review .review-form {
    border: 1px solid var(--dark-color-border);
}

.active-dark-mode .course-rate .course-rate__summary-value {
    color: var(--color-white-dark);
}

.active-dark-mode .course-rate .course-rate__details-row .course-rate__details-row-value .rating-gray {
    background: var(--color-bodyest-2);
}

.active-dark-mode.learnpress-page input[type=text], 
.active-dark-mode.learnpress-page input[type=email], 
.active-dark-mode.learnpress-page input[type=number], 
.active-dark-mode.learnpress-page input[type=password],
.active-dark-mode.learnpress-page textarea {
    border-bottom: 2px solid var(--dark-color-border);;
}



.active-dark-mode .review-form .review-fields > li > label,
.active-dark-mode .learnpress-course-review .course-reviews-list li .review-title,
.active-dark-mode .lp-archive-courses .learnpress-course-review .course-reviews-list .review-text .review-content {
    color: var(--color-white-off)!important;
}

.active-dark-mode .course-tab-panel-reviews.course-tab-panel .course-review-head {
    border-bottom: 2px solid var(--dark-color-border);
}

.active-dark-mode .rbt-card .rbt-card-body .rbt-card-bottom .course_details:hover,
.active-dark-mode .course-curriculum .course-item.item-locked .course-item-status::before,
.active-dark-mode .course-sidebar .course-results-progress .number {
    color: var(--color-white-off)!important;
}

.lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link:hover .course-item-info span, 
.lp-entry-content.lp-content-area .course-curriculum .course-item .section-item-link:hover::before, 
 .course-curriculum .section-content .section-item-link:hover .course-item-meta .item-meta,
.course-curriculum .course-item.item-locked .section-item-link:hover .course-item-status::before {
    color: var(--color-primary)!important;
}

.active-dark-mode .items-progress__heading {
    color: var(--color-white-off)!important;
}

.active-dark-mode .learn-press-progress .progress-bg {
    background: var(--color-bodyest-2);
    border-radius: 0;
}

.active-dark-mode #popup-sidebar .search-course,
.active-dark-mode.course-item-lp_lesson .course-curriculum.learnpress-course-curriculum {
    background-color: var(--color-bodyest);
    border-right: 1px solid var(--dark-color-border-2);
    margin-top: 0!important;
    border-radius: 0;
}

.active-dark-mode #popup-sidebar .search-course input[name=s] {
    color: var(--color-white-off);
}

.active-dark-mode #popup-sidebar .search-course button::before {
    color: var(--color-white-dark) !important;
}

.lp-archive-courses .search-course input[type="text"] {
    padding-top: 5px;
}

.active-dark-mode .lp-archive-courses .search-course input[type="text"]::placeholder {
    color: var(--color-white-dark) !important;
}

.active-dark-mode .learnpress-course-curriculum .course-curriculum .course-item .section-item-link .course-item-info span, 
.active-dark-mode .learnpress-course-curriculum .course-curriculum .course-item .section-item-link::before, 
.active-dark-mode .course-curriculum .section-content .course-item-meta .item-meta {
    color: var(--color-white-dark) !important;
    transition: all 0.3s ease-in-out;
}
.active-dark-mode .learnpress-course-curriculum .course-curriculum .course-item .section-item-link:hover .course-item-info span {
    color: var(--color-primary) !important;
}

.course-curriculum .course-item.has-status.status-completed .course-item-status::before, 
.course-curriculum .course-item.has-status.status-evaluated .course-item-status::before {
    font-size: 13px;
    width: 18px;
    height: 18px;
    background: var(--color-primary)!important;
    border-radius: 100%;
    color: var(--color-white)!important;
    display: inline-block;
    text-align: center;
    line-height: 18px;
}

.logged-in .course-curriculum .course-item-meta .course-item-status::before {
    font-family: 'feather' !important;
    content: "\e937"!important;
    background: transparent;
    color: var(--color-primary-opacity);
    font-size: 16px;
    width: auto;
    height: auto;
}

.active-dark-mode.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-value .rating-count,
.active-dark-mode.single-lp_course .course-rate .course-rate__details-row .course-rate__details-row-star {
    color: var(--color-white-off)!important;
}

.active-dark-mode.single-lp_course .course-curriculum.learnpress-course-curriculum {
    /* Add scrollbar styling */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: var(--color-bodyest) var(--color-darker); /* Track color and thumb color for Firefox */
}

.active-dark-mode.single-lp_course .course-curriculum.learnpress-course-curriculum::-webkit-scrollbar-track {
    background: var(--color-bodyest);
}

.active-dark-mode.single-lp_course .course-curriculum.learnpress-course-curriculum::-webkit-scrollbar-thumb {
    background-color: var(--color-bodyest); 
    border-radius: 10px; 
    border: 2px solid var(--color-bodyest); 
}

.active-dark-mode.single-lp_course .course-curriculum.learnpress-course-curriculum::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-bodyest); 
}

.active-dark-mode .lp-archive-courses #popup-content {
    background: var(--color-darker) !important;;
    color: var(--color-white-dark);
}

.active-dark-mode.single-lp_course .lp-archive-courses .course-summary,
.active-dark-mode.single-lp_course .lp-archive-courses #popup-footer {
    background: #333d51 !important;
}

.active-dark-mode.single-lp_course .lp-archive-courses .course-item-nav a,
.active-dark-mode #popup-footer .course-item-nav .prev::before, 
.active-dark-mode #popup-footer .course-item-nav .next::before {
    color: var(--color-white-off)!important;
}

.active-dark-mode #popup-footer {
    border-top: 4px solid var(--color-primary);
}

.active-dark-mode.course-item-lp_lesson .content-item-wrap .content-item-description p {
    color: var(--color-white-off);
}

.active-dark-mode .quiz-result .result-grade .result-achieved,
.active-dark-mode .quiz-result .result-grade .result-require {
    color: var(--color-white-dark);
}

.active-dark-mode .quiz-result .result-statistic .result-statistic-field span,
.active-dark-mode .quiz-result .result-statistic .result-statistic-field p {
    color: var(--color-white-off);
}

.active-dark-mode #popup-header {
    border-bottom: 1px solid var(--dark-color-border);
}

.active-dark-mode #popup-sidebar .section-header {
    background: var(--color-bodyest);
}


.active-dark-mode .course-curriculum .course-item .section-item-link:hover::before {
    color: var(--color-primary)!important;
}



.active-dark-mode .course-rate .course-rate__summary-text {
    color: var(--color-white-off);
}

.active-dark-mode.single-lp_course .histudy-breadcrumb-item .bread-current.bread-archive {
    color: var(--color-white-dark);
    opacity: 0.6;
}

.active-dark-mode .rbt-course-details-area,
.active-dark-mode.single-lp_course .lp-archive-courses #learn-press-course {
    background: var(--color-darker)!important;
}

.active-dark-mode .lp-checkout-form .lp-form-fields input:not([type=checkbox]),
.active-dark-mode #checkout-order .lp-checkout-order__inner, 
.active-dark-mode .order-comments {
    border: 2px solid var(--dark-color-border)!important;
}

.active-dark-mode .lp-checkout-form .lp-form-fields input:not([type=checkbox])::placeholder {
    color: var(--color-white-off);
}

.active-dark-mode .lp-form-fields li {
    color: var(--color-white-off);
}

.active-dark-mode .lp-checkout-form .lp-checkout-remember label,
.active-dark-mode .lp-checkout-form .lp-checkout-remember a,
.active-dark-mode #checkout-account-register .lp-checkout-sign-in-link,
.active-dark-mode #checkout-account-register .lp-checkout-sign-up-link, 
.active-dark-mode #checkout-account-login .lp-checkout-sign-in-link, 
.active-dark-mode #checkout-account-login .lp-checkout-sign-up-link,
.active-dark-mode .lp-terms-and-conditions {
    color: var(--color-white-off);
}

.active-dark-mode .lp-terms-and-conditions a {
    text-decoration: underline;
    color: var(--color-white-off);
}

.active-dark-mode .lp-terms-and-conditions a:hover { 
    color: var(--color-primary);
}

.active-dark-mode .lp-checkout-form .lp-checkout-remember a:hover {
    color: var(--color-primary);
}

.active-dark-mode #checkout-order td, 
.active-dark-mode #checkout-order th {
    border: 1px solid var(--dark-color-border);
    border-top: 0;
    border-right: 0;
    border-left: 0;
}

.active-dark-mode #checkout-order .course-name a,
.active-dark-mode #checkout-order tfoot .order-total th {
    color: var(--color-white)!important;
}

.active-dark-mode #checkout-order .course-name a:hover {
    color: var(--color-primary)!important;
}

.active-dark-mode #checkout-order .col-number {
    color: var(--color-white-off)!important;
}

.active-dark-mode.learnpress-checkout .learnpress .learn-press-message {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-2);
    color: var(--color-white-off);
}

.active-dark-mode .learn-press-pagination .page-numbers, 
.active-dark-mode .learn-press-pagination .page-numbers.current {
    box-shadow: var(--dark-shadow-3);
    background: var(--color-bodyest);
}

.active-dark-mode .learn-press-pagination .page-numbers > li .page-numbers {
    color: var(--color-white);
}

.active-dark-mode .lp-single-instructor .ul-instructor-courses .price-categories .course-item-price .free,
.active-dark-mode .lp-single-instructor .ul-instructor-courses .price-categories .course-categories {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .lp-single-instructor .ul-instructor-courses h2 {
    border-bottom: 1px solid rgba(from var(--color-white) r g b / 0.1)!important;
}

.active-dark-mode .lp-list-instructors .ul-list-instructors li.item-instructor .instructor-display-name {
    color: var(--color-white);
}

.active-dark-mode .lp-single-instructor .ul-instructor-courses .course-count div,
.active-dark-mode .lp-list-instructors .ul-list-instructors li.item-instructor .instructor-info .instructor-count-students {
    color: var(--color-white-off);
}

.active-dark-mode .lp-user-profile .lp-profile-content-area,
.active-dark-mode .lp-single-instructor__info,
.active-dark-mode .lp-single-instructor .ul-instructor-courses li,
.active-dark-mode .lp-list-instructors .ul-list-instructors li.item-instructor {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .lp-single-instructor__info__right .instructor-description,
.active-dark-mode .instructor-total-courses, 
.active-dark-mode .wrapper-instructor-total-students,
.active-dark-mode .lp-ico.lp-icon-courses {
    color: var(--color-white-dark);
}

.active-dark-mode .dashboard-statistic__row .statistic-box {
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .lp-profile-right .lp-profile-user-bio p {
    color: var(--color-white-off);
}

.active-dark-mode .profile-courses-list .rbt-price .current-price,
.active-dark-mode .profile-courses-list .rbt-price .off-price,
.active-dark-mode .learn-press-filters > li a,
.active-dark-mode .statistic-box .statistic-box__text,
.active-dark-mode .statistic-box:hover .statistic-box__text {
    color: var(--color-white);
}

.active-dark-mode .learn-press-filters > li::after {
    color: rgba(from var(--color-white) r g b / 0.5);
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist::after {
    background: var(--color-darker);
}

.active-dark-mode .dashboard-statistic__row .statistic-box {
    border: 2px dashed var(--dark-color-border);
}

.active-dark-mode .lp-user-profile #profile-sidebar,
.active-dark-mode .lp-user-profile #profile-sidebar::before {
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li {
    border-top: 1px solid var(--dark-color-border);
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a {
    color: var(--color-white-dark);
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li.active > a,
.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li:hover > a {
    color: var(--color-primary);
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li.has-child > a:after {
    opacity: 1;
}

.active-dark-mode .learn-press-profile-course__progress .lp_profile_course_progress__header th,
.active-dark-mode .learn-press-profile-course__progress .lp_profile_course_progress__item td {
    border: 0.5px solid var(--dark-color-border);
    color: var(--color-white-off);
}

.active-dark-mode .lp-list-table thead tr th {
    background: var(--primary-opacity); 
}

.active-dark-mode .lp-profile-content table.lp-list-table tr th, 
.active-dark-mode .lp-profile-content table.lp-list-table tr td {
    border-right: 1px solid var(--dark-color-border);
    border-bottom: 1px solid var(--dark-color-border);
} 

.active-dark-mode .lp-profile-content table.lp-list-table {
    border-top: 1px solid var(--dark-color-border);
    border-left: 1px solid var(--dark-color-border);
} 

.active-dark-mode .lp-list-table tbody, 
.active-dark-mode .lp-list-table tfoot {
    border: 1px solid var(--dark-color-border);
    border-top: 0;
}


.active-dark-mode .lp-list-table tbody tr td, 
.active-dark-mode .lp-list-table tbody tr th, 
.active-dark-mode .lp-list-table tfoot tr td, 
.active-dark-mode .lp-list-table tfoot tr th {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest) !important;
}

.active-dark-mode .lp-profile-content table.lp-list-table tr td a,
.active-dark-mode .lp-list-table tbody tr td, 
.active-dark-mode .lp-list-table tbody tr th,
.active-dark-mode .lp-list-table tfoot tr td, 
.active-dark-mode .lp-list-table tfoot tr th,
.active-dark-mode .lp-list-table tbody tr .column-status .lp-label, 
.active-dark-mode .lp-list-table tfoot tr .column-status .lp-label ,
.active-dark-mode .lp-list-table .label-completed .order-status-completed,
.active-dark-mode .recover-order__description,
.active-dark-mode div#profile-content-settings p.description {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist:hover::after {
    opacity: 1;
}

.active-dark-mode div.order-recover input[type=text] {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs li > ul {
    box-shadow: var(--dark-shadow);
    background: var(--color-bodyest) !important;
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li ul li a ,
.active-dark-mode span.order-status.order-status-completed,
.active-dark-mode span.lp-label.label-completed {
    border-bottom: 1px solid var(--dark-color-border);
    color: var(--color-white-off);
    font-size: 16px;
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li ul li a:hover {
    box-shadow: var(--dark-shadow);
    background: var(--color-bodyest) !important;
    color: var(--color-primary);
}

.active-dark-mode .lp-user-profile input[type=text], 
.active-dark-mode .lp-user-profile input[type=password], 
.active-dark-mode .lp-user-profile input[type=email], 
.active-dark-mode .lp-user-profile input[type=number], 
.active-dark-mode .lp-user-profile input[type=tel], 
.active-dark-mode .lp-user-profile textarea,
.active-dark-mode  .lp-checkout-form__after .acf-user-register-fields .acf-input input[type="text"],
.active-dark-mode  .lp-checkout-form__after .acf-user-register-fields .acf-input input[type="url"] {

    border-color: var(--dark-color-border);
    color: var(--color-white-dark)!important;
}

.active-dark-mode .lp-user-profile input[type=text]:focus, 
.active-dark-mode .lp-user-profile input[type=password]:focus, 
.active-dark-mode .lp-user-profile input[type=email]:focus,
.active-dark-mode .lp-user-profile input[type=number]:focus,
.active-dark-mode .lp-user-profile input[type=tel]:focus,
.active-dark-mode .lp-user-profile textarea:focus,
.active-dark-mode  .lp-checkout-form__after .acf-user-register-fields .acf-input input[type="text"]:focus,
.active-dark-mode  .lp-checkout-form__after .acf-user-register-fields .acf-input input[type="url"]:focus {

    border-color: var(--color-primary)!important;
}

.active-dark-mode .learn-press-form .form-fields .form-field > label,
.active-dark-mode.single-lp_course #learn-press-course .course-sidebar .rbt-badge-2  {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs li.active > ul .active,
.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs li.settings.has-child.active > ul,
.active-dark-mode .lp-user-profile #profile-nav .lp-profile-nav-tabs > li.active ul li a:hover  {
    background: transparent!important;
}

.active-dark-mode .lp-user-profile #profile-sidebar {
    position: relative;
}

.active-dark-mode .lp-user-profile #profile-sidebar::before {
    background: var(--color-darker);
    content: "";
    z-index: -1;
    top: 3px;
    left: 3px;
    position: absolute;
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    border-radius: var(--radius);
}

.active-dark-mode .learn-press-tabs .learn-press-tabs__checker:nth-child(1):checked ~ .learn-press-tabs__nav .learn-press-tabs__tab:nth-child(1) {
    box-shadow: var(--dark-shadow);
    background: var(--color-bodyest) !important;
    border-bottom: 0;
}

.active-dark-mode .learn-press-tabs .learn-press-tabs__tab {
    border-right: 1px solid var(--dark-color-border);
}

.active-dark-mode .lp-user-profile #profile-content-settings .learn-press-tabs__nav {
    border: 1px solid var(--dark-color-border);
}

.active-dark-mode .lp-user-profile .learn-press-tabs {
    background: var(--color-bodyest) !important;
}

.active-dark-mode .lp-user-profile #profile-content-settings .learn-press-tabs__nav {
    border: 1px solid var(--color-bodyest) !important;
}

.active-dark-mode .learn-press-tabs .learn-press-tabs__tab,
.active-dark-mode .learnpress_avatar__form__upload {
    background: var(--color-bodyest) !important;
}

.active-dark-mode .learnpress_avatar__form__upload { 
    border: 1px dashed var(--dark-color-border);
}


.active-dark-mode .learn-press-tabs .learn-press-tabs__tab > label a {
    color: var(--color-white);
    font-size: 18px;
    font-weight: 500;
}

.active-dark-mode .rbt-course-details-area .leave-comment-form .comment-list .single-comment {
    border: 1px solid var(--dark-color-border);
}

.active-dark-mode .lp-course-students-list-wrapper ul.lp-students-list-wrapper li {
    box-shadow: var(--dark-shadow-2);
    background: var(--color-bodyest);
    border: none;
}

.active-dark-mode.single-lp_course .rbt-instructor.rbt-shadow-box.intructor-wrapper .author-info .hover-flip-item-wrapper,
.active-dark-mode.single-lp_course #learn-press-course .rbt-course-details-area .leave-comment-form .comment-list .single-comment .commenter a,
.active-dark-mode.single-lp_course #learn-press-course .lp-students-list-wrapper .lp-student-enrolled .student-course-item .student-info a {
    color:var(--color-white)!important;
} 

.active-dark-mode.single-lp_course #learn-press-course .lp-students-list-wrapper .lp-student-enrolled .student-course-item .student-info p,
.active-dark-mode.single-lp_course .lp-course-students-list-wrapper .lp-load-ajax-element .lp-target p  {
    color: var(--color-white-off)!important;
}

.active-dark-mode .students-list-filter {
    background: var(--color-bodyest) url(../images/icons/arrow.png) no-repeat center right 20px;
    border:  2px solid var(--dark-color-border);
    color: var(--color-white-off);
}

.active-dark-mode .rbt-bookmark-btn button.lp-btn-wishlist:hover i  {
    color: var(--color-primary) !important;
}

.active-dark-mode .lp-checkout-form .lp-checkout-remember label input[type=checkbox] {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode  #learn-press-checkout .payment-methods .lp-payment-method.selected > label {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode  #checkout-payment .lp-payment-method .gateway-input {
    background: var(--color-bodyest);
    border: 1px solid var(--dark-color-border);
}

.active-dark-mode  #learn-press-checkout .payment-methods .payment-method-form {
    border-top: 2px solid var(--dark-color-border);
    background: var(--color-bodyest);
    color: var(--color-white-off);
}

.active-dark-mode .lp-content-area .order_details th, 
.active-dark-mode .lp-content-area .order_details td {
    border: 1px solid var(--dark-color-border);
} 

.active-dark-mode .rbt-generic-banner-course-filter-banner .rbt-lp-course-sidebar-filter .lp-form-course-filter {
    box-shadow: var(--dark-shadow-3);
    background: var(--color-darker);
}

.active-dark-mode .rbt-generic-banner-course-filter-banner .lp-form-course-filter__title {
   
    border-bottom: 2px solid var(--dark-color-border);
}

.active-dark-mode .rbt-generic-banner-course-filter-banner .lp-form-course-filter .lp-form-course-filter__content {
    background: transparent;
}

.active-dark-mode .rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter__field label {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter-search-field input {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode .rbt-generic-banner-course-filter-banner .lp-form-course-filter__content .lp-course-filter-search-field input:focus {
    border: 2px solid var(--color-primary);
}

.active-dark-mode.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .rbt-search-style input {
    border-bottom: 2px solid var(--color-white)!important;
}

.active-dark-mode.post-type-archive-lp_course .rbt-generic-banner-course-filter-banner .rbt-search-style input:focus {
    border-bottom: 2px solid var(--color-primary)!important;
}

.active-dark-mode.post-type-archive-lp_course nav.learn-press-breadcrumb li span {
    color: var(--color-white-off);
}

.active-dark-mode .lp-main-content .learn-press-courses[data-layout=grid] .course .rt-course-default, 
.active-dark-mode .lp-main-content .learn-press-courses[data-layout=list] .course .rt-course-default {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .rainbow-course-not-found-error {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-1);
}

.active-dark-mode .histudy-learnpress-sidebar-section .widget_course_filter {
    box-shadow: var(--dark-shadow-3);
    background: var(--color-darker);
}

.active-dark-mode  .histudy-learnpress-sidebar-section .widget_course_filter .lp-course-filter-search {
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .histudy-learnpress-sidebar-section .widget_course_filter .lp-form-course-filter__item,
.active-dark-mode .histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__item {
    background: var(--color-darker);
}

.active-dark-mode .histudy-learnpress-sidebar-section .lp-form-course-filter__item .lp-form-course-filter__title, 
.active-dark-mode .histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__title {
    color: var(--color-white);
}

.active-dark-mode .histudy-learnpress-sidebar-section .lp-form-course-filter__item .lp-form-course-filter__title, 
.active-dark-mode .histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__title,
.active-dark-mode .histudy-learnpress-sidebar-section .lp-course-filter-category .lp-form-course-filter__item:first-child .lp-form-course-filter__title {
    border-bottom: 2px solid var(--dark-color-border);
}

.active-dark-mode .histudy-learnpress-sidebar-section .widget_course_filter .lp-course-filter-search {
    border: 2px solid var(--dark-color-border);
    color: var(--color-white-dark);
}

.active-dark-mode .histudy-learnpress-sidebar-section .lp-form-course-filter__content .lp-course-filter__field .count {
    background-color: var(--color-body);
    color: var(--color-white-off);
}

.active-dark-mode .course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .section-content,
.active-dark-mode .course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .section-content .course-item  {
    background-color: #333D51;
}

.active-dark-mode .course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .course-item.course-item-lp_lesson {
    color: var(--color-white-dark)!important;
}


.active-dark-mode .course-curriculum .course-item .item-name, 
.active-dark-mode .course-curriculum .course-item .section-item-link .course-item-info .course-item-info-pre .item-meta.duration,
.active-dark-mode .course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item .section-item-link::before {
    color: var(--color-white-dark)!important;
    transition: all 0.3s ease-in-out!important;
}

.active-dark-mode .course-curriculum .section-header .section-left .section-toggle {
    color: var(--color-white)!important;
}


.active-dark-mode .course-curriculum .course-item:hover .item-name, 
.active-dark-mode .course-curriculum .course-item:hover .section-item-link .course-item-info .course-item-info-pre .item-meta.duration,
.active-dark-mode .course-content.rainbow-featured-single-tutor-course.rainbow-learnpress-featured-course-widget-single .course-curriculum .course-item:hover .section-item-link::before {
    color: var(--color-primary)!important;
}

.active-dark-mode .wc-block-components-radio-control--highlight-checked .wc-block-components-radio-control-accordion-option--checked-option-highlighted, 
.active-dark-mode .wc-block-components-radio-control--highlight-checked label.wc-block-components-radio-control__option--checked-option-highlighted {
    box-shadow: unset !important;
}

.active-dark-mode .wc-block-components-radio-control--highlight-checked .wc-block-components-radio-control-accordion-option--checked-option-highlighted, 
.active-dark-mode .wc-block-components-radio-control--highlight-checked label.wc-block-components-radio-control__option--checked-option-highlighted {
    border: 2px solid var(--dark-color-border);
}

.active-dark-mode  .wc-block-components-address-card {
    border: 2px solid var(--dark-color-border)!important;
}

.active-dark-mode button.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button.wc-block-components-checkout-place-order-button--full-width.contained:focus {
    box-shadow: unset !important;
border: unset!important;
}

.active-dark-mode .wc-blocks-components-select .wc-blocks-components-select__container {
    border: 2px solid var(--dark-color-border) !important;
}

.active-dark-mode a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart, 
.active-dark-mode .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, 
.active-dark-mode .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart {
    color: var(--color-white)!important;
}

.active-dark-mode .rbt-course-details-area .rbt-card-body a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    color: var(--color-white)!important;
}

.active-dark-mode  a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart::after, 
.active-dark-mode  .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button,
.active-dark-mode  .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart::after {
    color: currentColor;
}

.active-dark-mode .tutor-alert.tutor-success {
    background: var(--color-bodyest);
    color: var(--color-white-off);
    border: 1px solid var(--dark-color-border);
}

.active-dark-mode .rbt-price .current-price .price del {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode .rbt-price .current-price .price ins { 
    color: var(--color-white);
}

.active-dark-mode .rbt-featured-course .tutor-native-add-to-cart,
.active-dark-mode .rbt-related-course-area .tutor-native-add-to-cart,
.active-dark-mode .related-course .tutor-native-add-to-cart,
.active-dark-mode .tutor-course-archive-page .tutor-native-add-to-cart,
.active-dark-mode .rbt-rbt-course-area .tutor-native-add-to-cart,
.active-dark-mode .elegant-course .tutor-native-add-to-cart,
.active-dark-mode .course-card .tutor-native-add-to-cart,
.active-dark-mode .rbt-course-area .tutor-native-add-to-cart,
.active-dark-mode .rbt-featured-course .tutor-btn.tutor-btn-outline-primary.tutor-btn-md,
.active-dark-mode .tutor-course-archive-page .tutor-btn.tutor-btn-outline-primary.tutor-btn-md,
.active-dark-mode .related-course .tutor-btn.tutor-btn-outline-primary.tutor-btn-md,
.active-dark-mode .rbt-related-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md,
.active-dark-mode .rbt-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md {
    color: var(--color-white) !important;
}

.active-dark-mode .rbt-featured-course .tutor-native-add-to-cart:hover,
.active-dark-mode .rbt-related-course-area .tutor-native-add-to-cart:hover,
.active-dark-mode .related-course .tutor-native-add-to-cart:hover,
.active-dark-mode .tutor-course-archive-page .tutor-native-add-to-cart:hover,
.active-dark-mode .rbt-rbt-course-area .tutor-native-add-to-cart:hover,
.active-dark-mode .elegant-course .tutor-native-add-to-cart:hover,
.active-dark-mode .course-card .tutor-native-add-to-cart:hover,
.active-dark-mode .rbt-course-area .tutor-native-add-to-cart:hover,
.active-dark-mode .rbt-featured-course .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover,
.active-dark-mode .tutor-course-archive-page .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover,
.active-dark-mode .related-course .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover,
.active-dark-mode .rbt-related-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover,
.active-dark-mode .rbt-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover {
    color: var(--color-white-off) !important;
}

.active-dark-mode .list-item-price del {
    color: var(--color-white);
    opacity: 0.4;
}

.active-dark-mode .rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary { 
    color: var(--color-white) !important;
}

.active-dark-mode .rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary:hover { 
    color: var(--color-white-off) !important;
}

.active-dark-mode .tutor-cart-page .tutor-cart-course-item:not(:last-child) {
    border-bottom: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .tutor-cart-page .tutor-cart-course-list,
.active-dark-mode .tutor-cart-page .tutor-cart-summery {
    border: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .tutor-cart-page .tutor-cart-summery .tutor-cart-summery-top {
    border-bottom: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .tutor-cart-page .tutor-cart-course-item .tutor-cart-course-title a,
.active-dark-mode .tutor-cart-page .tutor-cart-summery .tutor-cart-summery-item,
.active-dark-mode .tutor-checkout-page .tutor-checkout-course-title a,
.active-dark-mode .tutor-checkout-page .tutor-checkout-summary-item {
    color: var(--color-white) !important;
}

.active-dark-mode .tutor-cart-page .tutor-cart-course-item .tutor-cart-course-info li {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-cart-page .tutor-cart-course-item .tutor-cart-course-price-wrapper .tutor-cart-discount-price,
.active-dark-mode .tutor-checkout-page .tutor-checkout-details .tutor-checkout-discount-price {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-details .tutor-checkout-details-inner,
.active-dark-mode .tutor-checkout-billing,
.active-dark-mode .tutor-alert.tutor-warning ,
.active-dark-mode .tutor-checkout-page .tutor-checkout-billing .dropdown-menu {
    background: var(--color-bodyest);
    box-shadow: var(--dark-shadow-3);
}

.active-dark-mode .tutor-alert.tutor-warning {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-details .tutor-border-bottom,
.active-dark-mode .tutor-checkout-page .tutor-checkout-detail-item:not(:last-child) {
    border-bottom: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .tutor-apply-coupon-form  {
    background: transparent;
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-details .tutor-text-right .tutor-fw-bold {
    color: var(--color-white) !important;
}

.active-dark-mode .tutor-checkout-page .tutor-apply-coupon-form {
    border: 1px solid var(--dark-color-border-2);
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .tutor-col-12:not(:last-of-type) .tutor-form-control {
    border-bottom: 1px solid var(--dark-color-border-2) !important;
}

.active-dark-mode .tutor-checkout-billing .tutor-billing-fields .tutor-form-control {
    border: 1px solid var(--dark-color-border-2) !important;
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-billing .bootstrap-select > .dropdown-toggle {
    border-color: transparent;
}

.active-dark-mode .tutor-checkout-page .tutor-checkout-billing .dropdown-menu {
    color: var(--color-white);
    border: 1px solid var(--dark-color-border-2) !important;
}

.active-dark-mode .tutor-checkout-billing .filter-option-inner {
    box-shadow: unset!important;
}

.active-dark-mode .tutor-cart-page-wrapper .tutor-cart-course-price .tutor-fw-bold {
    color: var(--color-white);
}

@media only screen and ( max-width: 991px ) {
    .active-dark-mode .tutor-user-public-profile .photo-area .pp-area .profile-rating-media .tutor-rating-container .rating-total-meta,
    .active-dark-mode .tutor-user-public-profile .photo-area .pp-area .profile-rating-media .tutor-rating-container .rating-digits,
    .active-dark-mode .tutor-user-public-profile .photo-area .pp-area .profile-name h3 {
        color: var(--color-white-dark)!important;
       
    }
}

.active-dark-mode .tutor-user-public-profile .photo-area .pp-area .profile-name,
.active-dark-mode .tutor-user-public-profile .photo-area .pp-area .profile-name span {
    color: var(--color-white-dark)!important;
}
.active-dark-mode.woocommerce-cart.woocommerce-page table.cart td.actions .coupon input {
    padding-left: 20px;
    border: 2px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode.woocommerce-page .cart-collaterals .cart_totals {
     background-color: var(--color-bodyest);
    box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
    border-radius: 6px;
}

.active-dark-mode.woocommerce-cart .cart-collaterals .cart_totals tr.order-total th,
.active-dark-mode.woocommerce-cart .cart-collaterals .cart_totals table tr:first-child th{
    color: white!important;
	padding-left: 30px;
	padding-right: 30px;
} 
.active-dark-mode.woocommerce-cart .cart-collaterals .cart_totals table {
    border: 0 !important;
}

.active-dark-mode .tutor-write-review-form .tutor-star-rating-container .tutor-form-group textarea {
    background: transparent;
    border: 2px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .tutor-download-certificate .tutor-certificate-course-title span,
.active-dark-mode .tutor-info-id-name,
.active-dark-mode .tutor-info-id-details,
.active-dark-mode .tutor-download-certificate .tutor-info-issued .tutor-info-issued-name,
.active-dark-mode .tutor-download-certificate .tutor-info-issued .tutor-info-issued-value,
.active-dark-mode .tutor-info-issued-date .tutor-info-date-name,
.active-dark-mode .tutor-info-issued-date .tutor-info-date-details  {
    color: var(--color-white);
}

.active-dark-mode .tutor-profile-completion-content-admin .tutor-mt-20 span {
    color: var(--color-white-dark)!important;
}

.active-dark-mode .tutor-instructor-application-body span.tutor-app-process-title{
    color: var(--color-white);
}

.active-dark-mode .withdraw-method-form .tutor-fs-7.tutor-color-secondary.withdraw-field-desc.tutor-mt-4 {
    color: var(--color-white-dark);
}

.active-dark-mode .tutor-dashboard-setting-social .tutor-fs-6.tutor-fw-medium.tutor-color-black.tutor-mt-32 {
    color: #fff;
}

.active-dark-mode .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field > div:first-child,
.active-dark-mode .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field > div:first-child i,
.active-dark-mode .tutor-radio-select .tutor-radio-select-content .tutor-radio-select-title,
.active-dark-mode .tutor-radio-select .tutor-radio-select-content,
.active-dark-mode .tutor-dashboard-setting-billing .tutor-color-black {
    color: var(--color-white-dark);
}

.active-dark-mode #tutor-withdraw-account-set-form  .tutor-radio-select {
    background-color: var(--color-bodyest);
    box-shadow: 0px 20px 34px rgba(0, 0, 0, 0.**********);
    color: var(--color-white);
    border: 2px solid rgba(from var(--color-white) r g b / 0.1);
}

.active-dark-mode .teacher-row-gutter span.tutor-fs-5.tutor-fw-medium.tutor-color-black {
    color: var(--color-white);
}

.active-dark-mode .tutor-form-check-input {
    background-color: transparent!important;
}

.active-dark-mode .bootstrap-select > .dropdown-toggle {
    background-color: var(--color-bodyest) !important;
    border-color: var(--dark-color-border-2)!important;
}

.active-dark-mode.tutor-screen-frontend-dashboard .bootstrap-select .dropdown-menu {
    background-color: var(--color-bodyest);
    color: var(--color-white);
}

.active-dark-mode .tutor-no-announcements .tutor-fs-6.tutor-fw-medium.tutor-color-black {
	color: var(--color-white-off);
}

.active-dark-mode .tutor-course-booking-progress .tutor-fw-medium.tutor-color-black {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-course-single-layout3-content-part .course-meta-bottom-part .course-details-layout3-brd i,
.active-dark-mode .rbt-course-single-layout3-content-part .course-meta-bottom-part i,
.active-dark-mode .rbt-course-single-layout3-content-part .course-meta-bottom-part i:hover,
.active-dark-mode .rbt-single-student-img-enroll .enroll-total-student .enroll-text,
.active-dark-mode .rbt-course-single-layout3-content-part .content.text-start .description,
.active-dark-mode .course-meta-bottom-part .rbt-author-meta .rbt-author-info  {
    color: var(--color-white-off);
}

.active-dark-mode .rbt-course-feature-has-video-thumbnail.rbt-course-feature-box.rbt-shadow-box.thuumbnail.rbt-layout3-brd-video-part {
    z-index: 3;
}

.active-dark-mode .rbt-single-student-img-enroll .enroll-total-student .student-count {
    color: var(--color-white);
}

.active-dark-mode .widget_tutor_related_course_widget .rbt-sidebar-price .current-price span {
    color: var(--color-white);
}

.active-dark-mode .rbt-single-widget ul.rbt-sidebar-list-wrapper.recent-post-list li + li {
    border-top: 1px solid var(--dark-color-border-2);
}

.active-dark-mode #tutor-quiz-single-multiple-choice .tutor-quiz-answer-single .tutor-quiz-question-item .tutor-card {
    background-color: var(--color-bodyest) !important;
    border-color: var(--dark-color-border-2)!important;
}

.active-dark-mode #tutor-quiz-single-multiple-choice .tutor-quiz-answer-single .tutor-quiz-question-item .tutor-card .tutor-color-black {
    color: var(--color-white-off);
}

.active-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .matching-quiz-question-desc .tutor-fs-7 p {
    color: var(--color-white);
}

.active-dark-mode .quiz-question .quiz-question-title {
    color: var(--color-white);
}

body.active-dark-mode .logo a img.logo-light-mode {
    display: none;
}

.popup-mobile-menu .logo-dark-mode {
    display: none;
}

body.active-dark-mode .popup-mobile-menu .logo-dark-mode {
    display: block;
}

body.active-dark-mode .rbt-banner-online-academy {
    background: linear-gradient(252deg, rgba(25, 35, 53, 0) 35.97%, rgba(47, 87, 239, 0.3) 100.98%), 
    linear-gradient(110deg, rgba(25, 35, 53, 0) 38.37%, rgba(185, 102, 231, 0.4) 102.05%) !important;
}

body.active-dark-mode .online-academy-banner-rating-part .left-text-part {
    color: var(--color-white);
}
body.active-dark-mode .banner-right-admission-intro .heading-part p,
body.active-dark-mode .banner-right-student-enroll-intro .enroll-top-part p {
    color: var(--color-white-dark);
}

body.active-dark-mode .banner-right-admission-intro .heading-part .heading,
body.active-dark-mode .banner-right-student-enroll-intro .enroll-top-part .heading {
    color: var(--color-white);
}


@media only screen and ( max-width: 767px) {
    .active-dark-mode .rbt-related-course-area .rbt-price {
        border-top: 0px solid var(--dark-color-border-2);
        padding-top: 0;
        margin-top: 0;
    }
}
