.tutor-course-single-content-wrapper {
    #tutor-single-entry-content {
        .tutor-quiz-wrapper {

            .tutor-start-quiz-wrapper {
                width: 950px;
                border-color: var(--color-border);

                .tutor-start-quiz-title {
                    border-bottom: 2px solid var(--color-border-2);

                    .tutor-fs-4 {
                        font-size: var(--h4) !important;
                        line-height: 1.25;
                        font-weight: var(--f-bold);
                        margin-bottom: 24px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid var(--color-border-2);
                    }

                    .tutor-fs-6 {
                        font-size: 14px !important;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        opacity: 0.5;
                        margin-bottom: 0;
                    }
                }

                p {
                    line-height: 1.67;
                }

                .tutor-quiz-info {
                    color: var(--color-heading);

                    .tutor-fs-6 {
                        font-size: 18px;
                    }

                    .tutor-color-black {
                        font-weight: 500;
                    }
                }

                .tutor-quiz-info-area.tutor-mb-60.tutor-mt-24 {
                    margin-bottom: 30px;
                }

                .tutor-quiz-btn-group {
                    border-top: 2px solid var(--color-border-2);
                    padding-top: 35px !important;

                    .tutor-btn-ghost {
                        color: var(--color-heading);
                        position: relative;
                        transition: 0.3s;
                        font-size: 16px;
                        display: inline-block;
                        line-height: 1.67;

                        &::after {
                            position: absolute;
                            content: "";
                            left: auto;
                            bottom: 0;
                            background: currentColor;
                            width: 0;
                            height: 2px;
                            transition: 0.3s;
                            right: 0;
                        }

                        &:hover {
                            color: var(--color-primary) !important;

                            &::after {
                                width: 100%;
                                left: 0;
                                right: auto;
                            }
                        }
                    }

                    #tutor-start-quiz {
                        button.tutor-btn.tutor-btn-primary {
                            padding: 0 25px;
                            font-size: var(--font-size-b3);
                            height: 50px;
                            line-height: 48px;
                            border: none !important;
                            transition: all 0.4s ease-in-out;

                            &:hover {
                                background-color: inherit;
                                background-position: 102% 0;
                            }
                        }
                    }

                }
            }

            #tutor-quiz-time-expire-wrapper {
                .tutor-quiz-alert-text {
                    &.tutor-fs-7 {
                        text-transform: none;
                    }
                }

                .reattempt-btn {
                    background: var(--color-warning);
                    color: var(--tutor-color-white);
                    padding: 0 22px;
                    font-size: var(--font-size-b3);
                    height: 45px;
                    line-height: 43px;
                }
            }

            .tutor-quiz-wrap {
                .tutor-fs-7 {
                    font-size: 14px !important;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    opacity: 0.5;
                    margin-bottom: 0;
                }

                .header-title {
                    font-size: var(--h4) !important;
                    line-height: 1.25;
                    font-weight: var(--f-bold);
                }

                .quiz-meta-info {
                    background: var(--primary-opacity) !important;
                    padding: 13px 20px 10px 20px;
                    border-radius: 5px;
                    box-shadow: var(--shadow-1);
                }

                .quiz-question-title {
                    font-size: var(--h5) !important;
                    line-height: 1.25;
                }

                .question-marks {
                    margin-bottom: 20px;
                }

                .quiz-image-ordering-ans-item {
                    border-color: var(--color-border) !important;
                }

                .answer_limit_desc,
                .answer-help-block p {
                    font-size: 14px;
                    font-weight: 400;
                    opacity: 0.8;
                }

                .quiz-question.tutor-mt-44 {
                    margin-top: 60px;
                }

                .quiz-question-ans-choice-area {
                    margin-top: 30px;
                }

                textarea.tutor-form-control.question_type_short_answer {
                    min-height: 100px !important;
                }

                .questions {
                    .tutor-fs-7 {
                        text-transform: none !important;
                    }
                }

                .given-answer {
                    .tutor-fs-7 {
                        text-transform: none !important;
                    }
                }

                .correct-answer {
                    .tutor-fs-7 {
                        text-transform: none !important;
                    }
                }

                input[type=text],
                input[type=password],
                input[type=email],
                input[type=number],
                input[type=tel],
                input[type=date],
                textarea {
                    box-shadow: none;
                }

                input[type=text]:focus,
                input[type=password]:focus,
                input[type=email]:focus,
                input[type=number]:focus,
                input[type=tel]:focus,
                input[type=date]:focus,
                textarea:focus {
                    border-color: var(--color-primary) !important;
                }

                .tutor-table tr th,
                .tutor-table tr td {
                    border: none !important;
                }

                .tutor-btn {
                    letter-spacing: 0.5px;
                    font-weight: 500;
                    display: inline-block;
                    position: relative;
                    z-index: 1;
                    transition: all 0.4s ease-in-out;
                    border-radius: 6px;
                    outline: none;

                    &.tutor-btn-outline-primary {
                        background: transparent;
                        border: 2px solid var(--color-border);
                        color: var(--color-heading);

                        &:hover {
                            background: var(--color-primary);
                            border-color: var(--color-primary);
                            color: var(--color-white);
                        }
                    }

                    &.tutor-btn-sm {
                        padding: 0 14px;
                        font-size: var(--font-size-b3);
                        line-height: 29px;
                        font-size: 14px;
                    }
                }
            }

            .question-type-image_answering {
                display: flex;
                gap: 24px;
            }

            .tutor-quiz-questions-pagination {
                ul {
                    .tutor-quiz-question-paginate-item {
                        border-radius: 6px;
                        background: #FFFFFF;
                        border-color: #FFFFFF;
                        box-shadow: 0px 6px 34px rgba(215, 216, 222, 0.41);
                        color: #6b7385;
                        width: 36px;
                        height: 36px;

                        &:hover,
                        &.active {
                            background: var(--color-primary);
                            color: var(--color-white);
                            opacity: 1;
                        }
                    }
                }
            }

            .tutor-badge-label {
                padding: 4px 16px;
                font-size: 14px;

                &.label-warning {
                    background: var(--warning-opacity);
                    color: var(--color-warning);
                    border-color: var(--warning-opacity);
                }

                &.label-danger {
                    background: var(--danger-opacity);
                    color: var(--color-danger);
                    border-color: var(--danger-opacity);
                }

                &.label-success {
                    background: rgba(36, 161, 72, 0.1);
                    color: var(--color-success);
                    border-color: rgba(36, 161, 72, 0.1);
                }
            }

            .matching-quiz-question-desc {
                .tutor-fs-7 {
                    opacity: 1 !important;

                    p {
                        color: var(--color-body);
                        font-size: 16px;
                        font-weight: var(--f-regular);
                        text-transform: none;
                    }
                }
            }

            #tutor-answering-quiz {
                .quiz-answer-footer-bar {
                    margin-top: 0 !important;
                }
            }

        }
    }
}

.tutor-border-bottom {
    border-bottom: 1px solid var(--color-border);
}

.tutor-border-top {
    border-top: 1px solid var(--color-border);
}

.tutor-quiz-answer-previous-btn {
    display: flex !important;
    align-items: center !important;

    .tutor-icon-previous {
        margin-right: 4px;
    }
}

.tutor-modal-content {
    border-radius: 12px;

    .tutor-btn-outline-primary {
        &:hover {
            background-color: var(--color-primary);
        }
    }

    .tutor-modal-close-o {
        &:hover {
            background-color: var(--color-primary);
            color: var(--color-white);
            font-size: 18px;
        }
    }
}


#tutor-course-details-tab-info {
    .tutor-accordion {
        .accordion-item {
            .rbt-course-main-content {
                .tutor-course-content-list-item {
                    .course-content-right {
                        >a {
                            margin-right: 0;

                            &:hover {
                                span {
                                    background: var(--color-primary);
                                    color: var(--color-white);

                                    i {
                                        color: var(--color-white);
                                    }
                                }
                            }

                            span {
                                padding: 0 12px;
                                transition: var(--transition);

                                i {
                                    color: var(--color-primary);
                                    transition: var(--transition);
                                    margin-right: 2px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.logged-in {
    &.admin-bar {
        .rbt-course-details-area {
            .sticky-top.course-sidebar-top {
                top: 142px;
            }
        }
    }
}

.rbt-course-details-area {
    .course-details-content {
        #tutor-course-details-tab-announcements {
            border-radius: var(--radius);
            background: var(--color-white);
            overflow: hidden;
            box-shadow: var(--shadow-1);
            padding: 0px;
    
            >div {
                margin-top: 0;
            }
        }
    
        .tutor-tab-items {
            >div {
                border-radius: var(--radius);
                background: var(--color-white);
                overflow: hidden;
                box-shadow: var(--shadow-1);
                padding: 30px;
                margin-top: 30px;
            }
        }
    }
}

.course-sidebar {
    .inner-title {
        font-size: var(--h6);
        font-weight: 600;
    }

    .title {
        font-weight: 600 !important;
        letter-spacing: normal !important;
        text-transform: none !important;
    }

    .instructor-box {
        p {
            margin-bottom: 20px;
            font-size: 16px;
            color: var(--color-body);
            display: none;
        }
    }

    .rbt-list-style-1 {
        display: flex !important;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;

        li {
            font-size: 16px;
            line-height: 1.5;
            margin: 0 !important;

            i {
                color: var(--color-body);
            }
        }
    }
}

.rbt-accordion-style.rbt-accordion-02.accordion:last-child .card .card-header:not(.is-active) {
    border: none !important;
}

.rbt-accordion-style.rbt-accordion-02.accordion:last-child .card .card-header {
    transition: none;
}

.rbt-course-menu-fixed-pos-bottom li span {
    margin-top: 6px!important;
}
