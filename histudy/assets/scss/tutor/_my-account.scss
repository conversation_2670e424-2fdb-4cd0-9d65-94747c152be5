nav.woocommerce-MyAccount-navigation ul li a {
    border: 1px solid var(--color-border);
    border-bottom: none;
    color: var(--color-body);
    font-weight: 500;
    font-size: 16px;
    display: block;
    padding: 20px 25px;
    border-right-color: transparent;
    border-left-color: transparent;
}

nav.woocommerce-MyAccount-navigation ul li {
    margin: 0;
}

nav.woocommerce-MyAccount-navigation ul li.is-active a,
nav.woocommerce-MyAccount-navigation ul li a:hover {
    background-color: var(--color-primary);
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    color: #fff;
    background-size: 300% 100%;
}

nav.woocommerce-MyAccount-navigation ul li:first-child a {
    border-top: 1px solid transparent;
}

nav.woocommerce-MyAccount-navigation ul {
    background-color: #fff;
    box-shadow: var(--shadow-1);
    border: 0 none;
    border-radius: 6px;
    overflow: hidden;
    padding: 0;
}

.woocommerce-account .woocommerce-MyAccount-content {
    background-color: #fff;
    font-size: 14px;
    border: 0 none;
    padding: 35px 30px 40px;
    box-shadow: var(--shadow-1);
    border-radius: 6px;
}

.woocommerce-account .woocommerce-MyAccount-content p:not(:last-child) {
    margin-bottom: 14px;
}

.woocommerce-edit-address button.btn.dropdown-toggle.bs-placeholder.btn-light {
    display: none;
}

.woocommerce-MyAccount-content fieldset {
    padding: 20px;
}

.wc-block-components-form .wc-block-components-text-input input[type=email],
.wc-block-components-form .wc-block-components-text-input input[type=number],
.wc-block-components-form .wc-block-components-text-input input[type=tel],
.wc-block-components-form .wc-block-components-text-input input[type=text],
.wc-block-components-form .wc-block-components-text-input input[type=url],
.wc-block-components-text-input input[type=email],
.wc-block-components-text-input input[type=number],
.wc-block-components-text-input input[type=tel],
.wc-block-components-text-input input[type=text],
.wc-block-components-text-input input[type=url] {
    border-color: var(--color-border);
}

.wc-block-components-form .wc-block-components-text-input input[type=email]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=number]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=tel]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=text]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=url]:focus,
.wc-block-components-text-input input[type=email]:focus,
.wc-block-components-text-input input[type=number]:focus,
.wc-block-components-text-input input[type=tel]:focus,
.wc-block-components-text-input input[type=text]:focus,
.wc-block-components-text-input input[type=url]:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px #2f57ef6e;
}

.tutor-course-progress-item .tutor-d-flex.tutor-fs-7.tutor-mb-32 {
    margin-bottom: 10px;
}

.filter-select-option.rbt-show-orderby-front .bootstrap-select.form-select {
    width: 100%;
    border-radius: 12px;
    background: transparent;
    padding-right: 0;
    padding-left: 0;
    border: 0;
}


.filter-select.rbt-modern-select {
    width: 246px;
}

// sabbir vai css from customizer
.wc-block-components-form .wc-block-components-text-input input[type=email],
.wc-block-components-form .wc-block-components-text-input input[type=number],
.wc-block-components-form .wc-block-components-text-input input[type=tel],
.wc-block-components-form .wc-block-components-text-input input[type=text],
.wc-block-components-form .wc-block-components-text-input input[type=url],
.wc-block-components-text-input input[type=email],
.wc-block-components-text-input input[type=number],
.wc-block-components-text-input input[type=tel],
.wc-block-components-text-input input[type=text],
.wc-block-components-text-input input[type=url] {
    border-color: var(--color-border);
    border-width: 1px;
}

.wc-block-components-form .wc-block-components-text-input input[type=email]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=number]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=tel]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=text]:focus,
.wc-block-components-form .wc-block-components-text-input input[type=url]:focus,
.wc-block-components-text-input input[type=email]:focus,
.wc-block-components-text-input input[type=number]:focus,
.wc-block-components-text-input input[type=tel]:focus,
.wc-block-components-text-input input[type=text]:focus,
.wc-block-components-text-input input[type=url]:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px #2f57ef6e;
}

.selected_course_filters.histudy-selected-course-filters-114 ul li span {
    cursor: pointer;
}

.selected_course_filters.histudy-selected-course-filters-114 ul li {
    display: inline-flex;
    margin-right: 7px;
    align-items: center;
}

a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    color: var(--color-heading) !important;
    background: none;
}

a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:hover {
    color: var(--color-primary) !important;
    background: none;

}

.custom-footer-two .social-icon {
    display: none;
}

.rbt-course-area .rbt-card-img img {
    width: 100%;
}


footer.rbt-footer.footer-style-1.bg-color-white.overflow-hidden:not(.has-rainbow-footer-style-1) .social-default.icon-naked {
    display: flex;
}

.rbt-cta-5 .title {
    padding-right: 26% !important;
}

.page-home-university-about .main-page-wrapper+.rbt-separator-mid,
.page-home-online-courses .main-page-wrapper+.rbt-separator-mid,
.page-home-online-course-education .main-page-wrapper+.rbt-separator-mid,
.page-home-gym-coachings .custom-footer-two>.rbt-separator-mid,
.page-home-online-school .custom-footer-two>.rbt-separator-mid,
.page-home-language-academy .custom-footer-two>.rbt-separator-mid {
    display: none;
}

.page-home-online-courses .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 220px !important;
}

.page-home-online-courses .dropdown-item.active {
    background-color: var(--black-opacity) !important;
    color: var(--color-primary) !important;
}

@media only screen and (max-width: 1400px) and (min-width:1200px) {
    .rbt-banner-1 .content .shape-wrapper {
        width: 380px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1599px) {
    .rbt-testimonial-box .inner {
        padding: 40px 30px;
    }
}

.footer-style-1 .mc4wp-form .right-icon::after {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-family: "feather" !important;
}

.footer-style-1 .mc4wp-form .right-icon.icon-email::after {
    content: "\e98a";
}

.rbt-search-dropdown .rbt-card.variation-01.rbt-hover:nth-child(2) {
    display: none;
}

.rbt-search-dropdown .rbt-card .rbt-card-body {
    margin-top: 0;
}

@media (min-width: 992px) {
    .rbt-big-banner-thumb .rbt-cta-default.style-4 .content-wrapper .thumbnail {
        width: 25%;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .rbt-big-banner-thumb .rbt-cta-default.style-4 .content-wrapper .thumbnail {
        width: 30%;
        bottom: -29px;
    }
}

@media (max-width: 767px) {

    .admin-bar .popup-mobile-menu,
    .rbt-cart-side-menu,
    .admin-bar .rbt-header .rbt-header-wrapper.rbt-sticky {
        top: 46px;
    }

    .rbt-split-area .container-fluid {
        padding: 0;
    }
}

.woocommerce-cart .quantity .qty {
    margin: 0 auto;
}

.rbt-course-details-area .rbt-card-body a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    color: inherit !important;
    background: transparent !important;
}

div#tutor-course-details-tab-reviews .rating-box .tutor-ratings-stars {
    justify-content: center;
    margin-bottom: 4px;
}

div .course_archive_page_identifier .load_more_button {
    margin-top: 0;
}

.rainbow-has-online-class-card .rbt-card.variation-03 .rbt-card-body {
    margin-top: 0;
    padding-top: 30px;
}

ul.item-rating {
    grid-gap: 0 6px;
}

@media (min-width: 768px) and (max-width: 991px) {
    .section-title.text-center .description.has-medium-font-size {
        font-size: 18px !important;
    }
}

.table-responsive.mobile-table-750 .rbt-table tr td,
.table-responsive .rbt-table tr th {
    text-align: left;
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .rbt-card.variation-02.rbt-hover .rbt-card-body .rbt-card-title {
        font-size: 26px;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .rbt-card.card-list-2 .rbt-card-img a img {
        height: auto;
    }
}

@media (max-width: 480px) {
    .rainbow-course-outline-swiper-space .gutter-swiper-30 {
        margin: 0;
    }
}

// Update CSS
.title-has-pr-0 {
    padding-right: 0;
}

.vh_100--32 {
    height: calc(100vh - 32px) !important;
}

.wpcf7 form.sent .wpcf7-response-output {
    color: var(--color-success);
}

.wpcf7 form.invalid .wpcf7-response-output,
.wpcf7 form.unaccepted .wpcf7-response-output {
    color: var(--black-danger);
}

.advance-tab-content-1 .thumbnail img {
    width: 100%;
}

/**
* Course not found
**/
.rainbow-course-not-found-error {
    box-shadow: var(--shadow-1);
    background: #fff;
    text-align: center;
    padding: 50px 15px;
}

.post-type-archive-courses .archive.course_block .load_more_button .load_more_btn {
    margin-bottom: 0;
}

@media (max-width: 767px) {
    .woocommerce.single.single-product .rbt-single-product-area.rbt-single-product .mb-90 {
        margin-bottom: 30px !important;
    }
}

.rbt-section-overlayping-top.tutor-course-archive-page,
.rbt-course-event-area.rbt-section-overlayping-top.rbt-section-gapBottom {
    margin: -175px auto 0 !important;
}

.rbt-banner-7 .wrapper {
    @media #{$sm-layout} {
        padding: 50px 0;
    }
}

.brand-style-1 li {
    @media #{$sm-layout} {
        padding: 20px 0;
    }
}

.brand-style-3 li {
    @media #{$sm-layout} {
        padding: 10px 30px;
    }
}

table.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block {
    margin-bottom: 0;
}

div.is-large.wc-block-cart {
    margin-bottom: 0;
}

.wc-block-components-text-input.wc-block-components-totals-coupon__input input,
.wc-block-components-address-form input {
    border-color: var(--color-border) !important;
    height: 50px;
    line-height: 50px;
    padding: 0;
}

.wc-block-components-address-form input {
    height: 60px;
    line-height: 60px;
}

.wp-block-woocommerce-cart.alignwide {
    margin-left: 0;
    margin-right: 0;
}

.wc-block-components-text-input.wc-block-components-totals-coupon__input input:focus {
    border-color: var(--color-primary) !important;
    box-shadow: none;
}

.wc-block-components-text-input.wc-block-components-totals-coupon__input label {
    margin-top: -5px;
}

body:not(.woocommerce-block-theme-has-button-styles) .wc-block-components-button:not(.is-link) {
    padding: 0 26px;
    background: var(--color-primary);
    height: 50px;
    line-height: 50px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    cursor: pointer;
}

.wc-block-components-totals-coupon__form {
    flex-direction: column;
    grid-gap: 20px 0;
}

.wc-block-components-totals-coupon__form button.components-button.wc-block-components-button {
    margin: 0;
}

.popup-mobile-menu.rainbow-mobile-sidebar-cat-menu button.close-button.rbt-round-btn {
    background: var(--color-primary);
    color: var(--color-white);
}

.popup-mobile-menu.rainbow-mobile-sidebar-cat-menu ul.mainmenu {
    padding: 0;
}

/*checkout css*/

.wc-block-components-form {
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    padding: 30px;
    border-radius: 6px;
}

.wc-block-components-form .wc-block-components-text-input input[type=email],
.wc-block-components-form .wc-block-components-text-input input[type=number],
.wc-block-components-form .wc-block-components-text-input input[type=tel],
.wc-block-components-form .wc-block-components-text-input input[type=text],
.wc-block-components-form .wc-block-components-text-input input[type=url],
.wc-block-components-text-input input[type=email],
.wc-block-components-text-input input[type=number],
.wc-block-components-text-input input[type=tel],
.wc-block-components-text-input input[type=text],
.wc-block-components-text-input input[type=url],
.components-combobox-control__suggestions-container input[type="text"] {
    width: 100%;
    background-color: transparent;
    border: 1px solid var(--color-border) !important;
    border-radius: 6px;
    line-height: 23px;
    padding: 10px 20px;
    font-size: 14px;
    color: var(--color-body);
}

.woocommerce-checkout .wp-block-woocommerce-checkout-actions-block {
    padding-bottom: 15px !important;
}

.wp-block-woocommerce-checkout-order-summary-block {
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    padding: 20px;
    border-radius: 6px;
    color: var(--color-body);
}

.wc-block-components-totals-wrapper:first-child {
    border: none;
}

.wc-block-components-sidebar {
    margin-top: 0 !important;
}

.wp-block-woocommerce-checkout {
    padding-top: 0 !important;
}

.wc-block-components-sidebar-layout {
    margin-bottom: 0 !important;
}

.wc-block-components-sidebar-layout .wc-block-components-main {
    padding-right: 10px !important;
}

body:not(.woocommerce-block-theme-has-button-styles) .wc-block-components-button:not(.is-link) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

ins {
    color: var(--color-primary);
    background-color: transparent;
}

.wc-block-cart table.wc-block-cart-items {
    margin-bottom: 0 !important;
}

.wp-block-woocommerce-cart.alignwide,
.is-medium .wc-block-cart__sidebar,
.is-mobile .wc-block-cart__sidebar,
.is-small .wc-block-cart__sidebar {
    margin-bottom: 0 !important;
}

/**
* Hemal css
*/
.rbt-section-overlayping-top .rainbow-course-not-found-error {
    margin-top: 32px;
}

.rbt-section-gapBottom.tutor-course-archive-page .rbt-course-grid-column {
    margin-left: 0;
    margin-right: 0;
}

.opacity-0 {
    opacity: 0 !important;
}

.default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner {
    grid-gap: 20px;
    justify-content: flex-start;
}

@media (max-width: 767px) {
    .rbt-course-top-wrapper button.discover-filter-button.discover-filter-activation {
        width: 100%;
    }

    .rbt-course-top-wrapper form.rbt-search-style {
        width: 100%;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-select-option>* {
        width: 100%;
    }

    .default-exp-wrapper .filter-inner .filter-select-option .dropdown.bootstrap-select.form-select {
        max-width: 100% !important;
    }

    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 31.33%;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-select-option>* {
        width: 100%;
    }

    .default-exp-wrapper .filter-inner .filter-select-option .dropdown.bootstrap-select.form-select {
        max-width: 100% !important;
    }

    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 31.33%;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-select-option>* {
        width: 100%;
    }

    .default-exp-wrapper .filter-inner .filter-select-option .dropdown.bootstrap-select.form-select {
        max-width: 100% !important;
    }

    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 48%;
    }
}

@media (max-width: 575px) {
    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-select-option>* {
        width: 100%;
    }

    .default-exp-wrapper .filter-inner .filter-select-option .dropdown.bootstrap-select.form-select {
        max-width: 100% !important;
    }

    .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 100%;
    }
}

.elementor-invisible {
    visibility: visible !important;
}

.dialog-lightbox-widget .animated {
    opacity: 1;
    transform: none;
}

.elementor-edit-area .animated {
    transform: none;
    opacity: 1;
}

.post-like.pt-like-it.rainbow-blog-details-like .like-button i {
    width: 40px;
    height: 40px;
    line-height: 34px;
    border: 2px solid var(--color-border);
    color: var(--color-body);
    border-radius: 100%;
    text-align: center;
    margin-right: 10px;
    font-size: 15px;
    display: inline-block;
}

.post-like.pt-like-it.rainbow-blog-details-like .like-button {
    background: transparent;
    font-size: 16px;
    padding: 0;
    border: 0;
}

@media (max-width: 767px) {
    .rbt-course-feature-box div#tutor-certificate-showcase {
        border-radius: var(--radius);
        background: var(--color-white);
        overflow: hidden;
        box-shadow: var(--shadow-1);
        padding: 30px;
        border: 0;
        padding-bottom: 50px;
    }
}
/**
 * Social integration
 * */
 #tutor-pro-google-authentication {
    width: 100%;
}
.fb_iframe_widget span {
	text-align: center !important;
}
.fb_iframe_widget span {
    max-width: 100%;
}

#tutor-pro-twitter-login {
    width: 100% !important;
}
.fb_iframe_widget iframe {
    position: relative !important;
}
.fb_iframe_widget span {
    width: 100% !important;
}

div#tutor-pro-facebook-authentication {
    width: 100%;
}

.fb_iframe_widget {
    width: 100%;
}
.rbt-section-gapBottom.tutor-course-archive-page .rbt-course-grid-column {
    margin-left: -15px;
    margin-right: -15px;
}
.rbt-course-details-right-sidebar {
    box-shadow: var(--shadow-1);
}
@media (max-width: 767px) {
    .single-format-quote .rbt-blockquote {
        margin-left: 0;
        margin-right: 0;
    }
}

.rbt-copyright-content-top {
    border-top: 1px solid var(--color-border);
    padding-top: 14px;
}
.footer-widget .logo a img {
    width: auto;
}
.rbt-card.variation-01.rbt-hover.elegant-course.card-list-2 {
    margin-bottom: 30px;
}
.rbt-card.variation-01.rbt-hover.elegant-course {
    margin-bottom: 30px;
}
.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    width: auto;
}

.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}

.rbt-course-grid-column.course_grid_archive .course-grid-1:last-child {
    margin-bottom: 0;
}

.rbt-course-grid-column.course_grid_archive .course-grid-1 {
    margin-bottom: 0;
}

/*course bundle css*/

.course-bundle.type-course-bundle .tutor-course-details-page {
    padding-top: 110px;
    padding-bottom: 120px;
}

.course-bundle.type-course-bundle .tutor-course-details-page .tutor-course-details-header .tutor-course-details-title > span,
.single-course-bundle .tutor-course-details-tab .tutor-fs-5 {
    font-size: var(--h4);
    line-height: 1.25;
    text-transform: capitalize;
}

.single-course-bundle .breadcrumb-inner .title {
    color: var(--color-heading);
    font-size: 50px;
    text-transform: capitalize;
}

.single-course-bundle .tutor-bundle-discount-info {
    position: absolute;
    top: 20px;
    left: 20px;
    height: 60px;
    width: 60px;
    border-radius: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1;
    background: transparent !important;
    -webkit-clip-path: unset!important;
    clip-path: unset!important;
    gap:7px;
}

.single-course-bundle .tutor-bundle-discount-info::before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    background: url(../images/icons/offer-badge-bg-color.svg);
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    left: 0;
    top: 0;
    z-index: -1;
    right: 0;
    bottom: 0;
}

.single-course-bundle .tutor-bundle-discount-info span {
    font-size: var(--font-size-b4);
    line-height: 1.2;
    color: var(--color-white);
    display: block;
    font-weight: var(--f-bold);
    font-family: var(--font-secondary);
    text-transform: capitalize;
}

.single-course-bundle .tutor-course-details-widget .inner-title { 
    font-size: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-border);
    margin-bottom: 15px;
}

.single-course-bundle .tutor-single-course-sidebar-more>div:first-child {
    padding: 0;
    border: none;
    background: var(--color-white);
}

.single-course-bundle .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a {
    border: 0 none;
    padding: 0px 19px;
    text-transform: capitalize;
    background: var(--color-white);
    color: var(--color-body);
    box-shadow: var(--shadow-1);
    height: 36px;
    line-height: 36px;
    border-radius: 500px;
    font-size: 14px;
    display: block;
    transition: 0.4s;
    text-align: center;
}

.single-course-bundle .tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.tutor-bundle-author-list {
    padding: 0;
}

.tutor-bundle-author-list .tutor-fs-5,
.tutor-course-details-widget-title,
.single-course-bundle .tutor-courses-instructors .tutor-form-label {
    font-size: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-border);
    margin-bottom: 15px;
}

.tutor-courses-instructors {
    margin-top: 10px;
    gap: 20px!important;
}

.single-course-bundle .course-sidebar .tutor-sidebar-card .tutor-card-footer {
    padding: 0;
}

.course-bundle-breadcrumb .bundle-author-img img {
    width: 40px;
    max-width: 40px;
    height: 40px;
    border-radius: 100%;
    object-fit: cover;
    border: 2px solid var(--primary-opacity);
    padding: 2px;
}

.course-bundle-breadcrumb .tutor-course-wishlist-btn i,
.course-bundle-breadcrumb .tutor-course-wishlist-btn,
.course-bundle-breadcrumb .tutor-btn,
.course-bundle-breadcrumb .tutor-btn i {
    color: var(--color-heading);
    font-size: 14px;
} 

.rbt-author-meta .rbt-author-info a {
    color: var(--color-heading);
    font-weight: 500;
}

.course-bundle-breadcrumb .content .title {
    text-transform: capitalize;
}

.course-bundle-breadcrumb .content .page-list li {
    text-transform: capitalize;
}

.course-bundle-breadcrumb-meta {
    color: var(--tutor-body-color);
}

.single-course-bundle .tutor-card-footer ul li {
    font-size: 15px;
    line-height: 25px;
    color: var(--body-color);
    font-weight: 400;
    cursor: pointer;
}

.tutor-screen-course-builder-frontend .tutor-course-price-toggle .tutor-form-check-input {
    display: none;
}

.tutor-wrap.tutor-wrap-parent.tutor-page-permission-denied {
    padding-top: 120px;
    padding-bottom: 120px;
}

.woocommerce-account .rbt-contact-form.contact-form-style-1 {
    width: 50%;
    margin: 0 auto;
}

.woocommerce-account .col2-set .col-1 .rbt-contact-form.contact-form-style-1, 
.woocommerce-account .col2-set .col-2 .rbt-contact-form.contact-form-style-1,
.rbt-account-modal .rbt-contact-form.contact-form-style-1 {
    float: left;
    width: 100%;
}

.tutor-forgot-password-form.tutor-ResetPassword.lost_reset_password {
    padding-top: 100px!important;
}

.tutor-dashboard-builder-header-right .tutor-btn-secondary {
    padding: 0 25px;
    font-size: var(--font-size-b3);
    height: 50px;
    line-height: 48px;
}
.tutor-dashboard-builder-header-right .tutor-btn-secondary:hover {
    color: var(--tutor-color-primary);
}

.tutor-screen-course-builder-frontend #course_setting_content_drip,
.tutor-screen-course-builder-frontend .content-drip-options-wrapper .tutor-form-check .tutor-form-check-input  {
    display: none;
}

.tutor-screen-course-builder-frontend .tutor-form-control.tutor-form-select .tutor-form-select-search .tutor-form-icon {
    margin-top: 0;
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend 
.select2-dropdown.select2-dropdown--below, 
body.tutor-screen-course-builder.tutor-screen-course-builder-frontend .select2-dropdown.select2-dropdown--above {
    margin-top: 0;
}

body.tutor-screen-course-builder.tutor-screen-course-builder-frontend .select2-search.select2-search--inline {
    margin-top: 0;
    margin-bottom: 5px;
}

.tutor-screen-frontend-dashboard .rbt-hover:hover { 
    transform: unset!important;
}

.tutor-screen-frontend-dashboard .social-default li a,
.tutor-screen-frontend-dashboard .rbt-teacher-details-sidebar-layout-1,
.tutor-screen-frontend-dashboard .rbt-btn.btn-border-gradient {
    z-index: 0;
}

.tutor-dashboard-content-inner.enrolled-courses .list-item-button a,.tutor-dashboard-content-inner.my-wishlist .list-item-button a {
    padding: 0 22px;
    font-size: var(--font-size-b3);
    height: 45px;
    line-height: 43px;
    background: var(--primary-opacity) !important;
    color: var(--color-primary) !important;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
}
.tutor-dashboard-content-inner.enrolled-courses .list-item-button .tutor-loop-cart-btn-wrap a ,
.tutor-dashboard-content-inner.my-wishlist .list-item-button .tutor-loop-cart-btn-wrap a {
    display: flex;
    align-items: center;
    justify-content: center;
}


.tutor-dashboard-content-inner.enrolled-courses .list-item-button a:hover,
.tutor-dashboard-content-inner.my-wishlist .list-item-button a:hover {
    background: var(--color-primary) !important;
    color: var(--color-white) !important;
}

.tutor-screen-course-builder-frontend .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #eff1f7;
    color: var(--color-body);
}

.tutor-screen-frontend-dashboard #tutor-pro-social-authentication {
    padding-bottom: 30px;
}

.rbt-contact-form.contact-form-style-1 #tutor-pro-social-authentication {
    padding-bottom: 0;
}

.rbt-header-wrapper .not-logged-in.tutor-course-wishlist-btn::before {
    display: none!important;
}

.tutor-screen-course-builder-frontend .tutor-course-sidebar-card-pricing .woocommerce-Price-amount bdi {
    font-size: 24px;
    font-weight: 700;
}


.tutor-screen-course-builder-frontend .tutor-course-sidebar-card-pricing .woocommerce-Price-amount bdi {
    font-size: 24px;
    font-weight: 700;
}

.tutor-screen-course-builder-frontend .tutor-course-sidebar-card-pricing .tutor-color-muted {
    text-decoration: none;
}

.tutor-screen-course-builder-frontend .tutor-course-sidebar-card-pricing .tutor-color-muted bdi {
    font-size: 20px;
    font-weight: 500;
    opacity: 0.4;
    text-decoration: line-through;
}

.tutor-screen-course-builder-frontend .tutor-sidebar-card .tutor-d-flex.tutor-align-center.tutor-gap-1 {
    font-size: 15px;
}

.tutor-screen-course-builder-frontend .tutor-sidebar-card .tutor-add-to-cart-button {
    margin-top: 20px;
    margin-bottom: 20px;
}

.tutor-bundle-author-list .tutor-courses-instructors .tutor-fw-bold,
.tutor-bundle-author-list .tutor-courses-instructors .tutor-instructor-designation {
    text-transform: capitalize;
}

.course-bundle-breadcrumb .tutor-course-details-top {
    margin-bottom: 8px;
}

#tutor-registration-form .tutor-alert.tutor-warning {
    padding: 0;
    max-width: 744px;
    margin: 0px auto 30px auto;
}

.rbt-card .rbt-card-img .tutor-bundle-course-count-badge {
    position: absolute;
    margin-top: 0;
    left: 15px;
    top: 15px;
}

.rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-bundle-course-count-text,
.rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-bundle-course-count-number {
    text-transform: capitalize;
    font-size: 15px;
}

.rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-icon-layer:before {
    font-size: 15px;
    display: block;
}

.rbt-card .rbt-card-img .tutor-bundle-course-count-badge {
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.course-bundle .tutor-woocommerce-view-cart {
    background: var(--color-primary);
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    margin-bottom: 20px;
    border: none;
    outline:none;
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    height: 50px;
}

.course-bundle .tutor-woocommerce-view-cart:hover {
    background-color: inherit;
    background-position: 102% 0;
}

.enrolment-expire-info {
    padding-bottom: 10px;
}


.demo-rtl {
    position: fixed;
    bottom: 90px;
    left: 30px;
    z-index: 9999;
}

.demo-rtl > button.rtl {
    background: var(--color-white);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    border: 2px solid var(--color-border);
    display: block;
    text-indent: inherit;
    font-size: 12px;
    width: 45px;
    height: 45px;
    line-height: 42px;
    text-align: center;
    font-weight: 700;
    margin: 0px;
    border-radius: 50%;
    
}

body.rtl .demo-rtl {
    display: none;
}

body.ltr .demo-ltr {
    display: none;
}

.demo-ltr {
    position: fixed;
    bottom: 90px;
    left: auto;
    right: 30px;
    z-index: 9999;
}

.demo-ltr button.ltr {
    background: var(--color-white);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    border: 2px solid var(--color-border);
    display: block;
    text-indent: inherit;
    font-size: 12px;
    font-weight: 700;
    width: 45px;
    height: 45px;
    line-height: 42px;
    text-align: center;
    margin: 0px;
    border-radius: 50%;
}