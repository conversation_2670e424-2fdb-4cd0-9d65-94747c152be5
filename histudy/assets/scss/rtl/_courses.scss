.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
    margin-left: 0;
    margin-right: 15px;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item ul.course-switch-layout {
    margin-right: 0;
    margin-left: 15px;
}

.rbt-single-widget input[type=checkbox]~label::before,
.rbt-single-widget input[type=radio]~label::before {
    left: auto;
    right: 0;
}

.rbt-single-widget input[type=checkbox]~label::before,
.rbt-single-widget input[type=radio]~label::after {
    left: auto;
    right: 3px;
}

.rbt-single-widget input[type=checkbox]~label,
.rbt-single-widget input[type=radio]~label {
    padding-left: 0;
    padding-right: 23px;
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
    text-align: start;
}

button.rbt-filter-rating-toggle {
    text-align: start;
}

.rbt-modern-select .bootstrap-select button.btn-light {
    padding-right: 20px;
}

.rbt-search-style .rbt-search-btn {
    right: auto;
    left: 5px;
}

.rbt-search-style input {
    padding-right: 20px;
    padding-left: 60px;
}

.rbt-page-banner-wrapper .rbt-banner-content-top .title-wrapper .rbt-badge-2 {
    margin-left: 0;
    margin-right: 20px;
}

.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
    right: auto;
    left: 15px;
}

.bootstrap-select.show-tick .dropdown-menu li a span.text {
    margin-right: 0;
    margin-left: 34px;
}

.rbt-btn i {
    padding-left: 0;
    padding-right: 6px;
}

.about-author .thumbnail img {
    margin-right: 0;
    margin-left: 30px;
}

i.feather-phone.mr--5 {
    margin-right: 0 !important;
    margin-left: 5px !important;
}

.rbt-btn .btn-icon {
    padding-right: 0;
    margin-right: 6px;
}

.meta-list li .author-thumbnail {
    margin-right: 8px;
    margin-left: 8px;
}

ul.blog-meta li {
    display: flex;
    align-items: center;

    i {
        margin-right: 0;
        margin-left: 5px;
    }
}

blockquote.rbt-blockquote.mt--0.alignwide.square.rbt-border-none {
    padding: 40px 100px 40px 40px;
}

.rainbow-post-content-wrapper {
    blockquote {
        padding: 40px 100px 40px 40px;
    }
}

blockquote::before {
    left: auto;
    right: 30px;
}

input[type=checkbox]~label::before,
input[type=radio]~label::before {
    left: auto;
    right: 0;
}

input[type=checkbox]~label,
input[type=radio]~label {
    padding-left: 0;
    padding-right: 20px;
}

input[type=checkbox]~label::after,
input[type=radio]~label::after {
    left: auto;
    right: 2px;
}

.histudy-post-wrapper .rbt-card ul.blog-meta li i {
    margin-right: 0;
    margin-left: 6px;
}

.rbt-single-widget .recent-post-list li .thumbnail {
    margin-right: 0;
    margin-left: 12px;
}

.footer-layout-4 .histudy-search .search-button,
.rbt-sidebar-widget-wrapper .histudy-search .search-button {
    left: 0;
    right: auto;
}

.post-like.pt-like-it.rainbow-blog-details-like .like-button i {
    margin-right: 0;
    margin-left: 12px;
}

.rbt-image-gallery-1 .image-2 {
    right: auto;
    left: 10%;
}

.rbt-image-gallery-1.text-end .image-2 {
    left: auto;
    right: 10%;
}

.progress .progress-number {
    right: auto;
    left: 0;
}

.service-card-5 .inner .icon {
    margin-right: 0;
    margin-left: 20px;
}

.newsletter-form-1 input {
    text-align: end;
    padding-right: 20px;
    padding-left: 180px;
}

.newsletter-form-1 .rbt-btn {
    right: auto;
    left: 10px;
}

.plan-offer-list li i {
    margin-right: 10px;
    margin-left: 10px;
}

.rbt-cta-default.style-2 .shape-text-image img {
    right: 73%;
}

.rbt-btn.rounded-player span i {
    padding-right: 0;
    padding-left: 6px;
}

input[type=checkbox]~label::after,
input[type=radio]~label::after {
    right: 5px;
}

.plan-offer-list-wrapper .plan-offer-list+.plan-offer-list {
    margin-left: 0;
    margin-right: 50px;
}

.plan-offer-list li i {
    margin-right: 0;
}

.addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
    padding-left: 0 !important;
    padding-right: 50px;
}

.tutor-user-public-profile .photo-area .pp-area {
    padding-right: 40px;
}

.tutor-user-public-profile.tutor-user-public-profile-pp-rectangle .profile-name {
    padding-left: 0;
    padding-right: 23px;
}

.form-group input {
    text-align: right;
}

.form-group label {
    left: auto;
    width: fit-content;
    white-space: nowrap;
    right: 0;
}

.rbt-contact-form.contact-form-style-1 {
    .rbt-btn.hover-icon-reverse .btn-icon {
        transform: rotate(180deg) translateX(124px);
    }

    .rbt-btn:hover.hover-icon-reverse .btn-icon+.btn-icon {
        transform: rotate(180deg) translateX(-78px);
    }

    .rbt-btn.hover-icon-reverse .btn-icon+.btn-icon {
        transform: rotate(180deg) translateX(0);
        ;
    }

    .rbt-btn.hover-icon-reverse .btn-icon+.btn-icon {
        transform: rotate(180deg) translateX(-102px);
    }
}

.rbt-contact-form.contact-form-style-1.max-width-auto {
    .rbt-btn:hover.hover-icon-reverse .btn-icon+.btn-icon {
        transform: rotate(180deg) translateX(0px);
    }
}

.rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn.hover-icon-reverse .btn-text {
    margin-inline-end: -23px;
    margin-inline-start: 0;
}

.rbt-contact-form.contact-form-style-1 .rbt-btn.hover-icon-reverse .btn-icon {
    transform: rotate(180deg) translateX(100px);
}

.rbt-contact-form.contact-form-style-1.max-width-auto .rbt-btn:hover.hover-icon-reverse .btn-icon+.btn-icon {
    transform: rotate(180deg) translateX(-98px);
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: none;
        transform: translate3d(100%, 0, 0);
    }

    100% {
        opacity: 1;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.rbt-single-product .pro-qty {
    margin-right: 0;
    margin-left: 20px;
}

.rbt-single-product .pro-qty .qtybtn.inc {
    padding-right: 0;
    padding-left: 7px;
}

.rbt-single-product .pro-qty .qtybtn.dec {
    padding-left: 0;
    padding-right: 7px;
}

.comment-list .comment .single-comment .comment-img {
    margin-right: 0;
    margin-left: 20px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
        padding-right: 0;
    }

    .rbt-mini-cart .rbt-cart-count {
        left: 73%;
        font-weight: 500;
        right: auto;
    }
}

@media only screen and (max-width: 767px) {
    .blog-content-wrapper blockquote.rbt-blockquote.mt--0.alignwide.square.rbt-border-none {
        padding: 24px;
    }

    blockquote::before {
        right: 84px;
    }

    .rainbow-post-content-wrapper {
        blockquote {
            padding: 20px 40px 20px 20px;

            &::before {
                right: 10px;
            }
        }
    }

    .addmission-guide-content.pl--50.pl_sm--0.pl_md--0.pl_lg--0 {
        padding-right: 0;
    }
}

@media only screen and (max-width: 575px) {
    .rbt-page-banner-wrapper .rbt-banner-content-top .title-wrapper .rbt-badge-2 {
        margin-right: 0;
    }

    .rbt-blog-details-area .about-author .thumbnail img.avatar {
        margin-bottom: 24px;
    }

    .rbt-accordion-style .card .card-header {
        padding-right: 20px;

        span {
            display: block;
            padding-left: 20px;
        }
    }

    .rbt-video-area {
        .inner.pr--50 {
            padding-right: 0px !important;
        }
    }

    .rbt-testimonial-area .section-title.pl--100.pl_sm--30 {
        padding-right: 0px;
    }

    .rbt-btn .btn-icon {
        margin-right: 0;
    }
}

.rbt-accordion-style .card .card-header button {
    padding-left: 30px;
}

.rbt-header-top-1 {
    .rbt-header-top-news .inner .content .news-text img {
        margin-right: 0px;
    }
}

.modern-course-features-box.one-colume-grid.h-100 .inner .content {
    margin-right: 0;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 li {
    display: flex !important;
}

.advance-pricing .pricing-right .plan-offer-list li.no {
    margin-right: 0;
}

.advance-pricing .pricing-right .plan-offer-list li.off {
    margin-right: 0;
}

.side-menu .inner-wrapper .description {
    padding-right: 0;
    padding-left: 18%;
}

.team ul.social-icon {
    left: auto;
    right: 35px;
}

.rbt-header-sec .search-field input {
    padding-right: 20px;
}

.rbt-banner-area.rbt-banner-8.variation-01 .content .rbt-badge-2 {
    padding-left: 20px;
    padding-right: 10px;
}

.rbt-contact-form.contact-form-style-1 .form-submit-group {
    display: flex;
    justify-content: end;
}

aside.rbt-sidebar-widget-wrapper .rbt-single-widget .recent-post-list li .content .rbt-meta li i {
    margin-right: 0;
}

.rbt-minicart-wrapper .product-content {
    padding-left: 0;
    padding-right: 20px;
}

.is-large.wc-block-cart .wc-block-cart__totals-title {
    text-align: right !important;
    padding-right: 16px !important;
}

.rbt-breadcrumb-default.rbt-breadcrumb-style-3 .content {
    padding-right: 0;
    padding-left: 10%;
}