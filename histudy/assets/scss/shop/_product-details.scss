/*---------------------------
    Product Details 
----------------------------*/
.pro-qty {
    max-width: 120px;
    height: 50px;
    border: 1px solid var(--color-border);
    border-radius: 6px;
    padding: 0;
    display: flex;
    margin: auto;
    min-width: 50px;
    align-items: center;
    & .qtybtn {
        width: 45px;
        display: block;
        float: left;
        line-height: 50px;
        cursor: pointer;
        text-align: center;
        font-size: 20px;
        font-weight: 700;
        color: var(--color-body);
        height: 50px;
    }
    & input {
        width: 28px;
        float: left;
        border: none;
        height: 33px;
        line-height: 33px;
        padding: 0;
        text-align: center;
        background-color: transparent;
        box-shadow: none;
    } 
    @media #{$large-mobile} {
        margin: 0;
    }
}


.rbt-single-product {
    .product-action {
        display: inline-flex;
        @media #{$large-mobile} {
            display: block;
        }
    }
    .pro-qty {
        margin-right: 20px;
        height: 60px;
        @media #{$large-mobile} {
            margin-right: 0;
            margin-bottom: 20px;
        }
        .qtybtn {
            &.dec {
                padding-left: 7px;
            }
            &.inc {
                padding-right: 7px;
            }

        }
    }

    .product-feature {
        @extend %liststyle;
        margin-top: 30px;
        @media #{$lg-layout} {
            margin-top: 10px;
        }
        @media #{$md-layout} {
            margin-top: 10px;
        }
        @media #{$sm-layout} {
            margin-top: 10px;
        }
        li {
            font-weight: 400;
            margin: 5px 0;
            span {
                font-weight: 700;
                color: var(--color-heading);
            }
            a {
                position: relative;
                display: inline-block;
                padding: 3px;
                color: var(--color-body);
                &::after {
                    position: absolute;
                    content: ",";
                    right: -3px;
                }
                &:hover {
                    color: var(--color-primary);
                }
            }
            a:last-child:after { 
                display: none;
            }
        }
    }
}




.product-description-nav {
    border-bottom: 1px solid #EEEEEE;
    justify-content: center;
    margin-bottom: 35px;

    .nav-item {
        margin: 0 25px;
        button {
            &.nav-link {
                background: transparent;
                border: 0 none;
                font-weight: 700;
                font-size: 18px;
                line-height: 28px;
                padding: 0;
                padding-bottom: 15px;
                color: var(--color-heading);
                position: relative;
                transition: 0.4s;
                &::after {
                    position: absolute;
                    content: "";
                    bottom: 0;
                    left: 0;
                    height: 2px;
                    background: var(--color-primary);
                    transition: 0.4s;
                    width: 0;
                }
                &:hover,
                &.active {
                    color: var(--color-primary);
                    &::after {
                        width: 100%;
                    }
                }
            }
        }
    }
}

.product-description-content {
    p {
        &:last-child {
            margin-bottom: 0;
        }
    }
    .comment-top {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .notification-text {
        .title {
            font-size: 16px;
            line-height: 26px;
            margin-right: 10px;
        }
    }
}



.product-description-content .comment-list .comment .commenter {
    margin-bottom: 10px;
}

.product-description-content .comment-list .comment .comment-meta {
    margin-bottom: 16px;
}
tr.woocommerce-shipping-totals.shipping th {
    padding: 0;
    border: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 23px;
    color: var(--color-body);
    margin: 0;
    padding: 12px 0;
    font-weight: 500 !important;
}

table.shop_table.shop_table_responsive tbody tr td {padding: 20px 0;}

table.shop_table.shop_table_responsive tbody tr th {
    padding-top: 14px !important;
}

.woocommerce-cart .cart-collaterals .cart_totals .woocommerce-shipping-destination {
    font-size: 16px;
    line-height: 23px;
    color: var(--color-body);
    font-weight: 400;
}

.woocommerce-cart .cart-collaterals .shipping-calculator-button {
    font-size: 16px;
    line-height: 23px;
    color: var(--color-body);
    font-weight: 400;
}

.woocommerce-cart .cart-collaterals .cart_totals tr.order-total th {border-top: 2px solid var(--color-border);padding-top: 14px;font-size: 18px;line-height: 23px;font-weight: 700;color: var(--color-heading);margin: 0;margin-top: 20px;border-top: 2px solid #e9e6ed;}

.woocommerce-cart .cart-collaterals .cart_totals tr td {
    border-top: 2px solid var(--color-border);
    padding-top: 14px;
    font-size: 18px;
    line-height: 23px;
    font-weight: 700;
    color: var(--color-heading);
}

.woocommerce-cart .cart-collaterals .cart_totals tr td:last-child {
    text-align: right;
}

.woocommerce-cart .cart-collaterals .shipping-calculator-button:hover {
    color: var(--color-primary);
}

.woocommerce nav.woocommerce-pagination ul {
    margin: -8px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
}

.woocommerce .woocommerce-pagination ul.page-numbers li, 
.woocommerce-page .woocommerce-pagination ul.page-numbers li {
    margin: 8px;
    border: 0;
    color: var(--color-body);
}

.woocommerce nav.woocommerce-pagination ul li a, 
.woocommerce nav.woocommerce-pagination ul li span {
    width: 45px;
    height: 45px;
    background: var(--color-white);
    border-radius: 6px;
    text-align: center;
    color: var(--color-body);
    transition: 0.4s;
    font-weight: 500;
    box-shadow: var(--shadow-1);
    display: flex;
    align-items: center;
    justify-content: center;
}
.woocommerce nav.woocommerce-pagination ul li a:hover, 
.woocommerce nav.woocommerce-pagination ul li .current {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.woocommerce nav.woocommerce-pagination ul li {
    overflow: visible;
}

.woocommerce nav.woocommerce-pagination ul li span.current, 
.woocommerce nav.woocommerce-pagination ul li a:focus {
    background: #3259ef;
    color: #fff;
}

.woocommerce  .rbt-related-product ul.products {
	margin-left: -15px;
	margin-right: -15px;
}
.woocommerce .rbt-related-product ul.products li {
   margin: 0px!important;
   padding-left: 15px;
   padding-right: 15px;
   margin-bottom: 30px!important;
}
.rbt-single-product .content .rbt-price + div p:last-child {
   margin-bottom: 15px;
}
.woocommerce-Reviews .dropdown-toggle::after {
	border-top: 0.5em solid;
    border-right: 0.5em solid transparent;
    border-left: 0.5em solid transparent;
}

.rbt-single-product-area .button.wc-forward,
.rbt-single-product-area .button.wc-forward:hover   {
    background: #3557ee !important;
    color: #fff;
}

.woocommerce-message {
    border-top-color: #3557ee;
    &:before {
        color: #3557ee;
    }
}
.woocommerce-info {
    border-top-color: #7f54b3;
}
.woocommerce-account .rbt-page-area .rbt-section-gap {
    padding-bottom: 90px;
}
.woocommerce-account .wc-block-components-notice-banner button.wc-forward {
    background: transparent!important;
}

@media ( max-width: 991px ) {
    .woocommerce.single.single-product .rbt-single-product-area.rbt-single-product {
        padding-bottom: 50px;
    }
    .single-product.woocommerce-page .rbt-breadcrumb-default.bg-gradient-1 {
        margin-bottom: 80px;
    }
    .single-product.woocommerce-page .rbt-breadcrumb-default.bg-gradient-1 {
        margin-bottom: 80px!important;
    }
}

@media (max-width: 767px) {
    .rbt-single-product .pro-qty {
        margin-right: 10px;
    }
    .rbt-single-product .product-action.mb--20 {
        margin-top: 20px;
    }
}