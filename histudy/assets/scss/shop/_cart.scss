/*---------------------------------------
    11. Cart
-----------------------------------------*/

.cart-submit-btn-group {
    margin: -10px;
    display: flex;
    flex-wrap: wrap;
    .single-button {
        padding: 10px;
    }
}

.rbt-cart-area {
    .section-title {
        .title {
            border-bottom: 1px dashed var(--color-border);
            padding-bottom: 10px;
        }
    }
}


.cart-table {
    & .table {
        border-bottom: 8px solid var(--color-border);
        margin: 0; 
        @media #{$large-mobile} {
            border-top: 8px solid transparent;
        } 
        & thead {
            background-color: var(--color-primary);
            background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
            background-size: 300% 100%;
            
            @media #{$large-mobile} {
                display: none;
            }

            & tr {
                & th {
                    text-align: center;
                    border: none;
                    font-size: 18px;
                    text-transform: inherit;
                    font-weight: 500;
                    color: var(--color-white);
                    padding: 12px 20px;
                    &:first-child {
                        border-radius: 6px 0 0 6px;
                    }
                    &:last-child {
                        border-radius: 0 6px 6px 0;
                    }
                }
            }
        } 
        & tbody {
            & tr {
                transition: 0.4s;
                &:hover {
                    box-shadow: var(--shadow-1);
                }
                & td {
                    text-align: center;
                    border: none;
                    padding: 25px 20px;
                    vertical-align: middle;
                    border-bottom: 1px solid var(--color-border); 
                    padding-left: 0;
                    @media #{$large-mobile} {
                        display: block;
                        width: 100%;
                        max-width: none;
                        padding: 15px;
                        text-align: left;
                    }
                }
            }
        }
    }
}

.cart-table .table tbody tr td .woocommerce-Price-amount span {
    display: inline-block;
}
.cart-table .table tbody tr td.pro-remove a {
    color: var(--color-heading) !important;
    line-height: 50px;
}
.cart-table .table>:not(:first-child) {
    border-top: 0 none;
}

.cart-table td.pro-price span {
    display: inline-block;
}

.cart-table {
    & th.pro-thumbnail,
    td.pro-thumbnail {
        max-width: 160px;
        min-width: 120px;
        width: 160px; 
        @media #{$large-mobile} {
            & a {
                width: 160px;
            }
        }
    }
    & th.pro-title,
    td.pro-title {
        min-width: 200px;
    }
    & td.pro-thumbnail {
        & a {
            display: block;
            & img {
                width: 100%;
                background-color: #f6f7f8;
                border-radius: 6px;
            }
        }
    }
    & td.pro-title {
        & a {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-heading);
            transition: 0.4s;
            &:hover {
                color: var(--color-primary);
            }
        }
    }
    & td.pro-price {
        & span {
            display: block;
            font-size: 15px;
            font-weight: 600;
            color: var(--color-body);
        }
    }
    & td.pro-quantity {
        & .pro-qty {
            max-width: 120px;
            height: 50px;
            border: 1px solid var(--color-border);
            border-radius: 6px;
            padding: 0;
            display: flex;
            margin: auto;
            min-width: 50px;
            align-items: center;

            & .qtybtn {
                width: 45px;
                display: block;
                float: left;
                line-height: 50px;
                cursor: pointer;
                text-align: center;
                font-size: 20px;
                font-weight: 700;
                color: var(--color-body);
                height: 50px;
            }
            & input {
                width: 28px;
                float: left;
                border: none;
                height: 33px;
                line-height: 33px;
                padding: 0;
                text-align: center;
                background-color: transparent;
                box-shadow: none;
            } 
            @media #{$large-mobile} {
                margin: 0;
            }
        }
    }
    & td.pro-subtotal {
        & span {
            display: block;
            font-size: 15px;
            font-weight: 600;
            color: var(--color-primary);
        }
    }
    & td.pro-addtocart {
        & button {
            width: 140px;
            border-radius: 50px;
            height: 36px;
            line-height: 24px;
            padding: 5px 20px;
            text-transform: capitalize;
        }
    }
    .pro-remove {
        a {
            display: block;
            font-weight: 600;
            position: relative;
            width: 50px;
            height: 50px;
            margin: 0 auto;
            background-color: transparent !important;
            line-height: 56px;
            z-index: 1;
            &::after {
                background: var(--color-gray-light);
                position: absolute;
                content: "";
                width: 100%;
                height: 100%;
                left: 0;
                top: 0;
                transition: 0.4s;
                opacity: 0;
                transform: scale(0.8);
                border-radius: 100%;
                z-index: -1;
            }

            &:hover {
                &::after {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            i {
                font-size: 24px;
            }

            @media #{$large-mobile} {
                width: 60px;
                text-align: center;
            }
        }
    }
}

/*-- Calculate Shipping --*/

.calculate-shipping {
    margin-bottom: 23px;
  
    & form {
        & .nice-select {
            width: 100%;
            border-radius: 6px;
            height: 45px;
            border: 1px solid var(--color-border);
            line-height: 24px;
            padding: 5px 20px;
            background-color: transparent;
            &::after {
                border-color: var(--color-body);
            }
            & .current {
                display: block;
                line-height: 24px;
                font-size: 14px;
                color: var(--color-body);
            }
        }
        & input {
            width: 100%;
            border-radius: 6px;
            height: 45px;
            border: 1px solid var(--color-border);
            line-height: 24px;
            padding: 5px 20px;
            color: var(--color-body);
            background-color: transparent;
            &[type="submit"] {
                font-weight: 700;
                text-transform: uppercase;
                color: #fff;
                background-color: var(--color-primary);
                border-color: var(--color-primary);
                width: 140px;
            }
        }
    }
}

/*-- Discount Coupon --*/

.discount-coupon {
   
    & form {
        & input {
            width: 100%;
            border-radius: 6px;
            height: 45px;
            border: 1px solid var(--color-border);
            line-height: 24px;
            padding: 5px 20px;
            color: var(--color-body);
            background-color: transparent;
            &[type="submit"] {
                font-weight: 700;
                text-transform: uppercase;
                color: #fff;
                background-color: var(--color-primary);
                border-color: var(--color-primary);
                width: 140px;
            }
        }
    }
}

/*-- Cart Summary --*/

.cart-summary {
    .cart-summary-wrap {
        background-color: var(--color-white);
        padding: 45px 50px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-1);
        border-radius: 6px;
        @media #{$small-mobile} {
            padding: 25px 30px;
        }

        p {
            font-size: 16px;
            font-weight: 500;
            line-height: 23px;
            color: var(--color-body);
            margin: 0;
            padding: 12px 0;
            span {
                float: right;
            }
            & + p {
                border-top: 1px dashed var(--color-border);
            }
        }
        h2 {
            border-top: 2px solid var(--color-border);
            padding-top: 14px;
            font-size: 18px;
            line-height: 23px;
            font-weight: 700;
            color: var(--color-heading);
            margin: 0;
            margin-top: 20px;
            span {
                float: right;
            }
        }

    }
    .cart-summary-button {
        overflow: hidden;
        width: 100%; 
        @media #{$sm-layout} {
            display: flex;
            justify-content: flex-start;
        }
        @media #{$large-mobile} {
            display: flex;
            justify-content: flex-start;
        }
        button {
            margin-top: 10px;
            width: 140px;
            border-radius: 50px;
            height: 36px;
            border: 1px solid var(--color-border);
            line-height: 24px;
            padding: 5px 20px;
            color: var(--color-body);
            background-color: transparent;
            margin-left: 20px;
            float: right;
            &:last-child {
                margin-left: 0;
            }
            &.checkout-btn {
                font-weight: 700;
                text-transform: uppercase;
                color: #fff;
                background-color: var(--color-primary);
                border-color: var(--color-primary);
            } 
            @media #{$sm-layout} {
                margin-left: 0;
                margin-right: 20px;
                &:last-child {
                    margin-right: 0;
                }
            }
            @media #{$large-mobile} {
                margin-left: 0;
                margin-right: 10px;
                &:last-child {
                    margin-right: 0;
                }
            }
            @media #{$small-mobile} {
                width: 130px;
            }
        }
    }
}
.shop_table.cart.woocommerce-cart-form__contents thead {
    background-color: var(--color-primary);
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    background-size: 300% 100%;
}

.shop_table.cart.woocommerce-cart-form__contents thead th {
    text-align: center;
    border: none;
    font-size: 18px;
    text-transform: inherit;
    font-weight: 500;
    color: var(--color-white);
    padding: 12px 20px;
}

.shop_table.cart.woocommerce-cart-form__contents>:not(:last-child)>:last-child>* {
    border-bottom-color: currentColor;
}

.shop_table.cart.woocommerce-cart-form__contents tbody tr .product-name {
    text-align: center;
    padding: 25px 20px;
    vertical-align: middle;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-heading);
    transition: 0.4s;
    padding-left: 0;
}

.shop_table.cart.woocommerce-cart-form__contents tbody tr .product-price {
    font-size: 15px;
    font-weight: 600;
    color: var(--color-body);
}

.woocommerce a.remove {
    font-size: 0;
}

.woocommerce a.remove::after {
    content: "\ea02";
    font-family: 'feather' !important;
    font-size: 24px;
    display: inline-block;
    transform: translateY(-2px);
    font-weight: 400;
    color: rgb(25, 35, 53);
}

.woocommerce a.remove {display: block;font-weight: 600;position: relative;width: 50px;background: transparent;height: 50px;margin: 0 auto;line-height: 63px;z-index: 1;}


.woocommerce a.remove::before {
    background: var(--color-gray-light);
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: 0.4s;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    z-index: -1;
}

.woocommerce a.remove:hover::before {
    opacity: 1;
    transform: scale(1);
}

.woocommerce a.remove:hover {
    background: transparent !important;
}

.woocommerce a.remove:hover::after {
    color: var(--color-primary);
}

.woocommerce table.shop_table td {
    text-align: center;
    border: none;
    padding: 25px 20px;
    vertical-align: middle;
    border-bottom: 1px solid var(--color-border);
    padding-left: 0;
}

.woocommerce table.shop_table td span.woocommerce-Price-amount.amount {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: var(--color-primary);
}
.woocommerce .cart button[name="apply_coupon"] {
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    font-weight: 500;
    display: inline-block;
    height: 45px;
    position: relative;
    line-height: 18px;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    outline: none;
    transition: all 0.4s ease-in-out;
}

.woocommerce .cart button[name="apply_coupon"]:hover {
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-7);
    background-position: 102% 0;
}

table.shop_table.shop_table_responsive  input[name="coupon_code"] {
    width: 100%;
    border-radius: 6px;
    height: 45px;
    border: 1px solid var(--color-border);
    line-height: 24px;
    padding: 5px 20px;
    color: var(--color-body);
    background-color: transparent;
}
.woocommerce-cart.woocommerce-page table.cart td.actions .coupon input {
    width: 280px;
    display: block;
    margin-right: 15px;
}
form.woocommerce-cart-form td.actions {
    padding: 20px 20px !important;
}
.woocommerce ul.products li.product, .woocommerce-page ul.products li.product {
    margin-top: 0px;
    margin-bottom: 15px;
}
.woocommerce #review_form #respond p.form-submit {
    margin-bottom: 0;
    margin-top: 30px;
}
.woocommerce #review_form #respond p.comment-notes {
    margin-bottom: 20px;
}
.woocommerce #review_form #respond .form-submit input {
    padding: 0 26px;
    background: var(--color-primary);
    height: 60px;
    line-height: 60px;
    color: var(--color-white);
    font-size: 16px;
    letter-spacing: 0.5px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    border-radius: 6px;
    border: 0 none;
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.woocommerce #review_form #respond .form-submit input:hover {
    transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);
    display: inline-block;
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}
@media (max-width: 767px) {
    .woocommerce-shop  .rbt-default-card.style-three  .inner {
        padding-bottom: 20px    ;
    }
}
@media (max-width: 575px) {
    .woocommerce ul.products[class*=columns-] li.product, .woocommerce-page ul.products[class*=columns-] li.product {
        width: 100%;
    }
    .woocommerce ul.products li.product .rbt-default-card.style-three.rbt-hover .rbt-btn, .woocommerce ul.products li.product .rbt-default-card.style-three.rbt-hover .add_to_cart_button {
        height: 50px;
        line-height: 30px;
    }
}

.woocommerce-page .cart-collaterals .cart_totals {
    background-color: var(--color-white);
    padding: 45px 50px;
    box-shadow: var(--shadow-1);
    border-radius: 6px;
}

.woocommerce-page .cart-collaterals .cart_totals h2 {
    border-bottom: 1px dashed var(--color-border);
    padding-bottom: 10px;
    font-weight: var(--f-bold);
    margin-bottom: 30px;
    font-size: var(--h4);
    line-height: 1.25;
}

.woocommerce-cart .cart-collaterals .cart_totals table {
    border: 0;
}

.woocommerce-cart .cart-collaterals .cart_totals table tr th, .woocommerce-cart .cart-collaterals .cart_totals table tr td {
    border: 0;
    border-bottom: 1px dashed var(--color-border);
}

.woocommerce-cart .cart-collaterals .cart_totals tr.order-total th {
    border-top: 0;
}
.select2-container--default .select2-selection--single {
    box-shadow: none;
    background-color: transparent;
    border: 1px solid var(--color-border);
    height: 45px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 43px;
    text-align-last: left;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 10px;
}

.woocommerce form .form-row input.input-text, .woocommerce form .form-row textarea {
    width: 100%;
    border-radius: 6px;
    height: 45px;
    border: 1px solid var(--color-border);
    line-height: 24px;
    padding: 5px 20px;
    color: var(--color-body);
    background-color: transparent;
}
:where(body:not(.woocommerce-block-theme-has-button-styles)) .woocommerce button.button {
    background: #3557ee !important;
    line-height: 26px;
    margin-top: 25px;
    color: #fff !important;
}
@media (max-width: 767px) {
    .shop_table.cart.woocommerce-cart-form__contents tbody tr {
        padding: 20px 20px;
    }
    
    .shop_table.cart.woocommerce-cart-form__contents tbody tr.woocommerce-cart-form__cart-item.cart_item td {
        background: transparent;
    }
    
    .woocommerce table.cart td.actions .coupon .input-text+.button {
        float: none;
        clear: both;
        margin-top: 60px;
        width: 100%;
    }
    
    .woocommerce-cart.woocommerce-page table.cart td.actions .coupon input {
        width: 100%;
    }
    
    .woocommerce-page table.cart td.actions .button {
        margin-top: 6px;
    }
    .woocommerce-cart .cart-collaterals .cart_totals table tr td {
        background-color: transparent;
    }
    .shop_table.cart.woocommerce-cart-form__contents tbody tr.woocommerce-cart-form__cart-item.cart_item td .quantity label {
        clip: unset;
        position: static !important;
        height: auto;
        width: auto;
        line-height: 1;
        word-wrap: unset;
        display: inline-block;
    }
    
    .shop_table.cart.woocommerce-cart-form__contents tbody tr.woocommerce-cart-form__cart-item.cart_item td .quantity {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    form.checkout_coupon.woocommerce-form-coupon > * {
        width: 100%;
    }
    .woocommerce-checkout .woocommerce-info::before {
        position: static;
    }
    
    .woocommerce-checkout .woocommerce-info {
        padding: 10px 15px;
        line-height: 1.5;
    }
    
    .woocommerce-checkout .woocommerce-info::before {
        display: block;
    }
}
.woocommerce-cart .cart-collaterals .cart_totals table tr:first-child th {
    padding-left: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 23px;
    color: var(--color-body);
    margin: 0;
    padding: 12px 0;
}

.woocommerce ul#shipping_method li label {
    font-size: 16px;
    font-weight: 500;
    line-height: 23px;
    color: var(--color-body);
    margin: 0;
    padding: 12px 0;
}

.woocommerce-cart .cart-collaterals .cart_totals .woocommerce-shipping-destination strong {
    font-size: 16px;
    font-weight: 500;
    line-height: 23px;
    color: var(--color-body);
    margin: 0;
    padding: 12px 0;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    color: var(--color-body);
    margin: 0;
    padding: 12px 20px;
}
body.woocommerce-cart .cart button[name=apply_coupon] {
    margin-top: 0;
}
.checkout_coupon button.button[name="apply_coupon"] {
    margin-top: 0;
}
.body-color {
    color: var(--color-body);
}
.tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item.active a .tutor-dashboard-menu-item-icon {
    color: #3557ee;
}
.tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) .tutor-nav-link {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 500;
}
.tutor-course-ratings .tutor-ratings-average, .tutor-course-ratings .tutor-ratings-count {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    line-height: 1.5;
    font-family: var(--font-primary);
    color: var(--color-body);
    font-weight: 400;
    margin-left: 4px;
    margin-top: 6px;
}
.tutor-dashboard-content-inner .rbt-card-body .rbt-card-title {
    font-size: 16px;
}
.tutor-course-progress span {
    font-size: 14px;
    line-height: 15px;
    color: var(--color-body);
    display: inline-block;
    margin: 7px;
}
.rbt-dashboard-content .content  .tutor-fs-5 {
    margin-bottom: 18px;
    font-size: 20px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--color-border-2);
}
@media (max-width: 1199px) {
    .tutor-dashboard-content-inner .tutor-ratings-stars > * {
        font-size: 10px;
    }
    .tutor-dashboard-content-inner .tutor-course-ratings .tutor-ratings-average, .tutor-course-ratings .tutor-ratings-count {
        margin-top: 0px;
    }
    .tutor-dashboard-content-inner .rbt-bookmark-btn .tutor-course-wishlist-btn {
        font-size: 12px;
        margin-top: -6px;
    }
}
@media (max-width: 991px) {
    table.rbt-table.table.table-borderless {
        width: 920px;
    }
}
.tutor-row.tutor-frontend-dashboard-maincontent .tutor-dashboard-reviews-wrap {
    overflow-x: auto;
}
.tutor-dashboard-content-inner.my-wishlist .tutor-course-ratings .tutor-ratings-average, .tutor-course-ratings .tutor-ratings-count {
    margin-top: 0px;
}
.tutor-ratings.tutor-ratings- .tutor-ratings-average, .tutor-ratings.tutor-ratings- .tutor-ratings-count {
    font-size: 14px;
    color: #fff;
}
.mb-90 {
    margin-bottom: 90px !important;
}
.tutor-course-progress-item.tutor-card .tutor-ratio.tutor-ratio-3x2 {
    height: 100%;
}
.tutor-instructor-apply-button .tutor-bg-primary:hover {
    color: #fff;
}
.woocommerce .dropdown-item.active, .woocommerce .dropdown-item:active {
    background: whitesmoke;
}
@media (min-width: 1200px) and (max-width: 1399px) {
    .content.tutor-dashboard-content .rbt-counterup .inner .content .counters {
        margin: 0 auto;
        font-size: 49px;
    }
   
}
.rbt-single-widget  ul.rbt-sidebar-list-wrapper.categories-list-check input[type="checkbox"] {
    display: none;
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart, .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart {color: var(--color-heading);position: relative;transition: 0.3s;font-size: 14px;font-weight: 500;display: inline-block;border: 0;padding: 0;line-height: 1;height: auto;padding-bottom: 5px; width: auto;}

a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:hover, .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart:hover {
    color: var(--color-primary);
    background: transparent;
}

a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart::after, .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart::after {
	position: absolute;
	content: "";
	left: auto;
	bottom: 0;
	width: 0;
	transition: 0.3s;
	right: 0;
	color: var(--color-heading);
}
.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button:hover {
	color: var(--color-primary);
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:hover::after, .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart:hover::after {
    width: 100%;
    left: 0;
    right: auto;
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:focus, .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart:focus {
    background: transparent;
}
.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button.added {
    display: none !important;
}
.rbt-card.card-list-2 .rbt-card-body {
    margin-top: 0;
}


.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button {
	position: static;
	width: auto;
}

a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    height: 60px;
    line-height: 60px;
    color: #fff;
    font-size: 16px;
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:hover {
    background: var(--color-primary);
    color: var(--color-white);
}
.course-sidebar .rbt-btn.btn-border {
    color: inherit;
    border: 1px solid var(--color-border);
}
.course-sidebar .rbt-btn.btn-border:hover {
    color: var(--color-white);
    border-color: transparent;
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:focus {
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}
.course-action-bottom-right.rbt-single-group > * {
    flex: 0 0 auto;
}

.course-action-bottom-right.rbt-single-group > .rbt-single-list {
    flex: 0 0 220px;
}

.course-action-bottom-right.rbt-single-group > .rbt-single-list button {
    width: 100%;
}
.woocommerce-checkout table.shop_table.woocommerce-checkout-review-order-table tfoot th {
    border-left: 0;
    border-top: 0;
}
.woocommerce-checkout table.shop_table.woocommerce-checkout-review-order-table tfoot .cart-subtotal td {
    border-top: 0;
}

.woocommerce-checkout table.shop_table.woocommerce-checkout-review-order-table tfoot .order-total td {
    border-bottom: 1px solid #e6e3f1;
}
.woocommerce-page .woocommerce-MyAccount-content .col2-set {
    padding-top: 30px;
}
.woocommerce-account .woocommerce-MyAccount-content p:not(:last-child) > span > em {
    display: block;
    margin-top: 11px;
}
.woocommerce-MyAccount-content a.woocommerce-Button.button {
    background-color: inherit;
    padding: 0 22px;
    font-size: var(--font-size-b3);
    height: 45px;
    line-height: 43px;
    transition: all 0.4s ease-in-out;
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    color: #fff;
}

.woocommerce-MyAccount-content a.woocommerce-Button.button:hover {
    background-color: inherit;
    background-position: 102% 0;
    transition: all 0.4s ease-in-out;
}

.rbt-cart-side-menu .rbt-no-cart-item-exits img {
	width: 120px;
	height: auto;
}
.rbt-cart-side-menu .side-nav {
    min-height: 50vh;
	flex-grow: unset;
}
.rbt-cart-side-menu .inner-wrapper {
	justify-content: flex-start;
}

.remove_from_cart_button {
    width: 40px;
    height: 40px;
    line-height: 41px;
    text-align: center;
    border-radius: 100%;
    position: relative;
    z-index: 1;
    background: transparent;
    padding: 0;
    border: 0 none;
    display: block;
}

.remove_from_cart_button:hover, .remove_from_cart_button.open {
    color: var(--color-primary);
}

.remove_from_cart_button::after {
    background: var(--color-gray-light);
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: 0.4s;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    z-index: -1;
}

.remove_from_cart_button:hover::after, 
.remove_from_cart_button.open::after {
    opacity: 1;
    transform: scale(1);
}
.remove_from_cart_button i {
    font-size: 16px;
}

.side-nav.cart-empty {
    min-height: 20vh;
}

.woocommerce a.remove::after {
    transform: translateY(-7px);
}

.woocommerce-cart.woocommerce-page table.cart td.actions .coupon input {
    padding-left: 20px;
}

.woocommerce-cart .woocommerce-error::before {
    top: 29px;
}

.woocommerce-checkout .woocommerce form .form-row input.input-text:focus, 
.woocommerce-checkout .woocommerce form .form-row textarea:focus {
    border-color: var(--color-primary);
} 

.select2-search--dropdown .select2-search__field:focus {
    border-color: var(--color-primary);
}

.select2-container--open .select2-dropdown--below {
    border: 1px solid var(--color-border)!important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    right: 10px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    border: 1px solid var(--color-border);
    border-bottom: 2px solid var(--color-border);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--color-border);
}

@media only screen and ( max-width: 767px ) {
	body .rbt-cart-side-menu {
			top: 0!important;
	}
}
@media (max-width: 991px) {
    .shop_table.cart.woocommerce-cart-form__contents tbody tr .product-name {
        padding-left: 15px;
    }
    .woocommerce table.shop_table_responsive tr td::before, .woocommerce-page table.shop_table_responsive tr td::before {
        margin-right: 10px;
    }
}
a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    background: none !important;
}