/*---------------------
    <PERSON><PERSON><PERSON> Style  
---------------------*/
.rbt-minicart-wrapper {
    padding: 0;
    margin: 0;
    list-style: none;
    .minicart-item {
        display: flex;
        @extend %transition;
        position: relative;
        margin: 0;
        margin-bottom: 20px;
        .close-btn {
            position: absolute;
            right: 0;
            top: 0;
            @media #{$large-mobile} {
                position: static;
            }
            button {
                font-size: 14px;
                top: 10px;
                position: relative;
            }
        }
        &:last-child {
            margin-bottom: 0;
        }

        & + .minicart-item {
            border-top: 1px dashed var(--color-border);
            padding-top: 20px;
        }
    }
    .thumbnail {
        flex-shrink: 0;
        a {
            display: block;
            img {
                width: 80px;
                height: 80px;
                border-radius: var(--radius);
            }
        }
    }
    .product-content {
        flex-grow: 1;
        padding-left: 20px;
        .title {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .quantity {
            font-size: 14px;
            color: var(--color-body);
            display: block;
            .price {

            }
        }
    }
}

.rbt-cart-side-menu {
    .inner-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .side-nav {
        //max-height: 50vh;
        overflow-y: auto;
        flex-grow: 1;
    }
    .rbt-cart-subttotal {
        padding: 15px 0;

        .subtotal {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0;
            color: var(--color-heading);
            font-weight: 700;
            font-size: 18px;
        }
    }
}



.rbt-search-dropdown .rbt-card.variation-01.rbt-hover {
    margin-bottom: 0;
}

.rbt-search-dropdown .col-lg-3:last-child .rbt-card.variation-01.rbt-hover {
    margin-bottom: 20px;
}

@media only screen and (max-width: 767px) {
    .rbt-header-1 .header-right .quick-access {
        margin-top: 4px;
    }

}