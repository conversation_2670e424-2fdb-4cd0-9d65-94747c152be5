.woocommerce-checkout {
    .woocommerce-billing-fields {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        padding: 30px;
        border-radius: 6px;
    }
    .woocommerce-billing-fields > h3 {
        font-size: 20px;
        line-height: 23px;
        text-transform: capitalize;
        font-weight: 700;
        margin-bottom: 30px;
    }
    form .form-row label {
        display: block;
        font-size: 14px;
        margin-bottom: 12px;
        font-weight: 600;
        text-transform: capitalize;
        color: var(--color-heading);
    }
    .woocommerce form .form-row input.input-text, .woocommerce-checkout .woocommerce form .form-row textarea {
        width: 100%;
        background-color: transparent;
        border: 2px solid var(--color-border);
        border-radius: 6px;
        line-height: 23px;
        padding: 10px 20px;
        font-size: 14px;
        color: var(--color-body);
        margin-bottom: 15px;
    }
    .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
        width: 100%;
    }
    .woocommerce-shipping-fields #ship-to-different-address label {
        position: relative;
    }
    
    .woocommerce-shipping-fields #ship-to-different-address label::before {
        content: " ";
        position: absolute;
        top: 6px;
        left: 0;
        width: 14px;
        height: 14px;
        background-color: #fff;
        border: var(--border-width) solid var(--color-border);
        border-radius: 2px;
        transition: all 0.3s;
    }
    
    .woocommerce-shipping-fields #ship-to-different-address label::after {
        content: " ";
        position: absolute;
        top: 9px;
        left: 2px;
        width: 10px;
        height: 5px;
        background-color: transparent;
        border-bottom: var(--border-width) solid #fff;
        border-left: var(--border-width) solid #fff;
        border-radius: 2px;
        transform: rotate(-45deg);
        opacity: 0;
        transition: all 0.3s;
    }
    .woocommerce-shipping-fields #ship-to-different-address input[type=checkbox] {
        display: none;
    }
    .woocommerce-shipping-fields #ship-to-different-address input[type=checkbox]:checked ~ label::before,.woocommerce-shipping-fields #ship-to-different-address input[type=radio]:checked ~ label::before {
        background-color: var(--color-primary);
        border-color: var(--color-primary);
    }
    .woocommerce-shipping-fields #ship-to-different-address input[type=checkbox]:checked ~ label::after,.woocommerce-shipping-fields #ship-to-different-address input[type=radio]:checked ~ label::after {
        opacity: 1;
    }
    .woocommerce-shipping-fields__field-wrapper {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        padding: 30px;
        border-radius: 6px;
    }
    
    .checkout .col-2 h3#ship-to-different-address {display: block;margin-top: -20px;}
    
    .woocommerce-additional-fields {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        padding: 30px;
        border-radius: 6px;
        margin-top: 30px;
    }
    
    .woocommerce-additional-fields textarea {
        min-height: 71px;
    }
    .woocommerce form .form-row input.input-text, .woocommerce form .form-row textarea {
        margin-bottom: 3px;
    }
    table.shop_table.woocommerce-checkout-review-order-table {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        border-radius: 6px;
        padding: 0;
        border: 0;
        box-shadow: none;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table thead {
        background-color: var(--color-primary);
        background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
        background-size: 300% 100%;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table thead th {
        text-align: center;
        border: none;
        font-size: 18px;
        text-transform: inherit;
        font-weight: 500;
        color: var(--color-white);
        padding: 12px 20px;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table thead th:first-child {
        border-radius: 6px 0 0 6px;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table thead th:last-child {
        border-radius: 0 6px 6px 0;
    }
    
    table.shop_table td {
        font-size: 16px;
        font-weight: 600;
        color: var(--color-heading);
        transition: 0.4s;
    }
    
    table.shop_table tr:hover {
        box-shadow: var(--shadow-1);
    }
    
    table.shop_table.woocommerce-checkout-review-order-table tbody tr {
        transition: all .3s;
    }
    
    #order_review {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        padding: 30px;
        border-radius: 6px;
    }
    
    form.checkout.woocommerce-checkout > h3 {
        font-size: 20px;
        line-height: 23px;
        text-transform: capitalize;
        font-weight: 700;
        margin-bottom: 30px;
        margin-top: 24px;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table tfoot th {
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-heading);
        transition: 0.4s;
        font-weight: 600;
    }
    
    table.shop_table.woocommerce-checkout-review-order-table tfoot td {border-bottom: 0;}
    
    .woocommerce-info {
        border-top-color: var(--color-primary);
    }
    
    .woocommerce-info::before {
        color: var(--color-primary);
    }
    
    .place-order p {
        width: 100%;
        background: transparent;
        position: relative;
        line-height: 25px;
        font-weight: 400;
        font-size: 15px;
        line-height: 1.4;
        cursor: pointer;
        margin-bottom: 0;
    }
    
    .place-order {
        width: 100%;
        background: transparent;
        color: var(--body-color);
    }
    
    #payment div.form-row {
        height: auto;
        min-height: auto;
        max-height: max-content;
        margin-top: 70px;
    }
    
    #payment div.form-row {
        float: none;
        background: transparent;
    }
    
    .woocommerce-page #payment #place_order {
        float: none;
    }
   #payment div.form-row.place-order {
        margin-top: 14px;
    }
    #payment {
        background: var(--gradient-1);
    }
    
    #payment ul.payment_methods {
        background-color: transparent;
    }
    
     #payment div.form-row.place-order {
        padding-top: 10px;
        padding-bottom: 26px;
    }
    
     #payment ul.payment_methods {
        padding-bottom: 0;
    }
    .place-order p {
        text-transform: initial;
    }
    .woocommerce form .form-row textarea {
        padding: 8px 10px;
    }
    #payment #place_order, #payment #place_order {float: none;height: 60px;padding: 0 26px;font-size: 16px;letter-spacing: 0.5px;font-weight: 500;display: inline-block;background-size: 300% 100%;}
}
@media (max-width: 767px) {
    .woocommerce-checkout .checkout .col-2 h3#ship-to-different-address {
        margin-top: 30px;
    }
    .woocommerce-shipping-fields__field-wrapper {
        margin-top: 90px !important;
    }
    .woocommerce-billing-fields__field-wrapper > * {
        width: 100%;
        float: none;
    }
}
.single_variation_wrap span.woocommerce-Price-amount.amount {
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
    font-size: 24px;
    font-weight: 700;
}

.woocommerce-orders-table .woocommerce-button.view,
.woocommerce-MyAccount-content .button.wc-forward {
	background: var(--color-primary ) !important;
    color: var(--color-white) !important;
}

.woocommerce-thankyou-order-received {
	background: var(--primary-opacity) ;
	padding: 9px 20px;
	color: var(--color-success);
}

.woocommerce .woocommerce-customer-details address {
    padding: 15px 20px;
    line-height: 30px;
}

.wc-blocks-components-select .wc-blocks-components-select__container {
    border: 1px solid var(--color-border) !important;
}

.wc-block-components-radio-control--highlight-checked .wc-block-components-radio-control-accordion-option--checked-option-highlighted, .wc-block-components-radio-control--highlight-checked label.wc-block-components-radio-control__option--checked-option-highlighted {
    box-shadow: inset 0 0 0 2px var(--color-border)!important;
}

.tutor-dashboard-content-inner.my-wishlist .tutor-bundle-course-count-badge  {
    color: var(--color-white);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
}

.tutor-dashboard-content-inner.my-wishlist .rbt-review .rating span {
    color: var(--color-white);
}

.tutor-dashboard-content-inner.my-wishlist .rbt-review .rating .tutor-ratings-stars span {
    color: #ed9700;
}

.tutor-dashboard .tutor-dashboard-content .tutor-table.tutor-table-quiz-attempts tr th, 
.tutor-dashboard .tutor-dashboard-content .tutor-table.tutor-table-quiz-attempts tr td {
    text-align: right;
}