/*------------------------
    Feature Styles  
-------------------------*/
.rbt-feature {
    display: flex;
    transition: var(--transition-3);
    .icon {
        width: 50px;
        min-width: 50px;
        height: 50px;
        background: var(--color-secondary);
        display: flex;
        border-radius: 100%;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
            color: var(--color-white);
            font-size: 24px;
        }

        &.bg-primary-opacity {
            i {
                color: var(--color-primary);
            }
        }

        &.bg-pink-opacity {
            i {
                color: var(--color-pink);
            }
        }

        &.bg-coral-opacity {
            i {
                color: var(--color-coral);
            }
        }

        &.bg-secondary-opacity {
            i {
                color: var(--color-secondary);
            }
        }
    }

    .number {
        width: 50px;
        min-width: 50px;
        height: 50px;
        display: flex;
        border-radius: 100%;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        position: relative;
        font-weight: var(--f-medium);
        font-size: var(--font-size-b3);
        line-height: 26px;
        color: var(--color-heading);
        transition: var(--transition-3);

        &::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            opacity: 0.1;
            transition: var(--transition-3);
        }

        &::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            transform: scale(0);
            transition: var(--transition-3);
        }

        span {
            position: relative;
            z-index: 2;
        }
    }

    .feature-content {
        .feature-title {
            font-weight: 500;
            font-size: 20px;
            line-height: 32px;
            margin-bottom: 0;
        }
        .feature-description {
            font-weight: 400;
            font-size: 16px;
            line-height: 26px;
            margin-bottom: 0;
            margin-top: 5px;
        }
    }
    & + .rbt-feature {
        margin-top: 30px;
        @media #{$sm-layout} {
            margin-top: 20px;
        }
    }
    &.feature-style-2 {
        padding: 20px;
        &:hover {
            box-shadow: var(--shadow-5);
            transform: scale(1.1);
            @media #{$sm-layout} {
                transform: scale(1);
            }
            .number {
                color: var(--color-white);
            }
           .number::before {
                opacity: 0;
            }
            .number::after {
                transform: scale(1);
            }
        }
        & + .feature-style-2 {
            margin-top: 10px;
        }
    }
}

.rbt-round-icon {
    width: 50px;
    min-width: 50px;
    height: 50px;
    background: var(--color-secondary);
    display: flex;
    border-radius: 100%;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    i {
        color: var(--color-white);
        font-size: 24px;
    }

    &.bg-primary-opacity {
        i {
            color: var(--color-primary);
        }
    }

    &.bg-pink-opacity {
        i {
            color: var(--color-pink);
        }
    }

    &.bg-coral-opacity {
        i {
            color: var(--color-coral);
        }
    }

    &.bg-secondary-opacity {
        i {
            color: var(--color-secondary);
        }
    }

    &.bg-violet-opacity {
        i {
            color: var(--color-violet);
        }
    }

    &.bg-warning-opacity {
        i {
            color: var(--color-warning);
        }
    }
}


.rbt-single-course-features {
    .subtitle {
        margin-top: 40px;
        margin-bottom: 40px;
        @media #{$lg-layout} {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        @media #{$md-layout} {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        @media #{$sm-layout} {
            margin-top: 20px;
            margin-bottom: 20px;
        }
    }
}

.feature-section-02 {
    margin-top: -100px;

    @media #{$lg-layout, $md-layout, $sm-layout, $large-mobile} {
        margin-top: 0;
        padding-top: 80px;
    }
    .icon {
        min-width: 60px; 
    }

}

.rbt-feature-card {
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    border-radius: 10px;
    padding: 30px 30px 40px 30px;
    position: relative;
    &::after {
        content: "";
        position: absolute;
        left: -2px;
        top: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: var(--gradient-8);
        border-radius: 12px;
        z-index: -1;
        opacity: 0;
        transition: var(--transition);
    }

    .icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--color-pink);
        flex: 0 0 auto;
    }
    .card-title {
        font-weight: 500;
        font-size: 20px;
        line-height: 30px;
        color: var(--color-heading);
        margin-bottom: 0;
    }
    .description {
        font-size: 16px;
        line-height: 26px;
        margin-top: 20px;
    }
    &:hover {
        &::after {
            opacity: 1;
        }

        .icon {
            img {
                animation: bounceIn 0.6s ease;
            }
        }
    }
}

@keyframes bounceIn {

    0%,
    20%,
    40%,
    60%,
    80%,
    100% {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(0.3, 0.3, 0.3);
        transform: scale3d(0.3, 0.3, 0.3);
    }

    20% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1);
    }

    40% {
        -webkit-transform: scale3d(0.9, 0.9, 0.9);
        transform: scale3d(0.9, 0.9, 0.9);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03);
    }

    80% {
        -webkit-transform: scale3d(0.97, 0.97, 0.97);
        transform: scale3d(0.97, 0.97, 0.97);
    }

    100% {
        opacity: 1;
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

.rbt-feature-card-two {
    text-align: center;
    padding: 88px 40px 109px;
    position: relative;
    min-height: 410px;

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: var(--histudy-card-shape, url("../../images/shape/feature-card-bg.png"));
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        z-index: -1;
    }

    @media #{$large-mobile} {
        padding: 70px 20px 90px;
        min-height: 360px;
    }
    
    .feature-body {
        max-width: 300px;
        margin: 0 auto;
    }

    .icon {
        width: 60px;
        height: 60px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 30px;
        color: var(--color-white);
    }

    .description {
        font-size: 17px;
        line-height: 28px;
    }
}
.rbt-feature-two {
    .content {
        max-width: 600px;
    }

    ul {
        list-style: none;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        gap: 15px 30px;

        li {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;

            .icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;

                &.bg-primary-opacity {
                    color: var(--color-primary);
                }

                &.bg-secondary-opacity {
                    color: var(--color-secondary);
                }
            }

            .text {
                font-weight: var(--f-medium);
                font-size: 20px;
                line-height: 32px;
                color: var(--color-body);
            }
        }
    }

    .feature-wrap {
        position: relative;
        max-width: 600px;

        .shape-1 {
            position: absolute;
            left: -52px;
            bottom: 62px;
        }

        .shape-2 {
            position: absolute;
            top: -39px;
            right: -52px;
            z-index: -1;

            @media #{$sm-layout, $large-mobile} {
                display: none;
            }
        }

        .since {
            position: absolute;
            right: -68px;
            bottom: 178px;
            min-width: 168px;
            max-width: 200px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(246, 247, 254, 0.46);
            border: 2px solid var(--color-white);
            box-shadow: 0px 7px 40.9px rgba(47, 87, 239, 0.11);
            backdrop-filter: blur(7.15px);
            border-radius: var(--radius-big);
            padding: 17px 20px;

            @media #{$sm-layout, $large-mobile} {
                right: 20px;
                bottom: 100px;
            }

            .title-wrap {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 4px;
            }
            .number {
                font-weight: var(--f-semi-bold);
                font-size: 36px;
                line-height: 25px;
                margin-bottom: 0;
            }

            .subtitle {
                font-weight: var(--f-medium);
                font-size: 14px;
                line-height: 20px;
                color: var(--color-body);
                margin-bottom: 0;
            }
        }

        .satisfied {
            position: absolute;
            left: 30px;
            bottom: 38px;
            background: rgba(255, 255, 255, 0.7);
            border: 2px solid var(--color-white);
            box-shadow: 0px 7px 40.9px rgba(47, 87, 239, 0.11);
            backdrop-filter: blur(7.15px);
            border-radius: 16px;
            min-width: 152px;
            max-width: 200px;
            display: flex;
            align-items: center;
            gap: 10px;
            border-radius: var(--radius-big);
            padding: 20px;

            @media #{$large-mobile} {
                left: 20px;
                bottom: 20px;
            }

            .title-wrap {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 4px;
            }

            .item-title {
                font-weight: var(--f-semi-bold);
                font-size: 20px;
                line-height: 25px;
                color: var(--color-darker);
                margin-bottom: 0;
            }

            .subtitle {
                font-weight: var(--f-medium);
                font-size: 14px;
                line-height: 20px;
                color: var(--color-body);
                margin-bottom: 0;
            }
        }

        .feature-img {
            img {
                border-radius: var(--radius-10);
            }
        }

        .title {
            font-weight: var(--f-regular);
            font-size: var(--font-size-b3);
            line-height: var(--line-height-b1);
            color: var(--color-body);
            text-align: center;
            margin-top: 15px;
            margin-bottom: 0;
        }
    }

    &.version-02 {
        position: relative;

        .u-shape {
            position: absolute;
            bottom: 0;
            right: 126px;

            @media #{$laptop-device} {
                right: 60px;
            }
            @media #{$lg-layout, $md-layout, $sm-layout, $large-mobile} {
                display: none;
            }
        }

        .feature-img-2 {
            max-width: 445px;
            margin-left: auto;
            margin-top: -170px;

            @media #{$large-mobile} {
                margin-top: 30px;
            }

            img {
                border-radius: var(--radius-10);
                border: 3px solid var(--color-white);
            }
        }

        .since {
            right: 82px;
            top: 108px;
            bottom: auto;

            @media #{$large-mobile} {
                right: 20px;
                top: 240px;
            }
        }

        .shape-1 {
            left: -56px;
            top: 128px;

            @media #{$lg-layout, $md-layout, $sm-layout, $large-mobile} {
                display: none;
            }
        }
    }
}