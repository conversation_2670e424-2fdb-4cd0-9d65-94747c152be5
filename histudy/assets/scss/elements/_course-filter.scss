/*---------------------------
    Filter Exanded  
-----------------------------*/
.default-exp-wrapper {
    border-top: 1px solid var(--color-border);
    .default-exp-expand.histudy-filter-style-1 .rbt-modern-select .bootstrap-select button.btn-light {
        box-shadow: none;
    }
    .filter-inner {
        display: flex;
        padding: 40px 0;
        justify-content: flex-end;
        flex-wrap: wrap;

        @media #{$lg-layout} {
            justify-content: flex-start;
        }

        @media #{$md-layout} {
            justify-content: start;
        }

        @media #{$sm-layout} {
            justify-content: start;
        }

        .filter-select-option {
            flex-basis: calc(20% - 16px);
            position: relative;
            @media #{$lg-layout} {
                flex-basis: 33.33%;
            }

            @media #{$md-layout} {
                flex-basis: 50%;
            }

            @media #{$sm-layout} {
                flex-basis: 50%;
            }

            @media #{$large-mobile} {
                flex-basis: 100%;
            }

            .filter-leble {
                display: block;
                font-size: 12px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 10px;
            }

            .nice-select {
                width: 100%;
                padding-left: 20px;
                padding-right: 40px;

                &::after {
                    right: 22px;
                    height: 8px;
                    width: 8px;
                    opacity: 0.5;
                }

                .list {
                    min-width: 100%;
                    max-height: 300px;
                    overflow-y: auto;
                }
            }
        }
    }
    .filter-select { 
        select {
            width: 100%;
            min-width: 100%;
        }
    }
}

.default-exp-expand {
    display: none;
}




/*-------------------------
    Price Filter  
--------------------------*/

.price--output input {
    border: 0 none;
}

.price--output span {
    color: var(--color-body);
    font-size: 14px;
}

.price--filter {
    display: inline-block;
}

.price--output {
    display: inline-block;
}
.ui-widget-content {
    background: var(--color-gray-lighter) none repeat scroll 0 0;
    border: 0 none;
    color: var(--color-primary);
}

.ui-slider-range.ui-widget-header.ui-corner-all {
    background: var(--color-primary) none repeat scroll 0 0;
}

.ui-slider-horizontal {
    height: 4px;
}

.ui-slider .ui-slider-handle {
    border-radius: 100%;
    cursor: default;
    height: 10px;
    position: absolute;
    touch-action: none;
    width: 10px;
    z-index: 2;
    outline: none;
    cursor: pointer;
}

.ui-slider-horizontal .ui-slider-handle {
    top: -3px;
    margin-left: -3px;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default {
    background: var(--color-primary) none repeat scroll 0 0;
    border: 0 none;
    color: var(--color-primary);
}

.ui-slider-range.ui-widget-header.ui-corner-all {
    background: var(--color-primary) none repeat scroll 0 0;
}

.ui-slider-horizontal {
    margin-bottom: 8px;
    margin-top: 15px;
}


.price__output--wrap {
    display: flex;
    align-items: center;
    .price--output {
        display: flex;
        align-items: center;
        flex-grow: 1;
        span {
            color: var(--color-body);
            font-size: 12px;
            display: inline-block;
            letter-spacing: 0.5px;
            font-weight: 600;
            opacity: 0.5;
            text-transform: uppercase;
        }
        input {
            border: 0 none;
            flex-grow: 1;
            font-size: 12px;
            font-weight: 400;
            width: 100px;
            letter-spacing: 0.5px;
            box-shadow: none;
            font-weight: 600;
            opacity: 0.5;
        }
    }
    .price--filter {
        display: inline-block;
        margin-top: -2px;
        a {
            &.rbt-btn {
                padding: 6px 10px;
                font-size: 12px;
                letter-spacing: 0.5px;
                text-transform: uppercase;
                border-radius: 6px;
                line-height: 16px;
                height: auto;
            }
        }
    }
}


/*-------------------
    Bootstrap Select  
----------------------*/

.rbt-modern-select {
    .bootstrap-select button.btn-light {
        border: 0 none;
        box-shadow: var(--shadow-1);
        min-height: 50px;
        padding: 10px 20px;
        outline: none;
        color: var(--color-body);
        border-radius: var(--radius);
        font-size: 16px;
        line-height: 28px;
        font-weight: 400;
        padding-right: 30px;
        background-color: var(--color-white);
        outline: none;
    }
    &.bg-transparent {
        .bootstrap-select button.btn-light {
            box-shadow: none;
            background-color: transparent;
            border: 1px solid var(--color-border);
        }
    }
    &.height-45 {
        .bootstrap-select button.btn-light {
            height: 45px;
        }
    }
    .bootstrap-select {
        button {
            &.actions-btn {
                padding: 7px 6px;
                font-size: 13px;
                box-shadow: none;
                background: #f8f9fa;
                min-height: 38px;
                line-height: 24px;
                transition: 0.4s;
                &:hover {
                    background: var(--color-primary);
                    color: var(--color-white);
                }
            }
        }
    }
    .bootstrap-select .dropdown-menu.inner {
        display: block;
        padding: 10px;
        margin: 0;
        max-height: 450px;
    }
    .bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
        top: 11px;
    }
    .dropdown-menu {
        padding: 0;
        box-shadow: var(--shadow-1);
        border: 0 none;
        border-radius: 6px !important;
        background-color: var(--color-white);
        min-width: 100%;
        max-width: 100%;
    }
    .dropdown-item {
        padding: 5px 10px;
        color: var(--color-body);
        border-radius: 4px;
    }
    .bootstrap-select {
        .dropdown-menu {
            li {
                margin: 0;
                padding: 0;
                
                &.no-results {
                    padding: 4px 8px;
                    background: transparent;
                    white-space: nowrap;
                    font-size: 16px;
                    color: var(--color-white);
                    padding-bottom: 10px;
                }
            }
        }
    }
    .dropdown-item.active, 
    .dropdown-item:active {
        background-color: var(--black-opacity);
        color: var(--color-primary);
    }
    .bootstrap-select .dropdown-menu li a span.text {
        font-size: 16px;
    }
    .bootstrap-select .dropdown-toggle .filter-option {
        display: flex;
        width: 100%;
        position: relative;
        flex: inherit;
        min-width: 100%;
        align-items: center;
    }
    .filter-option-inner {
        display: block;
        width: 100%;
    }
    .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
        overflow: hidden;
        display: block;
    }
    .bootstrap-select .dropdown-toggle:focus {
        outline: none !important;
    }
    .dropdown-toggle::after {
        border-top: 5px solid;
        border-right: 5px solid transparent;
        border-bottom: 0;
        border-left: 5px solid transparent;
        opacity: 0.5;
    }
    
    .btn-check:active+.btn-light:focus, 
    .btn-check:checked+.btn-light:focus, 
    .btn-light.active:focus, 
    .btn-light:active:focus, 
    .show>.btn-light.dropdown-toggle:focus {
        box-shadow: none;
    }
    .bs-searchbox .form-control {
        outline: none;
        box-shadow: none;
        border: 2px solid var(--color-border);
        border-radius: 6px;
        margin-bottom: 2px;
        font-size: 16px;
    }
    .dropdown-item:focus, .dropdown-item:hover {
        color: var(--color-primary);
        background-color: var(--black-opacity);
    }
    .btn-group>.btn-group:not(:last-child)>.btn, 
    .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
        border-top-right-radius: var(--radius);
        border-bottom-right-radius: var(--radius);
    }
    .bs-actionsbox, 
    .bs-donebutton, 
    .bs-searchbox {
        padding: 10px;
        padding-bottom: 5px;
    }
    .bs-searchbox + .bs-actionsbox {
        padding: 0 10px 4px;
    }
    .bs-actionsbox .btn-group button {
        width: calc(50% - 10px);
        margin: 5px;
        margin-left: 5px !important;
    }
    .bs-actionsbox .btn-group {
        display: block;
        margin: -5px;
    }

    .bootstrap-select>select {
        left: 0;
    }
    
}
.selected_course_filters.histudy-selected-course-filters-114{
    & ul {
        padding-left: 0;
        & li {
            color: #fff;
            list-style: none;
            & span {
                color: #fff;
                list-style: none;
                display: inline-block;
                margin-right: 17px;
            }
        }
    }
}

.selected_course_filters.histudy-selected-course-filters-114 {
	ul {
		li {
            display: inline-block;
            margin-right: 7px;
			span {
				display: inline-block;
				background: #2f57f2;
				line-height: 1;
				height: 20px;
                font-size: 16px;
				width: 16px;
				text-align: center;
				margin-left: 7px;
			}
		}
	}
}
button.btn.filter_reset {
    background: #2f57f2;
    color: #fff;
    font-size: 12px;
}
button.load_more_btn.btn-fill {
    background: var(--color-primary);
    height: 50px;
    display: inline-block;
    width: auto;
    border-radius: 50px;
    padding: 0 30px;
    border: 0;
    color: #fff;
}
.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button, .rbt-card.variation-01.rbt-hover a.tutor-btn.added_to_cart {
    padding: 0 14px;
    height: 33px;
    display: inline-block;
    line-height: 33px;
    width: auto;
    &.added {
        display: none;
    }
}
.rbt-tutor-archive-section-wrap-padding {
    padding-bottom: 65px;
}

.filter-select-option .bootstrap-select>.dropdown-toggle.bs-placeholder {
    box-shadow: none !important;
}
.rbt-course-top-wrapper .default-exp-wrapper .filter-inner .bootstrap-select {
    border-radius: var(--radius);
    border: 0;
    padding: 0 0;
}
button.rbt-filter-rating-toggle {
    height: 50px;
    width: 100%;
    font-size: 14px;
    border-radius: var(--radius);
    background: #fff;
    border: 0;
    text-align: left;
    padding: 0 12px;
    margin-bottom: 2px;
    color: #837395;
}
.rbt-single-widget.rbt-widget-rating {
    display: none;
}
.rbt-single-widget.rbt-widget-rating {
    position: absolute;
    width: 100%;
    z-index: 9;
    box-shadow: 0 0 15px rgba(0,0,0,0.05);
}
.tutor-ratings-count {
    color: var(--tutor-body-color);
    font-weight: 500;
    margin-left: 12px;
}
.rbt-breadcrumb-default.rbt-breadcrumb-style-3  .tutor-ratings-count {
    display: none;
}
.rbt-course-top-wrapper .rbt-single-widget.rbt-widget-rating.rbt-single-rating-widget-padding {
    padding: 10px;
}
.rbt-sidebar-list-wrapper.rating-list-check > .rbt-check-group {
	margin-top: 0;
	margin-bottom: 4px;
}

.tutor-course-archive-page .alert-warning {
    margin-left: 18px;
    color: #a94442;;
}
.page-home-online-courses .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 200px!important;
}

.page-home-online-courses .dropdown-item.active {
    background-color: var(--black-opacity)!important;
    color: var(--color-primary)!important;
}
.post-type-archive-courses .archive.course_block .load_more_button {
    margin-top: 60px;
    position: relative;
    z-index: 3;
}
.page-home-online-courses .header-info .shopping-cart {
	margin-right: 20px;
}
.course_archive_page_identifier .load_more_button {
    margin-top: 0;
}

.histudy-blog-featured-right-list:last-child {
	margin-bottom: 0;
}

.bt-banner-inner-layout-1 .rbt-card .rbt-card-body {
    margin-top: 30px;
}

.courses-school-extra a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
	height:unset;
	line-height:unset;
}

@media only screen and (min-width: 768px) and (max-width: 1199px) {
.rainbow-has-online-class-card .kindergarden-event-card .rbt-card .rbt-card-body {
			padding-top: 20px;
	}
}

@media (max-width: 767px) {
	.rbt-blog-area .histudy-post-wrapper > .row > *:nth-child(1) .mt--30, .rbt-blog-area .histudy-post-wrapper > .row > *:nth-child(2) .mt--30 {
			margin-top: 0px !important;
	}
	.rainbow-has-online-class-card .kindergarden-event-card .rbt-card .rbt-card-body {
			padding-top: 15px;
	}
	.rbt-rbt-blog-area .rbt-card.card-list .rbt-card-img img {
		height: 100%;
	}
}
.home-univeristy .rb-has-arrow-btn a::after {
	display: none;
}

.rainbow-featured-single-tutor-course .rbt-badge.variation-03{
	transition: all 0.3s ease;
} 

.custom-course-single .rbt-course-main-content .course-content-right {
	gap: 8px;
}

.rainbow-featured-single-tutor-course .tutor-course-content-list-item {
	padding: 5px 0;
}


.rbt-course-top-wrapper .selected_course_filters  ul {
	margin-bottom: 0px;
}
.post-type-archive-courses .rbt-page-banner-wrapper.show-course-archive-tab {
		padding-bottom: 200px;
}

.post-type-archive-courses .rbt-page-banner-wrapper  {
		padding-bottom: 225px;
}
.rainbow-featured-single-tutor-course .tutor-course-content-list-item {
	padding: 5px 0;
}

.custom-course-single .rbt-course-main-content .course-content-right {
	gap: 8px;
}

.rbt-featured-course-single-1 .tutor-course-content-list-item .rbt-badge {
	text-align: center;
}
.rbt-featured-course-single-1 .tutor-course-content-list-item .rbt-badge i {
		display: none;
        @media #{$sm-layout} {
           display: block;
        }
}
.rbt-featured-course-single-1 .tutor-course-content-list-item div:first-child {
	padding-right: 20px;
}
.rbt-course-main-content li .course-content-right > a {
	margin-right: -10px;
}

.rainbow-featured-single-tutor-course .rbt-badge.variation-03{
	transition: all 0.3s ease;
} 

.rbt-search-dropdown #rbt-course-search-wrapper-layout-1 .rbt-card-title {
	font-size: 16px!important;
}
.rbt-search-dropdown #rbt-course-search-wrapper-layout-1 {
	overflow-y: unset;
}

.tutor-btn-secondary[disabled], .tutor-btn-secondary.disabled {
	border-color: rgba(var(--tutor-color-primary-rgb), 0.1);
    background-color: rgba(var(--tutor-color-primary-rgb), 0.1);
	color: var(--tutor-color-primary);
	opacity: 0.6;
}

.tutor-single-course-content-next .tutor-btn:hover,
.tutor-single-course-content-prev .tutor-btn:hover {
	color: var(--tutor-color-primary);
}

@media only screen and (min-width: 1400px) {
    .histudy-reg-img {
        max-height: 695px;
    }
}

@media only screen and (max-width: 991px) {
    .course_archive_page_identifier .load_more_button   {
        margin-top: -20px;
    }
    .ptt-120 {
        padding-top: 80px;
    }
    .post-type-archive-courses .rbt-course-top-wrapper .col-md-6  {
        width: 100%;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
        margin-top: 0px;
    }

    .rbt-section-overlayping-top.tutor-course-archive-page, 
    .rbt-course-event-area.rbt-section-overlayping-top.rbt-section-gapBottom {
        margin-top: -230px;
    }
}

.innovative-banner-area .rbt-banner-area.rbt-banner-7 {
    padding-top: 140px;
}

@media only screen and ( max-width: 767px ) {
    .rbt-sorting-list .rbt-search-style input,.rbt-sorting-list .rbt-search-style  {
        width: 300px;
        min-width: 300px;
    }
    .post-type-archive-courses .rbt-page-banner-wrapper {
        padding-bottom: 180px;
    }
    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item {
        flex: unset;
    }
    .post-type-archive-courses .rbt-sorting-list {
        gap: 15px;
    }
    .default-exp-wrapper .filter-inner .filter-select-option {
        padding: 10px;
    }
    .rbt-course-area .load-more-btn .rbt-btn.btn-lg {
        height: 70px;
        line-height: 70px;
        font-size: 18px;
	}

}
/**
* Course details
*/
.single.single-courses footer.rbt-footer.footer-style-1.has-rainbow-footer-style-1 {
    padding-top: 120px;
}
.single-lesson .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comment-box .tutor-comment-textarea {
    border: 0;
}
.rbt-sidebar-list-wrapper.has-show-more-inner-content.instructor-list-check.rbt-list {
    max-height: 124px !important;
    overflow-y: auto;
}
.rbt-single-widget.rbt-widget-instructor.has-show-more.active ul.rbt-sidebar-list-wrapper.has-show-more-inner-content.instructor-list-check.rbt-list {
    max-height: max-content !important;
}

.rbt-dot-bottom-center .rbt-swiper-pagination {
    position: absolute;
    bottom: 0;
    left: 50% !important;
    width: max-content !important;
    transform: translateX(-50%) !important;
}
.swiper-cards .swiper-slide {
    overflow: visible !important;
}

.rbt-course-filter-modal.open .rbt-mobile-course-archive-filter .filter-select-option.rbt-show-orderby-front {
    display: none;
}

.wc-block-components-notice-banner>.wc-block-components-button {
    height: 24px !important;
    line-height: 0!important;
}

.wc-block-components-notice-banner.is-error>svg {
    margin-top: 3px;
}

button.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button.wc-block-components-checkout-place-order-button--full-width.contained:focus {
    box-shadow: unset !important;
}

.woocommerce-cart .wc-block-components-spinner {
	position: static!important;
}


@media only screen and ( max-width: 991px ) {
    .rbt-course-details-right-sidebar {
        margin-bottom: 60px;
    }

    .custom-course-details-right-sidebar {
        margin-top: 60px;
    }
}

@media only screen and (max-width: 767px) {
    .rbt-banner-area.rbt-banner-1 {
        padding: 0 15px;
        padding-top: 60px;
        padding-bottom: 50px;
    }

    .active-dark-mode .rbt-breadcrumb-default.rbt-breadcrumb-style-3::before {
        z-index: 0!important;
    }
}



/*-------------------
    Tab Button  
----------------------*/

.rbt-course-tab-button {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px 16px;
    position: relative;
    z-index: 1;

    li {
        button {
            color: var(--color-body);
            font-family: var(--font-primary);
            font-size: 15px;
            font-weight: var(--f-semi-bold);
            line-height: var(--line-height-b4);
            padding: 14px 24px;
            white-space: nowrap;
            border-radius: var(--radius-10);
            transition: var(--transition);
            border: none;
            position: relative;
            z-index: 1;
            background: transparent;
    
            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                color: var(--color-white);
                border-radius: var(--radius-10);
                z-index: -1;
                scale: 0.2;
                opacity: 0;
                transition: 0.3s;
                background: radial-gradient(64.58% 59% at 37.13% 12%, rgba(255, 255, 255, 0.315) 0%, rgba(255, 255, 255, 0) 55.41%, rgba(255, 255, 255, 0) 100%), var(--color-primary);
                box-shadow: 4px 10px 20px rgba(81, 99, 255, 0.26), inset -3px -4px 7px rgba(255, 255, 255, 0.15);
            }

            .icon {
                img {
                    margin-right: 8px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    display: inline-block;
                }
            }
    
            &.active {
                color: var(--color-white);
                background: none;
    
                &::before {
                    opacity: 1;
                    scale: 1;
                }

            }
    
            &:not(.active) {
                &:hover {
                    color: var(--color-primary);
                }
            }
        }
    }
}
