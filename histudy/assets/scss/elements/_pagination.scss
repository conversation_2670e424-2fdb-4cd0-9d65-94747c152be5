/*----------------------------
    Edu Pagination Style  
------------------------------*/
.rbt-pagination {
    margin: -8px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    @media #{$sm-layout} {
        margin: -4px;
    }
    ul{
        &.page-numbers,
        &.page-list{
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 0;
        }
    }
    li {
        margin: 8px;
        @media #{$sm-layout} {
            margin: 4px;
        }
        a, .current {
            width: 45px;
            height: 45px;
            background: var(--color-white);
            border-radius: 6px;
            text-align: center;
            color: var(--color-body);
            transition: 0.4s;
            font-weight: 500;
            box-shadow: var(--shadow-1);
            display: flex;
            align-items: center;
            justify-content: center;
            @media #{$sm-layout} {
                width: 45px;
                height: 45px;
            }
            i {
                font-size: 22px;
                font-weight: 500;
            }
        }
        .current{
            background: var(--color-primary);
            color: var(--color-white);
        }
        &.active,
        &:hover {
            a {
                background: var(--color-primary);
                color: var(--color-white);
            }
        }
    }
}
.rbt-course-event-area ul.rbt-pagination ul.page-numbers {
    display: flex;
    align-items: center;
}
.rbt-course-event-area ul.rbt-pagination ul.page-numbers > li > span {
    width: 45px;
    height: 45px;
    background: var(--color-white);
    border-radius: 6px;
    text-align: center;
    color: var(--color-body);
    transition: 0.4s;
    font-weight: 500;
    box-shadow: var(--shadow-1);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-primary);
    color: #fff;
}