.rbt-contact-form {
    &.contact-form-style-1 {
        padding: 50px;
        position: relative;
        z-index: 1;
        max-width: 490px;
        padding-left: 60px;
        z-index: 2;
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        border-radius: var(--radius);
        @media #{$lg-layout} {
            padding: 30px 35px;
        }
        @media #{$md-layout} {
            max-width: inherit;
        }
        @media #{$sm-layout} {
            padding: 30px 20px;
            padding-left: 20px;
            max-width: inherit;
        }
        .callto-action-wrapper {
            span{
                &.text{
                    font-size: 15px;
                    line-height: 24px;  
                    display: inline-block;
                    padding-right: 10px;
                }
                i { 
                    font-size: 22px;
                    font-weight: 400;
                    color: var(--color-heading);
                    line-height: 26px;
                    padding-right: 4px;
                }
                a {
                    font-size: 22px; 
                    font-weight: 700;
                    color: var(--color-heading);
                    line-height: 26px;
                    text-decoration: none;
                    @extend %transition;
                    @media #{$sm-layout} {
                        font-size: 14px;
                    }
                    &:hover{
                        color: var(--color-primary);
                    }
                }
            }
        }
        .shape-group {
            @media #{$lg-layout} {
                display: none;
            }
           
            .shape {
                position: absolute;
                right: -193px;
                top: 50%;
                z-index: -1;
                @media #{$lg-layout} {
                    right: -151px;
                }
                @media #{$sm-layout} {
                    display: none;
                }
                &.shape-01 {
                    right: -64px;
                    top: 26%;
                }
            }
        }
    }
}

/* Form Group  */
.form-group {
    position: relative;
    z-index: 2;
    margin-bottom: 30px;
    display: block;
    label {
        position: absolute;
        left: 0;
        width: 100%;
        top: 9px;
        color: var(--color-body);
        transition: 0.3s;
        z-index: -1;
        letter-spacing: 0.5px;
        font-size: 18px;
        line-height: 28px;
    }

    input,
    textarea {
        border: 0;
        border-bottom: 2px solid var(--color-border);
        background-color: transparent;
        padding-bottom: 8px;
        padding-top: 20px;
        border-radius: 0;
        padding-left: 0;
        padding-right: 0;
    }
  

    input[type="submit"] {
        border: 2px solid var(--color-heading);
        background: var(--color-white);
        color: var(--color-heading);
        font-size: 16px;
        font-weight: 700;
        height: 46px;
        line-height: 44px;
        padding: 0;
        text-transform: capitalize;
        @extend %transition;
        &:hover {
            background: var(--color-heading);
            color: var(--color-white);
        }
    }
    textarea {
        border: 0;
        padding-bottom: 8px;
        padding-top: 8px;
        border-bottom: 2px solid var(--color-border);
        background-color: transparent;
        resize: none;
        min-height: 133px;
    }
    span {
        &.focus-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--color-primary);
            transition: 0.4s;
        }
    }
    &.focused {
        label {
            top: -10px;
            font-size: 12px;
            color: var(--color-primary);
            transition: 0.3s;
        }
        span {
            &.focus-border {
                width: 100%;
                transition: 0.4s;
            }
        }
    }
}

.rbt-contact-form.contact-form-style-1 textarea {
    height: 133px;
    min-height: 133px;
}
.rbt-contact-me .rb-radius-10-img img {
    width: 100%;
}
.tutor-instructor-apply-button a.tutor-bg-primary {
    background: var(--color-primary);
}

.tutor-instructor-apply-button a.tutor-bg-primary:hover {
    background-color: var(--color-secondary);
}

span.tutor-app-process-subtitle {
    line-height: 28px;
    margin-bottom: 25px;
    font-size: 18px;
    color: var(--color-body);
}
.wpcf7-spinner {
	margin: 0;
	width: 0;
}
.wpcf7 form .wpcf7-response-output {
    margin: 30px 0 0;
}
.wpcf7 form .wpcf7-response-output {
	border-color: var(--color-primary)!important;
}

.mc4wp-response .mc4wp-alert.mc4wp-error {
    margin-top: 15px;
}

.tutor-instructor-application-body span.tutor-app-process-title {
    font-size: var(--h4);
    line-height: 1.25;
    font-weight: 700;
    font-family: 'Euclid Circular';
}

.wpcf7 form .wpcf7-response-output {
	border: none;
    background: var(--primary-opacity) !important;
    padding: 10px 20px;
	border-radius: 4px;
}
.wpcf7 form.invalid .wpcf7-response-output {
	color: #dc3232;
}

.rbt-newsletter-area .mc4wp-alert.mc4wp-error,.rbt-newsletter-area .mc4wp-alert.mc4wp-error a,.rbt-newsletter-area .mc4wp-alert.mc4wp-error a:hover {
	color: #9b0101;
}

.newsletter-style4 .rbt-btn ,.subscribe-form-custom input[type="email"]{
	border-radius: 500px !important;
}

.widget_mc4wp_form_widget input[type="email"]{
	border: 1px solid var(--color-border) !important;
    border-radius: 500px;
}

.widget_mc4wp_form_widget  .newsletter-form-1 .rbt-btn {
    border-radius: 500px;
}
.widget_mc4wp_form_widget  .newsletter-form-1 {
	margin-top: 30px!important;
}

.widget_mc4wp_form_widget .icon-reverse-wrapper .btn-icon {
	display: none;
}
.widget_mc4wp_form_widget .rbt-btn:hover.hover-icon-reverse .btn-text {
    transition-delay: 0.1s;
    transform: translateX(0px);
}
.widget_mc4wp_form_widget .rbt-btn.hover-icon-reverse .btn-icon + .btn-icon {
	display:none;
}

.widget_mc4wp_form_widget .rbt-btn.hover-icon-reverse .btn-text {
    margin-inline-start: 0;
}
.widget_mc4wp_form_widget .newsletter-form-1 input {
    height: 60px;
    line-height: 60px;
}
.widget_mc4wp_form_widget .newsletter-form-1 .rbt-btn {
    right: 7px;
}