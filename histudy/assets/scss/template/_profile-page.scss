.single-view-course{
    padding-top: 80px;
    padding-bottom: 0px;
    .single-profile{
        padding-bottom: 62px;
        position: relative;
        border-bottom: 1px solid #DCE3EE;
        .single-course-content{
            padding-left: 20px;
            align-self: center;
            h2.title{
                font-size: 36px;
                line-height: 48px;
                margin-bottom: 0;
            }
            p.title-text{
                color: var(--color-body);
                font-size: var(--font-size-b3);
                margin-bottom: 20px;
            }
            p.content-text{
                color: var(--color-body);
                font-size: var(--font-size-b3);
                margin-right: 30px;
                max-width: 600px;
            }
            .follow-section{
                display: flex;
                justify-content: flex-start;
                align-items: center;
                p.follow-text{
                    color: var(--color-primary);
                    font-size: var(--font-size-b3);
                    margin-right: 20px;
                    margin-bottom: 0;
                }
                ul.social-icons{
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    li{
                        margin: 0;
                        color: var(--color-white);
                        display: inline-block;
                        a{
                            display: flex;
                            width: 40px;
                            height: 40px;
                            line-height: 40px;
                            font-size: var(--font-size-b3);
                            border-radius: 100%;
                            background: var(--color-lighter);
                            border: none;
                            color: var(--color-heading);
                            justify-content: center;
                            align-items: center;
                        }
                        a:hover{
                            background: var(--color-secondary);
                            border-color: transparent;
                            color: var(--color-white);
                        }
                    }
                    li+li{
                        margin-left: 5px;
                    }
                }
            }
        }
    }
    .contact-section{
        padding: 85px 0;
        h3.title{
            font-size: var(--font-size-b1);
            margin-bottom: 40px;
        }
        .single-icon-box{
            padding: 28px 0;
            padding-top: 20px;
            border-radius: 6px;
            box-shadow: 0px 50px 25px -40px rgba(25, 35, 53, 0.08);
            @media #{$small-mobile} {
                .single-icon-box{
                    margin-top: 20px;
                }
            }
            .single-icon{
                font-size: 50px;
                line-height: 50px;
                color: var(--color-secondary);
            }
            p.icon-text{
                margin-top: 20px;
                font-size: 18px;
                color: var(--color-body);
                a{
                    font-size: 18px;
                    color: var(--color-body);
                    &:hover{
                        color: var(--color-primary);
                    }
                }
            }
        }
    }
}