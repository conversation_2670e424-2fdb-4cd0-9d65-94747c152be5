/*-------------------------
    Course Details  
--------------------------*/

/*-------------------------
    Single Course Meta  
--------------------------*/

.rbt-single-course-meta {
    .rbt-course-review {
        margin-left: 15px;
    }
}


/*---------------------------
    Video Course Content  
----------------------------*/
.rbt-course-main-content {
    li {
        a {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;

            @media #{$large-mobile} {
                display: block;
            }
        }

        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .course-content-left {
        display: flex;
        align-items: center;

        @media #{$large-mobile} {
            margin-bottom: 12px;
        }

        i {
            padding-right: 6px;
        }

        span {
            font-size: 16px;
        }
    }

    .course-content-right {
        margin: 0 -5px;
        margin-bottom: -10px;
        display: flex;

        span {
            display: inline-block;
            margin: 0 5px;
            margin-bottom: 10px;
        }

        .min-lable,
        .course-lock {
            font-size: 16px;
        }

        &.only-lock {
            margin: 0;
            margin-bottom: -10px;
        }
    }
}

.about-author-list {
    .about-author {
        &:last-child {
            border-bottom: 0;
            padding-bottom: 0;
        }
    }
}

.rbt-instructor {
    .about-author {
        .media {
            @media #{$large-mobile} {
                display: block;
            }
        }

        .thumbnail {
            a {
                img {
                    margin-bottom: 0;
                    min-width: 250px;
                    max-height: 250px;
                    object-fit: cover;
                    border-radius: 100%;

                    @media #{$large-mobile} {
                        margin-bottom: 0;
                        min-width: 100%;
                        max-height: initial;
                        margin-right: 0;
                    }
                }
            }
        }

        .media-body {
            @media #{$large-mobile} {
                margin-top: 20px;
            }

            .rbt-meta {
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                li {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}


.video-course-content {
    li {
        a {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}




/*----------------------------
    Rbt rating Styles  
------------------------------*/

.rating-box {
    border-radius: 5px;
    background: var(--warning-opacity);
    text-align: center;
    padding: 22px 10px;
    padding-top: 10px;

    .rating-number {
        font-weight: 800;
        font-size: 66px;
        line-height: 80px;
        color: var(--color-heading);
    }

    span {
        font-weight: 500;
        font-size: 16px;
        line-height: 26px;
    }

    .rating {
        svg {
            color: var(--color-warning);
        }
    }

    .sub-title {
        color: var(--color-warning);
    }
}


.rating-text {
    display: inline-block;
    position: relative;
    top: 14px;

    svg {
        color: var(--color-warning);
    }
}

.review-wrapper {
    .single-progress-bar {
        position: relative;
        top: -14px;
    }

    .progress {
        max-width: 70%;
        margin-left: 115px;
        height: 6px;
        background: #EEEEEE;
        border-radius: 0;

        @media #{$lg-layout} {
            max-width: 80%;
        }

        .progress-bar {
            background-color: var(--color-warning);
            border-radius: 0;
        }
    }

    span {
        &.value-text {
            position: absolute;
            right: 0;
            top: 50%;
            font-weight: 500;
            font-size: 16px;
        }
    }
}



/*---------------------------------
    Featured Review List Wrapper  
-----------------------------------*/

.rbt-course-review {
    &.about-author {
        padding: 25px 0;

        &:first-child {
            padding-top: 0;
        }

        .rating {
            margin-top: 5px;
            margin-bottom: 10px;
        }
    }
}


/*---------------------------
    Course Top  
----------------------------*/

.rbt-course-top-wrapper {
    .select-label {
        opacity: 0.8;
        color: var(--color-heading);
    }

    .default-exp-wrapper {
        border-top: 1px solid var(--color-border-2);
        margin-top: 30px;

        .filter-inner {
            padding-bottom: 0;

            .bootstrap-select {
                width: 100% !important;
                max-width: 245px;
            }
        }

        &.top-border-less {
            border: 0 none;
            margin-top: 0;
        }
    }

    .price__output--wrap {
        .price--output {

            input,
            span {
                opacity: 0.8;
                color: var(--color-heading);
            }
        }
    }

    .ui-widget-content {
        background: var(--white-opacity) none repeat scroll 0 0;
    }
}

.rbt-sorting-list {
    .rbt-short-item {
        margin: 10px 10px;
    }

    .course-switch-layout {
        @extend %liststyle;
        display: flex;
        align-items: center;
        background: var(--white-opacity);
        border-radius: 500px;
        padding: 8px;
        margin: 0;

        li {
            margin: 0;

            button {
                background: transparent;
                display: block;
                padding: 7px 16px;
                border-radius: 500px;
                font-size: 16px;
                transition: 0.4s;
                border: 0 none;
                box-shadow: none;

                i {
                    font-size: 14px;
                    transition: 0.4s;
                }

                &.active {
                    background: var(--color-white);
                    color: var(--color-primary);

                    i {
                        color: var(--color-primary);
                    }
                }
            }
        }
    }
}

.rbt-short-item {
    .course-index {
        color: var(--color-heading);
        font-size: 16px;
    }
}

.woocommerce-Tabs-panel h2 {
    font-size: var(--h3);
}


/*-------------------------
    Course Grid Column  
---------------------------*/

.rbt-course-grid-column {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    margin-top: -30px;

    .course-grid-3,
    .course-grid-2,
    .course-grid-4 {
        padding-right: 15px;
        padding-left: 15px;
        margin-top: 30px;

        .rbt-card {
            transition: 0.4s;
        }
    }

    .course-grid-4 {
        flex: 0 0 auto;
        width: 25%;

        @media #{$lg-layout} {
            width: 33.33%;
        }

        @media #{$md-layout} {
            width: 50%;
        }

        @media #{$sm-layout} {
            width: 100%;
        }
    }

    .course-grid-3 {
        flex: 0 0 auto;
        width: 33.33%;

        @media #{$md-layout} {
            width: 50%;
        }

        @media #{$sm-layout} {
            width: 100%;
        }
    }

    .course-grid-2 {
        flex: 0 0 auto;
        width: 50%;
    }

    &.active-list-view {

        .course-grid-3,
        .course-grid-2 {
            width: 100%;
        }
    }

    &.list-column-half {
        &.active-list-view {

            .course-grid-3,
            .course-grid-2,
            .course-grid-4 {
                width: 50%;

                @media #{$sm-layout} {
                    width: 100%;
                }
            }
        }
    }

    .rbt-card {
        .rbt-card-img {
            transition: none;

            a {
                transition: none;

                img {
                    transition: none;
                }
            }
        }
    }
}


.rbt-inner-onepage-navigation.mt--30 {
    nav.tutor-nav {
        border: 0;

        a.tutor-nav-link.is-active {
            border: 0;
            background: var(--color-primary);
            color: #fff;
        }

        a.tutor-nav-link {
            font-size: 18px;
        }
    }
}

ul.rbt-list-style-1.rbt-course-details-list-50 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);

    @media (max-width: 767px) {
        grid-template-columns: repeat(1, 1fr);
    }
}

$color_1: var(--color-primary);

.accordion-header.card-header.tutor-accordion-item-header.is-active.is-active {
    .accordion-button.collapsed {
        color: $color_1;

        &::before {
            content: "\e996";
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
            white-space: nowrap;
        }
    }
}

.rbt-course-feature-inner {
    .tutor-accordion-item-header {
        &::after {
            display: none;
        }
    }

    .tutor-accordion-item-body-content {
        border: 0;
    }

    .tutor-course-content-list-item {
        padding: 0;

        &:hover {
            background: transparent;
        }

        & i {
            color: rgb(25, 35, 53);
            margin-right: 10px;
        }

        span.min-lable {
            color: var(--color-heading);
        }

        span.rbt-badge.variation-03.bg-primary-opacity i {
            margin-right: 1px;
            display: inline-block;
            transform: translateY(1px);
            color: var(--color-primary);
        }
    }
}

.rbt-shadow-box .rating i {
    font-size: 16px;
}

.review-wrapper .tutor-progress-bar {
    border-radius: 0;
}

.review-wrapper .tutor-progress-bar.tutor-ratings-progress-bar {
    height: 6px !important;
}

.tutor-ratings-progress-bar .tutor-progress-value {
    background: #ff8f3c;
}

.review-wrapper .tutor-ratings-average {
    min-width: 46px;
}

.rbt-course-review.about-author.tutor-qna-single-question .thumbnail {
    flex: 0 0 135px;
}

.rbt-course-review.about-author.tutor-qna-single-question .media-body {
    flex: 0 0 calc(100% - 135px);
}

.rbt-course-badge-5 {
    background: var(--color-gray-light);
    padding: 5px 7px;
    font-size: 12px;
    height: 12px;
    border-radius: var(--radius-small);
}

.rbt-course-review.about-author p.description {
    color: rgb(107, 115, 133);
}

.rbt-course-feature-box .tutor-empty-state {
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
    margin-top: 30px;
}

.tutor-course-details-content.tutor-toggle-more-content.tutor-toggle-more-collapsed.active {
    height: auto !important;
}

.tutor-course-details-content.tutor-toggle-more-content.tutor-toggle-more-collapsed.active::before {
    display: none;
}

.tutor-card.tutor-sidebar-card {
    border: 0;
}

.tutor-card.tutor-sidebar-card .tutor-card-body {
    padding: 0;
}

.rbt-tutor-course-details-widebar-widget-load-more .inner-title {
    font-size: 20px;
    font-weight: 700;
    display: block;
    margin-top: 30px;
}

.rbt-tutor-course-details-widebar-widget-load-more .content-box .sub-title {
    font-size: 16px;
}

.rbt-tutor-course-details-widebar-widget-load-more .content-box .title {
    font-size: 16px;
    margin-top: 7px;
    margin-bottom: 8px;
    margin-top: 15px;
}

.rbt-tutor-course-details-widebar-widget-load-more .instructor-box .title {
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 1;
    margin-bottom: 8px;
    color: rgb(25, 35, 53);
}

.rbt-tutor-course-details-widebar-widget-load-more .content-box .sub-title {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.5;
    margin-bottom: 5px;
}

.rbt-tutor-course-details-widebar-widget-load-more .inner-title {
    margin-bottom: 17px;
}

.tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a {
    background-color: var(--color-gray-light);
    border: 0;
    padding: 6px 15px;
    transition: all .3s;
    margin: 0;
    margin-right: 10px;
}

.tutor-course-details-widget .tutor-tag-list li {
    margin-bottom: 0px;
}

.tutor-course-details-widget .tutor-course-details-widget-tags .tutor-tag-list li a:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.rbt-accordion-style.rbt-accordion-02 .card .card-header {
    margin-top: 15px !important;
}

@media (max-width: 767px) {
    .rbt-related-course-area .rbt-price {
        flex-wrap: wrap;
        margin-bottom: 10px;
    }

    .rbt-related-course-area .rbt-price {
        flex-wrap: wrap;
        margin-bottom: 10px;
        padding-top: 10px;
        margin-top: 15px;
    }

    .rbt-related-course-area .rbt-price>* {
        flex: 0 0 100%;
    }

    .rbt-related-course-area .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button {
        width: 100%;
    }

    .rbt-related-course-area .rbt-card .rbt-card-body .rbt-card-bottom {
        display: block;
        text-align: center;
    }
}

@media (max-width: 575px) {
    #tutor-course-details-tab-reviews .review-wrapper {
        overflow-x: auto;
    }

    .tutor-review-summary-rating {
        width: 450px;
    }

    #tutor-course-details-tab-reviews .tutor-review-summary-rating .tutor-col-4 {
        flex: 0 0 90px;
    }
}

@media (max-width: 1199px) {
    .rbt-widget-details .rbt-course-details-list-wrapper li span.rbt-feature-value {
        height: auto;
    }
}

.woocommerce-cart table.cart img {
    width: 140px;
    background-color: #f6f7f8;
    border-radius: 6px;
}

.course-sidebar .tutor-course-single-pricing {
    display: none;
}

.rbt-course-main-content li .course-content-right>a {
    min-width: 110px;
}

button.tutor-btn[name="complete_course_btn"] {
    display: none;
}

.rbt-course-area .rbt-card.variation-01.rbt-hover .rbt-review .tutor-ratings-average,
.rbt-course-area .rbt-card.variation-01.rbt-hover .rbt-review .tutor-ratings-count {
    display: none;
}

.load-more .rbt-btn.rbt-switch-btn.btn-border {
    background: transparent;
}

.load-more .rbt-btn.rbt-switch-btn.btn-border:hover {
    background: var(--color-primary);
}

.tutor-ratings.tutor-ratings- .tutor-ratings-average,
.tutor-ratings.tutor-ratings- .tutor-ratings-count {
    color: rgb(107, 115, 133);
    font-weight: 400;
}

.rbt-course-feature-box .tutor-empty-state {
    box-shadow: none;
}

.related-course a.rbt-btn.rbt-switch-btn.btn-border {
    background: transparent;
}

.related-course a.rbt-btn.rbt-switch-btn.btn-border:hover {
    background: var(--color-primary);
}

.rbt-card .rbt-card-body .rbt-card-bottom {
    flex-wrap: wrap;
    grid-gap: 10px;
}

.rbt-dashboard-content-wrapper .tutor-ratings.tutor-ratings->* {
    color: #fff !important;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item {
    margin: 0;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
    margin-left: 15px;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item ul.course-switch-layout {
    margin-right: 15px;
}

.rbt-sorting-list .rbt-show-orderby-front .filter-select.rbt-modern-select {
    margin-left: 20px;
}

.tutor-user-profile-content .tutor-grid>.rbt-card {
    padding: 0;
}

.post-type-archive-courses a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:focus {
    background: transparent;
}

@media (max-width: 991px) {
    .default-exp-wrapper .filter-inner .filter-select-option {
        margin-bottom: 15px;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
        margin-top: 10px;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item {
        margin-top: 10px;
    }

    .rbt-sorting-list .rbt-search-style input {
        min-width: 100%;
    }
}

.rbt-sorting-list .rbt-show-orderby-front .filter-select.rbt-modern-select {
    @media (max-width: 1199px) {
        margin-left: 0;
        margin-top: 15px;
    }
}

@media (max-width: 767px) {
    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
        margin-top: 0;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item .view-more-btn {
        margin-left: 0;
        margin-top: 0;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item {
        flex: 0 0 100%;
    }

    .default-exp-wrapper .filter-inner .filter-select-option>* {
        width: 100%;
    }

    .rbt-course-top-wrapper .default-exp-wrapper .filter-inner .bootstrap-select {
        max-width: 100%;
    }
}

.rbt-course-details-area a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
    color: var(--color-white) !important;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary)) !important;
}

.rbt-course-area .rbt-card .rbt-meta {
    margin-top: 0;
}

body.tutor-lms.lesson-template-default.single.single-lesson .rbt-breadcrumb-default {
    display: none;
}

.rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item ul.course-switch-layout {
    width: fit-content;
}

.rbt-course-feature-has-video-thumbnail {
    position: relative;
}

.rbt-course-feature-has-video-thumbnail .rbt-featured-course-source-video {
    position: absolute;
    left: 50%;
    top: 50%;
    display: none;
    transform: translate(-50%, -50%);
}

@media (max-width: 991px) {
    .rbt-course-feature-has-video-thumbnail .rbt-featured-course-source-video {
        display: block;
    }
}

.course-sidebar.rbt-gradient-border.sticky-top.rbt-shadow-box.course-sidebar-top .tutor-video-player {
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
}

.tutor-dashboard-my-courses .tutor-course-co-author-badge {
    font-size: 14px;
    background: var(--color-primary);
    display: inline-block;
    padding: 2px 12px;
    border-radius: 3px;
    color: var(--color-white);
    margin-top: 20px;
}

.tutor-dashboard-my-courses .rbt-card.variation-01 .tutor-card-body {
    padding-top: 15px;
}

.rbt-course-details-right-sidebar {
    padding: 30px;
    background: var(--color-white);
    border-radius: 10px;
    margin-left: 0;
}

.rbt-banner-content .rbt-course-top-wrapper ul.rbt-portfolio-filter.filter-tab-button.justify-content-start.nav.nav-tabs {
    padding-bottom: 40px;
}

@media (max-width: 991px) {
    .rbt-banner-content .rbt-course-top-wrapper ul.rbt-portfolio-filter.filter-tab-button.justify-content-start.nav.nav-tabs {
        padding-bottom: 0;
    }
}

.rbt-banner-content .rbt-course-top-wrapper .rbt-sorting-list {
    grid-gap: 20px;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 li {
    display: block !important;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 {
    grid-gap: 20px;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 a {
    color: var(--color-primary);
}

.rbt-course-feature-box.overview-wrapper .rbt-shadow-box a {
    color: var(--color-primary);
}

.rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner {
    grid-gap: 20px;
    justify-content: flex-start;
}

.rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner .filter-select {
    width: 100%;
}

@media (min-width: 992px) and (max-width: 1199px) {
    .rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 31.33%;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 31.33%;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner>* {
        flex: 0 0 48.00%;
    }
}

.rbt-course-top-wrapper .default-exp-wrapper.histudy-filter-style-1 .filter-inner .filter-select-option {
    width: 100%;
}
.rbt-course-details-area .rbt-card-body a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:hover {
    box-shadow: none;
}

.rbt-demo-login-area {
    background: #ddd;
    padding: 7px 11px;
    border-radius: 3px;
    margin-bottom: 20px;
}

.rbt-demo-login-area p {
    font-size: 16px;
}

.rbt-demo-login-area a {
    color: var(--color-primary);
    margin-left: 8px;
    font-size: 16px;
}

.rbt-demo-login-area a:hover {
    color: #b866e7;
}

.video-popup-with-text.video-popup-wrapper::before {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 85%;
    display: block !important;
    z-index: 1;
    content: "";
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0), black 100%);
    transition: opacity 0.65s cubic-bezier(0.05, 0.2, 0.1, 1);
    cursor: pointer;
    border-radius: 6px;
}
.rbt-card.card-list-2.elegant-course .rbt-card-img a img {
    height: 100% !important;
    min-height: 100%;
}

@media (max-width: 767px) {
    .rbt-course-grid-column .course-grid-2, .rbt-course-grid-column .course-grid-3 {
        width: 100%;
    }
}
.related-course {
    & .section-title span.subtitle {
        color: var(--color-subtitle);
        letter-spacing: 1px;
        display: inline-block;
        line-height: 14px;
    }
}

.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary {
    background: transparent;
    color: var(--color-heading);
}