.grid-view-course{
    background-color: var(--color-light);
    .grid-box{
        margin-bottom: 60px;
        .single-grid-box{
            background-color: var(--color-white);
            padding: 20px 30px;
            border-radius: 6px;
            position: relative;
            line-height: 15px;
            width: 100%;
            font-size: 16px;
            border: none;
            color: var(--color-heading);
            font-weight: 500;
            select{
                border: none;
                width: 100%;
                outline: none;
            }
        }
    }
}
.rbt-card.variation-01.rbt-hover .rbt-avater img {
    max-height: 40px;
}

.rainbow_recent_event_widget-2.rbt-single-widget.rainbow_recent_event_widget {
    padding-top: 0;
}