/*------------------------
    Edu Common Styles  
--------------------------*/

/*-------------------------
    Rbt Default
-------------------------*/
.rbt-index-upper {
    position: relative;
    z-index: 1;
}

.sticky-top {
    top: 30px;
    z-index: 9;
}

.rbt-single-group {
    margin: -5px;
    .rbt-single-list {
        margin: 5px;
    }
}

.transform-sm-none {
    @media #{$sm-layout} {
        transform: none !important;
    }
}

/*-------------------------
    Rbt Round Btn
-------------------------*/

.rbt-round-btn {
    width: 40px;
    height: 40px;
    line-height: 41px;
    text-align: center;
    border-radius: 100%;
    position: relative;
    z-index: 1;
    background: transparent;
    padding: 0;
    border: 0 none;
    display: block;
    @media #{$sm-layout} {
        width: 30px;
        height: 30px;
        line-height: 30px;
    }
    i {
        margin-right: 0;
    }
    
    &::after {
        background: var(--color-gray-light);
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        transition: 0.4s;
        opacity: 0;
        transform: scale(0.8);
        border-radius: 100%;
        z-index: -1;
    }

    &.btn-white-off {
        color: var(--color-white-off);
    }

    &:hover,
    &.open {
        color: var(--color-primary);
        &::after {
            opacity: 1;
            transform: scale(1);
        }
    }
}


/*------------------------------
    Rbt Hover Style
-------------------------------*/

.rbt-hover {
    transition: var(--transition-2);
    &:hover {
        transform: scale(1.02);
    }
}

.rbt-hover-02 {
    transition: var(--transition-2);
    &:hover {
        transform: translateY(-10px);
    }
}

.rbt-hover-03 {
    transition: transform .65s cubic-bezier(.23,1,.32,1);
    &:hover {
        transform: translateY(-3px);
    }
}

.rbt-link-hover {
    a {
        position: relative;
        &::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: currentColor;
            transform: scaleX(0);
            transform-origin: bottom right;
            transition: transform 0.3s;
        }
        &:hover {
            &::after {
                transform-origin: bottom left;
                transform: scaleX(1);
            }
        }
    }
}

/*------------------------------
    Rbt Theme Gradient
-------------------------------*/

.theme-gradient {
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
}

%theme-gradient {
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
    white-space: nowrap;
}

.theme-gradient {
    &.new-big-heading-gradient{
        background: linear-gradient(180deg, rgb(228 226 250) 0%, rgb(57 78 244 / 3%) 80%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: rgba(255, 255, 255, 0.001);
        white-space: nowrap;
    }
}

.rbt-gradient-border {
    background: linear-gradient(-145deg, #CFA2E8, #637FEA) !important;
    z-index: 10;
    &::before {
        content: "";
        z-index: -1;
        top: 3px;
        left: 3px;
        position: absolute;
        background: #fff;
        width: calc(100% - 6px);
        height: calc(100% - 6px);
        border-radius: var(--radius);
    }
}



/*------------------------------
    Rbt Slider Gutter
-------------------------------*/
.gutter-swiper-30 {
    margin: -15px;
    .single-slide {
        padding: 15px;
    }
}

.gutter-swiper-20 {
    margin: -10px;
    .single-slide {
        padding: 10px;
    }
}

.gutter-swiper-10 {
    margin: -5px;
    .single-slide {
        padding: 5px;
    }
}

/*------------------------------
    Rbt rating
-------------------------------*/

.rating {
    a {
        display: inline-block;
        i {
            color: var(--color-warning);
            font-style: normal;
        }
    }
}

/*------------------------------
    Rbt Shape Style
-------------------------------*/

.theme-shape {
    position: relative;
    z-index: 2;
    overflow: hidden;
    &::before {
        position: absolute;
        left: -250px;
        top: 250px;
        right: auto;
        bottom: auto;
        z-index: -1;
        width: 500px;
        height: 500px;
        border-radius: 1000px;
        background-image: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
        opacity: 0.2;
        filter: blur(100px);
        content: "";
    }
    &::after {
        position: absolute;
        z-index: -1;
        width: 500px;
        height: 500px;
        border-radius: 1000px;
        opacity: 0.2;
        filter: blur(100px);
        content: "";
        left: auto;
        top: -250px;
        right: -250px;
        bottom: auto;
        background-image: linear-gradient(45deg, var(--color-violet), var(--color-pink));
    }
}


.rbt-round-bottom-shape {
    position: relative;
    z-index: 1;
    &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        background: url(../images/bg/banner-bg-shape-1.png);
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        width: 100%;
        height: 148px;
        z-index: -1;
    }
}


.edu-bg-shade {
    background: var(--color-white);
    box-shadow: var(--shadow-1);
    border-radius: 6px;
    padding: 30px;
}

.edu-bg-gray {
    background: var(--color-grey);
    border-radius: 6px;
    padding: 20px;
}


.card-info {
    display: inline-block;
    .inner {
        background: var(--color-white);
        box-shadow: var(--shadow-1);
        border-radius: 5px;
        padding: 20px 30px;
        .name {
            font-weight: bold;
            font-size: 18px;
            line-height: 28px;
            color: var(--color-heading);
            span {
                font-size: 14px;
                color: var(--color-body);
                font-weight: 400;
            }
        }
        .rating-wrapper {
            span {
                display: inline-block;
                margin-left: 10px;
                font-weight: 500;
                font-size: 16px;
                line-height: 26px;
                @media #{$large-mobile} {
                    margin-left: 0;
                }
            }
            i {
                color: #ffa41b;
            }
        }
    }
    .notify-icon {
        width: 100px;
        border-radius: 100%;
        position: absolute;
        top: -50px;
        left: -50px;
        text-align: center;
        height: 100px;
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            svg {
                color: var(--color-white);
                width: 28px;
            }
        }
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}


/*-------------------------
    Card Author Meta 
-------------------------*/
.rbt-author-meta {
    display: flex;
    align-items: center;
    .rbt-avater {
        margin-right: 10px;
        a {
            display: block;
        }
        img {
            width: 40px;
            max-width: 40px;
            height: 40px;
            border-radius: 100%;
            object-fit: cover;
            border: 2px solid var(--primary-opacity);
            padding: 2px;
        }
    }
    .rbt-author-info {
        font-size: 14px;
        a {
            color: var(--color-heading);
            font-weight: 500;
            @extend %transition;
            &:hover {
                color: var(--color-primary);
            }
        }
    }
}

/*-----------------------------
    Rbt Border Style  
------------------------------*/
hr {
    background-color: var(--color-border);
    opacity: 1;
}

.rbt-border-none {
    border: 0 none !important;
}

.rbt-border {
    border: 1px solid var(--color-border) !important;
}
.rbt-border-2 {
    border: 2px solid var(--color-border) !important;
}

.rbt-border-dashed {
    border: 2px dashed var(--color-border) !important;
}

.rbt-border-with-box {
    padding: 30px;
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    border: 1px solid var(--color-border);
    @media #{$sm-layout} {
        padding: 15px;
    }
}
.rbt-border-bottom {
    border-bottom: 1px solid var(--color-border);
}

.rbt-border-top {
    border-top: 1px solid var(--color-border);
}
.rbt-border-bottom-light {
    border-bottom: 1px solid var(--color-border-2);
}

.border-bottom-4 {
    border-bottom: 4px solid var(--color-primary);
}

.border-top-bar-primary-color {
    border-top: 5px solid var(--color-primary);
}

.border-top-bar-secondary-color {
    border-top: 5px solid var(--color-secondary);
}

.rbt-separator {
    position: relative;
    &::after {
        position: absolute;
        content: "";
        height: 20px;
        width: 1px;
        background: var(--color-border);
        top: 50%;
        transform: translateY(-50%);
    }
}

// Border Color

.border-color-primary {
    border-color: var(--color-primary);
}
.border-color-secondary {
    border-color: var(--color-secondary);
}
.border-color-coral {
    border-color: var(--color-coral);
}
.border-color-violet {
    border-color: var(--color-violet);
}
.border-color-pink {
    border-color: var(--color-pink);
}
.border-color-card-1 {
    border-color: var(--color-card-1);
}
.border-color-card-2 {
    border-color: var(--color-card-2);
}
.border-color-card-3 {
    border-color: var(--color-card-3);
}
.border-color-card-4 {
    border-color: var(--color-card-4);
}


/*-----------------------------
    Rbt Border Radius  
-------------------------------*/

.square {
    border-radius: 0 !important;
}
.rbt-radius {
    border-radius: 6px !important;
}
.radius {
    border-radius: 6px !important;
}
.radius-6 {
    border-radius: 6px !important;
}
.radius-10 {
    border-radius: 10px !important;
}
.radius-round {
    border-radius: 500px !important;
}


/*-----------------------------
    Rbt Shadow
-------------------------------*/

.rbt-shadow-box {
    border-radius: var(--radius);
    background: var(--color-white);
    overflow: hidden;
    box-shadow: var(--shadow-1);
    padding: 30px;
    @media #{$lg-layout} {
        padding: 20px;
    }
    @media #{$md-layout} {
        padding: 20px;
    }
    @media #{$sm-layout} {
        padding: 20px;
    }
}

.bg-no-shadow {
    box-shadow: none !important;
}
.shadow-1 {
    box-shadow: var(--shadow-1);
}

.shadow-2 {
    box-shadow: var(--shadow-2);
}

.shadow-3 {
    box-shadow: var(--shadow-3);
}

.shadow-4 {
    box-shadow: var(--shadow-1);
}

.shadow-5 {
    box-shadow: var(--shadow-5);
}

.shadow-6 {
    box-shadow: var(--shadow-1);
}

.shadow-7 {
    box-shadow: var(--shadow-7);
}

.shadow-8 {
    box-shadow: var(--shadow-8);
}

/*--------------------------
    Font Weight 
---------------------------*/

.w-300 {
    font-weight: 300 !important;
}

.w-400 {
    font-weight: 400 !important;
}

.w-500 {
    font-weight: 500 !important;
}

.w-600 {
    font-weight: 600 !important;
}

.w-700 {
    font-weight: 700 !important;
}

.w-800 {
    font-weight: 800 !important;
}

.w-900 {
    font-weight: 900 !important;
}

/*-----------------------------
    Card bg Inner Color 
--------------------------------*/

.bg-card-color-1 {
    .inner {
        background: #fde29275 !important;
    }
}

.bg-card-color-2 {
    .inner {
        background: #ffdbe175  !important;
    }
}

.bg-card-color-3 {
    .inner {
        background: #ffc5fa75 !important;
    }
}

.bg-card-color-4 {
    .inner {
        background: #c8ffe975 !important;
    }
}

.bg-card-color-5 {
    .inner {
        background: #e4a7f675 !important;
    }
}


/*-----------------------------
    Rbt Background Image
--------------------------------*/

.bgImagePosition {
    @extend %bgImagePosition;
}
.bg_image_fixed {
    background-attachment: fixed !important;
}
.bg_image {
    @extend %bgImagePosition;
}

.bg--fixed {
    background-attachment: fixed !important;
}

.bg--sticky {
    position: sticky !important;
    top: 3.75rem;
}
@for $i from 1 through 29 {
    .bg_image--#{$i} {
        background-image: url(../images/bg/bg-image-#{$i}.jpg);
    }
}

.bg_image--9 {
    background-position: bottom 44% center;
}

.rbt-alert-success {
    &.alert-success {
        color: var(--color-body);
        background-color: var(--color-grey);
        border: 0 none;
        border-top: 5px solid var(--color-success);
        padding: 10px 0;
        text-align: center;
        a {
            color: var(--color-heading);
        }
    }
}


.rbt-avatars {
    min-width: 70px;
    max-width: 70px;
    &.size-lg {
        min-width: 120px;
        max-width: 120px;
        width: 120px;
        height: 120px;
    }
    &.size-sm {
        min-width: 52px;
        max-width: 52px;
    }
    img {
        border-radius: 100%;
        width: 100%;
        background: var(--color-white);
        padding: 4px;
        border: 2px solid var(--primary-opacity);
        object-fit: cover;
    }
}


.rbt-edit-photo-inner {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    .rbt-edit-photo {
        background: var(--color-gray-light);
        border-radius: 100%;
        color: var(--color-primary);
        width: 100%;
        height: 100%;
        padding: 0;
        border: 0 none;
        transition: 0.3s;
        &:hover {
            background: var(--color-primary);
            color: var(--color-white);
        }
    }
}
.tutor-dashboard .tutor-frontend-dashboard-maincontent ul.tutor-dashboard-permalinks:before {
    border-right: 0;
}

.tutor-dashboard .tutor-frontend-dashboard-maincontent ul.tutor-dashboard-permalinks {
    margin: 0;
    padding: 0;
}
.rbt-related-course-area {
    padding-bottom: 120px;
}
.bg_image--19 {
    @media #{$md-layout} {
        background-position: center right 25%;
    }
    @media #{$sm-layout} {
        background-position: center right 25%;
    }
}

.bg_image--22 {
    @media #{$md-layout} {
        background-position: center right 33%;
    }
    @media #{$sm-layout} {
        background-position: center right 33%;
    }
}


#course-filter-popup {
    display: none; /* Initially hidden */
}

.rbt-course-single-video-mobile .rbt-course-feature-has-video-thumbnail {
    display: none;
}

.rbt-courses-single-video-desktop .rbt-course-feature-has-video-thumbnail {
    display: block;
}

.page-instructor .rbt-page-area {
    padding: 0!important;
}

.rbt-course-filter-modal .rbt-portfolio-filter.nav-tabs {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 30px;
}

.rbt-course-filter-modal .default-exp-wrapper {
    border-top: 0px solid var(--color-border);
}

@media only screen and ( max-width: 991px ) {
    .post-type-archive-courses .rbt-page-banner-wrapper {
        padding-bottom: 180px;
    }
}

@media only screen and ( max-width: 767px ) {
    /* All columns with 50% width */
    .rbt-courses-single-video-desktop .rbt-course-feature-has-video-thumbnail {
        display: none;
    }
    .rbt-mobile-row {
        flex-wrap: nowrap;
        overflow-x: auto;
        &::-webkit-scrollbar {
            display: none;
        }
        .col-12 {
            width: 85%;
        }
        &.g-5 {
            --bs-gutter-x: 1.5rem;
        }
    }

    .rbt-mobile-view-tab {
        flex-wrap: nowrap!important;
        overflow-x: auto;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .rbt-mobile-view-tab.rbt-team-tab-thumb li {
        flex-basis: 75%;
        display: block;
        width: 100%;
    }

    .rbt-mobile-view-tab.rbt-team-tab-thumb .rbt-team-thumbnail {
        min-width: 350px;
    }

    .mobile-course-archive-page .rbt-course-grid-column .course-grid-2,
    .mobile-course-archive-page .rbt-course-grid-column .course-grid-3
    {
        width: 50%;
    }

    .mobile-course-archive-page .rbt-course-grid-column .rbt-card {
        padding: 10px;
    }

    .mobile-course-archive-page .rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-bundle-course-count-text, 
    .mobile-course-archive-page .rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-bundle-course-count-number {
        font-size: 10px;
    }
    .mobile-course-archive-page .rbt-card .rbt-card-img .tutor-bundle-course-count-badge .tutor-icon-layer:before {
        font-size: 12px;
    }

    .mobile-course-archive-page .rbt-badge-3 {
        height: 48px;
        width: 48px;
    }
    .mobile-course-archive-page .rbt-review .rating {
        margin-right: 6px;
    }
    .mobile-course-archive-page .rbt-review .rating-count {
        font-size: 12px;
    }

    .mobile-course-archive-page .rbt-bookmark-btn .tutor-course-wishlist-btn {
        width: 25px;
        height: 25px;
        line-height: 25px;
    }

    .mobile-course-archive-page .rbt-card .rbt-card-body .rbt-card-title {
        font-size: 16px;
    }

    .mobile-course-archive-page .rbt-meta li {
        margin: 3px;
        white-space: nowrap;
    }

    .mobile-course-archive-page .rbt-author-meta .rbt-avater img {
        width: 25px;
        max-width: 25px;
        height: 25px;
    }

    .mobile-course-archive-page .rbt-author-meta .rbt-author-info {
        font-size: 12px;
    }

    .mobile-course-archive-page .rbt-card.variation-01.rbt-hover .rbt-price > * {
        font-size: 13px;
    }

    .mobile-course-archive-page .rbt-card .rbt-card-body .rbt-card-bottom .rbt-btn-link {
        font-size: 12px;
    }

    .mobile-course-archive-page .rbt-card .rbt-card-body .rbt-card-text {
        display: none;
    }

    .mobile-course-archive-page .rbt-card .rbt-card-body .rbt-meta {
        display: flex;
        gap: 5px;
        flex-wrap: nowrap;
        overflow-x: auto;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .mobile-course-archive-page .rbt-course-grid-column .course-grid-3, 
    .mobile-course-archive-page .rbt-course-grid-column .course-grid-2, 
    .mobile-course-archive-page .rbt-course-grid-column .course-grid-4 {
        padding-right: 5px;
        padding-left: 5px;
    }

    .mobile-course-archive-page .rbt-review .rating span {
        font-size: 10px;
    }
    .rbt-bookmark-btn .tutor-course-wishlist-btn  i {
        font-size: 12px;
    }

    .mobile-course-archive-page .rbt-course-grid-column .course-grid-3 {
        font-size: 12px;
        margin-bottom: 10px!important;
    }

    .post-type-archive-courses .archive.course_block .load_more_button {
        margin-top: 30px!important;
        margin-bottom: 60px!important;
    }

    .tutor-course-archive-page {
        padding: 10px!important;
    }

    .mobile-course-archive-page .rbt-author-info a:nth-of-type(2),
    .mobile-course-archive-page .rbt-author-info a:nth-of-type(2) ~ * {
        display: none;
    }

    .mobile-course-banner-breadcrumb { 
        display: block;
    }

    .course-mobile-view-button {
        display: block;
        width: 54px;
        height: 48px;
        flex-shrink: 0;
        border-radius: 14px;
        background: var(--color-primary);
        box-shadow: 0px 6px 16px 0px rgba(19, 44, 74, 0.02);
        border: none;
        outline: none;
        cursor: pointer;
    }

    .course-mobile-view-button i {
        color: #fff;
    }

    .rainbow-tutor-lms-breadcrumb-center-content .layout2-order1 .rbt-search-style input {
        border-radius: 14px;
        box-shadow: var(--shadow-1);
        background: var(--color-white);
        height: 48px;
        border: none;
    }

    .rainbow-tutor-lms-breadcrumb-center-content .layout2-order1 {
        margin-top: 0;
    }

    .default-exp-wrapper .filter-inner .filter-select-option {
        margin-bottom: 0!important;
    }

    .default-exp-wrapper .filter-inner .filter-select-option {
        padding: 0!important;
    }

    .rbt-banner-content .rbt-course-top-wrapper ul.rbt-portfolio-filter.filter-tab-button.justify-content-start.nav.nav-tabs {
        padding-bottom: 5px!important;
    }

    .rbt-course-top-wrapper .rbt-sorting-list .rbt-short-item ul.course-switch-layout {
        width: fit-content;
        margin-left: -10px;
    }

    .rbt-course-top-wrapper .rbt-sorting-list.rbt-mobile-course-archive-filter {
        flex-wrap: nowrap!important; 
        justify-content: flex-start;
    }
    
    .rbt-course-top-wrapper .rbt-sorting-list.rbt-mobile-course-archive-filter .rbt-short-item {
        box-sizing: border-box; 
    }
    
    .rbt-course-top-wrapper .rbt-sorting-list.rbt-mobile-course-archive-filter .rbt-short-item.rbt-short-item-filter {
        flex: 0 0 60px;
    }
    
    .rbt-course-top-wrapper .rbt-sorting-list.rbt-mobile-course-archive-filter .rbt-short-item.rbt-short-item-searchbar {
        flex: 1; 
        min-width: calc(100% - 60px - 30px);
        box-sizing: border-box; 
    }

    .rbt-sorting-list .rbt-search-style input, .rbt-sorting-list .rbt-search-style {
        width: 100%!important;
        min-width: unset!important;
    }

    .rbt-mobile-course-archive-filter .discover-filter-button span {
        width: 0;
        opacity: 0;
        font-size: 0;
        visibility: hidden;
    }

    .rbt-mobile-course-archive-filter .discover-filter-button.rbt-btn i {
        padding-left: 0;
    }

   /* Basic styles for the modal */
    #course-filter-popup {
        position: fixed;
        top: 0;
        left: -100%; /* Initially hidden off-screen to the left */
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Optional: Dark background for overlay */
        transition: left 0.4s ease; /* Smooth sliding effect */
        z-index: 1000; /* Ensure it is on top */
        display: block;
    }

    /* Styles for the content inside the modal */
    .course-filter-modal-content {
        position: absolute;
        top: 0;
        left: 0; /* Align the content to the left edge of the modal */
        width: 300px; /* Adjust width as needed */
        height: 100%;
        background: #fff; /* Background color for the modal content */
        overflow-y: auto; /* Optional: Scrollbar if content overflows */
    }

    /* When the modal is active, move it into view */
    #course-filter-popup.open {
        left: 0; /* Slide in to visible area */
    }


    .admin-bar .rbt-course-filter-modal {
        top: 45px!important;
    }

    /* Modal Content */
    .course-filter-modal-content {
        background-color: #f7f7f7;
        padding: 20px;
        width: 80%;
        max-width: 500px;
    }

    /* Close Button */
    .course-filter-modal-content .close-button {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .course-filter-modal-content .close-button:hover,
    .course-filter-modal-content .close-button:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }

    .course-filter-modal-content .close-button {
        background: var(--color-white);
        border: 0 none;
        color: var(--color-heading);
        width: 40px;
        height: 40px;
        font-size: 21px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
    }


    .rbt-course-filter-modal .dropdown.bootstrap-select.form-select {
        padding: 0;
        border: none;
    }

    .rbt-course-filter-modal  .rbt-modern-select .bootstrap-select button.btn-light {
        border: 0 none;
        box-shadow: var(--shadow-1);
        height: 50px;
        padding: 10px 20px;
        outline: none;
        color: var(--color-body);
        border-radius: var(--radius);
        font-size: 16px;
        line-height: 28px;
        font-weight: 400;
        padding-right: 30px;
        background-color: var(--color-white);
        outline: none;
    }

    .rbt-course-filter-modal  .select-label {
        opacity: 0.8;
    }

    .rbt-course-filter-modal .rbt-modern-select.select-rating .rbt-widget-rating {
        padding: 10px!important;
        padding-top: 10px;
    }

    .rbt-course-filter-modal .selected_course_filters ul li {
        color: var(--color-body)!important;
    }

    .rbt-course-filter-modal .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 .filter-inner > * {
        flex: 0 0 100%;
    }

    .rbt-mobile-view-filter-custom {
        display: none;
    }

    .rbt-mobile-course-archive-filter {
        margin-bottom: 30px;
    }

    .rbt-mobile-view-filter-custom {
        display: none!important;
    }

    .rbt-course-grid-list-custom {
        flex-wrap: nowrap!important;
    }

    .rbt-course-grid-list-custom .rbt-short-item {
        flex: 0 0 50%!important;
    }

    .rbt-course-filter-modal .default-exp-wrapper.default-exp-expand.histudy-filter-style-1 {
        display: block!important;
    }

    .rbt-course-single-video-mobile .rbt-course-feature-has-video-thumbnail {
        display: block;
        margin-bottom: 30px;
    }

    .rbt-archive-course-bundle-main.tutor-course-archive-page,
    .has-filter-layout.rbt-section-gapBottom.course-layout.tutor-course-archive-page.ptt-120 {
        padding-top: 60px!important;
    }

    .rbt-course-details-area {
        padding-top: 30px!important;
    }

    .post-type-archive-courses .rbt-page-banner-wrapper {
        padding-bottom: 140px!important;
    }
}

@media only screen and ( max-width: 575px ) {
    
    .rbt-mobile-counterup {
        .col-12 {
            width: 50%;
        }
        &.g-5 {
            --bs-gutter-x: 1.5rem;
        }

        .rbt-counterup .inner .content .counters {
            font-weight: 700;
            font-size: 35px;
        }
    }
}

@media only screen and ( max-width: 550px ) {
    .rbt-mobile-view-tab.rbt-team-tab-thumb .rbt-team-thumbnail {
        min-width: 300px;
    }
}

@media only screen and ( max-width: 480px ) {
    .rbt-mobile-view-tab.rbt-team-tab-thumb .rbt-team-thumbnail {
        min-width: 230px;
    }
}

@media only screen and ( max-width: 420px ) {

    .rbt-counterup-area-mobile {
        padding-bottom: 55px;
    }
    
    .rbt-mobile-counterup {
        flex-wrap: nowrap;
        overflow-x: auto;
        &::-webkit-scrollbar {
            display: none;
        }
        .col-12 {
            width: 75%;
        }
        &.g-5 {
            --bs-gutter-x: 1.5rem;
        }
        .mt_mobile--60 {
            margin-top: 30px !important;
        }
        &.hanger-line > .col-lg-3:nth-child(3) {
            margin-top: 30px;
        }
    }

    .about-style-1 .thumbnail-wrapper .thumbnail.image-2 img {
        max-height: 120px!important;
        object-fit: cover;
    }

    .rbt-about-area.about-style-1 .thumbnail-wrapper .image-1 img {
        width: 150px!important;
    }

    .about-style-1 .thumbnail-wrapper .thumbnail.image-3 img {
        max-height: 200px!important;
    }

    .rbt-about-area.about-style-1 .thumbnail-wrapper .image-3 {
        top: -40px;
        left: unset!important;
        text-align: center;
    }
    .about-style-1 .thumbnail-wrapper .thumbnail.image-2 img {
        max-height: 150px;
    }

    .rbt-about-area.about-style-1 .thumbnail-wrapper .image-2 {
        top: 0!important;
    }

    .rbt-course-grid-list-custom {
        flex-wrap: wrap!important;
    }

    .rbt-course-grid-list-custom .rbt-short-item {
        flex: 0 0 100%!important;
    }

    .rbt-banner-content .rbt-course-top-wrapper .rbt-sorting-list {
        grid-gap: 10px!important;
    }

    .mobile-course-archive-page .rbt-badge-3 {
        bottom: 5px!important;
    }
    
}

.rbt-counterup.style-3 .inner .content .counters::after,
.rbt-counterup.style-5 .inner .content .counters::after,
.rbt-counterup.style-6 .inner .content .counters::after,
.bg-black-overlay .rbt-counterup .inner .content .counter::after {
    display: none!important;
}

.rbt-header-currency-switcher .header-info .rbt-dropdown-menu a {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.rbt-custom-header-navigation-elementor .mainmenu-nav .with-megamenu.position-static .rbt-megamenu.grid-item-full {
    width: 100vw;
    transform: translateX(-50%);
    left: 50%;
    padding-left: 170px;
    padding-right: 170px;
}

.elementor-custom-humberger-menu {
    display: none;
}


.rbt-inner-onepage-navigation {
    border-radius: 28px;
}

.tooltip-wrap .tooltip-bottom {
    transform: translateX(-50%) translateY(2px);
}

.tooltip-wrap:hover .tooltip-bottom {
    transform: translateX(0%) translateY(0)!important;
}

.rbt-team-area .rbt-modal-default .modal-body .inner .col-lg-4 {
    margin-top: 0;
}

.popup-mobile-menu .mainmenu li.with-megamenu a.rbt-btn.btn-gradient::after {
    top: 1px;
	color: var(--color-white)!important;
}

.rbt-badge-2 img {
    border-radius: 100%!important;
}

.rbt-team-area .rbt-modal-default {
    margin-top: 0;
}

div#tutor-course-details-tab-info .tutor-course-benefits-content ul.rbt-list-style-1.rbt-course-details-list-50 li {
    margin: 0;
}

.rbt-card .rbt-card-body .rbt-card-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
}

.video-popup-wrapper img {
    border-radius: 6px !important;
}

.rbt-banner-area.rbt-banner-8 .rbt-swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
   display: none;
}

.rbt-price .current-price .price del {
    font-size: 20px;
    font-weight: 500;
    opacity: 0.4;
    margin-right: 7px;
}

.rbt-price .current-price .price ins { 
    font-size: 24px;
    font-weight: 700;
    color: var(--color-body);
}

.rbt-testimonial-box .clint-info-wrapper .thumb {
    width: 70px;
}

.tutor-bundle-course-count-badge {
    margin-top: -8px;
    border-radius: 25px;
    background-color: #9342e7;
    color: #fff;
    padding: 0 12px;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    margin-bottom: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 7px;
}

.rbt-categories-area-style11 .swiper.category-activation-stylefive {
    margin-left: -15px;
    margin-right: -15px;
    padding-bottom: 55px !important;
}

.rbt-categories-area-style11 .swiper-pagination .swiper-pagination-bullet {
    border-radius: 50%;
    width: 8px;
    height: 8px;
    opacity: 0.5;
    background: var(--color-body);
    position: relative;
}

.rbt-categories-area-style11 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: transparent;
}

.rbt-categories-area-style11 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 12px;
    border: 2px solid var(--color-primary);
    background: transparent;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.rbt-categories-area-style11 .rbt-arrow-between.icon-bg-gray .rbt-swiper-arrow {
    display: none;
}

.tutor-dashboard-content-inner.my-wishlist .rbt-card .tutor-card-footer {
    padding: 15px 0 0 0;
}

.tutor-dashboard-content-inner.my-wishlist .rbt-card .tutor-card-footer .list-item-button {
    text-align: center;
}

.tutor-dashboard-content-inner.my-wishlist .add_to_cart_button   {
    width: unset!important;
    height: unset!important;
    line-height: unset!important;
}

#tutor_calendar_wrapper .tutor-calendar-wrapper.is-sticky {
    position: static;
}

.tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-start-quiz-wrapper {
    max-width: 950px;
}

.tutor-checkout-page {
    padding: 0;
}

.tutor-checkout-page .tutor-checkout-details .tutor-fs-5.tutor-fw-medium {
    font-size: 20px;
    line-height: 23px;
    text-transform: capitalize;
    font-weight: 700;
    margin-bottom: 0;
    padding-bottom: 15px;
}

.tutor-checkout-billing {
    background-color: #fafafa;
    border-radius: 5px;
    padding: 24px;
} 

.tutor-checkout-billing .tutor-checkout-billing-inner .tutor-fs-5.tutor-fw-medium {
    font-size: 20px;
    line-height: 23px;
    text-transform: capitalize;
    font-weight: 700;
}

.tutor-checkout-page .tutor-apply-coupon-form button:hover {
    color: var(--color-primary);
}

.tutor-checkout-billing .tutor-billing-fields .tutor-form-control {
    width: 100%;
    background-color: transparent;
    border: 2px solid var(--color-border)!important;
    border-bottom: 2px solid var(--color-border)!important;
    border-radius: 6px;
    line-height: 23px;
    padding: 10px 20px;
    font-size: 14px;
    color: var(--color-body);
    margin-bottom: 15px;
    height: 50px!important;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .tutor-col-12:not(:last-of-type) .tutor-form-control {
    border-bottom: 2px solid var(--color-border)!important;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .btn-check:focus+.btn-light, 
.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .btn-light:focus {
    box-shadow: unset!important;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .bootstrap-select>.dropdown-toggle {
    padding: 0;
    line-height: 25px;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .bootstrap-select>.dropdown-toggle:after {
    border-top: 0.4em solid;
    border-right: .4em solid transparent;
    border-left: .4em solid transparent;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .bootstrap-select > .dropdown-menu.show {
    transform: translate(0px, -38px)!important;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .bootstrap-select > .dropdown-toggle {
    height: unset!important;
}

.tutor-checkout-billing .tutor-alert.tutor-danger ul li {
    margin-top: 0;
    margin-bottom: 0;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .tutor-col-12:first-of-type .tutor-form-control {
    border-top-left-radius: 0;
}

.tutor-checkout-page .tutor-checkout-billing .tutor-billing-fields .tutor-col-12:nth-of-type(2) .tutor-form-control {
    border-top-right-radius: 0;
}

.post-type-archive-courses .default-exp-wrapper .bootstrap-select>.dropdown-toggle:after {
    margin-top: 2px;
}

.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary {
    height: unset!important;
    line-height: 1.5em !important;
    background: transparent!important;
    background-image: unset!important;
    color: var(--color-heading)!important;
    font-size: 14px!important;
    position: relative;
    padding: 0!important;
}

.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary:after,
.rbt-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md::after {
    position: absolute;
    content: "";
    left: auto;
    bottom: 0;
    background: currentColor;
    width: 0;
    height: 2px;
    transition: 0.3s;
    right: 0;
}

.rbt-card.variation-01.rbt-hover.elegant-course .tutor-btn.tutor-btn-outline-primary:hover:after,
.rbt-course-area .tutor-btn.tutor-btn-outline-primary.tutor-btn-md:hover:after {
    width: 100%;
    left: 0;
    right: auto;
}

.tutor-download-certificate {
    padding-top: 100px;
    padding-bottom: 130px;
}

@media only screen and ( max-width: 1199px ) {
    .elementor-custom-humberger-menu {
        display: block;
    }
}




/*-----------------------------
    Section Circle Shadow
--------------------------------*/ 
.rbt-sec-cir-shadow-1 {
    position: relative;
    overflow: hidden;

    .gradient-shape-top {
        position: absolute;
        width: 347px;
        height: 347px;
        border-radius: 50%;
        right: 262px;
        top: -96px;
        background: linear-gradient(180deg, var(--color-primary) 0%, #C586EE 100%);
        filter: blur(300px);
        transform: rotate(-45deg);

        &.version-02 {
            width: 262px;
            height: 262px;
            right: 271px;
            top: -106px;
        }

        &.version-03 {
            right: 194px;
            top: -150px;
        }

        &.version-04 {
            width: 262px;
            height: 262px;
            right: 261px;
            top: -156px;
        }
    }

    .gradient-shape-bottom {
        position: absolute;
        width: 282px;
        height: 282px;
        border-radius: 50%;
        left: 0!important;
        bottom: -68px;
        background: linear-gradient(180deg, #FB64AD 0%, #C586EE 100%);
        filter: blur(300px);
        transform: rotate(-45deg);

        &.version-02 {
            left: 252px;
            bottom: -50px;
        }

        &.version-03 {
            left: 264px;
            bottom: -445px;
        }

        &.version-04 {
            left: 212px;
            bottom: -280px;
        }
    }
}

@media only screen and ( max-width: 520px ) {
    .rbt-sec-cir-shadow-1 { 
        .gradient-shape-bottom { 
            left: 0 !important;
        }
    }    
}

/*-----------------------------
    Section Box
--------------------------------*/ 

.rbt-section-box {
    margin: 0 50px;
    border-radius: var(--radius-10);

    @media #{$laptop-device} {
        margin: 0 25px;
    }

    @media #{$lg-layout, $md-layout, $sm-layout, $large-mobile} {
        margin: 0;
        border-radius: 0;
    }

    &.box-footer {
        margin-bottom: 50px;

        @media #{$laptop-device} {
            margin-bottom: 25px;
        }

        @media #{$lg-layout, $md-layout, $sm-layout, $large-mobile} {
            margin-bottom: 0;
        }
    }
}


/*-----------------------------
    Primary Font Class
--------------------------------*/ 
.rbt-font-primary {
    font-family: var(--font-primary) !important;
}

.rbt-countdown-area.countdown-style-1.version-02.mt--50.bg_image.bg_image_fixed  .countdown {
    padding: 20px 30px;
}

.rbt-schedule-author-box-single {
    flex: 0 0 100%;
}

.single-courses .course-sidebar-top a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button {
    height: 60px !important;
    line-height: 60px !important;
}

.rbt-course-details-area a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button {
    background-size: 300% 100% !important;
    background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-secondary), var(--color-primary));
    color: #fff;
}

.rbt-course-details-area a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button {
	width: 100%;
}

.post-type-archive-courses a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart {
	height: unset;
	line-height: 1.1em;
	position: relative;
}

.post-type-archive-courses a.tutor-btn.tutor-btn-outline-primary.tutor-btn-md.tutor-btn-block.product_type_simple.add_to_cart_button.ajax_add_to_cart:after {
	color: currentcolor!important;
}

.post-type-archive-courses .rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button {
    position: relative;
    width: auto;
}

.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button {
    position: relative;
}

.rbt-card.variation-01.rbt-hover a.tutor-btn.add_to_cart_button:hover {
    color: var(--color-primary)!important;
}

@media only screen and (max-width: 1646px) and (min-width: 1600px) {
    .page-home-university-about .rbt-header-top.rbt-header-top-1 .rbt-header-sec-col.rbt-header-right {
        flex-basis: 60%;
    }
    
    .page-home-university-about .rbt-header-top.rbt-header-top-1 .rbt-header-sec-col.rbt-header-left {
        flex-basis: 40%;
    }
}

body.active-light-mode .logo a img.logo-dark-mode {
    display: none;
}

.rbt-course-sidebar-main-wrapper .contact-with-us {
    position: relative;
}

.rbt-course-details-layout-style-five .related-course {
    padding-bottom: 120px;
}

img.logo-dark-mode {
    display: none;
}

.tutor-single-course-sidebar .tutor-sidebar-card .tutor-card-body {
	background-color: transparent;
}

.mainmenu-nav .rbt-megamenu.grid-item-full {
    overflow: hidden;
    max-height: 640px;
    overflow-y: scroll !important;
    scrollbar-width: none;
}


@media only screen and ( max-width: 520px ) {
    .rbt-sec-cir-shadow-1 { 
        .gradient-shape-bottom { 
            left: 0 !important;
        }
    }    
}

.rbt-countdown-area.countdown-style-1.version-02.mt--50.bg_image.bg_image_fixed  .countdown {
    padding: 20px 30px;
}

.tutor-btn-block{
	width: auto;
}

.single-courses button.tutor-btn.tutor-btn-primary.tutor-btn-lg.tutor-btn-block.tutor-mt-24.tutor-enroll-course-button.tutor-static-loader,
.single-courses .add_to_cart_button  {
		width: 100%;
}
