.rbt-dashboard-top-wrapper-main {

    /* Admin dashboard */
    a,
    button {
        transition: all .3s;
    }

    .rainbow-dashboard-box {
        margin-right: 20px;
        background: #fff;
        border-radius: 5px;
        padding: 30px;
        margin-top: 30px;
    }

    .rainbow-dashboard-box-single {
        padding: 30px;
        padding: 50px 60px;
        border-radius: 5px;
    }

    .bg-cover {
        background-size: cover;
        background-position: center center;
    }

    .rainbow-dashboard-box-single .rainbow-subtitle {
        font-size: 12px;
        color: #fff;
        margin-bottom: 20px;
        display: block;
    }

    .rainbow-dashboard-box-single .rainbow-title,
    .rbt-dashboard-single-card .rainbow-title {
        font-size: 30px;
        color: #fff;
        margin: 0;
        line-height: 1.2;
        margin-bottom: 10px;
    }

    .rainbow-dashboard-box-single .rainbow-content {
        font-size: 16px;
        margin: 0;
        color: #fff;
        margin-bottom: 30px;
    }

    .rainbow-dashboard-btn {
        background: #fff;
        border: 1px solid rgba(255, 255, 255, 0.33);
        box-shadow: 0px 23px 20.9px -21px #5B048B;
        border-radius: 4px;
        display: inline-block;
        text-decoration: none;
        padding: 10px 26px;
        color: #fff;
        color: #000;
    }

    .rainbow-dashboard-btn:hover {
        outline: none;
        box-shadow: none;
        color: #fff;
        background-color: #029CFF;
        border-color: transparent;
    }

    .m-0 {
        margin: 0 !important;
    }

    .rbt-dashboard-single-card {
        text-align: center;
        border-radius: 10px;
    }

    .rbt-support-banner a {
        display: block;
        text-align: right;
        background-size: cover;
        background-position: center left;
    }

    .rbt-dashboard-single-card .rainbow-dashboard-btn {
        margin-top: 10px;
        font-weight: 500;
        box-shadow: 0px 20px 30px -18px #073983;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
    }

    .bg-default {
        background-size: cover;
        background-position: center center;
    }

    .rbt-dashboard-single-card {
        position: relative;
        min-height: 416px;
    }

    .rbt-dashboard-single-card .content {
        padding: 30px;
        padding-bottom: 0;
        text-align: left;
    }

    .rbt-dashboard-single-card .image {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        align-items: flex-end;
    }

    .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner {
        padding: 0;
    }

    .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner .content {
        padding: 30px;
        padding-bottom: 0;
    }

    div[data-background] {
        background-size: cover;
    }

    img {
        max-width: 100%;
    }

    .rbt-dashboard-single-card .rainbow-title {
        font-size: 30px;
        letter-spacing: -1px;
    }

    .rbt-content-inner .rbt-support-img {
        transform: translateY(5px);
    }

    .rbt-text-start.rbt-support-box-content {
        padding: 30px;
        padding-bottom: 0;
    }

    .rbt-content-inner .rbt-support-img img {
        margin-top: 42px;
    }

    .rbt-plugin-card {
        background: #FFFFFF;
        border: 1px solid #ddd;
        border-radius: 6px;
        box-shadow: 0px 6px 34px rgba(215, 216, 222, 0.41);
        ;
        padding: 16px;
    }

    .rainbow-dashboard-box {
        margin: 12px !important;
        margin-bottom: 0 !important;
    }



    .el-license-valid {
        padding: 1px 8px;
    }

    .plugin-name {
        margin-top: 0;
    }

    .version {
        margin-top: 0;
    }

    .rbt-plugin-list {
        margin-top: 36px;
    }

    .screenshot img {
        border-radius: 6px;
    }

    .rbt-plugin-card {
        padding: 30px 25px;
    }

    span.alert.alert-warning {
        color: inherit;
    }

    .rbt-tab-buttons .rbt-tab-content-left {
        display: flex;
        flex-wrap: wrap;
        grid-gap: 10px 0;
    }

    .rbt-dashboard-tab-area .ocdi__content-container {
        background: transparent;
        padding: 0;
    }

    .rbt-dashboard-tab-area .ocdi__content-container * {
        text-decoration: none;
    }

    .rbt-dashboard-tab-area .ocdi__theme-about {
        background: #fff;
        box-shadow: none;
        padding: 50px;
    }

    .rbt-plugin-card .version {
        margin-bottom: 23px;
    }

    .rbt-plugin-card {
        min-height: 168px;
        border: 0;
    }

    .rbt-plugin-card a {
        text-decoration: none;
    }

    .directivate-btn {
        background: #dc3545;
        color: #fff;
        padding: 5px 7px;
        text-decoration: none;
        border-radius: 5px;
        margin-left: 5px;
    }

    .activate-btn {
        background: #28a745;
        color: #fff;
        padding: 5px 7px;
        text-decoration: none;
        border-radius: 5px;
        margin-left: 5px;
    }

    .install-btn {
        background: #ffc107;
        color: #fff;
        padding: 5px 7px;
        text-decoration: none;
        border-radius: 5px;
        margin-left: 5px;
    }

    .directivate-btn:hover,
    .install-btn:hover,
    .activate-btn:hover {
        color: #fff;
    }

    .rbt-tab-content-wrapper a {
        text-decoration: none;
        border-radius: 5px;
    }

    .rbt-tab-content-wrapper a {
        text-decoration: none;
    }

    a.rbt-tab-content-link {
        display: flex;
        align-items: center;
        grid-gap: 0 5px;
        color: #394EF4;
    }

    .rbt-tab-buttons {
        background: #fff;
        padding: 15px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    a.rbt-tab-content-link:hover {
        background: #007aff !important;
        color: #fff;
    }

    a.rbt-tab-content-link:hover img {
        filter: brightness(100);
    }

    .rbt-tab-buttons button,
    .rbt-tab-buttons a {
        background: #eaeefe;
        color: var(--color-dark-1);
        border: 0;
        padding: 15px 30px;
        cursor: pointer;
        margin-right: 10px;
        border-radius: 5px;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
    }

    .rbt-tab-buttons button.active {
        background: #007aff !important;
        position: relative;
        color: #fff;
    }

    .rbt-tab-buttons button:hover,
    .rbt-tab-buttons a:hover {
        background: #d0d7f4;
    }

    .rbt-tab-buttons button.active::after {
        position: absolute;
        width: 21px;
        height: 7.51px;
        content: "";
        left: 50%;
        transform: translateX(-50%);
        bottom: -6.51px;
        clip-path: polygon(0 0, 100% 0, 50% 100%);
        background: #007aff;
    }

    .rainbow-dashboard-box-wrapper {
        margin: 0 12px;
        margin-top: 30px;
    }

    .rbt-m-0-i {
        margin: 0 !important;
    }

    .rbt-mt-30-i {
        margin-top: 30px !important;
    }

    .rbt-license-tab-form {
        background: #fff;
        border-radius: 10px;
        margin-top: 30px;
        padding: 30px;
    }

    .rbt-license-tab-form .el-license-container {
        margin: 0;
        padding: 0;
    }

    .rbt-license-tab-form .el-license-title {
        font-size: 36px;
        color: #1B182C;
        margin: 0;
        line-height: 1;
    }

    .rbt-inactive--page.rbt-license-wrapper {
        background: #fff;
        margin-left: 20px;
        margin-top: 20px;
        padding: 50px;
        border-radius: 10px;
    }

    ul.el-license-info.rbt-el-license-info p {
        font-size: 16px;
        margin: 0;
        color: #717189;
        line-height: 26px;
        margin-bottom: 10px;
    }

    .rbt-license-tab-form hr {
        display: none;
    }

    .rbt--video-container iframe {
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }

    .rbt-inactive--page.rbt-license-wrapper .el-license-container {
        background: transparent;
        padding: 0;
        margin: 0;
    }

    .rbt-tab-content-right a {
        color: #394EF4;
    }

    .rbt-tab-content .ocdi__title-container {
        background: transparent;
        box-shadow: none;
        padding: 43px 0px;
        padding-bottom: 20px;
    }

    .rbt-tab-content .ocdi__title-container-title {
        display: block;
        font-size: 36px;
        font-weight: 500;
    }

    .rainbow-dashboard-box-wrapper {
        background: #F6F7FE;
        padding: 30px;
        border-radius: 10px;
    }

    .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title .theme-version {
        position: absolute;
        top: -2px;
        right: -100%;
        background: linear-gradient(126.67deg, #748BFC -5.73%, #FF72BA 96.56%);
        border-radius: 30px;
        padding: 5px 12px;
        color: #fff;
        font-size: 14px;
    }

    .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title {
        display: inline-block;
        position: relative;
    }

    .rbt-dashboard-tab-area .ocdi__theme-about-info .theme-title .theme-version::after {
        position: absolute;
        left: 10px;
        top: 86%;
        content: "";
        width: 9.53px;
        height: 8px;
        clip-path: polygon(0 0, 100% 50%, 0 100%);
        background: #a582e5;
    }

    .rbt-tab-content .ocdi__theme-about-info .theme-description {
        color: #717189;
    }

    .rbt-tab-content .ocdi__theme-about-info .theme-tags {
        color: #717189;
    }

    .rbt-tab-content .ocdi__theme-about-info .ocdi-import-mode-switch {
        background: #3458F0;
        color: #fff;
        padding: 12px;
        border-radius: 5px;
    }

    .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl hr {
        display: none;
    }

    .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl.js-ocdi-gl::before {
        content: "Import Pre-Built Demos";
        text-align: center;
        display: block;
        color: #1B182C;
        font-size: 36px;
        font-weight: 500;
        padding-bottom: 80px;
        padding-top: 50px;
    }

    .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
        position: relative;
        -ms-flex: 0 0 calc(25% - 30px);
        flex: 0 0 calc(25% - 30px);
        padding: 0 10px;
        padding-top: 30px;
        background: #fff;
        border: 0;
        box-shadow: 0px 0px 35.4466px 14.6974px rgba(0, 0, 0, 0.05);
        border-radius: 10px;
    }

    .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item::after {
        position: absolute;
        left: 15px;
        top: 5px;
        content: url(../../../assets/images/dashboard/three-dot.svg);
    }

    .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:first-child,
    .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:last-child {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(9, 2, 47, 0.1);
        background: #fff !important;
        padding: 0 12px;
        border-radius: 4px;
        color: rgba(9, 2, 47, 0.6);
    }

    .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl-item-buttons a:last-child {
        border: 0;
        background-color: #3458F0 !important;
        color: #fff;
    }

    .rbt-dashboard-tab-area .ocdi__gl-item:hover .ocdi__gl-item-footer {
        border: 0;
        padding: 12px 0;
    }

    .rbt-dashboard-tab-area .ocdi__gl-item:hover .ocdi__gl-item-buttons {
        display: flex;
        justify-content: center;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        padding: 19px 0;
    }

    .rbt-dashboard-tab-area .ocdi__gl-item-footer.ocdi__gl-item-footer--with-preview {
        border: 0;
    }

    .rbt-justify-content-center {
        justify-content: center;
    }

    /**
    * table css
    **/
    .rbt-box.rbt-status.rbt-theme-style {
        background: #fff;
        padding: 30px;
        margin-right: 20px;
        border-radius: 5px;
        margin-top: 20px;
    }

    .rbt-table {
        margin: 0;
        padding: 0;
        border-collapse: collapse;
        width: 100%;
    }

    .rbt-table-row {
        display: flex;
    }

    .rbt-table-row>div {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
    }

    /* Header styles */
    .rbt-box-header h3,
    .rbt-box-header h4 {
        margin: 0;
    }

    /* Theme and Server section styles */
    .rbt-box-content {
        margin-top: 10px;
    }

    .rbt-box-content h4 {
        margin-top: 20px;
    }

    /* Error message styles */
    .rbt-status-error {
        color: #ff0000;
    }

    /* Odd row styles */
    .rbt-odd .rbt-table-row:nth-child(odd)>div {
        background-color: #f9f9f9;
    }

    /* Hover effect */
    .rbt-table-row:hover>div {
        background-color: #f1f1f1;
    }

    .rbt-content-inner .rbt-content-right {
        position: absolute;
        right: -1px;
        top: 0;
        height: 100%;
    }

    .rainbow-dashboard-box-single {
        position: relative;
    }

    .rbt-content-inner .rbt-content-right img {
        height: 100%;
    }

    .rbt-license-tab-form .el-license-container .el-license-field input {
        max-width: 100%;
    }

    .rbt-support-author img {
        width: 100%;
        object-fit: cover;
        max-height: 416px;
        border-radius: 10px;
    }

    .rbt-tab-content-right a {
        margin-right: 0;
    }

    /**
    * responsive
    */
    @media (max-width: 1399px) {
        .rbt-content-inner .rbt-content-right img {
            display: none;
        }
    }

    @media (max-width: 1199px) {

        .rbt-tab-buttons button,
        .rbt-tab-buttons a {
            padding: 9px 18px;
        }
    }

    @media (min-width: 1200px) and (max-width: 1399px),
    (min-width: 992px) and (max-width: 1199px),
    (min-width: 768px) and (max-width: 991px) {
        .rbt-tab-buttons .rbt-tab-content-left {
            flex: 0 0 70%;
        }

        .rbt-tab-buttons button {
            margin-bottom: 15px;
        }

        .rbt-content-inner .rbt-support-img img {
            margin-top: 0px;
        }

        .rbt-tab-buttons button,
        .rbt-tab-buttons a {
            margin-bottom: 15px;
        }
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .rbt-dashboard-single-card {
            min-height: 396px;
        }

        .rbt-dashboard-single-card .rainbow-title {
            font-size: 21px;
        }
    }

    @media (max-width: 767px) {
        .rbt-dashboard-single-card .rainbow-title {
            font-size: 21px;
        }

        .rbt-dashboard-single-card.rbt-box-no-space .rbt-content-inner .content {
            padding: 20px;
        }

        .rbt-dashboard-single-card {
            min-height: 336px;
        }

        .rbt-dashboard-single-card .content {
            padding: 20px;
            padding-bottom: 0;
        }

        .rbt-dashboard-single-card .rainbow-dashboard-btn {
            padding: 10px 12px;
        }
    }

    @media (min-width: 992px) and (max-width: 1199px) {

        .rbt-dashboard-single-card.bg-default.padding-box .rbt-content-inner {
            padding: 0;
        }

        .rbt-dashboard-single-card.padding-box:not(p-0) {
            padding-bottom: 0;
        }

        .rbt-dashboard-single-card {
            border-radius: 10px;
        }

        .rbt-support-banner {
            margin-top: 0;
        }

        .rbt--video-container {
            margin-left: 0;
        }

        .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
            flex: 0 0 calc(33.33% - 30px);
        }

        .rbt-dashboard-tab-area .ocdi__content-container .ocdi__theme-about-screenshots {
            flex: 0 0 100%;
        }

        .rbt-dashboard-tab-area .ocdi__theme-about {
            flex-wrap: wrap;
            grid-gap: 30px 0;
        }
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .rbt-dashboard-single-card.rbt-no-box-padding.bg-default {
            height: 100%;
        }

        .rbt-support-banner {
            margin-top: 0;
        }

        .rbt--video-container {
            margin-left: 0;
        }

        .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
            flex: 0 0 calc(50% - 30px);
        }

        .rbt-dashboard-single-card.bg-default.padding-box .rbt-content-inner {
            padding: 0;
        }
    }

    @media (max-width: 767px) {
        .rbt-tab-buttons {
            display: block;
        }

        .rbt-dashboard-single-card .rainbow-title {
            font-size: 25px;
        }

        .rbt-support-banner a {
            background-size: contain;
            background-repeat: no-repeat;
            min-height: auto;
        }

        .rbt-support-banner a {
            background-size: contain;
            background-repeat: no-repeat;
            min-height: auto;
        }

        .rainbow-dashboard-box-single.bg-default .rbt-content-inner {
            padding: 0;
        }

        .rainbow-dashboard-box-single.bg-default .rbt-content-inner .rainbow-title {
            font-size: 28px;
        }

        .rbt-tab-buttons button,
        .rbt-tab-buttons a {
            margin-bottom: 15px;
        }

        .rbt-tab-content-right a.rbt-tab-content-link {
            display: inline-block;
            margin-bottom: 0;
        }

        .rbt--video-container {
            margin-left: 0;
        }

        .rbt-license-tab-form {
            padding: 10px 10px;
        }

        .rbt-dashboard-tab-area .ocdi__gl-item-container.js-ocdi-gl-item-container .ocdi__gl-item {
            flex: 0 0 calc(100%);
        }

        .rbt-dashboard-tab-area .ocdi__content-container .bottom-content {
            margin-top: 20px;
        }

        .rbt-dashboard-tab-area .ocdi__content-container * .ocdi-import-mode-switch {
            float: none;
        }
    }

    @media (max-width: 575px) {
        .rbt-tab-content .ocdi__title-container {
            padding: 50px 19px;
        }

        .rbt-tab-content .ocdi__title-container .ocdi__title-container-title {
            font-size: 26px;
        }

        .rbt-dashboard-tab-area .ocdi__content-container .ocdi__intro-text {
            padding: 0 22px;
        }

        .rainbow-dashboard-box-single {
            padding: 21px;
        }

        .rbt-tab-buttons button,
        .rbt-tab-buttons a {
            font-size: 12px;
        }

        .rbt-dashboard-tab-area .ocdi__theme-about {
            padding: 20px;
        }

        .rbt-dashboard-tab-area .ocdi__theme-about {
            padding: 20px;
        }

        .rbt-dashboard-tab-area .ocdi__content-container .ocdi__gl.js-ocdi-gl::before {
            font-size: 20px;
            line-height: 1.4;
        }
    }

    @media (max-width: 1599px) {
        .rbt-order-last-max-xxl {
            order: 9;
        }
    }

    /**
    * modal style
    */
    /*-- Variables --*/
    :root {
        --color-primary: #ea3940;
        --color-secondary: #10b3d6;
        --color-warning: #ffa100;
        --color-success: #0ed193;
        --color-primary-opacity: rgba(255, 69, 81, 0.40);
        --color-white: #fff;
        --color-white-2: #edeaf5;
        --color-white-3: #ececec;
        --color-white-4: #f3f2fb;
        --color-white-5: #fdfcfe;
        --color-white-6: #f4f7fb;
        --color-white-7: #f9f8ff;
        --color-black: #000;
        --color-dark-1: #04122b;
        --color-dark-3: #444;
        --color-dark-4: #192a55;
        --color-dark-blue-1: #162f6a;
        --color-dark-blue-2: #2f415f;
        --color-border-2: #ededed;
        --color-border-3: #e3e0f5;
        --color-placeholder: #2b4d9e;
        --color-body: #6b7385;
        --color-gray-2: #c1c6cb;
        --color-gray-3: #e6e6e6;
        --color-gray-5: #9facb6;
        --color-gray-6: #7085aa;
        --color-blue-2: #6e8393;
        --color-grayscale-1: #b8b8b8;
        --color-grayscale-2: #dee2ed;
        --color-grayscale-3: #d4d4d4;
        --color-grayscale-4: #efefef;
        --color-grayscale-5: #eeebff;
        --color-grayscale-7: #e5e8f0;
        --color-grayscale-8: #f3f1ff;
        --color-grayscale-11: #f8f8f8;
        --radius-full: 100%;
        --radius-xxl: 20px;
        --radius-lg: 12px;
        --f-medium: 500;
        --f-bold: 700;
        --shadow-1: 0px 10px 30px 0px rgba(19, 65, 98, 0.08);
        --transition: 0.2s;
        --font-primary: "Manrope", sans-serif;
        --font-secondary: "IBM Plex Sans", sans-serif;
        --font-size-b1: 18px;
        --font-size-b2: 16px;
        --font-size-b3: 14px;
        --font-size-b4: 12px;
        --font-size-b5: 10px;
        --h5: 24px;
        --h6: 20px;
        --line-height-b2: 1.67;
        --line-height-b3: 1.67;
        --div-gap-1: 10px;
        --gradient-primary: linear-gradient(94deg, #fe2a5e 1.16%, #ffa61b 216.18%);
        --gradient-secondary: linear-gradient(0deg, rgba(37, 132, 174, 0.00) 0%, rgba(37, 132, 174, 0.14) 100%);
    }

    .backdrop {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(7.5px);
        padding-right: 0px !important;
    }

    .modal-content {
        width: 745px;
        border-radius: var(--radius-lg);
    }

    .modal-content.w--fit {
        width: fit-content;
        max-width: 100%;
    }

    .modal-dialog {
        max-width: fit-content;
    }

    .modal-close-btn {
        position: absolute;
        top: -24px;
        right: -24px;
        display: inline-block;
        background: var(--color-white);
        border: 1px solid #e3e0f5;
        width: 48px;
        height: 48px;
        border-radius: var(--radius-full);
    }

    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .modal-close-btn {
            top: 0;
            right: 0;
            width: 32px;
            height: 32px;
            background: transparent;
            border: none;
            font-size: var(--h6);
        }
    }

    @media only screen and (max-width: 767px) {
        .modal-close-btn {
            top: 0;
            right: 0;
            width: 32px;
            height: 32px;
            background: transparent;
            border: none;
            font-size: var(--h6);
        }
    }

    .rbt-modal-title {
        color: var(--color-dark-1);
        font-family: var(--font-primary);
        font-size: var(--h5);
        font-weight: var(--f-bold);
        line-height: 141.667%;
        border-bottom: 1px solid #e3e0f5;
        padding-bottom: 24px;
        margin-bottom: 32px;
    }

    .rbt-modal-title.inner-title {
        font-size: var(--font-size-b1);
        line-height: 140%;
        border: none;
        margin-bottom: 0;
    }

    .rbt-product-changelog {
        margin-bottom: 40px;
    }

    .changelog-info {
        padding: 12px 16px;
    }

    @media only screen and (max-width: 767px) {
        .changelog-info {
            padding: 8px;
        }
    }

    .rbt-product-changelog .changelog-info:nth-child(even) {
        background: #f8f8f8;
    }

    .changelog-info:last-child {
        border-radius: 0 0 8px 8px;
    }

    .changelog-info.filled {
        background: var(--color-grayscale-11);
    }

    .changelog-info .info-text {
        color: var(--color-blue-2);
        font-family: var(--font-secondary);
        font-size: var(--font-size-b2);
        font-weight: var(--f-medium);
        line-height: 150%;
    }

    .rbt-license-tab-form .notice.notice-error.is-dismissible {
        margin-bottom: 12px;
    }

    @media only screen and (max-width: 767px) {
        .changelog-info .info-text {
            font-size: var(--font-size-b3);
        }
    }

    .rbt-badge {
        padding: 3px 7px;
        border-radius: var(--radius-xxl);
        color: var(--color-white);
        font-family: var(--font-secondary);
        font-size: var(--font-size-b4);
        font-weight: var(--f-medium);
        line-height: 100%;
        letter-spacing: 0.6px;
        background: var(--color-secondary);
        font-size: 12px;
        border-radius: 3px;
    }

    @media only screen and (max-width: 767px) {
        .rbt-badge {
            font-size: var(--font-size-b5);
        }
    }

    .el-license-title .rbt-badge {
        text-wrap: nowrap;
    }

    .el-license-container .el-license-field input {
        max-width: 100%;
        border-color: #f1f2f3;
    }

    .el-license-active-btn input#submit:focus {
        box-shadow: none;
    }

    .el-license-container .el-license-field small {
        color: #717188;
        font-size: 16px;
        margin-top: 16px;
        display: block;
        line-height: 1.4;
    }

    .text-heading {
        color: #1d2327 !important;
    }

    .rbt-badge.badge-bg-2 {
        background: var(--color-primary);
    }

    .rbt-badge.badge-bg-3 {
        background: var(--color-success);
    }

    .rbt-badge.badge-bg-4 {
        background: var(--color-primary);
    }

    .rbt-badge.badge-bg-5 {
        background: var(--color-warning);
    }

    .inspired-badge {
        position: absolute;
        top: 24px;
        right: 24px;
    }

    @media only screen and (max-width: 767px) {
        .inspired-badge {
            top: 8px;
            right: 8px;
        }
    }

    .rbt-modal-wrapper>.rbt-modal-title {
        margin: 0;
        border: none;
        color: var(--color-dark-1);
        background: #eaeefe;
        padding: 12px 20px;
        border-top-left-radius: 9px;
        border-top-right-radius: 10px;
        font-size: 18px;
        border-bottom: 1px solid #e3e0f5;
    }

    .rbt-product-changelog {
        padding: 0 20px;
    }

    .rbt-modal-wrapper {
        position: sticky;
        top: 60px;
    }

    .rbt-modal-wrapper {
        border: 1px solid #e3e0f5;
        border-radius: 10px;
        padding-top: 0;
    }

    .rbt-changelog-body {
        max-height: 700px;
        overflow-y: auto;
    }

    .changelog-info.filled .row {
        display: flex;
    }


    .changelog-info .row .item:last-child {
        width: calc(100% - 84px);
    }

    .rbt-text-left {
        text-align: left;
    }

    .changelog-info .row .item:first-child {
        width: 60px;
    }

    @media (min-width: 1600px) and (max-width: 1799px) {
        .rbt-dashboard-single-card {
            min-height: 390px;
        }
    }

    @media (min-width: 1200px) and (max-width: 1399px) {
        .rbt-dashboard-single-card.rbt-no-box-padding {
            padding: 30px;
        }
    }

    .rbt-danger {
        color: red;
    }

    .redux-container .redux-sidebar .redux-group-menu li.hasSubSections.redux-section-hover>a {
        background: linear-gradient(90deg, #f61b10, #ef0963);
    }


    /**
    * OCDI CSS
    */
    .ocdi {
        max-width: none;
    }

    .ocdi h2 {
        text-align: inherit;
        font-size: 32px;
        line-height: 39px;
        font-weight: 500;
        margin-bottom: 20px;
    }

    .ocdi h2:first-child,
    .ocdi h3:first-child {
        margin-top: 0;
    }

    .ocdi hr {
        margin: 30px 0;
    }

    .ocdi .notice,
    .ocdi .update-nag,
    .ocdi #update-nag {
        display: block !important;
        margin: 0 0 30px 0;
    }

    .ocdi-notices-wrapper {
        display: none;
    }

    .ocdi-notices-wrapper .notice,
    .ocdi-notices-wrapper .update-nag,
    .ocdi-notices-wrapper #update-nag {
        margin: 0 0 15px 0;
    }

    .ocdi-notices-wrapper .notice:last-child,
    .ocdi-notices-wrapper .update-nag:last-child,
    .ocdi-notices-wrapper #update-nag:last-child {
        margin-bottom: 30px;
    }

    .ocdi__admin-notices .ocdi-notices-wrapper {
        display: block;
    }

    .ocdi-button-disabled {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
    }

    .ocdi-content-notice {
        padding: 25px;
        background: #e5f5fa;
        border-radius: 6px;
        margin: 30px;
    }

    .ocdi-content-notice p {
        font-size: 14px;
        line-height: 24px;
        color: #444;
        margin: 0 0 10px 0;
    }

    .ocdi-content-notice p:last-child {
        margin-bottom: 0;
    }

    .ocdi-content-notice--warning {
        background: #fff8e5;
        margin-top: 0;
    }

    .ocdi-content-notice--warning p {
        font-size: 16px;
        line-height: 24px;
    }

    .ocdi-importing,
    .ocdi-imported {
        display: none;
        text-align: center;
        padding: 30px;
    }

    .ocdi-importing p,
    .ocdi-imported p {
        font-size: 16px;
        line-height: 19px;
        font-weight: 300;
        color: #444;
        margin: 0 0 10px 0;
    }

    .ocdi-importing p:last-child,
    .ocdi-imported p:last-child {
        margin-bottom: 0;
    }

    .ocdi-importing-header h2,
    .ocdi-imported-header h2 {
        font-size: 24px;
        line-height: 29px;
        margin-bottom: 10px;
    }

    .ocdi-importing-header p,
    .ocdi-imported-header p {
        margin-bottom: 5px;
    }

    .ocdi-importing-header p:last-child,
    .ocdi-imported-header p:last-child {
        margin-bottom: 0;
    }

    .ocdi-importing-content,
    .ocdi-imported-content {
        margin: 0 20px;
    }

    .ocdi-importing-content-importing,
    .ocdi-imported-content-importing {
        width: 415px;
        height: 228px;
        margin: 50px 0 20px 0;
    }

    .ocdi-importing-content-imported,
    .ocdi-imported-content-imported {
        margin: 80px 0 50px 0;
    }

    .ocdi-importing-content-imported--success,
    .ocdi-imported-content-imported--success {
        width: 156px;
        height: 124px;
    }

    .ocdi-importing-content-imported--error,
    .ocdi-imported-content-imported--error,
    .ocdi-importing-content-imported--warning,
    .ocdi-imported-content-imported--warning {
        width: 124px;
        height: 124px;
    }

    .ocdi-importing-content .notice,
    .ocdi-imported-content .notice {
        text-align: left;
        margin: 30px 0;
    }

    .ocdi-importing-content .notice p,
    .ocdi-imported-content .notice p {
        padding: 15px 3px;
        font-size: 14px;
        line-height: 22px;
        color: #777;
    }

    .ocdi-importing-footer,
    .ocdi-imported-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        min-height: 100px;
        background-color: #f8f8f8;
        border-top: 1px solid #ddd;
        margin: 30px -30px -30px;
    }

    .ocdi-importing-footer a.button,
    .ocdi-imported-footer a.button {
        flex: 0 1 25%;
        margin-right: 30px;
    }

    .ocdi-importing-footer a.button:last-child,
    .ocdi-imported-footer a.button:last-child {
        margin-right: 0;
    }

    .ocdi .button.button-hero {
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
        min-height: 40px;
        padding: 9px 18px;
    }

    .ocdi .button.button-hero.ocdi__button.button-primary:disabled,
    .ocdi .button.button-hero.ocdi__button.button-primary[disabled] {
        color: #fff !important;
        background: #999 !important;
        border-color: #999 !important;
        opacity: 0.5;
    }

    .ocdi__redux-option-name-input {
        margin-left: 10px;
        width: 137px;
        border-radius: 3px !important;
        padding: 0 10px !important;
        font-size: 13px !important;
        line-height: 16px !important;
    }

    .ocdi-hide-input {
        width: 0.1px !important;
        height: 0.1px !important;
        opacity: 0 !important;
        overflow: hidden !important;
        position: absolute !important;
        z-index: -1 !important;
    }

    .feature-section+hr {
        margin-top: 0;
    }

    #wpbody select {
        height: auto;
        padding: 0.62em;
        line-height: inherit;
    }

    .ocdi__title-container {
        height: 30px;
        background-color: #fff;
        padding: 20px 30px;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.07);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .ocdi__title-container-title {
        margin: 0;
        font-size: 24px;
        line-height: 29px;
        font-weight: 700;
    }

    .ocdi__title-container-icon {
        width: 19px;
        height: 19px;
    }

    .ocdi__content-container {
        padding: 30px;
    }

    .ocdi__content-container-content {
        display: flex;
    }

    .ocdi__content-container-content--main {
        flex: 1;
        margin-right: 30px;
        background: #fff;
        box-sizing: border-box;
        border: 1px solid #ddd;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.07);
    }

    .ocdi__content-container-content--side {
        width: 373px;
    }

    .ocdi__content-container-content--side .ocdi__card-content {
        padding: 0;
    }

    .ocdi__content-container-content--side .screenshot.blank {
        height: 278px;
        border: 1px solid #ccd0d4;
        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAALElEQVQYGWO8d+/efwYkoKioiMRjYGBC4WHhUK6A8T8QIJt8//59ZC493AAAQssKpBK4F5AAAAAASUVORK5CYII=);
    }

    .ocdi__content-container-content--side .ocdi__card-footer {
        padding: 15px;
    }

    .ocdi__content-container-content--side .ocdi__card-footer h3 {
        font-size: 16px;
        line-height: 19px;
        font-weight: 500;
        margin: 0;
    }

    .ocdi__content-container-content--side img {
        width: 100%;
        display: block;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header,
    .ocdi__content-container-content .ocdi-create-content-header {
        padding: 30px;
        border-bottom: 1px solid #ddd;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header h2,
    .ocdi__content-container-content .ocdi-create-content-header h2 {
        font-size: 22px;
        line-height: 26px;
        font-weight: normal;
        margin: 0 0 6px 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header p,
    .ocdi__content-container-content .ocdi-create-content-header p {
        font-size: 16px;
        line-height: 22px;
        font-weight: 300;
        color: #444;
        margin: 0 0 10px 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header p:last-child,
    .ocdi__content-container-content .ocdi-create-content-header p:last-child {
        margin: 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header .notice,
    .ocdi__content-container-content .ocdi-create-content-header .notice {
        margin: 30px 0 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-header .notice p,
    .ocdi__content-container-content .ocdi-create-content-header .notice p {
        margin: 0.5em 0;
        font-size: 13px;
        line-height: 1.5;
        color: #3c434a;
        font-weight: normal;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item,
    .ocdi__content-container-content .ocdi-create-content-content .content-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 30px;
        padding: 25px 0;
        border-bottom: 1px solid #eee;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .ocdi-loading,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .ocdi-loading,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .ocdi-loading,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .ocdi-loading {
        display: none;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active {
        cursor: default;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox {
        border: none;
        background: #64b450;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox .ocdi-check-icon {
        display: inline-block;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--active .content-item-checkbox input[type=checkbox]:checked+.checkbox::after {
        display: none !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--required,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--required,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--required,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--required {
        cursor: default;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .ocdi-loading,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .ocdi-loading,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .ocdi-loading,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .ocdi-loading {
        display: block;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox {
        border: none !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox .ocdi-lock-icon {
        display: none !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .plugin-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item--loading .content-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item--loading .content-item-checkbox input[type=checkbox]+.checkbox::after {
        display: none !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-content {
        margin-right: 15px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title {
        display: flex;
        align-items: center;
        margin: 0 0 4px 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title span,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title span,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title span,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title span {
        margin-left: 5px;
        background-color: #ffb900;
        width: 16px;
        height: 16px;
        border-radius: 8px;
        margin-top: -1px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-content-title span img,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-content-title span img,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-content-title span img,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-content-title span img {
        width: 10px;
        height: 10px;
        margin: 3px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-info p,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-info p,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-info p,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-info p {
        color: #00a32a !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item-error p,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item-error p,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item-error p,
    .ocdi__content-container-content .ocdi-create-content-content .content-item-error p {
        color: #d63638 !important;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item:last-child,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item:last-child,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item:last-child,
    .ocdi__content-container-content .ocdi-create-content-content .content-item:last-child {
        padding-bottom: 30px;
        border-bottom: none;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item label,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item label,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item label,
    .ocdi__content-container-content .ocdi-create-content-content .content-item label {
        display: block;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item h3,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item h3,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item h3,
    .ocdi__content-container-content .ocdi-create-content-content .content-item h3 {
        font-size: 18px;
        line-height: 22px;
        color: #444;
        font-weight: 500;
        margin: 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item p,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item p,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item p,
    .ocdi__content-container-content .ocdi-create-content-content .content-item p {
        font-size: 14px;
        line-height: 17px;
        color: #777;
        margin: 0 0 6px 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item p:last-child,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item p:last-child,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item p:last-child,
    .ocdi__content-container-content .ocdi-create-content-content .content-item p:last-child {
        margin: 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox {
        box-sizing: border-box;
        position: relative;
        display: block;
        background: #f1f1f1;
        width: 32px;
        height: 32px;
        border-radius: 16px;
        border: 1px solid #ddd;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox .ocdi-check-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox .ocdi-check-icon {
        display: none;
        width: 20px;
        height: 20px;
        margin: 6px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox .checkbox .ocdi-lock-icon,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox .checkbox .ocdi-lock-icon {
        position: absolute;
        width: 14px;
        height: 17px;
        bottom: -5px;
        right: -2px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox input[type=checkbox],
    .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox input[type=checkbox] {
        display: none;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .plugin-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .plugin-item .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .plugin-item .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-install-plugins-content-content .content-item .content-item-checkbox input[type=checkbox]:checked+.checkbox::after,
    .ocdi__content-container-content .ocdi-create-content-content .content-item .content-item-checkbox input[type=checkbox]:checked+.checkbox::after {
        content: '';
        display: block;
        font-size: 10px;
        background: #007cba;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        margin: 5px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content-notice,
    .ocdi__content-container-content .ocdi-create-content-content-notice {
        display: none;
        padding: 25px;
        background: #e5f5fa;
        border-radius: 6px;
        margin: -10px 30px 30px 30px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content-notice p,
    .ocdi__content-container-content .ocdi-create-content-content-notice p {
        font-size: 14px;
        line-height: 24px;
        color: #444;
        margin: 0 0 10px 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content-notice p:last-child,
    .ocdi__content-container-content .ocdi-create-content-content-notice p:last-child {
        margin-bottom: 0;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content span.ocdi-recommended-star,
    .ocdi__content-container-content .ocdi-create-content-content span.ocdi-recommended-star {
        background-color: #ffb900;
        width: 16px;
        height: 16px;
        border-radius: 8px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-content span.ocdi-recommended-star img,
    .ocdi__content-container-content .ocdi-create-content-content span.ocdi-recommended-star img {
        width: 10px;
        height: 10px;
        margin: 2px 3px;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-footer,
    .ocdi__content-container-content .ocdi-create-content-footer {
        padding: 30px;
        background: #fafafa;
        border-top: 1px solid #ddd;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-footer .button,
    .ocdi__content-container-content .ocdi-create-content-footer .button {
        font-size: 16px;
        line-height: 19px;
        padding: 10px 18px;
        display: flex;
        align-items: center;
    }

    .ocdi__content-container-content .ocdi-install-plugins-content-footer .button img,
    .ocdi__content-container-content .ocdi-create-content-footer .button img {
        width: 16px;
        height: auto;
        margin-right: 5px;
    }

    .ocdi__intro-text p {
        font-size: 18px;
        line-height: 26px;
        font-weight: 300;
        color: #666;
        margin: 0 0 24px;
    }

    .ocdi__intro-text ul {
        padding: 0 4%;
        list-style-type: square;
    }

    .ocdi__theme-about {
        margin-bottom: 30px;
        display: flex;
        justify-content: space-between;
    }

    .ocdi__theme-about-screenshots {
        flex: 11;
        margin-right: 30px;
    }

    .ocdi__theme-about-screenshots .screenshot {
        box-sizing: border-box;
    }

    .ocdi__theme-about-screenshots .screenshot img {
        border: 1px solid #ccd0d4;
        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
    }

    .ocdi__theme-about-screenshots .screenshot.blank {
        border: 1px solid #ccd0d4;
        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.07));
        min-height: 500px;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAALElEQVQYGWO8d+/efwYkoKioiMRjYGBC4WHhUK6A8T8QIJt8//59ZC493AAAQssKpBK4F5AAAAAASUVORK5CYII=);
    }

    .ocdi__theme-about-screenshots img {
        width: 100%;
    }

    .ocdi__theme-about-info {
        flex: 10;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .ocdi__theme-about-info .theme-title {
        display: flex;
        align-items: baseline;
    }

    .ocdi__theme-about-info .theme-title .theme-name {
        margin-bottom: 16px;
    }

    .ocdi__theme-about-info .theme-title .theme-version {
        margin-left: 10px;
        color: #72777c;
        font-size: 13px;
        line-height: 16px;
    }

    .ocdi__theme-about-info .theme-author {
        font-size: 16px;
        line-height: 19px;
        color: #72777c;
        margin: 0 0 20px;
    }

    .ocdi__theme-about-info .theme-description {
        font-size: 16px;
        line-height: 24px;
        color: #555;
        margin: 0 0 20px;
    }

    .ocdi__theme-about-info .theme-tags {
        font-size: 13px;
        line-height: 20px;
        color: #555;
        margin: 4px 0 0;
    }

    .ocdi__theme-about-info .theme-tags span {
        font-weight: 700;
    }

    .ocdi__theme-about-info .ocdi-import-mode-switch {
        float: right;
        font-size: 14px;
        line-height: 17px;
    }

    @media (max-width: 880px) {
        .ocdi__theme-about {
            flex-direction: column;
        }

        .ocdi__theme-about-screenshots {
            margin: 0 0 30px 0;
        }
    }

    .ocdi__demo-import-files {
        width: 100%;
    }

    .ocdi__demo-import-preview-image-message {
        font-style: italic;
    }

    .ocdi__title:before {
        width: auto;
        height: auto;
        font-size: inherit;
    }

    .ocdi__multi-select-import,
    .ocdi__demo-import-notice:not(:empty) {
        padding: 20px;
        margin: 30px 0;
        font-size: 14px;
        line-height: 19px;
        background-color: #fff;
        border: 1px solid #e5e5e5;
    }

    .ocdi__file-upload-container {
        border: 1px solid #ddd;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.07);
    }

    .ocdi__file-upload-container--header {
        padding: 30px;
        border-bottom: 1px solid #ddd;
        background-color: #fff;
    }

    .ocdi__file-upload-container--header h2 {
        font-size: 22px;
        line-height: 27px;
        margin: 0;
    }

    .ocdi__file-upload-container-items {
        padding: 30px 30px 0 30px;
        background-color: #fff;
        display: flex;
        flex-wrap: wrap;
    }

    .ocdi__file-upload-container-items--second-row {
        padding-top: 0;
    }

    .ocdi__file-upload-container-items .ocdi__card {
        box-shadow: none;
    }

    .ocdi__file-upload-container-items .ocdi__card-content {
        position: relative;
    }

    .ocdi__file-upload-container-items .ocdi__card-content-info {
        position: absolute;
        right: 10px;
        top: 10px;
    }

    .ocdi__file-upload-container-items .ocdi__card-content-info img {
        width: 16px;
        height: 16px;
    }

    .ocdi__file-upload-container--footer {
        padding: 30px;
        background-color: #fafafa;
        border-top: 1px solid #ddd;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .ocdi__demo-import-notice:not(:empty) {
        border: 0;
        border-left: 4px solid #00a0d2;
        box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
    }

    [dir="rtl"] .ocdi__demo-import-notice:not(:empty) {
        border: 0;
        border-right: 4px solid #00a0d2;
    }

    .ocdi__button-container {
        margin-top: 30px;
    }

    .ocdi__ajax-loader {
        font-size: 1.5em;
    }

    .ocdi__ajax-loader .spinner {
        display: inline-block;
        float: none;
        visibility: visible;
        margin-bottom: 6px;
    }

    .ocdi__gl-navigation li a {
        box-shadow: none;
    }

    .ocdi__gl-item-container {
        display: flex;
        flex-wrap: wrap;
    }

    .ocdi__gl-item {
        flex: 0 0 100%;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.07);
        box-sizing: border-box;
        background-color: #fafafa;
    }

    .ocdi__gl-item-image-container {
        display: block;
        overflow: hidden;
        position: relative;
        -webkit-backface-visibility: hidden;
        transition: opacity 0.2s ease-in-out;
    }

    .ocdi__gl-item-image-container::after {
        content: "";
        display: block;
        padding-top: 66.66666%;
    }

    .ocdi__gl-item-image {
        height: auto;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        transition: opacity 0.2s ease-in-out;
    }

    .ocdi__gl-item-image--no-image {
        display: inline-block;
        width: 50%;
        text-align: center;
        position: absolute;
        top: 45%;
        right: 25%;
        left: 25%;
    }

    .ocdi__gl-item-footer {
        margin: 0;
        padding: 8px 10px;
        border-top: 1px solid #ddd;
        background: #fff;
        background: rgba(255, 255, 255, 0.65);
    }

    .ocdi__gl-item-title {
        white-space: nowrap;
        text-overflow: ellipsis;
        display: block;
        margin: 0;
        font-size: 16px;
        line-height: 19px;
        text-align: center;
        font-weight: 500;
        color: #23282d;
        padding: 5px 0 6px;
    }

    @media (max-width: 782px) {
        .ocdi__gl-item-title {
            padding: 12px 0 13px;
        }
    }

    .ocdi__gl-item-buttons {
        display: none;
        text-align: center;
    }

    .ocdi__gl-item-button+.ocdi__gl-item-button {
        margin-left: 15px;
    }

    @media (max-width: 782px) {
        .ocdi__gl-item-button {
            width: calc(50% - 10px);
            margin-bottom: 10px;
        }

        .ocdi__gl-item-button+.ocdi__gl-item-button {
            float: left;
        }
    }

    .ocdi__gl-item:hover .ocdi__gl-item-buttons {
        display: block;
    }

    .ocdi__gl-item:hover .ocdi__gl-item-title {
        display: none;
    }

    .ocdi__gl-item:hover .ocdi__gl-item-footer {
        background: #fff;
    }

    .ocdi__gl-header {
        display: inline-block;
        width: calc(100% - 24px);
        background-color: #fff;
        border: 1px solid #ccd0d4;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.07);
        margin-bottom: 30px;
        padding: 0 11px;
    }

    .ocdi__gl-navigation {
        font-size: 13px;
        line-height: 16px;
        width: 100%;
        float: left;
    }

    .ocdi__gl-navigation ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .ocdi__gl-navigation li {
        margin: 0;
    }

    .ocdi__gl-navigation li.active a span,
    .ocdi__gl-navigation li.active a:hover span {
        padding-bottom: 14px;
        border-bottom: 4px solid #666;
    }

    .ocdi__gl-navigation li a {
        display: block;
        text-align: center;
        text-decoration: none;
        color: #23282d;
        padding: 18px 10px;
    }

    .ocdi__gl-navigation li a span {
        padding-bottom: 14px;
        border-bottom: 4px solid #fff;
    }

    .ocdi__gl-navigation li a:hover {
        color: #00a0d2;
        cursor: pointer;
    }

    .ocdi__gl-navigation li a:hover span {
        border-bottom: 4px solid #fff;
    }

    .ocdi__gl-search-input {
        width: 100%;
        margin: 10px 0;
        font-size: 13px;
        line-height: 16px;
        color: #72777c !important;
    }

    @media (min-width: 640px) {
        .ocdi__gl-navigation {
            width: calc(100% - 280px);
        }

        .ocdi__gl-navigation li {
            margin: 0 15px;
            float: left;
        }

        .ocdi__gl-navigation li a {
            padding: 18px 10px;
        }

        .ocdi__gl-search-input {
            display: inline-block;
            width: 280px;
            height: 30px;
            margin: 0;
            margin-top: 11px;
        }

        .ocdi__gl-item-container {
            margin-right: -20px;
        }

        .ocdi__gl-item {
            flex: 0 0 calc(50% - 20px);
            margin-right: 20px;
        }
    }

    @media (min-width: 1120px) {
        .ocdi__gl-item-container {
            margin-right: -30px;
        }

        .ocdi__gl-item {
            flex: 0 0 calc(33.33% - 30px);
            margin-bottom: 30px;
            margin-right: 30px;
        }
    }

    .ocdi__card {
        background: #fff;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #ddd;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.07);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .ocdi__card-content {
        padding: 30px;
    }

    .ocdi__card-content .ocdi-icon--content {
        width: 53px;
        height: 53px;
    }

    .ocdi__card-content .ocdi-icon--widgets {
        width: 56px;
        height: 49px;
    }

    .ocdi__card-content .ocdi-icon--brush {
        width: 55px;
        height: 52px;
    }

    .ocdi__card-content .ocdi-icon--redux {
        width: 82px;
        height: 70px;
    }

    .ocdi__card-content .ocdi-icon--plugins {
        width: 65px;
        height: 64px;
    }

    .ocdi__card-content .ocdi-icon--copy {
        width: 42px;
        height: 52px;
    }

    .ocdi__card-content .ocdi-icon--layout {
        width: 53px;
        height: 52px;
    }

    .ocdi__card-content .ocdi-icon-container {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ocdi__card-content h3 {
        margin: 5px 0;
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
    }

    .ocdi__card-content p {
        margin: 0;
        font-size: 13px;
        line-height: 16px;
        color: #666;
    }

    .ocdi__card-footer {
        background: #fafafa;
        box-shadow: 0px -1px 0px #ddd;
        padding: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .ocdi__card-footer .button-secondary {
        background: #fafafa;
    }

    .ocdi__card-footer input[type="file"] {
        width: 200px;
        padding: 4px 0;
    }

    .ocdi__card-footer .button-disabled {
        cursor: not-allowed;
    }

    .ocdi__card--three {
        flex: 0 0 100%;
        margin-bottom: 20px;
    }

    @media (min-width: 768px) {
        .ocdi__card--three {
            flex: 0 0 calc(50% - 10px);
            margin-right: 20px;
        }

        .ocdi__card--three:nth-child(2n) {
            margin-right: 0;
        }
    }

    @media (min-width: 1120px) {
        .ocdi__card--three {
            flex: 0 0 calc(33.33% - 20px);
            margin-bottom: 30px;
            margin-right: 30px;
        }

        .ocdi__card--three:nth-child(2n) {
            margin-right: 30px;
        }

        .ocdi__card--three:nth-child(3n) {
            margin-right: 0;
        }
    }

    .ocdi__card--four {
        flex: 0 0 100%;
        margin-bottom: 20px;
    }

    @media (min-width: 768px) {
        .ocdi__card--four {
            flex: 0 0 calc(50% - 10px);
            margin-right: 20px;
        }

        .ocdi__card--four:nth-child(2n) {
            margin-right: 0;
        }
    }

    @media (min-width: 1120px) {
        .ocdi__card--four {
            flex: 0 0 calc(50% - 15px);
            margin-bottom: 30px;
            margin-right: 30px;
        }
    }

    @keyframes ocdi-fade {
        from {
            opacity: 1;
        }

        to {
            opacity: 0;
        }
    }

    .ocdi-is-fadeout {
        animation: ocdi-fade linear 200ms 1 forwards;
    }

    .ocdi-is-fadein {
        animation: ocdi-fade linear 200ms 1 reverse forwards;
    }

    .ocdi__modal-image-container {
        width: 100%;
        height: 180px;
        margin: 0;
        overflow: hidden;
    }

    .ocdi__modal-image-container img {
        width: 100%;
    }

    .ocdi__modal-item-title {
        margin-top: 0.5em;
        font-weight: bold;
    }

    .ocdi__modal-notice.ocdi__demo-import-notice:not(:empty) {
        border: 1px solid #e5e5e5;
        border-left: 4px solid #00a0d2;
        margin: 20px 0 0;
    }

    .ocdi-loading {
        animation: 0.65s linear infinite ocdi-loading-spin;
    }

    .ocdi-loading-md {
        width: 32px;
        height: 32px;
    }

    .ocdi-loading-sm {
        width: 16px;
        height: 16px;
    }

    @keyframes ocdi-loading-spin {
        0% {
            transform: rotateZ(270deg);
        }

        100% {
            transform: rotateZ(630deg);
        }
    }

    /**
    * Customize scrollbar
    */
    .rbt-changelog-body::-webkit-scrollbar {
        width: 12px;
        /* Adjust the width of the scrollbar */
        height: 12px;
        /* Adjust the height of the scrollbar (for horizontal scroll) */
    }

    /* Customize the scrollbar thumb */
    .rbt-changelog-body::-webkit-scrollbar-thumb {
        background: #e3e0f5;
        /* Change the color of the scrollbar thumb */
        border-radius: 6px;
        /* Make the scrollbar thumb rounded */
    }

    /* Customize the scrollbar track */
    .rbt-changelog-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        /* Change the background color of the scrollbar track */
        border-radius: 6px;
        /* Make the scrollbar track rounded */
    }

    /* Customize the scrollbar thumb when hovered */
    .rbt-changelog-body::-webkit-scrollbar-thumb:hover {
        background: var(--color-secondary);
        /* Change the color of the scrollbar thumb on hover */
    }

    .rbt-inactive--page {
        margin-right: 20px;
    }

    .el-license-container {
        margin-right: 0;
    }

    /**
      * Generic style
    */

    .rbt-inactive--page ul.el-license-info>li:nth-child(odd) {
        background: #f8f8f8;
    }

    .rbt-inactive--page .el-license-container .el-license-info li {
        padding: 6px 13px;
    }

    .rbt-inactive--page h3.el-license-title img {
        max-width: 50px;
    }

    .el-license-title.text-heading .rbt-badge.badge-bg-5 {
        transform: translateY(-3px);
        display: inline-block;
    }

    .rbt-el-license-info ul.el-license-info {
        margin-top: 30px;
    }

    .rbt--video-container {
        position: relative;
    }

    .rbt--video-container a.rbt-video-play-btn {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 77.73px;
        height: 77.73px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: 115% 220%;
        text-align: center;
        border-radius: 50%;
        background-color: #2f57ef;
        color: #fff;
        font-size: 13.818px;
        font-style: normal;
        font-weight: 600;
        text-decoration: none;
    }

    a.rbt-help-center-btn:focus,
    .rbt-video-splash-play-btn-box a {
        box-shadow: none;
        border: 0;
        outline: none;
    }

    .rbt--video-container.text-xl-end.rbt-has-license-activated-play-btn a.rbt-video-play-btn:hover {
        width: 90px;
        height: 90px;
    }

    .text-end {
        text-align: right;
    }

    @keyframes scaleUP {
        0% {
            opacity: 1;
        }

        100% {
            width: 130%;
            height: 130%;
            opacity: 0;
        }
    }

    .rbt--video-container img {
        border-radius: 10px;
    }

    .rbt--video-container a.rbt-video-play-btn:hover {
        background-position: 102% 0;
    }

    .rbt--video-container img {
        border: 10px solid #f5f7fe29;
    }

    .el-license-container .el-license-field input,
    .el-license-container .el-license-field input::placeholder {
        font-size: 18px !important;
    }

    .el-license-container .el-license-field input::placeholder {
        color: #717189 !important;
    }

    .el-license-field label {
        font-weight: 500;
        color: #1d2327;
    }

    .el-license-container .el-license-title {
        color: #8fcc77;
    }

    @media only screen and (max-width: 767px) {
        .rbt-inactive--page.rbt-license-wrapper {
            padding: 20px;
        }

        .el-license-container .el-license-title {
            font-size: 23px;
            line-height: 1.4;
        }

        .el-license-container .el-license-field input {
            font-size: 16px;
        }

        .rbt--video-container a.rbt-video-play-btn {
            width: 57.73px;
            height: 57.73px;
            line-height: 57.73px;
        }

        .rbt-inactive--page.rbt-license-wrapper .el-license-container .el-license-title {
            font-size: 20px;
        }

        .el-license-active-btn input#submit {
            height: 20px;
            line-height: 20px;
            font-size: 15px;
        }
    }

    .rbt-dashboard-tab-area .ocdi__content-container p.about-description {
        margin-top: 14px;
    }

    .el-license-active-btn.rbt-license-deactivate input#submit {
        background: var(--color-primary);
        border-color: transparent;
    }

    .ranbow-dashboard-box-wrapper .ocdi {
        background: #eaedff;
        padding: 30px;
    }

    .rbt-success-alert {
        margin-bottom: 20px;
        background: #5CC659;
        color: #fff;
        display: flex;
        justify-content: space-between;
        padding: 30px;
        border-radius: 6px;
        margin-top: 20px;
    }

    .rbt-success-alert p {
        margin: 0;
        font-size: 16px;
    }

    .rbt-success-alert img {
        cursor: pointer;
    }

    .rbt-success-alert>* {
        text-wrap: wrap;
        word-wrap: break-word;
    }

    /**
    * dashboard play button style
    */
    .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
        font-size: 80px;
        font-weight: 600;
        color: #fff;
        line-height: 1;
        margin: 0;
    }

    .rbt-video-splash-play-btn-box a {
        width: 266.727px;
        height: 266.727px;
        border-radius: 50%;
        background: linear-gradient(90deg, #FE61CA 0.47%, #81F 105.96%);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .rbt-video-splash-play-btn-box img {
        filter: brightness(1);
    }

    .rbt-video-splash-play-btn-box a img {
        border: 0;
        border-radius: 0;
    }

    .rbt-video-splash-play-btn-box {
        position: absolute;
        right: -103px;
        top: 50%;
        transform: translateY(-50%);
    }

    .rbt-video-splash-play-box {
        position: relative;
        height: 358.25px;
        background-size: cover;
        background-position: center;
        overflow: hidden;
        border-radius: 10px;
        display: flex;
        align-items: center;
        padding: 0 40px;
    }

    .rbt-video-splash-area .rbt-video-splash-title {
        font-size: 24px;
        font-weight: 600;
        line-height: 1.3;
        color: #03031F;
        margin: 0;
        margin-bottom: 14px;
    }

    .rbt-video-splash-area p {
        font-size: 16px;
        color: rgba(3, 3, 31, 0.74);
        margin: 0;
    }

    .rbt-video-splash-play-box {
        margin-top: 29px;
    }

    .rbt-video-splash-play-btn-box a img {
        transform: translateX(-34px);
    }

    .rbt-splash-text {
        display: inline-block;
        position: absolute;
        left: 0;
        font-size: 80px;
        top: 50%;
        font-weight: 500;
        transform: translateY(-50%) rotate(-90deg);
        color: rgb(255 255 255 / 19%);
    }

    .rbt-video-splash-area .rbt-video-splash-title {
        font-size: 24px;
        font-weight: 600;
        color: #03031F;
        margin: 0;
        margin-bottom: 14px;
    }

    .rbt-video-splash-area p {
        font-size: 16px;
        color: rgba(3, 3, 31, 0.74);
        margin: 0;
    }

    .rbt-video-splash-play-box {
        margin-top: 29px;
    }

    .rbt-video-splash-area {
        background: #F5F7FF;
        padding: 50px;
        border-radius: 10px;
        max-width: 720px;
        margin-left: auto;
    }

    .el-license-container .el-license-title.rbt-has-license-activated-title img {
        max-width: 30px;
    }

    .rbt-has-license-activated-play-btn img {
        width: 100%;
    }

    a.rbt-help-center-btn {
        display: block;
        text-align: center;
        color: #03031fc7;
        font-weight: 500;
        margin-top: 25px;
    }

    .rbt-video-splash-play-btn-box a::after {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        border: 1.5px solid #FFFFFF;
        opacity: .3;
        content: "";
        border-radius: 50%;
        animation-name: scaleUP;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
    }

    @media (min-width: 1400px) and (max-width: 1699px) {
        .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
            font-size: 60px;
            font-weight: 600;
            color: #fff;
            line-height: 1;
            margin: 0;
        }

        .rbt-video-splash-play-btn-box a {
            width: 226.727px;
            height: 226.727px;
        }
    }

    @media (max-width: 1399px) {
        .rbt-video-splash-area {
            margin-left: 0;
        }
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
            font-size: 68px;
        }
    }

    @media (max-width: 767px) {
        .rbt-video-splash-area {
            padding: 20px;
        }

        .rbt-video-splash-play-btn-box {
            bottom: -130px;
            right: 50%;
            transform: translateX(50%);
        }

        .rbt-video-splash-play-btn-box a img {
            transform: translateY(-40px);
        }

        .rbt-splash-text {
            font-size: 50px;
            left: 50%;
            transform: translateX(-50%) rotate(-90deg) translateY(-49%);
            top: 28%;
        }

        .rbt-video-splash-play-box {
            height: 330px;
            padding: 30px 12px;
            display: block;
        }

        .rbt-video-splash-play-box .rbt-video-splash-play-box-title {
            font-size: 35px;
        }
    }

    .rainbow-dashboard-box-single .rbt-content-left {
        position: relative;
        z-index: 1;
    }

    .rainbow-dashboard-box-single .wp-core-ui .notice.is-dismissible {
        margin-left: 0;
        margin-top: 10px;
        max-width: 680px;
    }

    .rainbow-dashboard-box-single .notice.notice-warning {
        background-image: linear-gradient(to right, #2f57ef, #b966e7, #b966e7, #2f57ef) !important;
        color: #fff;
        border: 0;
        border-left: 3px solid #ffffff9c;
        margin-left: 0;
        max-width: 650px;
        background-size: 300% 100%;
    }

    .rainbow-dashboard-box-single .notice.notice-warning a {
        color: #fff;
    }

    .rainbow-dashboard-box-single .notice.notice-warning button::before {
        color: #fff;
    }

    .rainbow-dashboard-box-single #setting-error-tgmpa strong>span>a {
        background: #fff;
        border: 1px solid rgba(255, 255, 255, 0.33);
        box-shadow: 0px 23px 20.9px -21px #5B048B;
        border-radius: 4px;
        display: inline-block;
        text-decoration: none;
        padding: 10px 26px;
        color: #fff;
        color: #000;
        margin-top: 10px;
    }

    .rainbow-dashboard-box-single #setting-error-tgmpa strong>span>a:hover {
        background: #007aff;
        color: #fff;
        border-color: transparent;
    }

    .rainbow-dashboard-box-single #setting-error-tgmpa strong>span>a.dismiss-notice {
        background-color: var(--color-primary);
        color: #fff;
        border-color: transparent;
    }

    @media (min-width: 1400px) and (max-width: 1599px) {
        .rbt-content-inner .rbt-content-right img {
            height: 100%;
            transform: translateX(270px);
        }

        .rainbow-dashboard-box-single.bg-default {
            overflow: hidden;
        }
    }

    @media (max-width: 767px) {
        .rainbow-dashboard-box-single .notice.notice-warning {
            padding-right: 15px;
        }

        .rainbow-dashboard-box-single #setting-error-tgmpa strong>span>a {
            padding: 9px 9px;
        }
    }

    .notice.notice-warning.mc4wp-is-dismissible {
        position: relative;
    }
}