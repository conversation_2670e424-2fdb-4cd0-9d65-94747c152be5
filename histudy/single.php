<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package histudy
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

get_header();
$rainbow_options = Rainbow_Helper::rainbow_get_options();
$quote_class = 'quote' == get_post_type() ? 'rbt-single-blockquote-width': '';

$rainbow_blog_details_brd_image = isset($rainbow_options['rainbow_blog_details_brd_background']['background-image']) ? $rainbow_options['rainbow_blog_details_brd_background']['background-image'] : '';
$rainbow_blog_details_brd_color = isset($rainbow_options['rainbow_blog_details_brd_background']['background-color']) ? $rainbow_options['rainbow_blog_details_brd_background']['background-color'] : '';

$histudy_blog_details_gradient_bg_color = isset($rainbow_options['histudy_blog_details_gradient_bg_color']) ? $rainbow_options['histudy_blog_details_gradient_bg_color'] : '';

$brd_url = '';


?>
<!-- Start Blog Area  -->
<div class="rbt-overlay-page-wrapper">
    <div class="breadcrumb-image-container breadcrumb-style-max-width">
        <div class="breadcrumb-image-wrapper">
        <?php 
            if(isset( $rainbow_blog_details_brd_image['url']) && !empty( $rainbow_blog_details_brd_image['url']) ) { ?>
            <img src="<?php echo esc_url($rainbow_blog_details_brd_image['url']); ?>" alt="<?php echo esc_attr__('blog banner background shape images', 'histudy'); ?>">
            <?php } else { 
                if(empty($rainbow_blog_details_brd_color) && empty($histudy_blog_details_gradient_bg_color)) {
                ?>
                <img src="<?php echo Rainbow_Helper::get_img('bg/bg-image-10.jpg'); ?>" alt="<?php echo esc_attr__('blog banner background shape images', 'histudy'); ?>">
           <?php  } } ?>
        </div>
        <div class="breadcrumb-content-top text-center">
            <?php Rainbow_Helper::rainbow_singlepostmeta_top(); ?>
            <h1 class="title"><?php the_title(); ?></h1>
            <?php Rainbow_Helper::rainbow_singlepostmeta(); ?>
        </div>
    </div>
    <div class="rbt-blog-details-area rbt-section-gapBottom breadcrumb-style-max-width">
        <div class="blog-content-wrapper rbt-article-content-wrapper <?php echo esc_attr( $quote_class ); ?>">
            <div class="content">
                <?php
                while (have_posts()) :
                    the_post();
                    get_template_part('template-parts/single-post/content-single', get_post_format());
                    rb_get_post_views(get_the_ID());
                endwhile; wp_reset_query(); // End of the loop.
                ?>
            </div>
        </div>
    </div>
</div>
<?php
get_footer();